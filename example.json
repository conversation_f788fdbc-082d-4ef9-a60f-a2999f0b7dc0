{"id": "grade-1-5-3", "lenguage": "en", "common_core": "CCSS", "unit_number": "3", "unit_title": "Properties of Operations and Addition Strategies within 20", "lesson_number": "1", "grade_level": "Grade 1", "Slide 1: Quick_Review": {"slide_pedagogical_name": "Quick Review", "type": "introduction", "script": "<ol><li><p>Hello math friends! Let's warm up our brains with a quick review.</p></li><li><p>Remember how we practiced putting numbers together to make a friendly number like 10?</p></li><li><p>Finding pairs of numbers that make 10 helps us add faster!</p></li><li><p>We also thought about how to take apart bigger numbers, like a number between 11 and 19.</p></li><li><p>We learned to see the 10 hiding inside those numbers and how many ones are left over.</p></li><li><p>Let's try a few problems to get ready for today's new math adventure!</p></li></ol>", "teacher_tips": {"general_tip": "<p>Review these concepts quickly to activate prior knowledge. Encourage students to explain their thinking.</p><p>Additional Questions to Try: Ask students to name pairs of numbers that make 10 (e.g., 3 and ?). Ask them to decompose 12, 17, or 19.</p><p>Variation theory is applied in Try 1 (Slide 13), Try 2 (Slide 20), and Practice (Slide 21) by varying the position of the numbers that make ten and gradually increasing the complexity or abstractness of the problems.</p>", "misconception_tip": "<p>Students might forget their pairs to 10. Have a chart or visual aid available if needed.</p><p>When decomposing, students might struggle to see the '10' and the 'ones' separately. Revisit using ten frames or base ten blocks if this is a struggle.</p>"}, "html_css_description_of_image": "Minimalist flat design illustration on a white background. Image shows three distinct areas corresponding to the three questions. Area 1 shows a ten frame with 8 dots and 2 empty spots, next to a ten frame with 5 dots. Area 2 shows three groups of dots or blocks, representing 5, 3, and 5, with a subtle visual cue (like a dashed line or grouping symbol) around the two groups of 5. Area 3 shows one ten block and 5 single unit blocks.", "q1": "<p>8 + 2 + 5 = ?</p>", "a1": "<p>15</p>", "q2": "<p>5 + 3 + 5 = ?</p>", "a2": "<p>13</p>", "q3": "<p>15 = 10 + ?</p>", "a3": "<p>5</p>"}, "Slide 2: Learning_Goals": {"slide_pedagogical_name": "Learning Goals", "type": "introduction", "script": "<ol><li><p><PERSON><PERSON> choo! Get ready to build some math trains!</p></li><li><p>Today we are becoming train conductors of numbers!</p></li><li><p>We're going to learn how to hook up our number train cars in different ways to make adding easier.</p></li><li><p>It's like adding car 1 and 2 first, then adding car 3, or adding car 2 and 3 first, then adding car 1.</p></li><li><p>The cool thing is, no matter which cars you hook up first, your train ends up the same length!</p></li><li><p>Let's see what amazing math tricks we'll be able to do by the end of our trip!</p></li></ol>", "lg_1": "I will be able to use the Associative Property to group numbers.", "lg_2": "I will be able to group numbers to help me make ten when I add.", "lg_3": "I will be able to add three numbers by grouping to make ten.", "standards_alignment": "CCSS.MATH.CONTENT.1.OA.B.3: Apply properties of operations as strategies to add and subtract.", "teacher_tips": {"general_tip": "<p>Emphasize that the 'Associative Property' sounds fancy but just means grouping numbers differently doesn't change the total.</p><p>Connect the train analogy to the math idea – the total sum is like the total train length.</p>", "misconception_tip": "<p>Students might confuse the Associative Property with the Commutative Property (changing order). Clarify that Associative is about *grouping* with parentheses or visually.</p><p>Ensure students understand that the goal of grouping is to make the calculation easier, often by making a ten.</p>"}}, "Slide 3: Vocabulary": {"slide_pedagogical_name": "Vocabulary", "type": "introduction", "script": "<ol><li><p>Every math adventure has special words, like secret codes!</p></li><li><p>Knowing these words helps us talk like mathematicians.</p></li><li><p>Let's look at a few words that will help us understand how to group numbers today.</p></li><li><p>We will see these words as we work through our math train problems.</p></li></ol>", "teacher_tips": {"general_tip": "<p>Introduce each term clearly with its simple definition. Use gestures or actions for each word (e.g., link fingers for associate, cup hands for group, hold up total fingers for sum, point to numbers for addends).</p><p>Keep vocabulary visible throughout the lesson.</p>", "misconception_tip": "<p>Students might mix up 'addends' and 'sum'. Use visual aids consistently, like showing the addends going INTO the plus sign and the sum coming OUT.</p><p>The term 'Associative Property' might be challenging; focus on the 'grouping' concept rather than just the name.</p>"}, "term_1": "Associative Property", "definition_1_with_emoji": "You can group numbers differently when you add, and the sum stays the same! 🤝", "term_2": "Group", "definition_2_with_emoji": "To put numbers together to add them first. 🤔", "term_3": "Sum", "definition_3_with_emoji": "The total amount when you add numbers together. ➕", "term_4": "Addends", "definition_4_with_emoji": "The numbers you add together. ✨", "manipulative_used": "Linking cubes"}, "Slide 4: hook_1": {"slide_pedagogical_name": "Hook 1", "type": "hook", "script": "<ol><li><p>Let's use our linking cubes to be math train conductors!</p></li><li><p>Get 2 red cubes, 3 blue cubes, and 4 yellow cubes.</p></li><li><p>First, link your 2 red cubes and 3 blue cubes together. How many are in that group?</p></li><li><p>Now, add the 4 yellow cubes to that group. How many cubes are there total?</p></li><li><p>Okay, now take them apart.</p></li><li><p>This time, link your 3 blue cubes and 4 yellow cubes together first. How many are in that group?</p></li><li><p>Now, add the 2 red cubes to that group. How many cubes are there total?</p></li><li><p>Did you get the same total both times? Wow!</p></li></ol>", "teacher_tips": {"general_tip": "<p>Ensure all students have the correct number of cubes. Circulate to observe how they group them.</p><p>If physical manipulatives are not available, guide students to use virtual linking cubes on Polypad (polypad.org) to perform the same steps.</p>", "misconception_tip": "<p>Students might struggle to link the correct colors together first. Guide them by pointing to the colors mentioned.</p><p>They might miscount the total number of cubes. Encourage them to count slowly or double-check their count.</p>"}, "html_css_description_of_image": "Minimalist flat design illustration on a white background. Two rows of linking cubes. Row 1 shows 2 red linked to 3 blue, with a line grouping them, and then 4 yellow cubes next to them. An arrow points to the total length. Row 2 shows 3 blue linked to 4 yellow, with a line grouping them, and then 2 red cubes next to them. An arrow points to the total length. Both total lengths appear equal.", "slide_text_1": "Use linking cubes.", "slide_text_2": "Group 2 red and 3 blue.", "slide_text_3": "Then add 4 yellow.", "slide_text_4": "Now group 3 blue and 4 yellow, then add 2 red."}, "Slide 5: hook_2": {"slide_pedagogical_name": "Hook 2", "type": "hook", "script": "<ol><li><p>Great job being cube conductors! Let's try one together as a class.</p></li><li><p>Let's use cubes for 5 + 1 + 2.</p></li><li><p>I'll put out 5 green, 1 orange, and 2 purple cubes.</p></li><li><p>Let's group the 5 green and 1 orange first. That's 6 cubes.</p></li><li><p>Then we add the 2 purple cubes. 6 + 2 equals 8.</p></li><li><p>Now let's try grouping the 1 orange and 2 purple first. That's 3 cubes.</p></li><li><p>Then we add the 5 green cubes. 5 + 3 equals 8.</p></li><li><p>See? We grouped them differently, but the total is still 8 cubes! The train is the same length!</p></li></ol>", "teacher_tips": {"general_tip": "<p>As the teacher, clearly demonstrate the two different groupings using a single set of cubes on a central surface or document camera.</p><p>Use virtual linking cubes on Polypad (polypad.org) to model if physical cubes are not available, showing the grouping steps on the screen.</p>", "misconception_tip": "<p>Students might get distracted by the colors instead of focusing on the *number* of cubes. Use consistent colors or point clearly to the numbers.</p><p>They might not see the connection between the cube groups and the numbers in the addition problem. State the numbers clearly as you group the cubes.</p>"}, "html_css_description_of_image": "Minimalist flat design illustration on a white background. Two examples of cube groupings for 5 + 1 + 2. The first shows 5 green and 1 orange linked, grouped together, with 2 purple next to them. The second shows 1 orange and 2 purple linked, grouped together, with 5 green next to them. Both show a total of 8.", "slide_text_1": "Let's add 5 + 1 + 2.", "slide_text_2": "Group 5 and 1 first. Add 2.", "slide_text_3": "Group 1 and 2 first. Add 5.", "slide_text_4": "The total is the same!"}, "Slide 6: hook_3": {"slide_pedagogical_name": "Hook 3", "type": "hook", "script": "<ol><li><p>Okay, partner power! Find a math buddy!</p></li><li><p>Together, get cubes for 5 + 2 + 3. You'll need 5 of one color, 2 of another, and 3 of a third color.</p></li><li><p>Take turns showing each other how you can group the cubes two different ways.</p></li><li><p>Which way did you group the numbers first? Did you group the 5 and 2? Or the 2 and 3? Or the 5 and 3?</p></li><li><p>When you find a pair that makes 10, that's a super smart way to group!</p></li><li><p>Talk about your different groupings and if the total number of cubes changed.</p></li></ol>", "teacher_tips": {"general_tip": "<p>Assign partners and distribute cubes. Encourage partners to explain their process to each other using math words.</p><p>Remind students that they can also use virtual linking cubes on Polypad (polypad.org) with their partner, sharing their screen or working side-by-side.</p>", "misconception_tip": "<p>Some partners might just build one long train without practicing different groupings. Guide them to make two separate models showing the different groupings.</p><p>They might not recognize that 2 and 3 don't make 10, but 5 and 5 (from 5 + (2+3)) does help! Guide them toward the 'make ten' strategy.</p>"}, "html_css_description_of_image": "Minimalist flat design illustration on a white background. Two cartoon characters (gender-neutral) working together with linking cubes. One side shows cubes grouped as (5+2)+3. The other side shows cubes grouped as 5+(2+3). Arrow<PERSON> point to the total.", "slide_text_1": "Work with a partner.", "slide_text_2": "Use cubes for 5 + 2 + 3.", "slide_text_3": "Try grouping in different ways.", "slide_text_4": "Does the total change?"}, "Slide 7: teach_1_1": {"slide_pedagogical_name": "Teach 1.1", "type": "teach_1", "script": "<ol><li><p>You were amazing cube conductors! You showed how grouping numbers differently doesn't change the total sum.</p></li><li><p>This math idea is called the Associative Property of Addition. It's like our train analogy!</p></li><li><p>Hooking car 1 and 2 first, then adding car 3, makes the same train length as hooking car 2 and 3 first, then adding car 1.</p></li><li><p>When we add three numbers, we can choose which two numbers to add first. We put those two numbers in a 'group' using parentheses (those are like little math hugs!).</p></li><li><p>A really smart way to group is to find two numbers that add up to 10.</p></li><li><p>Why? Because adding 10 is super easy! Like 10 + 7 = 17.</p></li><li><p>So, when you see three numbers to add, look for a pair that makes 10!</p></li></ol>", "teacher_tips": {"general_tip": "<p>Introduce parentheses visually and explain they show which numbers are grouped together to be added first.</p><p>Explicitly state that the goal is to find the 'make ten' pair within the three addends.</p>", "misconception_tip": "<p>Students might get stuck on adding from left to right because that's what they are used to. Emphasize that the Associative Property *allows* them to change the order of adding the pairs.</p><p>They might not see the pair that makes ten if the numbers are not next to each other. Remind them they can look anywhere in the set of three numbers.</p>"}, "html_css_description_of_image": "Minimalist flat design illustration on a white background. An equation showing (A+B)+C = A+(B+C) with A, B, and C represented by small groups of dots or simple shapes. Below it, show a group of 7 dots and a group of 3 dots enclosed in parentheses, then a plus sign and a group of 5 dots. This illustrates (7+3)+5. An arrow points to the sum.", "slide_text_1": "Associative Property:", "slide_text_2": "You can group numbers differently.", "slide_text_3": "The sum stays the same!", "slide_text_4": "Look for numbers that make 10!"}, "Slide 8: teach_1_2": {"slide_pedagogical_name": "Teach 1.2", "type": "teach_1", "script": "<ol><li><p>Let's look at an example together: 6 + 4 + 3.</p></li><li><p>We want to add these three numbers. Which two numbers can we group together to make 10?</p></li><li><p>That's right! 6 and 4 make 10! We can put a math hug around 6 and 4: (6 + 4) + 3.</p></li><li><p>First, we solve what's in the math hug: 6 + 4 equals 10.</p></li><li><p>Now our problem looks like this: 10 + 3.</p></li><li><p>That's easy peasy! 10 + 3 equals 13.</p></li><li><p>So, 6 + 4 + 3 equals 13.</p></li></ol>", "teacher_tips": {"general_tip": "<p>Model writing the parentheses clearly. Use a different color marker to show the '10' resulting from the first step.</p><p>Reinforce the 'making ten' strategy as the reason for this specific grouping choice.</p>", "misconception_tip": "<p>Students might correctly add 6 + 4 to get 10 but then forget to add the last number (3). Remind them there are *three* addends, so the sum must account for all of them.</p><p>Ensure they understand that the parentheses mean 'do this first'.</p>"}, "html_css_description_of_image": "Minimalist flat design illustration on a white background. The equation 6 + 4 + 3 is shown. Below it, parentheses group 6 and 4: (6+4) + 3. Below that, 10 + 3 is shown. Finally, the answer 13 is shown.", "slide_text_1": "Add: 6 + 4 + 3.", "slide_text_2": "Group (6 + 4).", "slide_text_3": "(6 + 4) = 10.", "slide_text_4": "10 + 3 = 13. The sum is 13."}, "Slide 9: teach_1_3": {"slide_pedagogical_name": "Teach 1.3", "type": "teach_1", "script": "<ol><li><p>Okay, let's try another one that's just a little different: 7 + 8 + 3.</p></li><li><p>We have three numbers: 7, 8, and 3. Can we find two numbers here that make 10?</p></li><li><p>Yes! 7 and 3 make 10! Even though they are not right next to each other, the Associative Property says we can group them!</p></li><li><p>We can write it like this: (7 + 3) + 8. We group the 7 and the 3 first.</p></li><li><p>First, solve the math hug: 7 + 3 equals 10.</p></li><li><p>Now the problem is 10 + 8.</p></li><li><p>What is 10 + 8? That's 18!</p></li><li><p>So, 7 + 8 + 3 equals 18!</p></li></ol>", "teacher_tips": {"general_tip": "<p>This example shows that the numbers that make ten might not be adjacent. Explicitly show rewriting the problem with the preferred grouping.</p><p>Emphasize the flexibility of the Associative Property (the train analogy again - you can hook cars in any order before linking them all up).</p>", "misconception_tip": "<p>Students might struggle to see the make-ten pair when the numbers are separated. Use visual aids like circling the numbers 7 and 3 in the original problem.</p><p>They might try to add 7 + 8 first, which is harder. Acknowledge they *could* do that, but explain why (7+3)+8 is an easier strategy.</p>"}, "html_css_description_of_image": "Minimalist flat design illustration on a white background. The equation 7 + 8 + 3 is shown. Below it, the numbers are rearranged and grouped: (7+3) + 8. Below that, 10 + 8 is shown. Finally, the answer 18 is shown.", "slide_text_1": "Add: 7 + 8 + 3.", "slide_text_2": "Find numbers that make 10.", "slide_text_3": "Group (7 + 3).", "slide_text_4": "(7 + 3) + 8 = 10 + 8 = 18."}, "Slide 10: talk_1_1": {"slide_pedagogical_name": "Talk 1.1", "type": "talk_1", "script": "<ol><li><p>It's time to talk math with a partner!</p></li><li><p>Look at the question on the screen.</p></li><li><p>Talk with your partner about what the math word 'sum' means.</p></li><li><p>What do you do to find the sum?</p></li><li><p>When you and your partner agree, write your answer on your whiteboard.</p></li><li><p>Hold up your whiteboards so I can see your brilliant thinking!</p></li></ol>", "teacher_tips": {"general_tip": "<p>Give partners 1-2 minutes to discuss. Listen to their conversations to gauge understanding.</p><p>Ensure all students are participating in the discussion and writing their answer.</p>", "misconception_tip": "<p>Misconception: Students might define 'addends' instead of 'sum'.</p><p>Correction: Ask them, 'If you add 2 and 3, what is 5 called?' Point to the sum in a previous example on the board.</p>"}, "html_css_description_of_image": "Minimalist flat design illustration on a white background. A simple addition problem like 2 + 3 = 5 with a label pointing to the '5' that says 'Sum'.", "slide_q": "<p>What does 'sum' mean?</p>", "slide_a": "<p>The sum is the total when you add numbers together.</p>"}, "Slide 11: talk_1_2": {"slide_pedagogical_name": "Talk 1.2", "type": "talk_1", "script": "<ol><li><p>Okay partners, look at this math problem and how someone started to solve it.</p></li><li><p>The problem is 8 + 2 + 5.</p></li><li><p>Someone grouped (8 + 2) and got 10.</p></li><li><p>But they stopped there! Look carefully.</p></li><li><p>Talk with your partner: What mistake did they make?</p></li><li><p>Write the mistake on your whiteboard.</p></li><li><p>Hold up your whiteboards!</p></li></ol>", "teacher_tips": {"general_tip": "<p>Ensure students focus on the math error, not just the presentation.</p><p>Encourage them to explain *why* it's a mistake in their discussion.</p>", "misconception_tip": "<p>Misconception: Students might not see that there are three numbers to be added.</p><p>Correction: Ask, 'How many numbers did we start with? How many numbers did they add?' Guide them to see the missing third addend.</p>"}, "html_css_description_of_image": "Minimalist flat design illustration on a white background. The problem 8 + 2 + 5 is shown. Below it, the step (8+2) = 10 is shown clearly, perhaps with a large checkmark next to it, but the final answer is missing or shows just '10'.", "slide_q": "<p>What mistake did the student make?</p>", "slide_a": "<p>They forgot to add the last number!</p>"}, "Slide 12: talk_1_3": {"slide_pedagogical_name": "Talk 1.3", "type": "talk_1", "script": "<ol><li><p>Now look at this problem. Someone started it for us.</p></li><li><p>The problem is 5 + 9 + 5.</p></li><li><p>They grouped (5 + 5) and got 10.</p></li><li><p>It says 10 + ?</p></li><li><p>Talk with your partner: How can you finish this problem?</p></li><li><p>What is the final sum?</p></li><li><p>Write the final sum on your whiteboard and hold it up!</p></li></ol>", "teacher_tips": {"general_tip": "<p>This checks if students can complete the final addition step after making ten.</p><p>Listen for students explaining the step 10 + 9 = 19.</p>", "misconception_tip": "<p>Misconception: Students might add the 9 to the 5 from the original problem again instead of to the 10.</p><p>Correction: Point to the '10' and say, 'This 10 came from the first two numbers. Now we just have the 10 and the last number left to add.'</p>"}, "html_css_description_of_image": "Minimalist flat design illustration on a white background. The problem 5 + 9 + 5 is shown. Below it, the grouping (5+5) + 9 is shown. Below that, the step 10 + ? = is shown.", "slide_q": "<p>How can you finish this problem?</p>", "slide_a": "<p>Add 10 + 9 to get 19.</p>"}, "Slide 13: try_1": {"slide_pedagogical_name": "Try 1", "type": "try_1", "script": "<ol><li><p>Okay mathematicians, it's your turn to try some problems on your own!</p></li><li><p>Remember to look for numbers that make 10 to help you add.</p></li><li><p>You can use your linking cubes if you like, or just use your math brain!</p></li><li><p>Solve each problem on your whiteboard.</p></li><li><p>Raise your whiteboard when you have an answer for the first problem.</p></li><li><p>We will check them together before you move to the next one.</p></li></ol>", "teacher_tips": {"general_tip": "<p>Circulate as students work, providing support as needed. Encourage them to show their grouping step (with parentheses or circling).</p><p>Variation theory was applied here: Q1 has the make-ten pair as the first two numbers, Q2 has the make-ten pair as the first and last numbers, and Q3 has the make-ten pair as the middle and last numbers. This helps students practice finding the pair regardless of position.</p>", "misconception_tip": "<p>Q1: Students might add 1 + 9 correctly but forget to add the 7.</p><p>Q2: Students might add 6 + 5 first instead of using the make-ten strategy.</p><p>Q3: Students might not see that 2 and 8 make 10.</p>"}, "q1": "<p>1 + 9 + 7 = ?</p>", "a1": "<p>17</p>", "q2": "<p>6 + 5 + 4 = ?</p>", "a2": "<p>15</p>", "q3": "<p>2 + 8 + 8 = ?</p>", "a3": "<p>18</p>"}, "Slide 14: teach_2_1": {"slide_pedagogical_name": "Teach 2.1", "type": "teach_2", "script": "<ol><li><p>You are getting so good at finding those make-ten pairs!</p></li><li><p>Now, let's think about the problems just using the numbers.</p></li><li><p>We can still use our train conductor brains to group the numbers that make 10, even without cubes!</p></li><li><p>Just like our train analogy, we are looking for the two car numbers that are easiest to hook together first to make 10.</p></li><li><p>When you see a problem with three numbers to add, scan through them.</p></li><li><p>Say the numbers out loud, looking for that special pair that adds up to 10.</p></li><li><p>Once you find them, mentally (or by writing parentheses) group them together.</p></li></ol>", "teacher_tips": {"general_tip": "<p>Transition from concrete (cubes) to abstract (numbers/equations). Encourage students to visualize the grouping.</p><p>Use dramatic scanning gestures when talking about looking for the make-ten pair in the equation.</p>", "misconception_tip": "<p>Students might still rely heavily on counting all the numbers individually instead of using the make-ten strategy.</p><p>Some students might get stuck if the numbers aren't in an order that makes 10 immediately when read left-to-right.</p>"}, "html_css_description_of_image": "Minimalist flat design illustration on a white background. Three numbers (e.g., 8, 7, 2) are shown in a row. A dashed line or light circle visually connects 8 and 2, indicating a potential grouping choice.", "slide_text_1": "Add three numbers.", "slide_text_2": "Look for numbers that make 10.", "slide_text_3": "Mentally group them.", "slide_text_4": "Make 10 first!"}, "Slide 15: teach_2_2": {"slide_pedagogical_name": "Teach 2.2", "type": "teach_2", "script": "<ol><li><p>Let's look at this problem: 8 + 7 + 2.</p></li><li><p>I see the numbers 8, 7, and 2.</p></li><li><p>I'm scanning... do I see two numbers that make 10?</p></li><li><p>Ah ha! 8 and 2 make 10! I'm going to group them first.</p></li><li><p>Mentally, I think (8 + 2) + 7. Or I can write it that way.</p></li><li><p>Okay, solve the group first: 8 + 2 equals 10.</p></li><li><p>Now I have 10 + 7.</p></li><li><p>10 + 7 equals 17! So, 8 + 7 + 2 equals 17.</p></li></ol>", "teacher_tips": {"general_tip": "<p>Model the 'scanning' process. Show the step-by-step writing of the grouped equation and the resulting sum.</p><p>Use think-alouds to explain your process of finding the pair and why you choose to group that way.</p>", "misconception_tip": "<p>Students might try to add 8+7 first, get 15, then add 2 to get 17. While the answer is correct, they missed using the strategy. Acknowledge their correct answer but guide them to try the make-ten way too.</p><p>Some might incorrectly pair numbers that don't make 10, like 8 and 7.</p>"}, "html_css_description_of_image": "Minimalist flat design illustration on a white background. The equation 8 + 7 + 2 is shown. Below it, the grouping (8+2) + 7 is shown. Below that, 10 + 7 is shown. Finally, the answer 17 is shown.", "slide_text_1": "Add: 8 + 7 + 2.", "slide_text_2": "Group (8 + 2).", "slide_text_3": "(8 + 2) = 10.", "slide_text_4": "10 + 7 = 17. The sum is 17."}, "Slide 16: teach_2_3": {"slide_pedagogical_name": "Teach 2.3", "type": "teach_2", "script": "<ol><li><p>Let's try one more like that: 9 + 6 + 1.</p></li><li><p>The numbers are 9, 6, and 1.</p></li><li><p>Do you see a pair that makes 10?</p></li><li><p>Yes! 9 and 1 make 10! Even though 6 is in the middle, we can still group 9 and 1 first.</p></li><li><p>We can think (9 + 1) + 6.</p></li><li><p>What is 9 + 1? It's 10.</p></li><li><p>Now we add 10 + 6.</p></li><li><p>10 + 6 equals 16! So, 9 + 6 + 1 equals 16.</p></li></ol>", "teacher_tips": {"general_tip": "<p>Reinforce that the order of the numbers doesn't stop us from grouping the make-ten pair.</p><p>This example (variation) uses numbers that require finding the pair that isn't the first two or the last two.</p>", "misconception_tip": "<p>Students might add 9+6 first (15), then add 1 (16). While correct, guide them to the make-ten strategy as the more efficient method for this type of problem.</p><p>They might overlook the 1 hiding at the end of the sequence.</p>"}, "html_css_description_of_image": "Minimalist flat design illustration on a white background. The equation 9 + 6 + 1 is shown. Below it, the rearranged grouping (9+1) + 6 is shown. Below that, 10 + 6 is shown. Finally, the answer 16 is shown.", "slide_text_1": "Add: 9 + 6 + 1.", "slide_text_2": "Find numbers that make 10.", "slide_text_3": "Group (9 + 1).", "slide_text_4": "(9 + 1) + 6 = 10 + 6 = 16."}, "Slide 17: talk_2_1": {"slide_pedagogical_name": "Talk 2.1", "type": "talk_2", "script": "<ol><li><p>Time to chat with your partner again!</p></li><li><p>We've been talking a lot about finding numbers that make 10.</p></li><li><p>Look at the question on the screen.</p></li><li><p>Talk with your partner about *why* making 10 first is a helpful strategy when you're adding three numbers.</p></li><li><p>Write your ideas on your whiteboard.</p></li><li><p>Show me your whiteboards when you are ready!</p></li></ol>", "teacher_tips": {"general_tip": "<p>Encourage partners to explain their reasoning using words like 'easy,' 'fast,' or 'friendly number.'</p><p>Listen for students connecting making 10 to adding with single-digit numbers, which they find easier.</p>", "misconception_tip": "<p>Misconception: Students might only say 'because the teacher told me to' or 'because it's the rule' without understanding the mathematical efficiency.</p><p>Correction: Ask, 'Is it easier to add 7+8+3 or 10+8? Why?' Guide them toward the concept of adding to 10 being a benchmark.</p>"}, "html_css_description_of_image": "Minimalist flat design illustration on a white background. A cloud shape with a large number 10 inside, perhaps with a smiley face, next to a '+' sign and a smaller number, like 7.", "slide_q": "<p>Why is making 10 helpful when adding?</p>", "slide_a": "<p>It makes adding easier and faster!</p>"}, "Slide 18: talk_2_2": {"slide_pedagogical_name": "Talk 2.2", "type": "talk_2", "script": "<ol><li><p>Here's another problem someone solved. The problem is 7 + 4 + 3.</p></li><li><p>They wrote (7 + 4) = 11.</p></li><li><p>Then they wrote 11.</p></li><li><p>Talk with your partner: What mistake did they make this time?</p></li><li><p>Write the mistake on your whiteboard.</p></li><li><p>Hold up your whiteboards so I can see!</p></li></ol>", "teacher_tips": {"general_tip": "<p>This error check focuses on finding *all* the numbers, similar to Talk 1.2, but in the more abstract presentation of Teach 2.x.</p><p>Ensure partners are discussing the 'missing' part of the problem.</p>", "misconception_tip": "<p>Misconception: Students might only see the calculation error (if there was one) or miss that the *strategic* make-ten pairing wasn't used, but the primary error here is not finishing the problem.</p><p>Correction: Ask, 'How many numbers did we start with? Did they add all three numbers?'</p>"}, "html_css_description_of_image": "Minimalist flat design illustration on a white background. The problem 7 + 4 + 3 is shown. Below it, the step (7+4) = 11 is shown correctly, perhaps with a checkmark, but the problem stops there with just '11' as the answer.", "slide_q": "<p>What mistake did they make here?</p>", "slide_a": "<p>They only added two numbers and stopped.</p>"}, "Slide 19: talk_2_3": {"slide_pedagogical_name": "Talk 2.3", "type": "talk_2", "script": "<ol><li><p>Last talk problem! Look at this one that's partially finished: 4 + 9 + 6.</p></li><li><p>Someone grouped (4 + 6) and got 10. Good job finding the make-ten pair!</p></li><li><p>It says 10 + ?</p></li><li><p>Talk with your partner: What is the last step to find the sum?</p></li><li><p>What is the final answer?</p></li><li><p>Write the answer on your whiteboard and show me!</p></li></ol>", "teacher_tips": {"general_tip": "<p>This checks the final step after successful grouping and making ten.</p><p>Listen for students articulating the addition of the remaining number to the ten.</p>", "misconception_tip": "<p>Misconception: Students might get 10 and then try to add the other two original numbers instead of just the one that wasn't used in the make-ten pair.</p><p>Correction: Point to the original problem and say, 'We used the 4 and the 6 to make 10. Which number is left?'</p>"}, "html_css_description_of_image": "Minimalist flat design illustration on a white background. The problem 4 + 9 + 6 is shown. Below it, the grouping (4+6) + 9 is shown. Below that, the step 10 + ? = is shown.", "slide_q": "<p>What is the last step?</p>", "slide_a": "<p>Add 10 + 9 to get 19.</p>"}, "Slide 20: try_2": {"slide_pedagogical_name": "Try 2", "type": "try_2", "script": "<ol><li><p>Ready for 'Try 2'?</p></li><li><p>These problems are similar to what we just practiced.</p></li><li><p>Look at the three numbers in each problem.</p></li><li><p>Scan the numbers and find the pair that makes 10.</p></li><li><p>Group that pair first, make 10, then add the last number!</p></li><li><p>Solve each one on your whiteboard and hold it up when you finish the first problem.</p></li></ol>", "teacher_tips": {"general_tip": "<p>Encourage students to write down the intermediate step (e.g., (3+7)+5) on their whiteboard before the final answer.</p><p>Variation theory is applied here: Q1 has the make-ten pair as the first and last numbers, Q2 has the make-ten pair as the first and last numbers again but with different numbers, and Q3 has the make-ten pair as the first and last numbers with slightly larger non-paired numbers resulting in a higher sum but still within 20.</p>", "misconception_tip": "<p>Q1: Students might add 3+5 first.</p><p>Q2: Students might miss that 8 and 2 make 10.</p><p>Q3: Students might struggle with 10+7 or 10+6 if they don't recognize those sums instantly.</p>"}, "q1": "<p>3 + 5 + 7 = ?</p>", "a1": "<p>15</p>", "q2": "<p>8 + 6 + 2 = ?</p>", "a2": "<p>16</p>", "q3": "<p>5 + 7 + 5 = ?</p>", "a3": "<p>17</p>"}, "manipulative_used": "Linking cubes", "Slide 21: practice": {"slide_pedagogical_name": "Practice", "type": "practice", "script": "<ol><li><p>You've worked hard today using the Associative Property to make 10!</p></li><li><p>Now it's time to practice everything you've learned.</p></li><li><p>Here are 12 problems for you to solve.</p></li><li><p>Remember to look for the pair that makes 10 in each problem.</p></li><li><p>Group those numbers first, find the 10, and then add the last number.</p></li><li><p>You can solve these problems on your own paper or in your math journal.</p></li></ol>", "teacher_tips": {"general_tip": "<p>This section can be independent practice or done in pairs. Encourage students to show their grouping step for accountability and to reinforce the strategy.</p><p>Variation theory is applied across these 12 questions. They progress from simpler cases (make-ten pair is first two numbers) to slightly more complex ones where the make-ten pair is not adjacent and includes slightly larger non-paired numbers, ensuring students practice the strategy regardless of number position and resulting sum within 20. The sequence includes repetitions and slight changes to consolidate learning.</p>", "misconception_tip": "<p>Misconception: Students might add the numbers from left to right without looking for the make-ten pair (Q1, Q3, Q7).</p><p>Misconception: Students might correctly make ten but forget to add the third number (Q2, Q5, Q9).</p><p>Misconception: Students might struggle to find the make-ten pair when the numbers are separated (Q4, Q8, Q11, Q12).</p><p>Misconception: Students might struggle with the final step of adding a single-digit number to 10 (Q6, Q10).</p><p>Misconception: Students might make calculation errors, especially when the non-paired number is larger than 5 (Q9, Q11, Q12).</p><p>Misconception: Students might struggle with repeated numbers in the problem (Q5, Q10).</p>"}, "q1": "<p>1 + 9 + 4 = ?</p>", "a1": "<p>14</p>", "q2": "<p>3 + 7 + 8 = ?</p>", "a2": "<p>18</p>", "q3": "<p>6 + 2 + 4 = ?</p>", "a3": "<p>12</p>", "q4": "<p>5 + 8 + 5 = ?</p>", "a4": "<p>18</p>", "q5": "<p>2 + 5 + 8 = ?</p>", "a5": "<p>15</p>", "q6": "<p>7 + 3 + 6 = ?</p>", "a6": "<p>16</p>", "q7": "<p>4 + 9 + 6 = ?</p>", "a7": "<p>19</p>", "q8": "<p>8 + 5 + 2 = ?</p>", "a8": "<p>15</p>", "q9": "<p>9 + 4 + 1 = ?</p>", "a9": "<p>14</p>", "q10": "<p>5 + 6 + 5 = ?</p>", "a10": "<p>16</p>", "q11": "<p>3 + 8 + 7 = ?</p>", "a11": "<p>18</p>", "q12": "<p>7 + 4 + 3 = ?</p>", "a12": "<p>14</p>"}, "summary_for_writer": {"summary_text": "This lesson teaches Grade 1 students to use the Associative Property of Addition within 20, focusing on the 'make ten' strategy when adding three numbers.\nStudents learn to identify and group numbers that sum to ten first, making the overall addition simpler.", "difficult_examples": ["3 + 8 + 7 = ?", "7 + 4 + 3 = ?"]}, "Slide 22: on_ramp_teach_1": {"slide_pedagogical_name": "On-Ramp Teach 1", "type": "on_ramp", "script": "<ol><li><p>Sometimes, to be a super Associative Property user, we first need to be super friends with the number 10!</p></li><li><p>Finding a pair of numbers that add up to exactly 10 is a really important skill.</p></li><li><p>Think of all the ways you can make 10 using two numbers. Like 1 and 9, 2 and 8, 3 and 7, 4 and 6, and 5 and 5.</p></li><li><p>Knowing these pairs fast in your head is like having a secret code for today's lesson!</p></li><li><p>If you see a 7, you know its friend is a 3 to make 10.</p></li><li><p>If you see an 8, you know its friend is a 2 to make 10.</p></li><li><p>Being good at finding these pairs makes the make-ten strategy easy!</p></li></ol>", "teacher_tips": {"general_tip": "<p>Spend time chanting or singing songs about pairs to 10. Use ten frames or number bonds visuals to reinforce these pairs.</p><p>Consider having students use fingers or objects to show pairs that make ten.</p>", "misconception_tip": "<p>Misconception: Students might know some pairs to 10 but not all of them automatically.</p><p>Correction: Use flashcards, quick games, or a visual chart of pairs to 10 to build fluency. Focus on one or two pairs at a time if needed.</p>"}, "html_css_description_of_image": "Minimalist flat design illustration on a white background. A central number 10 in a circle. Arrows point from the 10 circle to pairs of numbers connected by plus signs, such as 1+9, 2+8, 3+7, 4+6, 5+5.", "slide_text_1": "What numbers add to 10?", "slide_text_2": "Pairs like 1+9 and 2+8.", "slide_text_3": "Know your pairs to 10!", "slide_text_4": "It helps you add faster."}, "Slide 23: on_ramp_talk_1": {"slide_pedagogical_name": "On-Ramp Talk 1", "type": "on_ramp", "script": "<ol><li><p>Let's check how well you know your pairs to 10!</p></li><li><p>Talk with your partner: Can you name all the number pairs that add up to exactly 10?</p></li><li><p>Try to list them all!</p></li><li><p>When you think you have them, write them on your whiteboard.</p></li><li><p>Show me your whiteboards!</p></li></ol>", "teacher_tips": {"general_tip": "<p>Listen for students listing the pairs systematically (e.g., starting with 0+10, then 1+9, etc.).</p><p>Provide support by showing a ten frame or part of a ten frame if they get stuck.</p>", "misconception_tip": "<p>Misconception: Students might only name a few pairs or list sums that are not 10 (e.g., 3+6).</p><p>Correction: Have them use their fingers or cubes to check if the pair really makes 10. Review the pairs listed on Slide 22.</p>"}, "html_css_description_of_image": "Minimalist flat design illustration on a white background. A large number 10 with several empty boxes or question marks around it, suggesting numbers that add up to it.", "slide_q": "<p>What number pairs make 10?</p>", "slide_a": "<p>1 and 9, 2 and 8, 3 and 7, 4 and 6, 5 and 5 (and 0 and 10).</p>"}, "Slide 24: on_ramp_try_1": {"slide_pedagogical_name": "On-Ramp Try 1", "type": "on_ramp", "script": "<ol><li><p>Let's practice finding those pairs to 10.</p></li><li><p>Look at each problem on the screen.</p></li><li><p>Figure out what number is missing to make 10.</p></li><li><p>Solve each one on your whiteboard.</p></li><li><p>Hold up your whiteboard after you solve the first one!</p></li></ol>", "teacher_tips": {"general_tip": "<p>This section provides direct practice on the prerequisite skill. Use ten frames as a visual aid for students who are struggling.</p><p>Variation theory is applied here: Q1 asks for the second addend, Q2 asks for the first addend, and Q3 requires identifying a pair within a set of three numbers, making it slightly more complex and closer to the main lesson's skill.</p>", "misconception_tip": "<p>Q1: Students might struggle to find the missing part if they don't know the pairs to 10 fluently.</p><p>Q2: Students might be confused by the missing number being first.</p><p>Q3: Students might struggle to scan the three numbers and find the correct two that make 10.</p>"}, "q1": "<p>4 + ? = 10</p>", "a1": "<p>6</p>", "q2": "<p>? + 8 = 10</p>", "a2": "<p>2</p>", "q3": "<p>Find two numbers that make 10: 5, 2, 5</p>", "a3": "<p>5 and 5</p>"}, "Slide 25: Lesson_Guide_PDF_Link": {"slide_pedagogical_name": "Lesson Guide PDF Link", "type": "printables", "link_to_lesson_guide_pdf": "/path/to/lesson_guide_unit5_lesson42.pdf", "teacher_tips": {"general_tip": "<p>Refer to the Lesson Guide PDF for detailed teaching instructions, material lists, differentiation strategies, and possible classroom management tips.</p>", "misconception_tip": ""}}, "Slide 26: Practice_PDF_Link": {"slide_pedagogical_name": "Practice PDF Link", "type": "printables", "link_to_practice_pdf": "/path/to/practice_unit5_lesson42.pdf", "teacher_tips": {"general_tip": "<p>The Practice PDF contains printable versions of the problems from Slide 21 for independent student work or homework.</p>", "misconception_tip": ""}}, "remediation_suggestion": "The On-Ramp slides (22-24) are designed to support students who need to build fluency with number pairs that make 10.\nThis foundational skill is critical for efficiently using the make-ten strategy taught in the main lesson.\nTeachers can spend additional time on these slides or the related On-Ramp practice materials for targeted remediation.", "acceleration_suggestion": "For students who quickly grasp the concept of using the Associative Property to make ten, refer to the Accelerator PDF (Slide 27).\nThese activities can extend their understanding by applying the strategy to problems with more than three addends or exploring other properties of addition.\nThis allows advanced students to deepen their understanding and maintain engagement."}