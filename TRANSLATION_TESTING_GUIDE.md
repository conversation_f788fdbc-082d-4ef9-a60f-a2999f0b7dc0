# Translation Testing Guide

## Prerequisites
1. Ensure the development server is running (`npm run dev`)
2. Have the `Grade_1_Lesson_49_DUMMY_Translated_ES.json` file ready
3. Ensure you have admin access to upload files

## Step-by-Step Testing Instructions

### Phase 1: Upload Spanish Translation

1. **Navigate to Upload Page**
   - Go to `/upload` in your browser
   - Login with admin credentials if required

2. **Upload Spanish Lesson**
   - Select `Grade_1_Lesson_49_DUMMY_Translated_ES.json`
   - Click upload
   - **Expected Result**: Success message indicating HTML was copied from English version
   - **Check Console**: Should show "HTML copied from English version (X slides)" instead of "X jobs created"

3. **Verify Database State**
   - Check MongoDB for two documents with `documentId: "6-49-Grade 1"`
   - One with `lang: "en"` (English)
   - One with `lang: "esp"` (Spanish)
   - Both should have similar HTML content in `html_css_description_of_image` fields

### Phase 2: Test Language Button Availability

1. **Navigate to Lesson Without Translation**
   - Go to any lesson other than Grade 1, Unit 6, Lesson 49
   - **Expected Result**: Language button should be disabled/grayed out
   - **Tooltip**: Should show "Language (English only)"

2. **Navigate to Lesson With Translation**
   - Go to Grade 1, Unit 6, Lesson 49 using filters
   - **Expected Result**: Language button should be enabled
   - **Tooltip**: Should show "Change language"

### Phase 3: Test Language Switching

1. **Switch to Spanish**
   - Click the enabled language button
   - Select "Español" from dropdown
   - **Expected Results**:
     - URL should update to include `?lang=esp`
     - Content should switch to Spanish
     - Slide titles should be in Spanish (e.g., "Repaso Rápido" instead of "Quick Review")
     - Navigation should reset to slide 1

2. **Switch Back to English**
   - Click language button again
   - Select "English"
   - **Expected Results**:
     - URL should remove `?lang=esp` parameter
     - Content should switch back to English
     - Navigation should reset to slide 1

### Phase 4: Test URL Persistence

1. **Copy Spanish URL**
   - While viewing Spanish version, copy the URL (should include `?lang=esp`)
   - Open in new tab or refresh page
   - **Expected Result**: Page should load in Spanish

2. **Copy English URL**
   - While viewing English version, copy the URL (should not include lang parameter)
   - Open in new tab or refresh page
   - **Expected Result**: Page should load in English

### Phase 5: Test Filter Isolation

1. **Check Grade Filter**
   - Open grade dropdown
   - **Expected Result**: Should only show English lessons, no "Grado 1" entries

2. **Check Unit Filter**
   - Select Grade 1, then check unit dropdown
   - **Expected Result**: Should only show English unit titles

3. **Check Lesson Filter**
   - Select Grade 1, Unit 6, then check lesson dropdown
   - **Expected Result**: Should only show English lesson titles

### Phase 6: Test Content Verification

1. **Compare Slide Content**
   - Navigate through several slides in both languages
   - **Expected Results**:
     - Scripts should be translated
     - Teacher tips should be translated
     - Questions and answers should be translated
     - HTML content should be identical (copied from English)

2. **Check Specific Slides**
   - Slide 1: "Quick Review" → "Repaso Rápido"
   - Slide 2: "Learning Goals" → "Objetivos de Aprendizaje"
   - Slide 10: "Talk 1.1" → "Hablar 1.1"
   - Slide 13: "Try 1" → "Intentar 1"

### Phase 7: Test Error Handling

1. **Invalid Language Parameter**
   - Manually add `?lang=invalid` to URL
   - **Expected Result**: Should fallback to English

2. **Missing Translation**
   - Navigate to lesson without translation while having `?lang=esp` in URL
   - **Expected Result**: Should show English content and disable language button

## Expected Console Messages

### During Upload
```
Processing document: Grade_1_Lesson_49_DUMMY_Translated_ES.json
Language detected: esp
Copying HTML from English version...
Copied HTML content for X slides from English to Spanish version
```

### During Language Check
```
Translation availability: true
Language button enabled for Grade 1, Unit 6, Lesson 49
```

### During Language Switch
```
Language updated to: esp
Loading lesson data for documentId: 6-49-Grade 1, lang: esp
```

## Troubleshooting

### Language Button Not Enabling
- Check if Spanish document exists in database with correct `documentId` and `lang: "esp"`
- Verify `checkTranslationAvailability()` is being called
- Check browser console for API errors

### Content Not Switching
- Verify URL parameter is being set correctly
- Check if `lang` parameter is being passed to API calls
- Ensure cache is being cleared when switching languages

### HTML Content Missing
- Verify English lesson has HTML content in `html_css_description_of_image` fields
- Check if `copyHtmlFromEnglishVersion()` function executed successfully
- Ensure both English and Spanish documents have same HTML content

### Filter Issues
- Verify `available-options` API includes `lang: 'en'` filter
- Check if Spanish documents are being excluded from filter results

## Success Criteria

✅ Spanish lesson uploads successfully without creating jobs
✅ Language button enables only for lessons with translations
✅ Content switches between languages correctly
✅ URL parameters persist language preference
✅ Filters show only English lessons
✅ HTML content is identical between language versions
✅ Page refreshes maintain language selection
✅ Error handling works for invalid scenarios

## Database Verification Queries

```javascript
// Check for both language versions
db.jsondocuments.find({documentId: "6-49-Grade 1"})

// Verify English version
db.jsondocuments.findOne({documentId: "6-49-Grade 1", lang: "en"})

// Verify Spanish version  
db.jsondocuments.findOne({documentId: "6-49-Grade 1", lang: "esp"})

// Check HTML content similarity
db.jsondocuments.find({documentId: "6-49-Grade 1"}, {"content.Slide 1: Quick_Review.html_css_description_of_image": 1, lang: 1})
```
