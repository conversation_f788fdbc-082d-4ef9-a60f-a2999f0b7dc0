"use client"

import type React from "react"

import { useState, useEffect, useCallback } from "react"
import { ChevronDown, Maximize2, Settings, LogOut, Home, Globe, ChevronRight } from "lucide-react"
import { Sidebar } from "@/components/sidebar"
import { SlideContent } from "@/components/slide-content"
import { TabContent } from "@/components/tab-content"

export default function MathLessonSlider() {
  const [currentSlide, setCurrentSlide] = useState(1)
  const [sidebarOpen, setSidebarOpen] = useState(true) // Set sidebar to open by default
  const [highContrast, setHighContrast] = useState(false)
  const [userMenuOpen, setUserMenuOpen] = useState(false)
  const totalSlides = 27
  const [activeTab, setActiveTab] = useState("home")

  const [showSettingsModal, setShowSettingsModal] = useState(false)
  const [userName, setUserName] = useState("<PERSON> Smith")
  const [isEditingName, setIsEditingName] = useState(false)
  const [userSettings, setUserSettings] = useState({
    school: "Lincoln Elementary",
    district: "Springfield School District",
    curriculum: "CCSS",
    grade: "5",
  })

  const [selectedLanguage, setSelectedLanguage] = useState("en")

  // Language menu state
  const [languageMenuOpen, setLanguageMenuOpen] = useState(false)

  const [curriculumMenuOpen, setCurriculumMenuOpen] = useState(false)
  const [gradeMenuOpen, setGradeMenuOpen] = useState(false)
  const [unitMenuOpen, setUnitMenuOpen] = useState(false)
  const [lessonMenuOpen, setLessonMenuOpen] = useState(false)

  // Curriculum, Grade, Unit, and Lesson state
  const [selectedCurriculum, setSelectedCurriculum] = useState("CCSS")
  const [selectedGrade, setSelectedGrade] = useState("5")
  const [selectedUnit, setSelectedUnit] = useState("Unit 1: Numbers")
  const [selectedLesson, setSelectedLesson] = useState("Lesson 1: Counting On")

  // State to track revealed items on the current slide
  const [revealedItems, setRevealedItems] = useState<number[]>([])
  // State to track the total number of revealable items on the current slide
  const [totalRevealableItems, setTotalRevealableItems] = useState(0)

  const [showDashboardModal, setShowDashboardModal] = useState(false)

  // Reset revealed items when changing slides
  useEffect(() => {
    setRevealedItems([])
  }, [currentSlide])

  // Function to reveal the next item
  const revealNextItem = useCallback(() => {
    if (revealedItems.length < totalRevealableItems) {
      const nextItemIndex = revealedItems.length
      setRevealedItems((prev) => [...prev, nextItemIndex])
      return true // Item was revealed
    }
    return false // No more items to reveal
  }, [revealedItems, totalRevealableItems])

  // Function to hide the last revealed item
  const hideLastItem = useCallback(() => {
    if (revealedItems.length > 0) {
      setRevealedItems((prev) => prev.slice(0, -1))
      return true // Item was hidden
    }
    return false // No items to hide
  }, [revealedItems])

  // Handle keyboard navigation - modified to use only arrow keys
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Get the currently focused element
      const target = e.target as HTMLElement
      const isEditableElement = target.tagName === "INPUT" || target.tagName === "TEXTAREA" || target.isContentEditable

      // Only prevent default for navigation keys when not in editable elements
      if ((e.code === "ArrowLeft" || e.code === "ArrowRight") && !isEditableElement) {
        e.preventDefault()
      }

      // Only prevent spacebar default when not in editable elements and not a button
      if (e.code === "Space" && !isEditableElement && target.tagName !== "BUTTON") {
        e.preventDefault()
      }

      // Only process arrow keys for navigation when not in editable elements
      if (!isEditableElement) {
        if (e.code === "ArrowRight") {
          // Try to reveal next item, if none left, go to next slide
          const revealed = revealNextItem()
          if (!revealed) {
            setCurrentSlide((prev) => Math.min(totalSlides, prev + 1))
          }
        } else if (e.code === "ArrowLeft") {
          // Try to hide last item, if none left, go to previous slide
          const hidden = hideLastItem()
          if (!hidden) {
            setCurrentSlide((prev) => Math.max(1, prev - 1))
          }
        }
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => {
      window.removeEventListener("keydown", handleKeyDown)
    }
  }, [revealNextItem, hideLastItem, totalSlides])

  // Function to be called from slide components to register their revealable items
  const registerRevealableItems = useCallback((count: number) => {
    setTotalRevealableItems(count)
  }, [])

  // Close user menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      if (userMenuOpen && !target.closest(".user-menu-container")) {
        setUserMenuOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [userMenuOpen])

  // Add an effect to close the language menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      if (languageMenuOpen && !target.closest(".language-menu-container")) {
        setLanguageMenuOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [languageMenuOpen])

  // Update header height CSS variable for proper button positioning
  useEffect(() => {
    const updateHeaderHeight = () => {
      const header = document.querySelector("header")
      if (header) {
        const headerHeight = header.offsetHeight
        document.documentElement.style.setProperty("--computed-header-height", `${headerHeight}px`)

        // Force a repaint to ensure buttons are positioned correctly
        const languageButton = document.querySelector(".language-menu-container") as HTMLElement

        if (languageButton) {
          languageButton.style.transform = 'translateZ(0)'
          setTimeout(() => {
            languageButton.style.transform = ''
          }, 0)
        }
      }
    }

    // Initial update
    updateHeaderHeight()

    // Update on resize
    window.addEventListener("resize", updateHeaderHeight)

    // Update when high contrast mode changes
    const timer = setTimeout(updateHeaderHeight, 100)

    return () => {
      window.removeEventListener("resize", updateHeaderHeight)
      clearTimeout(timer)
    }
  }, [highContrast])

  // Close curriculum dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      if (curriculumMenuOpen && !target.closest(".curriculum-menu-container")) {
        setCurriculumMenuOpen(false)
      }
      if (gradeMenuOpen && !target.closest(".grade-menu-container")) {
        setGradeMenuOpen(false)
      }
      if (unitMenuOpen && !target.closest(".unit-menu-container")) {
        setUnitMenuOpen(false)
      }
      if (lessonMenuOpen && !target.closest(".lesson-menu-container")) {
        setLessonMenuOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [curriculumMenuOpen, gradeMenuOpen, unitMenuOpen, lessonMenuOpen])

  // Calculate header height for positioning
  useEffect(() => {
    const updateHeaderHeight = () => {
      const header = document.querySelector("header")
      if (header) {
        const headerHeight = header.offsetHeight
        document.documentElement.style.setProperty("--computed-header-height", `${headerHeight}px`)
      }
    }

    updateHeaderHeight()
    window.addEventListener("resize", updateHeaderHeight)

    return () => {
      window.removeEventListener("resize", updateHeaderHeight)
    }
  }, [])

  return (
    <div
      className="flex h-screen flex-col bg-white"
      style={{ "--header-height": "var(--computed-header-height, 116px)" } as React.CSSProperties}
    >
      {/* Header */}
      <header className="bg-[linear-gradient(150deg,#2B6DFE_0%,#2B6DFE_70%,#00F2FF_100%)] text-white p-4">
        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 shadow-lg">
          {/* Top row with logo and user controls */}
          <div className="flex items-center justify-between mb-4">
            {/* Logo */}
            <div className="flex items-center gap-4">
              <div className="flex h-8 w-8 items-center justify-center rounded-md bg-white/10">
                <img src="/images/embrs-logo.png" alt="EMBRS Logo" className="h-5 w-5" />
              </div>
              <span className="font-montserrat text-xl font-[900] tracking-wide text-white">EMBRS</span>
              <button
                onClick={() => setShowDashboardModal(true)}
                className="flex items-center gap-2 rounded-md bg-white/10 px-3 py-2 text-sm font-medium text-white hover:bg-white/20 transition-colors"
              >
                <Home className="h-4 w-4" />
                <span>Dashboard</span>
              </button>
            </div>

            {/* Right side controls */}
            <div className="flex items-center gap-4">
              {/* Action buttons */}
              <button
                onClick={() => {
                  if (!document.fullscreenElement) {
                    document.documentElement.requestFullscreen().catch((err) => {
                      console.error(`Error attempting to enable full-screen mode: ${err.message}`)
                    })
                  } else {
                    if (document.exitFullscreen) {
                      document.exitFullscreen()
                    }
                  }
                }}
                className="flex items-center gap-2 rounded-md bg-white/10 px-3 py-2 text-sm font-medium text-white "
              >
                <Maximize2 className="h-4 w-4" />
                <span>Fullscreen</span>
              </button>

              {/* User Menu */}
              <div className="relative user-menu-container">
                <button
                  onClick={() => setUserMenuOpen(!userMenuOpen)}
                  className="flex h-9 w-9 items-center justify-center rounded-full bg-[#fadb9a] text-[#2B6DFE] font-medium text-sm hover:bg-[#fadb9a]/90 transition-colors"
                >
                  TS
                </button>

                {userMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 z-50">
                    <div className="py-1">
                      <button
                        className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => {
                          setUserMenuOpen(false)
                          setShowSettingsModal(true)
                        }}
                      >
                        <Settings className="mr-2 h-4 w-4" />
                        Settings
                      </button>
                      <button
                        className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => {
                          setUserMenuOpen(false)
                          // Add logout functionality here
                          alert("Logout clicked")
                        }}
                      >
                        <LogOut className="mr-2 h-4 w-4" />
                        Logout
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Bottom row with curriculum selectors */}
          <div className="flex items-center gap-4">
            {/* Curriculum Dropdown */}
            <div className="relative curriculum-menu-container">
              <button
                onClick={() => setCurriculumMenuOpen(!curriculumMenuOpen)}
                className="appearance-none rounded-md bg-white/10 px-3 py-1.5 text-sm font-medium text-white  pr-8 w-40 text-left flex items-center cursor-pointer"
              >
                <span className="truncate">
                  {selectedCurriculum === "CCSS"
                    ? "Common Core (CCSS)"
                    : selectedCurriculum === "Ontario"
                      ? "Ontario"
                      : selectedCurriculum === "Alberta"
                        ? "Alberta"
                        : selectedCurriculum === "BC"
                          ? "British Columbia"
                          : selectedCurriculum === "Australia"
                            ? "Australia"
                            : selectedCurriculum === "UK"
                              ? "UK"
                              : selectedCurriculum}
                </span>
                <ChevronDown className="absolute right-2 top-1/2 h-4 w-4 -translate-y-1/2 pointer-events-none" />
              </button>
              {curriculumMenuOpen && (
                <div className="absolute left-0 mt-2 w-40 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 z-50">
                  <div className="py-1">
                    <button
                      className={`flex w-full items-center px-4 py-2 text-sm text-left ${selectedCurriculum === "CCSS" ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                      onClick={() => {
                        setSelectedCurriculum("CCSS")
                        setCurriculumMenuOpen(false)
                      }}
                    >
                      Common Core (CCSS)
                    </button>
                    <button
                      className={`flex w-full items-center px-4 py-2 text-sm text-left ${selectedCurriculum === "Ontario" ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                      onClick={() => {
                        setSelectedCurriculum("Ontario")
                        setCurriculumMenuOpen(false)
                      }}
                    >
                      Ontario
                    </button>
                    <button
                      className={`flex w-full items-center px-4 py-2 text-sm text-left ${selectedCurriculum === "Alberta" ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                      onClick={() => {
                        setSelectedCurriculum("Alberta")
                        setCurriculumMenuOpen(false)
                      }}
                    >
                      Alberta
                    </button>
                    <button
                      className={`flex w-full items-center px-4 py-2 text-sm text-left ${selectedCurriculum === "BC" ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                      onClick={() => {
                        setSelectedCurriculum("BC")
                        setCurriculumMenuOpen(false)
                      }}
                    >
                      British Columbia
                    </button>
                    <button
                      className={`flex w-full items-center px-4 py-2 text-sm text-left ${selectedCurriculum === "Australia" ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                      onClick={() => {
                        setSelectedCurriculum("Australia")
                        setCurriculumMenuOpen(false)
                      }}
                    >
                      Australia
                    </button>
                    <button
                      className={`flex w-full items-center px-4 py-2 text-sm text-left ${selectedCurriculum === "UK" ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                      onClick={() => {
                        setSelectedCurriculum("UK")
                        setCurriculumMenuOpen(false)
                      }}
                    >
                      UK
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Grade Dropdown */}
            <div className="relative grade-menu-container">
              <button
                onClick={() => setGradeMenuOpen(!gradeMenuOpen)}
                className="appearance-none rounded-md bg-white/10 px-3 py-1.5 text-sm font-medium text-white  pr-8 w-32 text-left flex items-center cursor-pointer"
              >
                <span className="truncate">{selectedGrade === "K" ? "Kindergarten" : `Grade ${selectedGrade}`}</span>
                <ChevronDown className="absolute right-2 top-1/2 h-4 w-4 -translate-y-1/2 pointer-events-none" />
              </button>
              {gradeMenuOpen && (
                <div className="absolute left-0 mt-2 w-32 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 z-50">
                  <div className="py-1 max-h-60 overflow-y-auto">
                    <button
                      className={`flex w-full items-center px-4 py-2 text-sm text-left ${selectedGrade === "K" ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                      onClick={() => {
                        setSelectedGrade("K")
                        setGradeMenuOpen(false)
                      }}
                    >
                      Kindergarten
                    </button>
                    {[...Array(12)].map((_, i) => (
                      <button
                        key={i + 1}
                        className={`flex w-full items-center px-4 py-2 text-sm text-left ${selectedGrade === String(i + 1) ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                        onClick={() => {
                          setSelectedGrade(String(i + 1))
                          setGradeMenuOpen(false)
                        }}
                      >
                        Grade {i + 1}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Unit Dropdown */}
            <div className="relative unit-menu-container">
              <button
                onClick={() => setUnitMenuOpen(!unitMenuOpen)}
                className="appearance-none rounded-md bg-white/10 px-3 py-1.5 text-sm font-medium text-white  pr-8 w-48 text-left flex items-center cursor-pointer"
              >
                <span className="truncate">{selectedUnit}</span>
                <ChevronDown className="absolute right-2 top-1/2 h-4 w-4 -translate-y-1/2 pointer-events-none" />
              </button>
              {unitMenuOpen && (
                <div className="absolute left-0 mt-2 w-48 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 z-50">
                  <div className="py-1">
                    <button
                      className={`flex w-full items-center px-4 py-2 text-sm text-left ${selectedUnit === "Unit 1: Numbers" ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                      onClick={() => {
                        setSelectedUnit("Unit 1: Numbers")
                        setUnitMenuOpen(false)
                      }}
                    >
                      Unit 1: Numbers
                    </button>
                    <button
                      className={`flex w-full items-center px-4 py-2 text-sm text-left ${selectedUnit === "Unit 2: Addition" ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                      onClick={() => {
                        setSelectedUnit("Unit 2: Addition")
                        setUnitMenuOpen(false)
                      }}
                    >
                      Unit 2: Addition
                    </button>
                    <button
                      className={`flex w-full items-center px-4 py-2 text-sm text-left ${selectedUnit === "Unit 3: Subtraction" ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                      onClick={() => {
                        setSelectedUnit("Unit 3: Subtraction")
                        setUnitMenuOpen(false)
                      }}
                    >
                      Unit 3: Subtraction
                    </button>
                    <button
                      className={`flex w-full items-center px-4 py-2 text-sm text-left ${selectedUnit === "Unit 4: Shapes" ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                      onClick={() => {
                        setSelectedUnit("Unit 4: Shapes")
                        setUnitMenuOpen(false)
                      }}
                    >
                      Unit 4: Shapes
                    </button>
                    <button
                      className={`flex w-full items-center px-4 py-2 text-sm text-left ${selectedUnit === "Unit 5: Measurement" ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                      onClick={() => {
                        setSelectedUnit("Unit 5: Measurement")
                        setUnitMenuOpen(false)
                      }}
                    >
                      Unit 5: Measurement
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Lesson Dropdown */}
            <div className="relative lesson-menu-container">
              <button
                onClick={() => setLessonMenuOpen(!lessonMenuOpen)}
                className="appearance-none rounded-md bg-white/10 px-3 py-1.5 text-sm font-medium text-white  pr-8 w-48 text-left flex items-center cursor-pointer"
              >
                <span className="truncate">{selectedLesson}</span>
                <ChevronDown className="absolute right-2 top-1/2 h-4 w-4 -translate-y-1/2 pointer-events-none" />
              </button>
              {lessonMenuOpen && (
                <div className="absolute left-0 mt-2 w-48 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 z-50">
                  <div className="py-1">
                    <button
                      className={`flex w-full items-center px-4 py-2 text-sm text-left ${selectedLesson === "Lesson 1: Counting On" ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                      onClick={() => {
                        setSelectedLesson("Lesson 1: Counting On")
                        setLessonMenuOpen(false)
                      }}
                    >
                      Lesson 1: Counting On
                    </button>
                    <button
                      className={`flex w-full items-center px-4 py-2 text-sm text-left ${selectedLesson === "Lesson 2: Counting Sets" ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                      onClick={() => {
                        setSelectedLesson("Lesson 2: Counting Sets")
                        setLessonMenuOpen(false)
                      }}
                    >
                      Lesson 2: Counting Sets
                    </button>
                    <button
                      className={`flex w-full items-center px-4 py-2 text-sm text-left ${selectedLesson === "Lesson 3: Number Recognition" ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                      onClick={() => {
                        setSelectedLesson("Lesson 3: Number Recognition")
                        setLessonMenuOpen(false)
                      }}
                    >
                      Lesson 3: Number Recognition
                    </button>
                    <button
                      className={`flex w-full items-center px-4 py-2 text-sm text-left ${selectedLesson === "Lesson 4: Number Comparison" ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                      onClick={() => {
                        setSelectedLesson("Lesson 4: Number Comparison")
                        setLessonMenuOpen(false)
                      }}
                    >
                      Lesson 4: Number Comparison
                    </button>
                    <button
                      className={`flex w-full items-center px-4 py-2 text-sm text-left ${selectedLesson === "Lesson 5: Number Patterns" ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                      onClick={() => {
                        setSelectedLesson("Lesson 5: Number Patterns")
                        setLessonMenuOpen(false)
                      }}
                    >
                      Lesson 5: Number Patterns
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Go Button */}
            <button
              onClick={() => {
                alert(`Changing to: ${selectedCurriculum} - ${selectedGrade} - ${selectedUnit} - ${selectedLesson}`)
                // In a real app, this would trigger the lesson change
              }}
              className="rounded-md bg-[#fadb9a] px-4 py-1.5 text-sm font-medium text-[#2B6DFE] hover:bg-[#fadb9a]/90 transition-colors"
            >
              Go
            </button>
          </div>
        </div>
      </header>

      {/* Language Selector */}
      <div className="language-menu-container fixed top-[calc(var(--header-height)+32px)] right-8 z-50">
        <button
          onClick={() => setLanguageMenuOpen(!languageMenuOpen)}
          className="flex h-10 w-10 items-center justify-center rounded-full bg-white/20 text-white hover:bg-white/30 transition-colors  shadow-md"
          aria-label="Change language"
        >
          <Globe className="h-5 w-5" />
        </button>

        {languageMenuOpen && (
          <div className="absolute right-0 mt-2 w-40 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 z-50">
            <div className="py-1">
              <button
                className={`flex w-full items-center px-4 py-2 text-sm text-left ${selectedLanguage === "en" ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                onClick={() => {
                  setSelectedLanguage("en")
                  setLanguageMenuOpen(false)
                }}
              >
                English
              </button>
              <button
                className={`flex w-full items-center px-4 py-2 text-sm text-left ${selectedLanguage === "es" ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                onClick={() => {
                  setSelectedLanguage("es")
                  setLanguageMenuOpen(false)
                }}
              >
                Español
              </button>
              <button
                className={`flex w-full items-center px-4 py-2 text-sm text-left ${selectedLanguage === "fr" ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                onClick={() => {
                  setSelectedLanguage("fr")
                  setLanguageMenuOpen(false)
                }}
              >
                Français
              </button>
              <button
                className={`flex w-full items-center px-4 py-2 text-sm text-left ${selectedLanguage === "ar" ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                onClick={() => {
                  setSelectedLanguage("ar")
                  setLanguageMenuOpen(false)
                }}
              >
                العربية
              </button>
            </div>
          </div>
        )}
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar - conditionally rendered based on sidebarOpen state */}
        {sidebarOpen && (
          <Sidebar
            currentSlide={currentSlide}
            setCurrentSlide={setCurrentSlide}
            setSidebarOpen={setSidebarOpen}
            activeTab={activeTab}
            setActiveTab={setActiveTab}
          />
        )}

        {/* Sidebar toggle button - show when sidebar is closed */}
        {!sidebarOpen && (
          <div className="fixed top-[calc(var(--header-height)+32px)] left-8 z-50">
            <button
              onClick={() => setSidebarOpen(true)}
              className="flex h-10 w-10 items-center justify-center rounded-full bg-white/20 text-white hover:bg-white/30 transition-colors  shadow-md"
              aria-label="Open sidebar"
            >
              <ChevronRight size={16} />
            </button>
          </div>
        )}

        {/* Main content */}
        <div
          className={`flex-1 overflow-y-auto ${
            highContrast ? "bg-white text-black" : "bg-[linear-gradient(150deg,#2B6DFE_0%,#2B6DFE_70%,#00F2FF_100%)]"
          } p-0 text-lg`}
          style={
            highContrast
              ? ({
                  "--fadb9a": "#000000",
                  "--concept-number-bg": "#ffffff",
                  "--concept-number-text": "#000000",
                  "--concept-content-bg": "#ffffff",
                  "--concept-content-text": "#000000",
                } as React.CSSProperties)
              : {}
          }
        >
          <div className={`h-full ${highContrast ? "high-contrast-content" : ""}`}>
            {/* Show TabContent when activeTab is not empty, otherwise show SlideContent */}
            {activeTab ? (
              <TabContent activeTab={activeTab} setActiveTab={setActiveTab} />
            ) : (
              <SlideContent
                slideNumber={currentSlide}
                highContrast={highContrast}
                revealedItems={revealedItems}
                registerRevealableItems={registerRevealableItems}
                setRevealedItems={setRevealedItems}
              />
            )}
          </div>
        </div>
      </div>

      {/* Dashboard Promotional Modal */}
      {showDashboardModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-full max-w-3xl shadow-xl overflow-hidden">
            {/* Close button */}
            <div className="absolute top-4 right-4">
              <button
                onClick={() => setShowDashboardModal(false)}
                className="text-gray-500 hover:text-gray-700 transition-colors"
                aria-label="Close"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="flex flex-col md:flex-row">
              {/* Left side - Illustration */}
              <div className="bg-gradient-to-br from-[#2B6DFE] to-[#00F2FF] p-8 text-white md:w-2/5 flex flex-col items-center justify-center">
                <div className="mb-6">
                  <img src="/images/embrs-logo.png" alt="EMBRS Logo" className="h-12 w-12" />
                </div>
                <div className="relative h-64 w-64">
                  {/* Dashboard illustration */}
                  <div className="absolute inset-0 bg-white/10 rounded-lg border border-white/20 backdrop-blur-sm shadow-xl"></div>
                  <div className="absolute top-4 left-4 right-4 h-8 bg-white/20 rounded-md flex items-center px-3">
                    <div className="w-2 h-2 rounded-full bg-white mr-2"></div>
                    <div className="w-2 h-2 rounded-full bg-white/50 mr-2"></div>
                    <div className="w-2 h-2 rounded-full bg-white/50"></div>
                  </div>
                  <div className="absolute top-16 left-4 right-4 h-20 bg-white/20 rounded-md"></div>
                  <div className="absolute top-40 left-4 w-1/2 h-16 bg-white/20 rounded-md"></div>
                  <div className="absolute top-40 right-4 w-1/3 h-16 bg-white/20 rounded-md"></div>
                  <div className="absolute bottom-4 left-4 right-4 h-12 bg-white/20 rounded-md"></div>

                  {/* Analytics graph */}
                  <div className="absolute bottom-20 left-8 right-8 h-24 flex items-end justify-between">
                    <div className="w-2 h-12 bg-white/80 rounded-t-sm"></div>
                    <div className="w-2 h-16 bg-white/80 rounded-t-sm"></div>
                    <div className="w-2 h-8 bg-white/80 rounded-t-sm"></div>
                    <div className="w-2 h-20 bg-white/80 rounded-t-sm"></div>
                    <div className="w-2 h-14 bg-white/80 rounded-t-sm"></div>
                    <div className="w-2 h-10 bg-white/80 rounded-t-sm"></div>
                  </div>
                </div>
              </div>

              {/* Right side - Content */}
              <div className="p-8 md:w-3/5">
                <div className="flex items-center mb-4">
                  <h2 className="text-2xl font-montserrat font-[900] text-gray-600 ml-3">Dashboard Coming Soon!</h2>
                </div>

                <p className="text-gray-600 mb-6">
                  We're building a powerful dashboard to enhance your teaching experience. Stay tuned for these exciting
                  features:
                </p>

                <div className="space-y-4">
                  <div className="flex items-start">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-[#2B6DFE]/10 mt-0.5">
                      <svg className="h-4 w-4 text-[#2B6DFE]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                        />
                      </svg>
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-bold font-montserrat text-gray-600">Interactive Scope & Sequence</h3>
                      <p className="text-gray-600">Plan your instruction with our intuitive scope and sequence.</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-[#2B6DFE]/10 mt-0.5">
                      <svg className="h-4 w-4 text-[#2B6DFE]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-bold font-montserrat text-gray-600">Quizzes & Tests</h3>
                      <p className="text-gray-600">Create comprehensive assessments for your students.</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-[#2B6DFE]/10 mt-0.5">
                      <svg className="h-4 w-4 text-[#2B6DFE]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                        />
                      </svg>
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-bold font-montserrat text-gray-600">Assessment Tools</h3>
                      <p className="text-gray-600">Track student progress with detailed performance metrics.</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-[#2B6DFE]/10 mt-0.5">
                      <svg className="h-4 w-4 text-[#2B6DFE]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"
                        />
                      </svg>
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-bold font-montserrat text-gray-600">Classroom Analytics</h3>
                      <p className="text-gray-600">Gain insights into class performance and identify learning gaps.</p>
                    </div>
                  </div>
                </div>

                <div className="mt-8 flex justify-end">
                  <button
                    onClick={() => setShowDashboardModal(false)}
                    className="px-4 py-2 bg-[#2B6DFE] rounded-md text-white hover:bg-[#2B6DFE]/90 transition-colors"
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
      {/* Settings Modal */}
      {showSettingsModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-[linear-gradient(150deg,#2B6DFE_0%,#2B6DFE_70%,#00F2FF_100%)] rounded-lg w-full max-w-md shadow-xl text-white overflow-hidden">
            <div className="flex justify-between items-center p-6 border-b border-white/10">
              <h2 className="text-xl font-semibold text-white">Settings</h2>
              <button
                onClick={() => setShowSettingsModal(false)}
                className="text-white/70 hover:text-white transition-colors"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="bg-gray-50 p-6 space-y-5 text-gray-700 text-sm">
              {/* Name - Editable */}
              <div className="space-y-1">
                <label className="block font-medium text-gray-600">Name</label>
                <div className="flex items-center">
                  {isEditingName ? (
                    <input
                      type="text"
                      value={userName}
                      onChange={(e) => setUserName(e.target.value)}
                      className="flex-1 bg-white border border-gray-200 rounded-md px-3 py-2 text-gray-700 focus:outline-none focus:ring-2 focus:ring-[#2B6DFE]"
                      autoFocus
                    />
                  ) : (
                    <span className="flex-1 py-2 text-gray-700">{userName}</span>
                  )}
                  <button
                    onClick={() => setIsEditingName(!isEditingName)}
                    className="ml-2 text-gray-400 hover:text-[#2B6DFE] transition-colors"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
                      />
                    </svg>
                  </button>
                </div>
              </div>

              {/* School - Not Editable */}
              <div className="space-y-1">
                <label className="block font-medium text-gray-600">School</label>
                <div className="py-2 px-3 bg-gray-100 rounded-md border border-gray-200 text-gray-500">
                  {userSettings.school}
                </div>
              </div>

              {/* School District - Not Editable */}
              <div className="space-y-1">
                <label className="block font-medium text-gray-600">School District</label>
                <div className="py-2 px-3 bg-gray-100 rounded-md border border-gray-200 text-gray-500">
                  {userSettings.district}
                </div>
              </div>

              {/* High Contrast Toggle */}
              <div className="space-y-1">
                <label className="block font-medium text-gray-600">Display Mode</label>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">High Contrast Mode</span>
                  <button
                    onClick={() => setHighContrast(!highContrast)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-[#2B6DFE] focus:ring-offset-2 ${
                      highContrast ? "bg-[#2B6DFE]" : "bg-gray-200"
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        highContrast ? "translate-x-6" : "translate-x-1"
                      }`}
                    />
                  </button>
                </div>
              </div>

              {/* Default Curriculum - Dropdown */}
              <div className="space-y-1">
                <label className="block font-medium text-gray-600">Default Curriculum</label>
                <div className="relative">
                  <select
                    value={userSettings.curriculum}
                    disabled
                    onChange={(e) => setUserSettings({ ...userSettings, curriculum: e.target.value })}
                    className="w-full bg-white border border-gray-200 rounded-md px-3 py-2 text-gray-700 appearance-none focus:outline-none focus:ring-2 focus:ring-[#2B6DFE] cursor-not-allowed"
                  >
                    <option value="CCSS">Common Core State Standards (CCSS)</option>
                    {/* <option value="Ontario">Ontario Curriculum</option>
                    <option value="Alberta">Alberta Curriculum</option>
                    <option value="BC">British Columbia Curriculum</option>
                    <option value="Australia">Australian Curriculum</option>
                    <option value="UK">UK National Curriculum</option> */}
                  </select>
                  <ChevronDown className="absolute right-2 top-1/2 h-4 w-4 -translate-y-1/2 pointer-events-none text-gray-400" />
                </div>
              </div>

              {/* Default Grade - Dropdown */}
              <div className="space-y-1 ">
                <label className="block font-medium text-gray-600">Default Grade</label>
                <div className="relative">
                  <select
                    value={userSettings.grade}
                    onChange={(e) => setUserSettings({ ...userSettings, grade: e.target.value })}
                    className="w-full bg-white border border-gray-200 rounded-md px-3 py-2 text-gray-700 appearance-none focus:outline-none focus:ring-2 focus:ring-[#2B6DFE] cursor-pointer"
                  >
                    <option value="K">Kindergarten</option>
                    <option value="1">Grade 1</option>
                    <option value="2">Grade 2</option>
                    <option value="3">Grade 3</option>
                    <option value="4">Grade 4</option>
                    <option value="5">Grade 5</option>
                    <option value="6">Grade 6</option>
                    <option value="7">Grade 7</option>
                    <option value="8">Grade 8</option>
                    <option value="9">Grade 9</option>
                    <option value="10">Grade 10</option>
                    <option value="11">Grade 11</option>
                    <option value="12">Grade 12</option>
                  </select>
                  <ChevronDown className="absolute right-2 top-1/2 h-4 w-4 -translate-y-1/2 pointer-events-none text-gray-400" />
                </div>
              </div>
            </div>

            <div className="p-6 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
              <button
                onClick={() => setShowSettingsModal(false)}
                className="px-3 py-1.5 border border-gray-200 rounded-md text-gray-700 hover:bg-gray-100 transition-colors text-sm"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  // Save settings logic would go here
                  setShowSettingsModal(false)
                  setIsEditingName(false)
                }}
                className="px-3 py-1.5 bg-gray-700 text-white font-medium rounded-md hover:bg-gray-600 transition-colors text-sm"
              >
                Save Changes
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
