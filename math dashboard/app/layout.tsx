import type React from "react"
import "./globals.css"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import { PresenterProvider } from "@/components/presenter-context"
import { VisualFooter } from "@/components/visual-footer"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Math Lesson Slider",
  description: "Interactive math lesson slides for teachers and students",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <PresenterProvider>
          {children}
          <VisualFooter />
        </PresenterProvider>
      </body>
    </html>
  )
}
