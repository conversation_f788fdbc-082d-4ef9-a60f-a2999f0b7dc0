"use client"

import { useEffect, useRef } from "react"
import { PresenterView } from "@/components/presenter-view"
import { usePresenter } from "@/components/presenter-context"

export default function PresenterPage() {
  const { setIsPresenterMode, setCurrentSlide, currentSlide, setRevealedItems, revealedItems } = usePresenter()
  const initializedRef = useRef(false)

  // Set presenter mode immediately and initialize state
  useEffect(() => {
    console.log("Presenter view initialized")
    setIsPresenterMode(true)

    // Only send the initial update once
    if (!initializedRef.current) {
      initializedRef.current = true

      // Force an initial update to ensure synchronization
      setTimeout(() => {
        console.log("Sending initial state update")
        // Store directly in localStorage as a fallback
        try {
          localStorage.setItem("presenter_slide", currentSlide.toString())
          localStorage.setItem("presenter_revealed_items", JSON.stringify(revealedItems))
        } catch (error) {
          console.error("Error updating localStorage:", error)
        }
      }, 500)
    }

    return () => setIsPresenterMode(false)
  }, [setIsPresenterMode]) // Remove dependencies that cause the loop

  return <PresenterView />
}
