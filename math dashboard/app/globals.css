@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221 98% 58%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221 98% 58%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 221 98% 58%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 221 98% 58%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

.slide-title {
  font-family: var(--font-montserrat), sans-serif;
  font-weight: 800;
  font-size: 2.5rem;
  margin-bottom: 2rem;
  text-align: center;
}

.blue-gradient {
  background: linear-gradient(150deg, #2b6dfe 0%, #2b6dfe 70%, #00f2ff 100%);
  color: white;
}

.concept-slide {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  padding: 1rem;
}

.concept-point {
  display: flex;
  align-items: flex-start;
  margin-bottom: 1.5rem;
}

.concept-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  background-color: #4169e1;
  color: white;
  border-radius: 50%;
  font-size: 1.5rem;
  font-weight: bold;
  margin-right: 1rem;
  flex-shrink: 0;
}

.concept-content {
  font-size: 1.25rem;
}

.illustration-box {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  overflow: hidden;
}

.illustration-box img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  background-color: transparent;
  border-radius: 0.5rem;
}

/* Fix for slides 1, 5, 6, and 8 images */
.slide-image-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border-radius: 0.5rem;
  background-color: transparent;
}

.slide-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  background-color: transparent;
}

.illustration-title {
  font-size: 1.75rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.illustration-text {
  font-size: 1.25rem;
}

/* Projector-friendly styles */
.text-lg {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-2xl {
  font-size: 1.75rem;
  line-height: 2.25rem;
}

.text-3xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

/* High contrast mode styles */
.high-contrast-content h1,
.high-contrast-content h2,
.high-contrast-content h3,
.high-contrast-content p,
.high-contrast-content li,
.high-contrast-content div {
  color: black !important;
}

.high-contrast-content .slide-title {
  color: black !important;
}

.high-contrast-content .concept-number {
  background-color: white !important;
  color: black !important;
  border: 2px solid black !important;
}

.high-contrast-content .border-[#fadb9a] {
  border-color: black !important;
}

.high-contrast-content .bg-white\/10,
.high-contrast-content .bg-white\/20 {
  background-color: white !important;
  border: 1px solid black !important;
}

.text-\[\#fadb9a\] {
  color: #fadb9a;
}

.high-contrast-content .illustration-box {
  background-color: white !important;
  border: 2px solid black !important;
}

.high-contrast-content .rounded-lg,
.high-contrast-content .rounded-xl,
.high-contrast-content .rounded-md {
  border: 1px solid black !important;
}

.bg-\[\#fadb9a\] {
  background-color: #fadb9a;
}

.border-\[\#fadb9a\] {
  border-color: #fadb9a;
}

/* Update button colors */
button.bg-\[\#fadb9a\] {
  background-color: #fadb9a;
  color: #4169e1;
}

button.hover\:bg-\[\#fadb9a\]\/90:hover {
  background-color: rgba(250, 219, 154, 0.9);
}

.high-contrast-content button {
  background-color: white !important;
  color: black !important;
  border: 2px solid black !important;
}

.high-contrast-content .blue-gradient {
  background: white !important;
  color: black !important;
  border: 2px solid black !important;
}
