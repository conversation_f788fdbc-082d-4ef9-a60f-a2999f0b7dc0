"use client"

import { useEffect, useRef } from "react"
import { AudienceView } from "@/components/audience-view"
import { usePresenter } from "@/components/presenter-context"

export default function AudiencePage() {
  const { setIsAudienceView } = usePresenter()
  const pollingRef = useRef<NodeJS.Timeout | null>(null)

  // Set audience view mode immediately
  useEffect(() => {
    console.log("Audience view initialized")
    setIsAudienceView(true)

    // Set up polling as an additional fallback
    pollingRef.current = setInterval(() => {
      try {
        const storedSlide = localStorage.getItem("presenter_slide")
        if (storedSlide) {
          const slideNum = Number.parseInt(storedSlide, 10)
          if (!isNaN(slideNum)) {
            console.log("Polling: found slide", slideNum)
            // We don't set the slide directly here to avoid conflicts
            // with the main state management, but this confirms polling works
          }
        }
      } catch (error) {
        console.error("Error in polling:", error)
      }
    }, 1000)

    return () => {
      setIsAudienceView(false)
      if (pollingRef.current) {
        clearInterval(pollingRef.current)
      }
    }
  }, [setIsAudienceView])

  return <AudienceView />
}
