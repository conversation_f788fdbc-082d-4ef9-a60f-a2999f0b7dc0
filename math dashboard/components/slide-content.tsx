"use client"

import { useState, useEffect } from "react"
import { ChevronDown, ChevronUp, Eye, Download } from "lucide-react"
import React from "react"
// First, make sure we import motion and AnimatePresence from framer-motion
import { motion, AnimatePresence } from "framer-motion"
import { useSession } from "next-auth/react"
import { PdfUploader } from "../../components/PdfUploader"

// Add a custom style for the illustration container
const illustrationStyle = {
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  width: "100%",
  height: "100%",
  overflow: "hidden",
  borderRadius: "0.5rem",
  backgroundColor: "transparent",
}

// Helper function to render images with proper styling
interface SlideImageProps {
  src: string
  alt: string
  className?: string
}

function SlideImage({ src, alt, className = "" }: SlideImageProps) {
  return (
    <div className="w-full h-full flex items-center justify-center overflow-hidden">
      <img
        src={src || "/placeholder.svg"}
        alt={alt}
        className={`max-w-full max-h-full object-contain ${className}`}
        onError={(e: any) => {
          console.error("Image failed to load:", e.target.src)
          e.currentTarget.src = "/place-value-comparison.png"
        }}
      />
    </div>
  )
}

interface SlideContentProps {
  slideNumber: number
  highContrast?: boolean
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
  setRevealedItems?: React.Dispatch<React.SetStateAction<number[]>>
}

export function SlideContent({
  slideNumber,
  highContrast = false,
  revealedItems = [],
  registerRevealableItems = () => {},
  setRevealedItems = (_value: React.SetStateAction<number[]>) => {},
}: SlideContentProps) {
  // Return different content based on the slide number
  switch (slideNumber) {
    case 1:
      return (
        <QuickReviewSlide
          key={`slide-${slideNumber}`}
          highContrast={highContrast}
          revealedItems={revealedItems}
          registerRevealableItems={registerRevealableItems}
          setRevealedItems={setRevealedItems}
        />
      )
    case 2:
      return (
        <LearningGoalsSlide
          key={`slide-${slideNumber}`}
          highContrast={highContrast}
          revealedItems={revealedItems}
          registerRevealableItems={registerRevealableItems}
        />
      )
    case 3:
      return (
        <VocabularySlide
          key={`slide-${slideNumber}`}
          revealedItems={revealedItems}
          registerRevealableItems={registerRevealableItems}
          setRevealedItems={setRevealedItems}
        />
      )
    case 4:
      return (
        <ConceptSlide
          key={`slide-${slideNumber}`}
          title="Hook 1: Building Numbers"
          points={[
            {
              title: "Build 34 with blocks.",
              content: "Use 3 'ten' rods and 4 'one' cubes.",
            },
            {
              title: "Build 37 with blocks.",
              content: "Use 3 'ten' rods and 7 'one' cubes.",
            },
            {
              title: "Look at the blocks.",
              content: "Examine both numbers carefully.",
            },
            {
              title: "What is the same? What is different?",
              content: "Notice what's similar and what's different between the two numbers.",
            },
          ]}
          revealedItems={revealedItems}
          registerRevealableItems={registerRevealableItems}
          slideNumber={slideNumber}
        />
      )
    case 5:
      return (
        <ConceptSlide
          key={`slide-${slideNumber}`}
          title="Hook 2: Comparing Numbers"
          points={[
            {
              title: "Compare 34 and 37.",
              content: "Let's look at the numbers we built with our blocks.",
            },
            {
              title: "Tens blocks are the same (3 tens).",
              content: "Both numbers have 3 ten rods! The big treasure chests are the same size!",
            },
            {
              title: "Look at the ones blocks.",
              content: "34 has 4 ones. 37 has 7 ones.",
            },
            {
              title: "4 ones is less than 7 ones. 34 < 37.",
              content: "Since 4 ones is less than 7 ones, we know that 34 is less than 37.",
            },
          ]}
          revealedItems={revealedItems}
          registerRevealableItems={registerRevealableItems}
          slideNumber={slideNumber}
        />
      )
    case 6:
      return (
        <ConceptSlide
          key={`slide-${slideNumber}`}
          title="Hook 3: Partner Activity"
          points={[
            {
              title: "Partner 1 builds 51.",
              content: "Use 5 'ten' rods and 1 'one' cube.",
            },
            {
              title: "Partner 2 builds 56.",
              content: "Use 5 'ten' rods and 6 'one' cubes.",
            },
            {
              title: "Compare the blocks.",
              content: "Are the tens the same? If yes, compare the ones!",
            },
            {
              title: "Which number is less?",
              content: "Decide which number is greater and which is less.",
            },
          ]}
          revealedItems={revealedItems}
          registerRevealableItems={registerRevealableItems}
          slideNumber={slideNumber}
        />
      )
    case 7:
      return (
        <ConceptSlide
          key={`slide-${slideNumber}`}
          title="Teach 1.1: Comparing Treasures"
          points={[
            {
              title: "Compare treasures.",
              content: "Comparing numbers is like comparing treasure stashes.",
            },
            {
              title: "Big chests (tens) are the same?",
              content: "First, we look at the big treasure chests, which are like the tens place.",
            },
            {
              title: "Look at small pouches (ones).",
              content: "If the big treasure chests are the exact same number, we look at the small pouches of coins!",
            },
            {
              title: "More coins in pouches means a bigger number.",
              content: "The stash with more coins in the small pouches is the greater treasure!",
            },
          ]}
          revealedItems={revealedItems}
          registerRevealableItems={registerRevealableItems}
          slideNumber={slideNumber}
        />
      )
    case 8:
      return (
        <ConceptSlide
          key={`slide-${slideNumber}`}
          title="Teach 1.2: Comparing Step by Step"
          points={[
            {
              title: "Compare 42 and 45.",
              content: "Let's use our treasure map steps!",
            },
            {
              title: "Tens are same (4).",
              content: "Step 1: Look at the tens place. Both numbers have a 4 in the tens place.",
            },
            {
              title: "Ones: 2 and 5.",
              content: "Step 2: Look at the ones place. 42 has 2 ones. 45 has 5 ones.",
            },
            {
              title: "2 ones is less than 5 ones. 42 < 45.",
              content: "Since 2 ones is less than 5 ones, 42 is less than 45.",
            },
          ]}
          revealedItems={revealedItems}
          registerRevealableItems={registerRevealableItems}
          slideNumber={slideNumber}
        />
      )
    case 9:
      return (
        <ConceptSlide
          key={`slide-${slideNumber}`}
          title="Teach 1.3: Another Example"
          points={[
            {
              title: "Compare 79 and 73.",
              content: "Let's try another comparison.",
            },
            {
              title: "Tens are same (7).",
              content: "First, look at the tens place. Both have a 7! The big chests match again.",
            },
            {
              title: "Ones: 9 and 3.",
              content: "So, we look at the ones place. 79 has 9 ones. 73 has 3 ones.",
            },
            {
              title: "9 ones is greater than 3 ones. 79 > 73.",
              content: "Since 9 ones is greater than 3 ones, 79 is greater than 73.",
            },
          ]}
          revealedItems={revealedItems}
          registerRevealableItems={registerRevealableItems}
          slideNumber={slideNumber}
        />
      )
    case 10:
      return (
        <ConceptSlide
          key={`slide-${slideNumber}`}
          title="Talk 1.1: Discussion Question"
          points={[
            {
              title: "Question",
              content: "Which place do you look at when the tens are the same?",
            },
            {
              title: "Answer",
              content: "You look at the ones place.",
            },
          ]}
          revealedItems={revealedItems}
          registerRevealableItems={registerRevealableItems}
          slideNumber={slideNumber}
        />
      )
    case 11:
      return (
        <ConceptSlide
          key={`slide-${slideNumber}`}
          title="Talk 1.2: Find the Mistake"
          points={[
            {
              title: "Question",
              content: "Find the mistake: 58 < 51",
            },
            {
              title: "Answer",
              content:
                "The symbol is wrong. 58 is greater than 51 because 8 ones is greater than 1 one (the tens are the same). It should be 58 > 51.",
            },
          ]}
          revealedItems={revealedItems}
          registerRevealableItems={registerRevealableItems}
          slideNumber={slideNumber}
        />
      )
    case 12:
      return (
        <ConceptSlide
          key={`slide-${slideNumber}`}
          title="Talk 1.3: Complete the Comparison"
          points={[
            {
              title: "Question",
              content: "How do you finish comparing 64 and 69?",
            },
            {
              title: "Answer",
              content: "Compare 4 ones and 9 ones. 4 ones is less than 9 ones. So, 64 < 69.",
            },
          ]}
          revealedItems={revealedItems}
          registerRevealableItems={registerRevealableItems}
          slideNumber={slideNumber}
        />
      )
    case 13:
      return (
        <ActivitySlide
          key={`slide-${slideNumber}`}
          title="Try 1: Practice Comparing"
          activities={[
            {
              question: "Compare 23 and 28. Use > or <.",
              answer: "23 < 28",
            },
            {
              question: "Compare 95 and 90. Use > or <.",
              answer: "95 > 90",
            },
            {
              question:
                "Lily has 71 stickers. Tom has 75 stickers. Who has less stickers? Use < or > to compare their numbers.",
              answer: "Lily has less stickers because 71 < 75.",
            },
          ]}
          revealedItems={revealedItems}
          registerRevealableItems={registerRevealableItems}
          setRevealedItems={setRevealedItems}
        />
      )
    case 14:
      return (
        <ConceptSlide
          key={`slide-${slideNumber}`}
          title="Teach 2.1: Comparing Different Tens"
          points={[
            {
              title: "Check the big chests (tens).",
              content: "Let's review what we learned about comparing treasure stashes.",
            },
            {
              title: "Are the tens different?",
              content: "What if the big treasure chests (the tens) are not the same?",
            },
            {
              title: "More tens means a bigger number.",
              content: "The one with more big chests is definitely bigger!",
            },
            {
              title: "The tens decide!",
              content: "We don't even need to count the coins in the small pouches!",
            },
          ]}
          revealedItems={revealedItems}
          registerRevealableItems={registerRevealableItems}
          slideNumber={slideNumber}
        />
      )
    case 15:
      return (
        <ConceptSlide
          key={`slide-${slideNumber}`}
          title="Teach 2.2: Comparing Different Tens Example"
          points={[
            {
              title: "Compare 45 and 38.",
              content: "Let's use our 'tens first' rule.",
            },
            {
              title: "Tens: 4 and 3.",
              content: "Step 1: Look at the tens place. 45 has 4 tens. 38 has 3 tens.",
            },
            {
              title: "Tens are different!",
              content: "Are the tens different? Yes! 4 tens is different from 3 tens.",
            },
            {
              title: "4 tens is greater than 3 tens. 45 > 38.",
              content: "Since 4 tens is greater than 3 tens, 45 is greater than 38.",
            },
          ]}
          revealedItems={revealedItems}
          registerRevealableItems={registerRevealableItems}
          slideNumber={slideNumber}
        />
      )
    case 16:
      return (
        <ConceptSlide
          key={`slide-${slideNumber}`}
          title="Teach 2.3: Another Different Tens Example"
          points={[
            {
              title: "Compare 81 and 90.",
              content: "Let's try another comparison where the tens are different.",
            },
            {
              title: "Tens: 8 and 9.",
              content: "First, look at the tens place. 81 has 8 tens. 90 has 9 tens.",
            },
            {
              title: "Tens are different!",
              content: "Are the tens different? Yes! 8 tens is different from 9 tens.",
            },
            {
              title: "8 tens is less than 9 tens. 81 < 90.",
              content: "Since 8 tens is less than 9 tens, 81 is less than 90.",
            },
          ]}
          revealedItems={revealedItems}
          registerRevealableItems={registerRevealableItems}
          slideNumber={slideNumber}
        />
      )
    case 17:
      return (
        <ConceptSlide
          key={`slide-${slideNumber}`}
          title="Talk 2.1: Different Tens Rule"
          points={[
            {
              title: "Question",
              content: "If the tens places are different, what do you do?",
            },
            {
              title: "Answer",
              content:
                "You compare the tens places to see which number is greater or less. You don't need to look at the ones place.",
            },
          ]}
          revealedItems={revealedItems}
          registerRevealableItems={registerRevealableItems}
          slideNumber={slideNumber}
        />
      )
    case 18:
      return (
        <ConceptSlide
          key={`slide-${slideNumber}`}
          title="Talk 2.2: Find the Mistake"
          points={[
            {
              title: "Question",
              content: "Find the mistake: 62 > 75",
            },
            {
              title: "Answer",
              content:
                "The symbol is wrong. You must compare the tens first. 6 tens is less than 7 tens, so 62 is less than 75. It should be 62 < 75.",
            },
          ]}
          revealedItems={revealedItems}
          registerRevealableItems={registerRevealableItems}
          slideNumber={slideNumber}
        />
      )
    case 19:
      return (
        <ConceptSlide
          key={`slide-${slideNumber}`}
          title="Talk 2.3: Complete the Comparison"
          points={[
            {
              title: "Question",
              content: "How do you finish comparing 59 and 34?",
            },
            {
              title: "Answer",
              content: "Compare the tens. 5 tens is greater than 3 tens. So, 59 > 34.",
            },
          ]}
          revealedItems={revealedItems}
          registerRevealableItems={registerRevealableItems}
          slideNumber={slideNumber}
        />
      )
    case 20:
      return (
        <ActivitySlide
          key={`slide-${slideNumber}`}
          title="Try 2: Practice Different Tens"
          activities={[
            {
              question: "Compare 56 and 21. Use > or <.",
              answer: "56 > 21",
            },
            {
              question: "Compare 19 and 84. Use > or <.",
              answer: "19 < 84",
            },
            {
              question: "Sam found 93 seashells. Maria found 68 seashells. Use > or < to compare their numbers.",
              answer: "93 > 68",
            },
          ]}
          revealedItems={revealedItems}
          registerRevealableItems={registerRevealableItems}
          setRevealedItems={setRevealedItems}
        />
      )
    case 21:
      return (
        <PracticeSlide
          key={`slide-${slideNumber}`}
          revealedItems={revealedItems}
          registerRevealableItems={registerRevealableItems}
          setRevealedItems={setRevealedItems}
        />
      )
    case 22:
      return (
        <OnRampTeachSlide
          key={`slide-${slideNumber}`}
          revealedItems={revealedItems}
          registerRevealableItems={registerRevealableItems}
        />
      )
    case 23:
      return (
        <ConceptSlide
          key={`slide-${slideNumber}`}
          title="On-Ramp Talk: Place Value"
          points={[
            {
              title: "Question",
              content: "What is the value of the 5 in 57?",
            },
            {
              title: "Answer",
              content: "The 5 is in the tens place, so its value is 50.",
            },
          ]}
          revealedItems={revealedItems}
          registerRevealableItems={registerRevealableItems}
          slideNumber={slideNumber}
        />
      )
    case 24:
      return (
        <OnRampTrySlide
          key={`slide-${slideNumber}`}
          revealedItems={revealedItems}
          registerRevealableItems={registerRevealableItems}
          setRevealedItems={setRevealedItems}
        />
      )
    case 25:
      return (
        <PrintableLessonGuideSlide
          key={`slide-${slideNumber}`}
          revealedItems={revealedItems}
          registerRevealableItems={registerRevealableItems}
          unitNumber="6"
          lessonNumber="51"
          gradeLevel="Grade 1"
        />
      )
    case 26:
      return (
        <PrintablePracticeSlideComponent
          key={`slide-${slideNumber}`}
          revealedItems={revealedItems}
          registerRevealableItems={registerRevealableItems}
          unitNumber="6"
          lessonNumber="51"
          gradeLevel="Grade 1"
        />
      )
    case 27:
      return (
        <PrintableAcceleratorSlideComponent
          key={`slide-${slideNumber}`}
          revealedItems={revealedItems}
          registerRevealableItems={registerRevealableItems}
          unitNumber="6"
          lessonNumber="51"
          gradeLevel="Grade 1"
        />
      )
    default:
      return <div>Slide content not found</div>
  }
}

// Individual slide components
interface QuickReviewSlideProps {
  highContrast?: boolean
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
  setRevealedItems?: React.Dispatch<React.SetStateAction<number[]>>
}

// Update the QuickReviewSlide component to use the JSON data
function QuickReviewSlide({
  highContrast = false,
  revealedItems = [],
  registerRevealableItems = () => {},
  setRevealedItems,
}: QuickReviewSlideProps) {
  // Register 3 revealable items (one for each card)
  useEffect(() => {
    registerRevealableItems(3)
  }, [registerRevealableItems])

  return (
    <div className={`tab-content p-8 ${highContrast ? "text-black" : "text-white"}`} data-tab-content="true">
      <h2 className={`slide-title ${highContrast ? "text-black" : ""}`}>Quick Review</h2>
      <div className="concept-slide">
        <div
          className={`space-y-8 ${
            highContrast ? "border-2 border-black rounded-xl" : "rounded-xl"
          } p-6 relative bg-white/10`}
          style={{ minHeight: "400px" }}
        >
          <div className="space-y-8">
            <QuickReviewCard
              number={1}
              question="Which number has more tens: 35 or 62?"
              answer={
                <div className="space-y-2">
                  <p>62</p>
                </div>
              }
              isVisible={revealedItems.includes(0)}
              onToggleVisibility={() => {
                if (setRevealedItems) {
                  if (revealedItems.includes(0)) {
                    setRevealedItems(revealedItems.filter((item) => item !== 0))
                  } else {
                    setRevealedItems([...revealedItems, 0])
                  }
                }
              }}
            />

            <QuickReviewCard
              number={2}
              question="What is the value of the 4 in 47?"
              answer={
                <div className="space-y-2">
                  <p>40</p>
                </div>
              }
              isVisible={revealedItems.includes(1)}
              onToggleVisibility={() => {
                if (setRevealedItems) {
                  if (revealedItems.includes(1)) {
                    setRevealedItems(revealedItems.filter((item) => item !== 1))
                  } else {
                    setRevealedItems([...revealedItems, 1])
                  }
                }
              }}
            />

            <QuickReviewCard
              number={3}
              question="Is 70 greater than or less than 20?"
              answer={
                <div className="space-y-2">
                  <p>Greater than</p>
                </div>
              }
              isVisible={revealedItems.includes(2)}
              onToggleVisibility={() => {
                if (setRevealedItems) {
                  if (revealedItems.includes(2)) {
                    setRevealedItems(revealedItems.filter((item) => item !== 2))
                  } else {
                    setRevealedItems([...revealedItems, 2])
                  }
                }
              }}
            />
          </div>
        </div>
        <div className="illustration-box" style={{ minHeight: "400px" }}>
          <div className="slide-image-container">
            <div className="w-full h-full flex justify-center items-center overflow-hidden">
              <img
                src="https://sjc.microlink.io/Z3q8hT-wpuyZDiZILungTT9jYLm-HhCLjXsChZzCTZHqOAtNlr_UcPvqmuI-ODd3EZNEyUl85PStCnRcFtcrpA.jpeg"
                alt="Place value comparison showing 52 (5 tens and 2 ones) and 48 (4 tens and 8 ones) with visual blocks"
                style={{
                  maxWidth: "100%",
                  maxHeight: "100%",
                  objectFit: "contain",
                  backgroundColor: "transparent",
                  border: "none",
                }}
                onError={(e: any) => {
                  console.error("Image failed to load:", e.target.src)
                  e.currentTarget.src = "/place-value-comparison.png"
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Simple QuickReviewCard component
interface QuickReviewCardProps {
  number: number
  question: string
  answer: React.ReactNode
  isVisible: boolean
  onToggleVisibility: () => void
}

// Update the QuickReviewCard component's animation
function QuickReviewCard({ number, question, answer, isVisible, onToggleVisibility }: QuickReviewCardProps) {
  const [showAnswer, setShowAnswer] = useState(false)

  useEffect(() => {
    if (!isVisible) {
      setShowAnswer(false)
    }
  }, [isVisible])

  return (
    <div className="rounded-lg bg-white/10 overflow-hidden">
      <div className="p-4 flex items-center justify-between gap-4 cursor-pointer" onClick={onToggleVisibility}>
        <div className="concept-number bg-[#2B6DFE] text-white">{number}</div>
        {isVisible ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
      </div>

      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ height: 0, opacity: 0, y: -10 }}
            animate={{ height: "auto", opacity: 1, y: 0 }}
            exit={{ height: 0, opacity: 0, y: -10 }}
            transition={{
              duration: 0.3,
              ease: "easeInOut",
              opacity: { duration: 0.2 },
            }}
            className="overflow-hidden"
          >
            <div className="p-4 pt-0">
              <div className="mt-4 text-3xl">{question}</div>

              <div className="mt-4 border-t border-white/20 pt-4">
                <div
                  className="flex items-center justify-between cursor-pointer mb-2"
                  onClick={(e) => {
                    e.stopPropagation()
                    setShowAnswer(!showAnswer)
                  }}
                >
                  <span className="font-medium">Answer</span>
                  <Eye size={18} className={showAnswer ? "text-[#fadb9a]" : ""} />
                </div>

                <AnimatePresence>
                  {showAnswer && (
                    <motion.div
                      initial={{ height: 0, opacity: 0, y: -5 }}
                      animate={{ height: "auto", opacity: 1, y: 0 }}
                      exit={{ height: 0, opacity: 0, y: -5 }}
                      transition={{ duration: 0.25 }}
                      className="overflow-hidden"
                    >
                      <div className="bg-white/10 p-4 rounded-md text-3xl">{answer}</div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

interface LearningGoalsSlideProps {
  highContrast?: boolean
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
}

// Update the LearningGoalsSlide component to use the JSON data
function LearningGoalsSlide({
  highContrast = false,
  revealedItems = [],
  registerRevealableItems = () => {},
}: LearningGoalsSlideProps) {
  // Register 3 revealable items (one for each learning goal)
  useEffect(() => {
    registerRevealableItems(3)
  }, [registerRevealableItems])

  // Add state for showing all points
  const [showAll, setShowAll] = useState(false)
  const [standardsOpen, setStandardsOpen] = useState(false)

  return (
    <div className={`p-8 ${highContrast ? "text-black" : "text-white"}`}>
      <h2 className={`slide-title ${highContrast ? "text-black" : ""}`}>Learning Goals</h2>
      <div
        className={`space-y-6 ${
          highContrast ? "border-2 border-black rounded-xl" : "rounded-xl"
        } p-6 relative bg-white/10`}
        style={{ minHeight: "400px" }}
      >
        {/* Add show all button */}
        <div className="flex justify-end mb-4">
          <button onClick={() => setShowAll(!showAll)} className="flex items-center gap-2 text-sm cursor-pointer">
            <span>{showAll ? "Hide All" : "Show All"}</span>
            <Eye size={18} className={showAll ? "text-[#fadb9a]" : ""} />
          </button>
        </div>

        <RevealPoint
          number={1}
          title="Compare Two-Digit Numbers"
          content="I will be able to compare two two-digit numbers when the tens digits are the same."
          forceReveal={revealedItems.includes(0) || showAll}
        />
        <RevealPoint
          number={2}
          title="Use Ones Digit"
          content="I will be able to use the ones digit to tell which number is greater or less."
          forceReveal={revealedItems.includes(1) || showAll}
        />
        <RevealPoint
          number={3}
          title="Use Comparison Symbols"
          content="I will be able to use the symbols >, =, and < to show how numbers compare."
          forceReveal={revealedItems.includes(2) || showAll}
        />

        <div className="mt-6">
          <div
            className={`rounded-lg ${standardsOpen ? "bg-white/20" : "bg-white/10"} p-3 backdrop-blur-sm cursor-pointer`}
            onClick={() => setStandardsOpen(!standardsOpen)}
          >
            <div className="flex items-center gap-3">
              <div
                className="concept-number bg-[#2B6DFE] text-white text-sm"
                style={{ width: "2.5rem", height: "2.5rem" }}
              >
                S
              </div>
              <h3 className="text-2xl font-montserrat font-extrabold text-white">Standards Alignment</h3>
            </div>

            <AnimatePresence>
              {standardsOpen && (
                <motion.div
                  initial={{ height: 0, opacity: 0, y: -10 }}
                  animate={{ height: "auto", opacity: 1, y: 0 }}
                  exit={{ height: 0, opacity: 0, y: -10 }}
                  transition={{
                    duration: 0.3,
                    ease: "easeInOut",
                  }}
                  className="overflow-hidden"
                >
                  <div className="mt-3 text-xl">
                    <p className="mb-2">
                      CCSS.MATH.CONTENT.1.NBT.B.3 - Compare two two-digit numbers based on meanings of the tens and ones
                      digits, recording the results of comparisons with the symbols &gt;, =, and &lt;.
                    </p>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            <div className="mt-1 text-center">
              {standardsOpen ? (
                <ChevronUp size={14} className="inline-block" />
              ) : (
                <ChevronDown size={14} className="inline-block" />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Simple RevealPoint component
interface RevealPointProps {
  number: number | string
  title: string
  content: string
  forceReveal?: boolean
}

// Update the RevealPoint component's animation
function RevealPoint({ number, title, content, forceReveal = false }: RevealPointProps) {
  const [isVisible, setIsVisible] = useState(false)

  React.useEffect(() => {
    if (forceReveal !== undefined) {
      setIsVisible(forceReveal)
    }
  }, [forceReveal])

  return (
    <div className="concept-point">
      <div
        className={`concept-number cursor-pointer ${
          isVisible ? "bg-[#fadb9a]/20 text-[#2B6DFE]" : "bg-white/10 text-white"
        }`}
        onClick={() => setIsVisible(!isVisible)}
        style={{ fontSize: "1.25rem" }}
      >
        {number}
      </div>
      <div className="flex-1">
        <AnimatePresence>
          {isVisible && (
            <motion.div
              initial={{ height: 0, opacity: 0, y: -10 }}
              animate={{ height: "auto", opacity: 1, y: 0 }}
              exit={{ height: 0, opacity: 0, y: -10 }}
              transition={{
                duration: 0.3,
                ease: "easeInOut",
              }}
              className="overflow-hidden"
            >
              <div className="concept-content ml-4 text-white text-3xl" style={{ lineHeight: "2.25rem" }}>
                {content}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}

// Update the VocabularySlide component to use the JSON data
function VocabularySlide({
  revealedItems = [],
  registerRevealableItems = () => {},
  setRevealedItems = (_value: React.SetStateAction<number[]>) => {},
}: {
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
  setRevealedItems?: React.Dispatch<React.SetStateAction<number[]>>
}) {
  const vocabularyTerms = [
    {
      term: "Compare",
      definition: "To look at two numbers and see how they are different, like which is bigger or smaller. 👀",
    },
    {
      term: "Tens Place",
      definition: "The spot on the left in a two-digit number that tells you how many groups of ten there are. 📦📦📦",
    },
    {
      term: "Ones Place",
      definition: "The spot on the right in a two-digit number that tells you how many single ones there are. 🪙",
    },
    {
      term: "Less Than (<)",
      definition:
        "A symbol that shows the first number is smaller than the second number. The symbol points to the smaller number! 👇",
    },
  ]

  // Register 4 revealable items (one for each vocabulary term)
  useEffect(() => {
    registerRevealableItems(vocabularyTerms.length)
  }, [registerRevealableItems, vocabularyTerms.length])

  return (
    <div className="rounded-xl overflow-hidden shadow-lg blue-gradient">
      <div className="p-8 text-white">
        <h2 className="slide-title text-3xl mb-6 text-center">Vocabulary</h2>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          {vocabularyTerms.map((item, index) => (
            <div
              key={item.term}
              className={`rounded-lg ${revealedItems.includes(index) ? "bg-white/20" : "bg-white/10"} p-4 backdrop-blur-sm cursor-pointer`}
              onClick={() => {
                // Toggle this item in the revealedItems array
                if (revealedItems.includes(index)) {
                  setRevealedItems((prev) => prev.filter((i) => i !== index))
                } else {
                  setRevealedItems((prev) => [...prev, index])
                }
                if (revealedItems.includes(index)) {
                  setRevealedItems((prev) => prev.filter((i) => i !== index))
                } else {
                  setRevealedItems((prev) => [...prev, index])
                }
              }}
            >
              <div className="flex items-center gap-4">
                <div className="concept-number bg-[#2B6DFE] text-white">{index + 1}</div>
                <h3 className="mb-3 text-4xl font-montserrat font-extrabold text-white">{item.term}</h3>
              </div>

              <AnimatePresence>
                {revealedItems.includes(index) && (
                  <motion.div
                    initial={{ height: 0, opacity: 0, y: -10 }}
                    animate={{ height: "auto", opacity: 1, y: 0 }}
                    exit={{ height: 0, opacity: 0, y: -10 }}
                    transition={{
                      duration: 0.3,
                      ease: "easeInOut",
                    }}
                    className="overflow-hidden"
                  >
                    <p className="mt-2 text-3xl">{item.definition}</p>
                  </motion.div>
                )}
              </AnimatePresence>

              <div className="mt-2 text-center">
                {revealedItems.includes(index) ? (
                  <ChevronUp size={16} className="inline-block" />
                ) : (
                  <ChevronDown size={16} className="inline-block" />
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

interface ConceptSlideProps {
  title: string
  points: { title: string; content: string }[]
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
  slideNumber?: number
}

// Update the ConceptSlide component for Hook 1 (Slide 4)
function ConceptSlide({
  title,
  points,
  revealedItems = [],
  registerRevealableItems = () => {},
  slideNumber = 0,
}: ConceptSlideProps) {
  // Register revealable items (one for each point)
  useEffect(() => {
    registerRevealableItems(points.length)
  }, [registerRevealableItems, points.length])

  // Add state for showing all points
  const [showAll, setShowAll] = useState(false)

  // Check if this slide should have swapped layout
  const swapLayout = [5, 7, 9, 11, 15, 17, 19, 23].includes(slideNumber)

  return (
    <div className="p-8 text-white">
      <h2 className="slide-title">{title}</h2>
      <div className="concept-slide">
        {swapLayout ? (
          <>
            <div className="illustration-box" style={{ minHeight: "400px", ...illustrationStyle }}>
              {slideNumber === 5 ? (
                <div className="slide-image-container">
                  <div className="w-full h-full flex justify-center items-center overflow-hidden">
                    <img
                      src="https://sjc.microlink.io/FyHVxSCn4DmliKSxA_KNxZRPkQ0LFy6Mm2yTxzpqaJekkyjhY8XXrA0HB4JHsq_l9_WdQndLyoOgi67486vdpw.jpeg"
                      alt="Slide 5 illustration showing comparison of 34 and 37 with base-10 blocks"
                      style={{
                        maxWidth: "100%",
                        maxHeight: "100%",
                        objectFit: "contain",
                        backgroundColor: "transparent",
                        border: "none",
                      }}
                      onError={(e: any) => {
                        console.error("Image failed to load:", e.target.src)
                        e.currentTarget.src = "/place-value-comparison.png"
                      }}
                    />
                  </div>
                </div>
              ) : slideNumber === 7 ? (
                <SlideImage
                  src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/slide7-MKMs4HfDzZH0AbE6YSM9O7p3Ul3ws9.png"
                  alt="Slide 7 illustration showing comparison of numbers with equal tens but different ones"
                />
              ) : slideNumber === 9 ? (
                <SlideImage
                  src="https://embrsmath.com/wp-content/uploads/2025/05/slide9.png"
                  alt="Slide 9 illustration"
                />
              ) : slideNumber === 11 ? (
                <SlideImage
                  src="https://embrsmath.com/wp-content/uploads/2025/05/slide11.png"
                  alt="Slide 11 illustration"
                />
              ) : slideNumber === 15 ? (
                <SlideImage
                  src="https://embrsmath.com/wp-content/uploads/2025/05/slide15.png"
                  alt="Slide 15 illustration"
                />
              ) : slideNumber === 17 ? (
                <SlideImage
                  src="https://embrsmath.com/wp-content/uploads/2025/05/slide17.png"
                  alt="Slide 17 illustration"
                />
              ) : slideNumber === 19 ? (
                <SlideImage
                  src="https://embrsmath.com/wp-content/uploads/2025/05/slide19.png"
                  alt="Slide 19 illustration"
                />
              ) : slideNumber === 23 ? (
                <SlideImage
                  src="https://embrsmath.com/wp-content/uploads/2025/05/slide23.png"
                  alt="Slide 23 illustration"
                />
              ) : (
                <>
                  <div className="w-40 h-40 bg-white rounded-full flex items-center justify-center mb-6">
                    <div className="relative w-32 h-32">
                      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-[#4169E1] rounded-full"></div>
                      <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-[#4169E1] rounded-full"></div>
                      <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-[#4169E1] rounded-full"></div>
                      <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-[#4169E1] rounded-full"></div>
                      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 h-full w-2 bg-[#fadb9a]"></div>
                      <div className="absolute top-1/2 left-0 transform -translate-y-1/2 h-2 w-full bg-[#fadb9a]"></div>
                    </div>
                  </div>
                  <div className="illustration-title">Comparing Numbers</div>
                  <div className="illustration-text">
                    Learning to compare numbers helps us understand which quantities are greater or less.
                  </div>
                </>
              )}
            </div>
            <div className="space-y-6 rounded-xl p-6 relative bg-white/10" style={{ minHeight: "400px" }}>
              {/* Add show all button */}
              <div className="flex justify-end mb-4">
                <button onClick={() => setShowAll(!showAll)} className="flex items-center gap-2 text-sm cursor-pointer">
                  <span>{showAll ? "Hide All" : "Show All"}</span>
                  <Eye size={18} className={showAll ? "text-[#fadb9a]" : ""} />
                </button>
              </div>

              {points.map((point, index) => (
                <RevealPoint
                  key={index}
                  number={
                    [10, 11, 12, 17, 18, 19, 23].includes(slideNumber)
                      ? index === 0
                        ? "Q"
                        : index === 1
                          ? "A"
                          : index + 1
                      : index + 1
                  }
                  title={point.title}
                  content={point.content}
                  forceReveal={revealedItems.includes(index) || showAll}
                />
              ))}
            </div>
          </>
        ) : (
          <>
            <div className="space-y-6 rounded-xl p-6 relative bg-white/10" style={{ minHeight: "400px" }}>
              {/* Add show all button */}
              <div className="flex justify-end mb-4">
                <button onClick={() => setShowAll(!showAll)} className="flex items-center gap-2 text-sm cursor-pointer">
                  <span>{showAll ? "Hide All" : "Show All"}</span>
                  <Eye size={18} className={showAll ? "text-[#fadb9a]" : ""} />
                </button>
              </div>

              {points.map((point, index) => (
                <RevealPoint
                  key={index}
                  number={
                    [10, 11, 12, 17, 18, 19, 23].includes(slideNumber)
                      ? index === 0
                        ? "Q"
                        : index === 1
                          ? "A"
                          : index + 1
                      : index + 1
                  }
                  title={point.title}
                  content={point.content}
                  forceReveal={revealedItems.includes(index) || showAll}
                />
              ))}
            </div>
            <div className="illustration-box" style={{ minHeight: "400px", ...illustrationStyle }}>
              <div className="w-full h-full flex justify-center items-center">
                {slideNumber === 4 ? (
                  <SlideImage
                    src="https://embrsmath.com/wp-content/uploads/2025/05/slide4.png"
                    alt="Slide 4 illustration"
                  />
                ) : slideNumber === 5 ? (
                  <div className="slide-image-container">
                    <div className="w-full h-full flex justify-center items-center overflow-hidden">
                      <img
                        src="https://sjc.microlink.io/FyHVxSCn4DmliKSxA_KNxZRPkQ0LFy6Mm2yTxzpqaJekkyjhY8XXrA0HB4JHsq_l9_WdQndLyoOgi67486vdpw.jpeg"
                        alt="Slide 5 illustration showing comparison of 34 and 37 with base-10 blocks"
                        style={{
                          maxWidth: "100%",
                          maxHeight: "100%",
                          objectFit: "contain",
                          backgroundColor: "transparent",
                          border: "none",
                        }}
                        onError={(e: any) => {
                          console.error("Image failed to load:", e.target.src)
                          e.currentTarget.src = "/place-value-comparison.png"
                        }}
                      />
                    </div>
                  </div>
                ) : slideNumber === 6 ? (
                  <div className="slide-image-container">
                    <div className="w-full h-full flex justify-center items-center overflow-hidden">
                      <img
                        src="https://sjc.microlink.io/KDs5dfEV2-Drs1lnL67WzGm_l3EQiCY3nOE299oxsPEipLu8tvOhLzYiQ7KrGuTeKoPQae9YUDqWEO1tglWZLw.jpeg"
                        alt="Slide 6 illustration showing comparison of 51 and 56 with base-10 blocks"
                        style={{
                          maxWidth: "100%",
                          maxHeight: "100%",
                          objectFit: "contain",
                          backgroundColor: "transparent",
                          border: "none",
                        }}
                        onError={(e: any) => {
                          console.error("Image failed to load:", e.target.src)
                          e.currentTarget.src = "/place-value-comparison.png"
                        }}
                      />
                    </div>
                  </div>
                ) : slideNumber === 7 ? (
                  <SlideImage
                    src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/slide7-MKMs4HfDzZH0AbE6YSM9O7p3Ul3ws9.png"
                    alt="Slide 7 illustration showing comparison of numbers with equal tens but different ones"
                  />
                ) : slideNumber === 8 ? (
                  <div className="slide-image-container">
                    <div className="w-full h-full flex justify-center items-center overflow-hidden">
                      <img
                        src="https://sjc.microlink.io/5gp9-QgtFBCMzfnDpppGvKWunx_SoCZN5ctj2R0Ae_scNpx7_J4kjy1hN3O3Mz4G89z7VHZkO6AJX5IzM4WCBQ.jpeg"
                        alt="Slide 8 illustration showing comparison of 42 and 45"
                        style={{
                          maxWidth: "100%",
                          maxHeight: "100%",
                          objectFit: "contain",
                          backgroundColor: "transparent",
                          border: "none",
                        }}
                        onError={(e: any) => {
                          console.error("Image failed to load:", e.target.src)
                          e.currentTarget.src = "/place-value-comparison.png"
                        }}
                      />
                    </div>
                  </div>
                ) : slideNumber === 9 ? (
                  <SlideImage
                    src="https://embrsmath.com/wp-content/uploads/2025/05/slide9.png"
                    alt="Slide 9 illustration"
                  />
                ) : slideNumber === 10 ? (
                  <img
                    src="https://embrsmath.com/wp-content/uploads/2025/05/slide10.png"
                    alt="Slide 10 illustration"
                    className="w-full h-full object-cover rounded-lg"
                    onError={(e: any) => {
                      console.error("Image failed to load:", e.target.src)
                      e.currentTarget.src = "/place-value-comparison.png"
                    }}
                  />
                ) : slideNumber === 11 ? (
                  <SlideImage
                    src="https://embrsmath.com/wp-content/uploads/2025/05/slide11.png"
                    alt="Slide 11 illustration"
                  />
                ) : slideNumber === 12 ? (
                  <img
                    src="https://embrsmath.com/wp-content/uploads/2025/05/slide12.png"
                    alt="Slide 12 illustration"
                    className="w-full h-full object-cover rounded-lg"
                    onError={(e: any) => {
                      console.error("Image failed to load:", e.target.src)
                      e.currentTarget.src = "/place-value-comparison.png"
                    }}
                  />
                ) : slideNumber === 14 ? (
                  <img
                    src="https://embrsmath.com/wp-content/uploads/2025/05/slide14.png"
                    alt="Slide 14 illustration"
                    className="w-full h-full object-cover rounded-lg"
                    onError={(e: any) => {
                      console.error("Image failed to load:", e.target.src)
                      e.currentTarget.src = "/place-value-comparison.png"
                    }}
                  />
                ) : slideNumber === 15 ? (
                  <SlideImage
                    src="https://embrsmath.com/wp-content/uploads/2025/05/slide15.png"
                    alt="Slide 15 illustration"
                  />
                ) : slideNumber === 16 ? (
                  <img
                    src="https://embrsmath.com/wp-content/uploads/2025/05/slide16.png"
                    alt="Slide 16 illustration"
                    className="w-full h-full object-cover rounded-lg"
                    onError={(e: any) => {
                      console.error("Image failed to load:", e.target.src)
                      e.currentTarget.src = "/place-value-comparison.png"
                    }}
                  />
                ) : slideNumber === 17 ? (
                  <SlideImage
                    src="https://embrsmath.com/wp-content/uploads/2025/05/slide17.png"
                    alt="Slide 17 illustration"
                  />
                ) : slideNumber === 18 ? (
                  <img
                    src="https://embrsmath.com/wp-content/uploads/2025/05/slide18.png"
                    alt="Slide 18 illustration"
                    className="w-full h-full object-cover rounded-lg"
                    onError={(e: any) => {
                      console.error("Image failed to load:", e.target.src)
                      e.currentTarget.src = "/place-value-comparison.png"
                    }}
                  />
                ) : slideNumber === 19 ? (
                  <SlideImage
                    src="https://embrsmath.com/wp-content/uploads/2025/05/slide19.png"
                    alt="Slide 19 illustration"
                  />
                ) : slideNumber === 22 ? (
                  <div className="slide-image-container">
                    <div className="w-full h-full flex justify-center items-center overflow-hidden">
                      <img
                        src="https://embrsmath.com/wp-content/uploads/2025/05/slide22.png"
                        alt="Place value diagram showing the number 34 with 3 tens (30) and 4 ones"
                        style={{
                          maxWidth: "100%",
                          maxHeight: "100%",
                          objectFit: "contain",
                          backgroundColor: "transparent",
                          border: "none",
                        }}
                        onError={(e: any) => {
                          console.error("Image failed to load:", e.target.src)
                          e.currentTarget.src = "/place-value-comparison.png"
                        }}
                      />
                    </div>
                  </div>
                ) : slideNumber === 23 ? (
                  <SlideImage
                    src="https://embrsmath.com/wp-content/uploads/2025/05/slide23.png"
                    alt="Slide 23 illustration"
                  />
                ) : (
                  <div className="w-40 h-40 bg-white rounded-full flex items-center justify-center mb-6">
                    <div className="relative w-32 h-32">
                      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-[#4169E1] rounded-full"></div>
                      <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-[#4169E1] rounded-full"></div>
                      <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-[#4169E1] rounded-full"></div>
                      <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-[#4169E1] rounded-full"></div>
                      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 h-full w-2 bg-[#fadb9a]"></div>
                      <div className="absolute top-1/2 left-0 transform -translate-y-1/2 h-2 w-full bg-[#fadb9a]"></div>
                    </div>
                  </div>
                )}
                {![4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 22, 23].includes(slideNumber) && (
                  <>
                    <div className="illustration-title">Comparing Numbers</div>
                    <div className="illustration-text">
                      Learning to compare numbers helps us understand which quantities are greater or less.
                    </div>
                  </>
                )}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  )
}

interface ActivitySlideProps {
  title: string
  activities: { question: string; answer: string }[]
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
  setRevealedItems?: React.Dispatch<React.SetStateAction<number[]>>
}

function ActivitySlide({
  title,
  activities,
  revealedItems = [],
  registerRevealableItems = () => {},
  setRevealedItems,
}: ActivitySlideProps) {
  // Register revealable items (one for each activity)
  useEffect(() => {
    registerRevealableItems(activities.length * 2) // One for question, one for answer
  }, [registerRevealableItems, activities.length])

  return (
    <div className="p-8 text-white">
      <h2 className="slide-title">{title}</h2>
      <div className="relative">
        <div className="grid grid-cols-3 gap-6" style={{ minHeight: "400px" }}>
          {activities.map((activity, index) => (
            <ActivityCard
              key={index}
              number={index + 1}
              question={activity.question}
              answer={activity.answer}
              isVisible={revealedItems.includes(index)}
              showAnswer={revealedItems.includes(index + activities.length)}
              onToggleVisibility={() => {
                if (setRevealedItems) {
                  if (revealedItems.includes(index)) {
                    // Hide question and answer
                    setRevealedItems(
                      revealedItems.filter((item) => item !== index && item !== index + activities.length),
                    )
                  } else {
                    // Show question only
                    setRevealedItems([...revealedItems, index])
                  }
                }
              }}
              onToggleAnswer={() => {
                if (setRevealedItems) {
                  if (revealedItems.includes(index + activities.length)) {
                    // Hide answer
                    setRevealedItems(revealedItems.filter((item) => item !== index + activities.length))
                  } else {
                    // Show answer
                    setRevealedItems([...revealedItems, index + activities.length])
                  }
                }
              }}
            />
          ))}
        </div>
      </div>
    </div>
  )
}

interface ActivityCardProps {
  number: number
  question: string
  answer: string
  isVisible: boolean
  showAnswer: boolean
  onToggleVisibility: () => void
  onToggleAnswer: () => void
}

function ActivityCard({
  number,
  question,
  answer,
  isVisible,
  showAnswer,
  onToggleVisibility,
  onToggleAnswer,
}: ActivityCardProps) {
  return (
    <div className="rounded-lg bg-white/10 overflow-hidden h-full flex flex-col">
      <div className="p-4 flex items-center justify-between gap-4 cursor-pointer" onClick={onToggleVisibility}>
        <div className="concept-number bg-[#2B6DFE] text-white">{number}</div>
        {isVisible ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
      </div>

      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ height: 0, opacity: 0, y: -10 }}
            animate={{ height: "auto", opacity: 1, y: 0 }}
            exit={{ height: 0, opacity: 0, y: -10 }}
            transition={{
              duration: 0.3,
              ease: "easeInOut",
              opacity: { duration: 0.2 },
            }}
            className="overflow-hidden flex flex-col flex-grow"
          >
            <div className="p-4 pt-0 flex flex-col flex-grow">
              <div className="text-3xl mb-4">{question}</div>

              <div className="mt-auto">
                <div
                  className="flex items-center justify-between p-3 bg-white/10 rounded-md cursor-pointer"
                  onClick={(e) => {
                    e.stopPropagation()
                    onToggleAnswer()
                  }}
                >
                  <span className="font-medium">Answer</span>
                  {showAnswer ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
                </div>

                <AnimatePresence>
                  {showAnswer && (
                    <motion.div
                      initial={{ height: 0, opacity: 0, y: -5 }}
                      animate={{ height: "auto", opacity: 1, y: 0 }}
                      exit={{ height: 0, opacity: 0, y: -5 }}
                      transition={{ duration: 0.25 }}
                      className="overflow-hidden"
                    >
                      <div className="mt-2 p-3 bg-[#fadb9a]/10 rounded-md text-3xl">{answer}</div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

interface PracticeSlideProps {
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
  setRevealedItems?: React.Dispatch<React.SetStateAction<number[]>>
}

function PracticeSlide({
  revealedItems = [],
  registerRevealableItems = () => {},
  setRevealedItems,
}: PracticeSlideProps) {
  const practiceQuestions = [
    { question: "34 __ 18", answer: "34 > 18" },
    { question: "52 __ 87", answer: "52 < 87" },
    { question: "90 __ 40", answer: "90 > 40" },
    { question: "Compare 26 and 22. Use > or <.", answer: "26 > 22" },
    { question: "Compare 73 and 79. Use > or <.", answer: "73 < 79" },
    { question: "85 __ 89", answer: "85 < 89" },
    {
      question: "There are 41 apples and 50 oranges. Which fruit is there less of? Use < or >.",
      answer: "There are less apples because 41 < 50.",
    },
    {
      question: "Sam read 67 pages. Mia read 63 pages. Who read more pages? Use < or >.",
      answer: "Sam read more pages because 67 > 63.",
    },
    { question: "Compare 15 and 15. Use >, <, or =.", answer: "15 = 15" },
    { question: "Compare 98 and 91. Use > or <.", answer: "98 > 91" },
    { question: "Compare 30 and 36. Use > or <.", answer: "30 < 36" },
    {
      question: "Juan has 55 toy cars. Ben has 49 toy cars. Who has more toy cars? Use < or >.",
      answer: "Juan has more toy cars because 55 > 49.",
    },
  ]

  // Register revealable items (one for each question)
  useEffect(() => {
    registerRevealableItems(practiceQuestions.length * 2) // One for question, one for answer
  }, [registerRevealableItems, practiceQuestions.length])

  // Function to toggle all answers
  const toggleAllAnswers = () => {
    if (!setRevealedItems) return

    const visibleQuestions = practiceQuestions.map((_, idx) => idx).filter((idx) => revealedItems.includes(idx))
    const answerIndices = visibleQuestions.map((idx) => idx + practiceQuestions.length)

    // Check if all answers for visible questions are already shown
    const allAnswersShown = visibleQuestions.every((idx) => revealedItems.includes(idx + practiceQuestions.length))

    if (allAnswersShown && visibleQuestions.length > 0) {
      // Hide all answers
      setRevealedItems(revealedItems.filter((idx) => idx < practiceQuestions.length))
    } else if (visibleQuestions.length > 0) {
      // Show answers for all visible questions
      const newRevealedItems = [...revealedItems]
      answerIndices.forEach((idx) => {
        if (!newRevealedItems.includes(idx)) {
          newRevealedItems.push(idx)
        }
      })
      setRevealedItems(newRevealedItems)
    }
  }

  return (
    <div className="rounded-xl overflow-hidden shadow-lg blue-gradient">
      <div className="p-8 text-white">
        <h2 className="slide-title text-3xl mb-6 text-center">Practice: Comparing Numbers</h2>

        <div className="toggle-answers mb-6 flex flex-wrap gap-2 justify-end">
          <button
            onClick={toggleAllAnswers}
            className="flex items-center gap-2 rounded-md bg-white/10 px-4 py-2 text-sm font-medium hover:bg-white/20"
          >
            <Eye size={18} />
            <span>Toggle All Answers</span>
          </button>
        </div>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
          {practiceQuestions.map((item, index) => (
            <PracticeCard
              key={index}
              number={index + 1}
              question={item.question}
              answer={item.answer}
              isVisible={revealedItems.includes(index)}
              showAnswer={revealedItems.includes(index + practiceQuestions.length)}
              onToggleVisibility={() => {
                if (setRevealedItems) {
                  if (revealedItems.includes(index)) {
                    // Hide question and answer
                    setRevealedItems(
                      revealedItems.filter((item) => item !== index && item !== index + practiceQuestions.length),
                    )
                  } else {
                    // Show question only
                    setRevealedItems([...revealedItems, index])
                  }
                }
              }}
              onToggleAnswer={() => {
                if (setRevealedItems) {
                  if (revealedItems.includes(index + practiceQuestions.length)) {
                    // Hide answer
                    setRevealedItems(revealedItems.filter((item) => item !== index + practiceQuestions.length))
                  } else {
                    // Show answer
                    setRevealedItems([...revealedItems, index + practiceQuestions.length])
                  }
                }
              }}
            />
          ))}
        </div>
      </div>
    </div>
  )
}

interface PracticeCardProps {
  number: number
  question: string
  answer: string
  isVisible: boolean
  showAnswer: boolean
  onToggleVisibility: () => void
  onToggleAnswer: () => void
}

function PracticeCard({
  number,
  question,
  answer,
  isVisible,
  showAnswer,
  onToggleVisibility,
  onToggleAnswer,
}: PracticeCardProps) {
  return (
    <div className="rounded-lg bg-white/10 overflow-hidden">
      <div className="p-4 flex items-center gap-2 cursor-pointer" onClick={onToggleVisibility}>
        <div className="concept-number bg-[#fadb9a]/30 text-[#4169E1]">{number}</div>
        <div className="flex-1"></div>
        {isVisible ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
      </div>

      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ height: 0, opacity: 0, y: -10 }}
            animate={{ height: "auto", opacity: 1, y: 0 }}
            exit={{ height: 0, opacity: 0, y: -10 }}
            transition={{
              duration: 0.3,
              ease: "easeInOut",
            }}
            className="overflow-hidden"
          >
            <div className="p-4 pt-0">
              <div className="text-3xl mb-4">{question}</div>

              <div
                className="flex items-center justify-between p-2 bg-white/10 rounded-md cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation()
                  onToggleAnswer()
                }}
              >
                <span className="text-sm font-medium text-[#fadb9a]">Answer</span>
                {showAnswer ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
              </div>

              <AnimatePresence>
                {showAnswer && (
                  <motion.div
                    initial={{ height: 0, opacity: 0, y: -5 }}
                    animate={{ height: "auto", opacity: 1, y: 0 }}
                    exit={{ height: 0, opacity: 0, y: -5 }}
                    transition={{ duration: 0.25 }}
                    className="overflow-hidden"
                  >
                    <div className="mt-2 p-3 bg-[#fadb9a]/10 rounded-md text-3xl">{answer}</div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

interface OnRampTeachSlideProps {
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
}

function OnRampTeachSlide({ revealedItems = [], registerRevealableItems = () => {} }: OnRampTeachSlideProps) {
  const teachPoints = [
    {
      title: "The left digit is the tens place.",
      content: "In a two-digit number, the digit on the left is in the tens place.",
    },
    {
      title: "It tells you how many groups of ten.",
      content: "That digit tells us how many groups of ten we have.",
    },
    {
      title: "3 in 34 is 3 tens.",
      content: "For example, in the number 34, the 3 is in the tens place.",
    },
    {
      title: "3 tens is the same as 30.",
      content: "It doesn't mean just 3! It means 3 groups of ten, which is 30!",
    },
  ]

  useEffect(() => {
    registerRevealableItems(teachPoints.length)
  }, [registerRevealableItems, teachPoints.length])

  const [showAll, setShowAll] = useState(false)

  return (
    <div className="p-8 text-white">
      <h2 className="slide-title">On-Ramp Teach: Place Value</h2>
      <div className="concept-slide">
        <div className="space-y-6 rounded-xl p-6 relative bg-white/10" style={{ minHeight: "400px" }}>
          {/* Add show all button */}
          <div className="flex justify-end mb-4">
            <button onClick={() => setShowAll(!showAll)} className="flex items-center gap-2 text-sm cursor-pointer">
              <span>{showAll ? "Hide All" : "Show All"}</span>
              <Eye size={18} className={showAll ? "text-[#fadb9a]" : ""} />
            </button>
          </div>

          {teachPoints.map((point, index) => (
            <RevealPoint
              key={index}
              number={index + 1}
              title={point.title}
              content={point.content}
              forceReveal={revealedItems.includes(index) || showAll}
            />
          ))}
        </div>
        <div className="illustration-box" style={{ minHeight: "400px", ...illustrationStyle }}>
          <div className="slide-image-container">
            <div className="w-full h-full flex justify-center items-center overflow-hidden">
              <img
                src="https://embrsmath.com/wp-content/uploads/2025/05/slide22.png"
                alt="Place value diagram showing the number 34 with 3 tens (30) and 4 ones"
                style={{
                  maxWidth: "100%",
                  maxHeight: "100%",
                  objectFit: "contain",
                  backgroundColor: "transparent",
                  border: "none",
                }}
                onError={(e: any) => {
                  console.error("Image failed to load:", e.target.src)
                  e.currentTarget.src = "/place-value-comparison.png"
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

interface OnRampTrySlideProps {
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
  setRevealedItems?: React.Dispatch<React.SetStateAction<number[]>>
}

function OnRampTrySlide({
  revealedItems = [],
  registerRevealableItems = () => {},
  setRevealedItems,
}: OnRampTrySlideProps) {
  const tryActivities = [
    {
      question: "What is the value of the 2 in 29?",
      answer: "20",
    },
    {
      question: "What is the value of the 8 in 81?",
      answer: "80",
    },
    {
      question: "What is the value of 6 tens?",
      answer: "60",
    },
  ]

  // Register revealable items (one for each activity)
  useEffect(() => {
    registerRevealableItems(tryActivities.length * 2) // One for question, one for answer
  }, [registerRevealableItems, tryActivities.length])

  return (
    <div className="p-8 text-white">
      <h2 className="slide-title">On-Ramp Try: Place Value Practice</h2>
      <div className="relative">
        <div className="grid grid-cols-3 gap-6" style={{ minHeight: "400px" }}>
          {tryActivities.map((activity, index) => (
            <ActivityCard
              key={index}
              number={index + 1}
              question={activity.question}
              answer={activity.answer}
              isVisible={revealedItems.includes(index)}
              showAnswer={revealedItems.includes(index + tryActivities.length)}
              onToggleVisibility={() => {
                if (setRevealedItems) {
                  if (revealedItems.includes(index)) {
                    // Hide question and answer
                    setRevealedItems(
                      revealedItems.filter((item) => item !== index && item !== index + tryActivities.length),
                    )
                  } else {
                    // Show question only
                    setRevealedItems([...revealedItems, index])
                  }
                }
              }}
              onToggleAnswer={() => {
                if (setRevealedItems) {
                  if (revealedItems.includes(index + tryActivities.length)) {
                    // Hide answer
                    setRevealedItems(revealedItems.filter((item) => item !== index + tryActivities.length))
                  } else {
                    // Show answer
                    setRevealedItems([...revealedItems, index + tryActivities.length])
                  }
                }
              }}
            />
          ))}
        </div>
      </div>
    </div>
  )
}

interface PrintableLessonGuideSlideProps {
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
  documentId?: string
  unitNumber?: string
  lessonNumber?: string
  gradeLevel?: string
}

function PrintableLessonGuideSlide({
  revealedItems = [],
  registerRevealableItems = () => {},
  documentId,
  unitNumber,
  lessonNumber,
  gradeLevel,
}: PrintableLessonGuideSlideProps) {
  const { data: session } = useSession()
  const [currentPdfUrl, setCurrentPdfUrl] = useState<string>('')

  // Check if user is admin
  const isAdmin = (session?.user as any)?.role === 'admin'

  // Generate document ID if not provided
  const effectiveDocumentId = documentId || (unitNumber && lessonNumber && gradeLevel ?
    `${unitNumber}-${lessonNumber}-${gradeLevel}` : '')

  // Handle PDF upload success
  const handlePdfUploadSuccess = (url: string) => {
    setCurrentPdfUrl(url)
  }

  // Load existing PDF URL from database
  useEffect(() => {
    const loadPdfUrl = async () => {
      if (effectiveDocumentId) {
        try {
          const response = await fetch(`/api/get-lesson-data?documentId=${effectiveDocumentId}`)
          if (response.ok) {
            const data = await response.json()
            const slideData = data.content?.['Slide 25: Lesson_Guide_PDF_Link']
            if (slideData?.link_to_lesson_guide_pdf) {
              setCurrentPdfUrl(slideData.link_to_lesson_guide_pdf)
            }
          }
        } catch (error) {
          console.error('Error loading PDF URL:', error)
        }
      }
    }
    loadPdfUrl()
  }, [effectiveDocumentId])

  useEffect(() => {
    registerRevealableItems(1)
  }, [registerRevealableItems])

  return (
    <div className="rounded-xl overflow-hidden shadow-lg blue-gradient">
      <div className="p-8 text-white">
        <h2 className="slide-title text-3xl mb-6 text-center">Printable: Lesson Guide</h2>
        <div className="rounded-lg bg-white/10 p-6 backdrop-blur-sm">
          <h3 className="mb-4 text-xl font-medium">Lesson Guide PDF</h3>
          <p className="mb-4">A printable version of the complete lesson guide for teacher reference</p>
          <div
            className={`mb-6 aspect-[8.5/11] rounded-lg border-2 border-dashed border-white/30 bg-white/5 flex flex-col items-center justify-center p-6 ${revealedItems.includes(0) ? "bg-white/20" : ""}`}
          >
            <span className="text-xl font-medium">Lesson Guide PDF Preview</span>
            <p className="mt-2 text-center text-white/80">
              Contains all lesson components, teaching notes, and answer keys
            </p>
            <a
              href={currentPdfUrl || "/printable-lesson-guide.pdf"}
              download
              target="_blank"
              rel="noopener noreferrer"
              className="mt-4 rounded-md bg-[#fadb9a] px-4 py-2 text-sm font-medium text-[#4169E1] hover:bg-[#fadb9a]/90 inline-flex items-center gap-2"
            >
              <Download size={16} />
              Download PDF
            </a>
          </div>
          <div className="rounded-lg bg-white/10 p-4">
            <h4 className="mb-2 font-medium text-[#fadb9a]">Contents Include:</h4>
            <ul className="ml-6 list-disc">
              <li>Detailed lesson plans</li>
              <li>Discussion questions and expected responses</li>
              <li>Differentiation strategies</li>
              <li>Assessment guidelines</li>
            </ul>
          </div>

          {/* Admin PDF Upload Section */}
          {isAdmin && effectiveDocumentId && (
            <div className="mt-4">
              <PdfUploader
                documentId={effectiveDocumentId}
                slideType="lesson_guide"
                currentPdfUrl={currentPdfUrl}
                onUploadSuccess={handlePdfUploadSuccess}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

interface PrintablePracticeSlideProps {
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
  documentId?: string
  unitNumber?: string
  lessonNumber?: string
  gradeLevel?: string
}

function PrintablePracticeSlideComponent({
  revealedItems = [],
  registerRevealableItems = () => {},
  documentId,
  unitNumber,
  lessonNumber,
  gradeLevel,
}: PrintablePracticeSlideProps) {
  const { data: session } = useSession()
  const [currentPdfUrl, setCurrentPdfUrl] = useState<string>('')

  // Check if user is admin
  const isAdmin = (session?.user as any)?.role === 'admin'

  // Generate document ID if not provided
  const effectiveDocumentId = documentId || (unitNumber && lessonNumber && gradeLevel ?
    `${unitNumber}-${lessonNumber}-${gradeLevel}` : '')

  // Handle PDF upload success
  const handlePdfUploadSuccess = (url: string) => {
    setCurrentPdfUrl(url)
  }

  // Load existing PDF URL from database
  useEffect(() => {
    const loadPdfUrl = async () => {
      if (effectiveDocumentId) {
        try {
          const response = await fetch(`/api/get-lesson-data?documentId=${effectiveDocumentId}`)
          if (response.ok) {
            const data = await response.json()
            const slideData = data.content?.['Slide 26: Practice_PDF_Link']
            if (slideData?.link_to_practice_pdf) {
              setCurrentPdfUrl(slideData.link_to_practice_pdf)
            }
          }
        } catch (error) {
          console.error('Error loading PDF URL:', error)
        }
      }
    }
    loadPdfUrl()
  }, [effectiveDocumentId])

  useEffect(() => {
    registerRevealableItems(1)
  }, [registerRevealableItems])

  return (
    <div className="rounded-xl overflow-hidden shadow-lg blue-gradient">
      <div className="p-8 text-white">
        <h2 className="slide-title text-3xl mb-6 text-center">Printable: Practice Sheet</h2>
        <div className="rounded-lg bg-white/10 p-6 backdrop-blur-sm">
          <h3 className="mb-4 text-xl font-medium">Practice Sheet PDF</h3>
          <p className="mb-4">A printable practice sheet for students to reinforce learning</p>
          <div
            className={`mb-6 aspect-[8.5/11] rounded-lg border-2 border-dashed border-white/30 bg-white/5 flex flex-col items-center justify-center p-6 ${revealedItems.includes(0) ? "bg-white/20" : ""}`}
          >
            <span className="text-xl font-medium">Practice Sheet PDF Preview</span>
            <p className="mt-2 text-center text-white/80">
              Includes practice problems, review questions, and extension activities
            </p>
            <a
              href={currentPdfUrl || "/printable-practice.pdf"}
              download
              target="_blank"
              rel="noopener noreferrer"
              className="mt-4 rounded-md bg-[#fadb9a] px-4 py-2 text-sm font-medium text-[#4169E1] hover:bg-[#fadb9a]/90 inline-flex items-center gap-2"
            >
              <Download size={16} />
              Download PDF
            </a>
          </div>
          <div className="rounded-lg bg-white/10 p-4">
            <h4 className="mb-2 font-medium text-[#fadb9a]">Skills Reinforced:</h4>
            <ul className="ml-6 list-disc">
              <li>Comparing two-digit numbers</li>
              <li>Using comparison symbols</li>
              <li>Understanding place value</li>
              <li>Problem-solving with comparisons</li>
            </ul>
          </div>

          {/* Admin PDF Upload Section */}
          {isAdmin && effectiveDocumentId && (
            <div className="mt-4">
              <PdfUploader
                documentId={effectiveDocumentId}
                slideType="practice"
                currentPdfUrl={currentPdfUrl}
                onUploadSuccess={handlePdfUploadSuccess}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

interface PrintableAcceleratorSlideProps {
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
  documentId?: string
  unitNumber?: string
  lessonNumber?: string
  gradeLevel?: string
}

function PrintableAcceleratorSlideComponent({
  revealedItems = [],
  registerRevealableItems = () => {},
  documentId,
  unitNumber,
  lessonNumber,
  gradeLevel,
}: PrintableAcceleratorSlideProps) {
  const { data: session } = useSession()
  const [currentPdfUrl, setCurrentPdfUrl] = useState<string>('')

  // Check if user is admin
  const isAdmin = (session?.user as any)?.role === 'admin'

  // Generate document ID if not provided
  const effectiveDocumentId = documentId || (unitNumber && lessonNumber && gradeLevel ?
    `${unitNumber}-${lessonNumber}-${gradeLevel}` : '')

  // Handle PDF upload success
  const handlePdfUploadSuccess = (url: string) => {
    setCurrentPdfUrl(url)
  }

  // Load existing PDF URL from database
  useEffect(() => {
    const loadPdfUrl = async () => {
      if (effectiveDocumentId) {
        try {
          const response = await fetch(`/api/get-lesson-data?documentId=${effectiveDocumentId}`)
          if (response.ok) {
            const data = await response.json()
            const slideData = data.content?.['Slide 27: Accelerator_PDF_Link']
            if (slideData?.link_to_accelerator_pdf) {
              setCurrentPdfUrl(slideData.link_to_accelerator_pdf)
            }
          }
        } catch (error) {
          console.error('Error loading PDF URL:', error)
        }
      }
    }
    loadPdfUrl()
  }, [effectiveDocumentId])

  useEffect(() => {
    registerRevealableItems(1)
  }, [registerRevealableItems])

  return (
    <div className="rounded-xl overflow-hidden shadow-lg blue-gradient">
      <div className="p-8 text-white">
        <h2 className="slide-title text-3xl mb-6 text-center">Printable: Accelerator Activity</h2>
        <div className="rounded-lg bg-white/10 p-6 backdrop-blur-sm">
          <h3 className="mb-4 text-xl font-medium">Accelerator Activity PDF</h3>
          <p className="mb-4">A printable accelerator activity for students who have mastered the core concepts</p>
          <div
            className={`mb-6 aspect-[8.5/11] rounded-lg border-2 border-dashed border-white/30 bg-white/5 flex flex-col items-center justify-center p-6 ${revealedItems.includes(0) ? "bg-white/20" : ""}`}
          >
            <span className="text-xl font-medium">Accelerator Activity PDF Preview</span>
            <p className="mt-2 text-center text-white/80">
              Includes challenging problems, real-world applications, and creative tasks
            </p>
            <a
              href={currentPdfUrl || "/accelerator-activities.pdf"}
              download
              target="_blank"
              rel="noopener noreferrer"
              className="mt-4 rounded-md bg-[#fadb9a] px-4 py-2 text-sm font-medium text-[#4169E1] hover:bg-[#fadb9a]/90 inline-flex items-center gap-2"
            >
              <Download size={16} />
              Download PDF
            </a>
          </div>
          <div className="rounded-lg bg-white/10 p-4">
            <h4 className="mb-2 font-medium text-[#fadb9a]">Skills Extended:</h4>
            <ul className="ml-6 list-disc">
              <li>Comparing numbers beyond 100</li>
              <li>Ordering multiple two-digit numbers</li>
              <li>Solving complex comparison word problems</li>
              <li>Creating number patterns based on comparisons</li>
            </ul>
          </div>

          {/* Admin PDF Upload Section */}
          {isAdmin && effectiveDocumentId && (
            <div className="mt-4">
              <PdfUploader
                documentId={effectiveDocumentId}
                slideType="accelerator"
                currentPdfUrl={currentPdfUrl}
                onUploadSuccess={handlePdfUploadSuccess}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
