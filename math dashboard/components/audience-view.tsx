"use client"

import { useEffect, useState } from "react"
import { usePresenter } from "./presenter-context"
import { SlideContent } from "./slide-content"

export function AudienceView() {
  const { currentSlide, revealedItems, setRevealedItems } = usePresenter()
  const [isFullscreen, setIsFullscreen] = useState(false)

  // Handle fullscreen on load
  useEffect(() => {
    const enterFullscreen = async () => {
      try {
        if (typeof document.documentElement.requestFullscreen === "function") {
          // await document.documentElement.requestFullscreen()
          setIsFullscreen(true)
          console.log("Entered fullscreen mode")
        }
      } catch (err) {
        console.error("Error attempting to enable full-screen mode:", err)
      }
    }

    // Slight delay to ensure the DOM is ready
    const timer = setTimeout(() => {
      enterFullscreen()
    }, 1000)

    // Debug logging
    console.log("Audience view mounted, current slide:", currentSlide)
    console.log("Revealed items:", revealedItems)

    return () => {
      clearTimeout(timer)
      if (document.fullscreenElement) {
        document.exitFullscreen().catch((err) => {
          console.error("Error attempting to exit full-screen mode:", err)
        })
      }
    }
  }, [currentSlide, revealedItems])

  // Define registerRevealableItems function locally
  const registerRevealableItems = (count: number) => {
    console.log(`Audience: registered ${count} revealable items for slide ${currentSlide}`)
    // In audience view, we don't need to do anything with this count
    // It's just for logging and compatibility with the SlideContent component
  }

  return (
    <div className="h-screen w-screen bg-[linear-gradient(150deg,#2B6DFE_0%,#2B6DFE_70%,#00F2FF_100%)] flex items-center justify-center">
      <div className="w-full h-full">
        <SlideContent
          slideNumber={currentSlide}
          highContrast={false}
          revealedItems={revealedItems}
          registerRevealableItems={registerRevealableItems}
          setRevealedItems={setRevealedItems as any}
        />
      </div>

      {/* Debug info - only visible during development */}
      {/* {process.env.NODE_ENV === "development" && (
        <div className="fixed bottom-0 left-0 bg-black/70 text-white p-2 text-xs">
          Slide: {currentSlide} | Items: {revealedItems.join(",")} | Fullscreen: {isFullscreen ? "Yes" : "No"}
        </div>
      )} */}
    </div>
  )
}
