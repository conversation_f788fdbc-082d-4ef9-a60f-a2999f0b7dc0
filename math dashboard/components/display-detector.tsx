"use client"

import { useState, useEffect } from "react"

interface DisplayInfo {
  available: boolean
  screens: number
  isExtended: boolean
}

export function useDisplayDetector() {
  const [displayInfo, setDisplayInfo] = useState<DisplayInfo>({
    available: false,
    screens: 1,
    isExtended: false,
  })

  useEffect(() => {
    // Check if the browser supports the Screen API
    if (window.screen) {
      setDisplayInfo((prev) => ({
        ...prev,
        available: true,
      }))

      // Try to detect multiple screens using window.screen properties
      // Note: This is a best-effort approach as the API has limitations
      const detectScreens = () => {
        // Check if window.screen.isExtended is available (Chrome 101+)
        const isExtendedAvailable = "isExtended" in window.screen

        // Use window.screen.isExtended if available
        if (isExtendedAvailable) {
          // @ts-ignore - TypeScript doesn't know about this property yet
          const isExtended = window.screen.isExtended
          setDisplayInfo({
            available: true,
            screens: isExtended ? 2 : 1, // Assume at least 2 screens if extended
            isExtended,
          })
        } else {
          // Fallback: Try to detect using screen dimensions
          // This is less reliable but can give a hint
          const screenWidth = window.screen.width
          const availWidth = window.screen.availWidth
          const isLikelyExtended = screenWidth > availWidth * 1.5 // Heuristic

          setDisplayInfo({
            available: true,
            screens: isLikelyExtended ? 2 : 1,
            isExtended: isLikelyExtended,
          })
        }
      }

      detectScreens()

      // Try to detect changes in screen configuration
      window.addEventListener("resize", detectScreens)
      return () => {
        window.removeEventListener("resize", detectScreens)
      }
    }
  }, [])

  return displayInfo
}
