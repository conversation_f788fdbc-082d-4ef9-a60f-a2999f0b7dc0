"use client"

import type React from "react"

import { useState, useEffect } from "react"
import {
  FileText,
  Search,
  ChevronDown,
  BookOpen,
  ClipboardList,
  BarChart3,
  Flame,
  Plus,
  Home,
  ChevronRight,
} from "lucide-react"

// Define the TabContent component
const TabContent: React.FC<{ activeTab: string; setActiveTab: (tab: string) => void }> = ({
  activeTab,
  setActiveTab,
}) => {
  const [expandedRows, setExpandedRows] = useState<number[]>([])
  const [expandedGrade, setExpandedGrade] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [chatMessages, setChatMessages] = useState([
    {
      role: "assistant",
      content: "Hello! I'm ELS<PERSON> (Enhanced Learning Support Assistant). How can I help you with your teaching today?",
    },
    {
      role: "user",
      content: "I need help creating a lesson plan for teaching fractions to 4th graders.",
    },
    {
      role: "assistant",
      content: `I'd be happy to help with that! Here's a suggested lesson plan for teaching fractions to 4th graders:

1. Start with a review of what fractions are using visual models
2. Introduce equivalent fractions using fraction strips
3. Practice identifying fractions in real-world contexts
4. Group activity: fraction matching game
5. Assessment: worksheet with visual fraction problems

Would you like me to elaborate on any of these steps or suggest some specific activities?`,
    },
    {
      role: "user",
      content: "Yes, please suggest some specific activities for the group work.",
    },
    {
      role: "assistant",
      content: `Here are some engaging group activities for teaching fractions:

- **Fraction Concentration:** Create matching cards with equivalent fractions (visual and numerical)
- **Fraction Pizza:** Groups create pizzas and divide them into equal parts to represent fractions
- **Fraction Relay Race:** Students race to order fractions from least to greatest
- **Fraction Bingo:** Call out fractions and students mark equivalent representations

These activities encourage collaboration and make learning fractions fun!`,
    },
  ])
  const [newMessage, setNewMessage] = useState("")
  const [gridColumns, setGridColumns] = useState(3) // Default to 3 columns
  const [errorMessage, setErrorMessage] = useState<string | null>(null)

  // Practice Builder state
  const [selectedLessons, setSelectedLessons] = useState<
    {
      unitNumber: number
      unitTitle: string
      lessonNumber: number
      lessonTitle: string
    }[]
  >([])
  const [showPracticeModal, setShowPracticeModal] = useState(false)
  const [practiceOptions, setPracticeOptions] = useState({
    numQuestions: 10,
    includeWordProblems: true,
    difficultyLevel: "developing",
  })

  // Helper function to get unit titles
  function getUnitTitle(unitNumber: number): string {
    const unitTitles = [
      "Numbers and Operations",
      "Addition and Subtraction",
      "Multiplication and Division",
      "Fractions",
      "Decimals",
      "Geometry",
      "Measurement",
      "Data and Statistics",
      "Algebra",
      "Probability",
      "Ratios and Proportions",
      "Financial Literacy",
    ]
    return unitTitles[unitNumber - 1] || `Unit ${unitNumber}`
  }

  // Helper function to get lesson titles
  function getLessonTitle(unitNumber: number, lessonNumber: number): string {
    const unitTitles = {
      1: [
        "Counting",
        "Place Value",
        "Comparing Numbers",
        "Rounding",
        "Number Patterns",
        "Number Lines",
        "Odd and Even",
        "Skip Counting",
        "Ordinal Numbers",
        "Number Sense",
      ],
      2: [
        "Addition Facts",
        "Addition Strategies",
        "Subtraction Facts",
        "Subtraction Strategies",
        "Word Problems",
        "Mental Math",
        "Regrouping",
        "Multi-digit Addition",
        "Multi-digit Subtraction",
        "Mixed Operations",
      ],
      3: [
        "Multiplication Facts",
        "Multiplication Strategies",
        "Division Facts",
        "Division Strategies",
        "Word Problems",
        "Arrays",
        "Factors",
        "Multiples",
        "Long Division",
        "Remainders",
      ],
      4: [
        "Fraction Basics",
        "Equivalent Fractions",
        "Comparing Fractions",
        "Adding Fractions",
        "Subtracting Fractions",
        "Mixed Numbers",
        "Improper Fractions",
        "Multiplying Fractions",
        "Dividing Fractions",
        "Fraction Word Problems",
      ],
      5: [
        "Decimal Basics",
        "Comparing Decimals",
        "Adding Decimals",
        "Subtracting Decimals",
        "Decimal Word Problems",
        "Multiplying Decimals",
        "Dividing Decimals",
        "Rounding Decimals",
        "Fractions to Decimals",
        "Decimals to Fractions",
      ],
      6: [
        "2D Shapes",
        "3D Shapes",
        "Angles",
        "Symmetry",
        "Coordinate Plane",
        "Perimeter",
        "Area",
        "Volume",
        "Transformations",
        "Congruence",
      ],
      7: [
        "Length",
        "Weight",
        "Capacity",
        "Time",
        "Area and Perimeter",
        "Metric System",
        "Imperial System",
        "Conversions",
        "Elapsed Time",
        "Measurement Word Problems",
      ],
      8: [
        "Collecting Data",
        "Representing Data",
        "Mean, Median, Mode",
        "Interpreting Data",
        "Creating Graphs",
        "Bar Graphs",
        "Line Graphs",
        "Pie Charts",
        "Histograms",
        "Data Analysis",
      ],
      9: [
        "Patterns",
        "Variables",
        "Expressions",
        "Equations",
        "Functions",
        "Inequalities",
        "Solving for X",
        "Graphing",
        "Linear Equations",
        "Systems of Equations",
      ],
      10: [
        "Basic Probability",
        "Experimental Probability",
        "Theoretical Probability",
        "Compound Events",
        "Probability Games",
        "Odds",
        "Combinations",
        "Permutations",
        "Tree Diagrams",
        "Probability Word Problems",
      ],
      11: [
        "Understanding Ratios",
        "Equivalent Ratios",
        "Rates",
        "Unit Rates",
        "Proportional Relationships",
        "Scaling",
        "Percentages",
        "Percent Change",
        "Markup and Discount",
        "Ratio Word Problems",
      ],
      12: [
        "Money Basics",
        "Saving",
        "Spending",
        "Budgeting",
        "Financial Decisions",
        "Interest",
        "Banking",
        "Credit",
        "Taxes",
        "Financial Planning",
      ],
    }

    return unitTitles[unitNumber as keyof typeof unitTitles]?.[lessonNumber - 1] || `Lesson ${lessonNumber}`
  }

  // Function to toggle lesson selection
  const toggleLessonSelection = (unitNumber: number, unitTitle: string, lessonNumber: number, lessonTitle: string) => {
    const isSelected = selectedLessons.some(
      (lesson) => lesson.unitNumber === unitNumber && lesson.lessonNumber === lessonNumber,
    )

    if (isSelected) {
      // If already selected, remove it
      setSelectedLessons(
        selectedLessons.filter((lesson) => !(lesson.unitNumber === unitNumber && lesson.lessonNumber === lessonNumber)),
      )
      setErrorMessage(null) // Clear any error message when removing
    } else {
      // Check if adding would exceed the limit
      if (selectedLessons.length >= 3) {
        setErrorMessage("You can select a maximum of 3 lessons for practice.")
        return
      }

      // Add the lesson
      setSelectedLessons([...selectedLessons, { unitNumber, unitTitle, lessonNumber, lessonTitle }])
      setErrorMessage(null) // Clear any previous error message
    }
  }

  // Function to check if a lesson is selected
  const isLessonSelected = (unitNumber: number, lessonNumber: number) => {
    return selectedLessons.some((lesson) => lesson.unitNumber === unitNumber && lesson.lessonNumber === lessonNumber)
  }

  // Function to create practice
  const createPractice = () => {
    // In a real app, this would send the data to an API
    console.log("Creating practice with:", {
      selectedLessons,
      practiceOptions,
    })

    // Close the modal and show a success message
    setShowPracticeModal(false)
    alert("Practice created successfully!")
  }

  // Function to send a new chat message
  const sendMessage = () => {
    if (!newMessage.trim()) return

    // Add user message
    setChatMessages([...chatMessages, { role: "user", content: newMessage }])

    // Clear input
    setNewMessage("")

    // Simulate AI response (in a real app, this would call an API)
    setTimeout(() => {
      setChatMessages((prev) => [
        ...prev,
        {
          role: "assistant",
          content: "I'm processing your request about \"" + newMessage + "\". I'll have a response for you shortly.",
        },
      ])
    }, 1000)
  }

  // Function to get the row number for a unit based on its index and grid columns
  const getRowNumber = (unitIndex: number) => {
    return Math.floor(unitIndex / gridColumns)
  }

  // Function to toggle row expansion
  const toggleRowExpansion = (unitIndex: number) => {
    const rowNumber = getRowNumber(unitIndex)

    if (expandedRows.includes(rowNumber)) {
      // If row is already expanded, collapse it
      setExpandedRows(expandedRows.filter((row) => row !== rowNumber))
    } else {
      // If row is not expanded, expand it
      setExpandedRows([...expandedRows, rowNumber])
    }
  }

  // Check if a unit should be expanded based on its row
  const isUnitExpanded = (unitIndex: number) => {
    const rowNumber = getRowNumber(unitIndex)
    return expandedRows.includes(rowNumber)
  }

  // Update expanded rows when grid columns change
  useEffect(() => {
    // Reset expanded rows when grid columns change
    setExpandedRows([])
  }, [gridColumns])

  const QuickAccessCard: React.FC<{ icon: React.ReactNode; title: string; count: number }> = ({
    icon,
    title,
    count,
  }) => (
    <div className="bg-white rounded-lg p-3 flex flex-col items-center justify-center hover:shadow-md transition-shadow">
      {icon}
      <h4 className="font-medium text-gray-800 mt-2">{title}</h4>
      <span className="text-sm text-gray-500">{count}</span>
    </div>
  )

  const ActivityItem: React.FC<{ icon: React.ReactNode; title: string; description: string; time: string }> = ({
    icon,
    title,
    description,
    time,
  }) => (
    <div className="flex items-center gap-3">
      <div className="rounded-full bg-gray-100 p-2">{icon}</div>
      <div>
        <h5 className="font-medium text-gray-800">{title}</h5>
        <p className="text-sm text-gray-500">{description}</p>
      </div>
      <span className="ml-auto text-sm text-gray-400">{time}</span>
    </div>
  )

  const UnitAccordion: React.FC<{
    unitNumber: number
    title: string
    index: number
    onToggle: () => void
    getLessonTitle: (lessonNumber: number) => string
    isPracticeBuilder?: boolean
  }> = ({ unitNumber, title, index, onToggle, getLessonTitle, isPracticeBuilder = false }) => {
    const expanded = isUnitExpanded(index)

    return (
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <button
          className={`flex items-center justify-between w-full ${
            gridColumns === 4 ? "px-2 py-2 text-sm" : "px-4 sm:px-6 lg:px-8 py-3 sm:py-4"
          } text-gray-500 font-light hover:bg-gray-50 transition-colors`}
          onClick={onToggle}
        >
          <div className="flex-grow flex items-center">
            <span
              className={`font-inter font-bold py-1 text-left w-full ${gridColumns === 4 ? "text-sm truncate" : ""}`}
            >
              Unit {unitNumber}: {title}
            </span>
          </div>
          <ChevronDown className={`h-5 w-5 flex-shrink-0 transition-transform ${expanded ? "rotate-180" : ""}`} />
        </button>
        {expanded && (
          <div className="p-4 border-t border-gray-200">
            <ul className="space-y-2">
              {Array.from({ length: 10 }).map((_, index) => {
                const lessonNumber = index + 1
                const lessonTitle = getLessonTitle(lessonNumber)

                return (
                  <LessonItem
                    key={index}
                    unitNumber={unitNumber}
                    lessonNumber={lessonNumber}
                    lessonTitle={lessonTitle}
                    isPracticeBuilder={isPracticeBuilder}
                    isSelected={isLessonSelected(unitNumber, lessonNumber)}
                    onToggleSelection={() => toggleLessonSelection(unitNumber, title, lessonNumber, lessonTitle)}
                  />
                )
              })}
            </ul>
          </div>
        )}
      </div>
    )
  }

  // Update the LessonItem component to make the entire row clickable
  const LessonItem: React.FC<{
    unitNumber: number
    lessonNumber: number
    lessonTitle: string
    isPracticeBuilder?: boolean
    isSelected?: boolean
    onToggleSelection?: () => void
  }> = ({
    unitNumber,
    lessonNumber,
    lessonTitle,
    isPracticeBuilder = false,
    isSelected = false,
    onToggleSelection = () => {},
  }) => {
    // Calculate continuous lesson number (1-120)
    const continuousLessonNumber = (unitNumber - 1) * 10 + lessonNumber

    return (
      <li
        className="group flex items-center justify-between px-4 py-3 rounded-lg bg-gradient-to-r from-blue-50 to-indigo-50 hover:from-blue-100 hover:to-indigo-100 transition-all duration-200 border border-blue-100 shadow-sm hover:shadow cursor-pointer"
        onClick={() =>
          isPracticeBuilder ? onToggleSelection() : console.log(`Viewing lesson ${unitNumber}.${lessonNumber}`)
        }
      >
        <div className="flex items-center gap-3">
          {isPracticeBuilder && (
            <input
              type="checkbox"
              checked={isSelected}
              onChange={onToggleSelection}
              className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
              onClick={(e) => e.stopPropagation()} // This is no longer needed but keeping for redundancy
            />
          )}
          <div className="flex items-center gap-2">
            <span className="flex items-center justify-center text-[0.8rem] font-semibold bg-gradient-to-r from-[#7DA7FF] to-[#A3C0FF] text-white px-3.5 py-1.75 rounded-xl shadow-md ring-2 ring-[#7DA7FF]/30">
              {continuousLessonNumber}
            </span>
            <span className="font-extralight text-gray-400 text-[80%]">
              <span className="font-inter font-light text-indigo-600">{lessonTitle}</span>
            </span>
          </div>
        </div>
      </li>
    )
  }

  // Add this new component for displaying quizzes and tests inside accordions
  const QuizTestAccordion: React.FC<{
    unitNumber: number
    title: string
    index: number
    onToggle: () => void
  }> = ({ unitNumber, title, index, onToggle }) => {
    const expanded = isUnitExpanded(index)

    // Generate quiz and test titles based on the unit
    const quizzes = [
      {
        title: `${title} Quiz 1`,
        type: "quiz",
        date: "May 10, 2025",
        status: "Published",
      },
      {
        title: `${title} Quiz 2`,
        type: "quiz",
        date: "May 17, 2025",
        status: "Draft",
      },
    ]

    const test = {
      title: `${title} Unit Test`,
      type: "test",
      date: "May 24, 2025",
      status: "Published",
    }

    return (
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <button
          className={`flex items-center justify-between w-full ${
            gridColumns === 4 ? "px-2 py-2 text-sm" : "px-4 sm:px-6 lg:px-8 py-3 sm:py-4"
          } text-gray-500 font-light hover:bg-gray-50 transition-colors`}
          onClick={onToggle}
        >
          <div className="flex-grow flex items-center">
            <span
              className={`font-inter font-bold py-1 text-left w-full ${gridColumns === 4 ? "text-sm truncate" : ""}`}
            >
              Unit {unitNumber}: {title}
            </span>
          </div>
          <ChevronDown className={`h-5 w-5 flex-shrink-0 transition-transform ${expanded ? "rotate-180" : ""}`} />
        </button>
        {expanded && (
          <div className="p-4 border-t border-gray-200">
            {/* Update the QuizTestAccordion component in the quizzes section to remove the buttons */}
            <div className="mb-4">
              <h4 className="text-xs font-light text-gray-400 uppercase tracking-wider mb-2">Quizzes</h4>
              <ul className="space-y-2">
                <li
                  key={index}
                  className="group flex items-center justify-between px-4 py-3 rounded-lg bg-gradient-to-r from-blue-50 to-indigo-50 hover:from-blue-100 hover:to-indigo-100 transition-all duration-200 border border-blue-100 shadow-sm hover:shadow cursor-pointer"
                  onClick={() => console.log(`Downloading quiz ${quizzes[0].title}`)}
                >
                  <div className="flex items-center gap-3">
                    <span className="flex items-center justify-center text-[0.8rem] font-semibold bg-gradient-to-r from-[#7DA7FF] to-[#A3C0FF] text-white px-3.5 py-1.75 rounded-xl shadow-md ring-2 ring-[#7DA7FF]/30">
                      Q1
                    </span>
                    <span className="font-extralight text-gray-400 text-[80%]">
                      <span className="font-inter font-light text-indigo-600">{quizzes[0].title}</span>
                    </span>
                  </div>
                </li>
                <li
                  key={index + 1}
                  className="group flex items-center justify-between px-4 py-3 rounded-lg bg-gradient-to-r from-blue-50 to-indigo-50 hover:from-blue-100 hover:to-indigo-100 transition-all duration-200 border border-blue-100 shadow-sm hover:shadow cursor-pointer"
                  onClick={() => console.log(`Downloading quiz ${quizzes[1].title}`)}
                >
                  <div className="flex items-center gap-3">
                    <span className="flex items-center justify-center text-[0.8rem] font-semibold bg-gradient-to-r from-[#7DA7FF] to-[#A3C0FF] text-white px-3.5 py-1.75 rounded-xl shadow-md ring-2 ring-[#7DA7FF]/30">
                      Q2
                    </span>
                    <span className="font-extralight text-gray-400 text-[80%]">
                      <span className="font-inter font-light text-indigo-600">{quizzes[1].title}</span>
                    </span>
                  </div>
                </li>
              </ul>
            </div>

            <div>
              <h4 className="text-xs font-light text-gray-400 uppercase tracking-wider mb-2">Test</h4>
              <ul className="space-y-2">
                <li
                  className="group flex items-center justify-between px-4 py-3 rounded-lg bg-gradient-to-r from-purple-50 to-indigo-50 hover:from-purple-100 hover:to-indigo-100 transition-all duration-200 border border-purple-100 shadow-sm hover:shadow cursor-pointer"
                  onClick={() => console.log(`Downloading test ${test.title}`)}
                >
                  <div className="flex items-center gap-3">
                    <span className="flex items-center justify-center text-[0.8rem] font-semibold bg-gradient-to-r from-[#7DA7FF] to-[#A3C0FF] text-white px-3.5 py-1.75 rounded-xl shadow-md ring-2 ring-[#7DA7FF]/30">
                      T
                    </span>
                    <span className="font-extralight text-gray-400 text-[80%]">
                      <span className="font-inter font-light text-indigo-600">{test.title}</span>
                    </span>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        )}
      </div>
    )
  }

  const PracticeModal: React.FC<{
    isOpen: boolean
    onClose: () => void
    selectedLessons: {
      unitNumber: number
      unitTitle: string
      lessonNumber: number
      lessonTitle: string
    }[]
    practiceOptions: {
      numQuestions: number
      includeWordProblems: boolean
      difficultyLevel: string
    }
    setPracticeOptions: React.Dispatch<
      React.SetStateAction<{
        numQuestions: number
        includeWordProblems: boolean
        difficultyLevel: string
      }>
    >
    onCreatePractice: () => void
  }> = ({ isOpen, onClose, selectedLessons, practiceOptions, setPracticeOptions, onCreatePractice }) => {
    if (!isOpen) return null

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50">
        <div className="backdrop-blur-md rounded-2xl w-full max-w-lg p-6 shadow-xl bg-blue-500/95 border-l-[10px] border-amber-200 overflow-hidden">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-white">Create Practice</h3>
            <button onClick={onClose} className="text-white hover:text-white/80">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div className="mb-4">
            <h4 className="font-medium text-white mb-2">Selected Lessons ({selectedLessons.length})</h4>
            <div className="max-h-32 overflow-y-auto bg-white/20 rounded-md p-2">
              {selectedLessons.map((lesson, index) => (
                <div key={index} className="text-sm text-white mb-1">
                  • Unit {lesson.unitNumber}, Lesson {lesson.lessonNumber}: {lesson.lessonTitle}
                </div>
              ))}
              {selectedLessons.length === 0 && <div className="text-sm text-white/70 italic">No lessons selected</div>}
            </div>
          </div>

          <div className="space-y-4 mb-6">
            <div>
              <label className="block text-sm font-medium text-white mb-1">Number of Questions</label>
              <div className="flex flex-wrap gap-2">
                {[5, 10, 15, 20].map((num) => (
                  <button
                    key={num}
                    onClick={() => setPracticeOptions({ ...practiceOptions, numQuestions: num })}
                    className={`px-4 py-2 text-sm rounded-md transition-colors ${
                      practiceOptions.numQuestions === num
                        ? "bg-blue-600 text-white"
                        : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                    }`}
                  >
                    {num}
                  </button>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-white mb-1">Difficulty Level</label>
              <div className="flex flex-wrap gap-2">
                {[
                  { value: "pre-requisite", label: "Pre-requisite" },
                  { value: "acquiring", label: "Acquiring" },
                  { value: "fluent", label: "Fluent" },
                  { value: "application", label: "Application" },
                ].map((level) => (
                  <button
                    key={level.value}
                    onClick={() => setPracticeOptions({ ...practiceOptions, difficultyLevel: level.value })}
                    className={`px-4 py-2 text-sm rounded-md transition-colors ${
                      practiceOptions.difficultyLevel === level.value
                        ? "bg-blue-600 text-white"
                        : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                    }`}
                  >
                    {level.label}
                  </button>
                ))}
              </div>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="includeWordProblems"
                checked={practiceOptions.includeWordProblems}
                onChange={(e) => setPracticeOptions({ ...practiceOptions, includeWordProblems: e.target.checked })}
                className="h-5 w-5 rounded border-2 border-white bg-transparent text-blue-600 focus:ring-white/50"
              />
              <label htmlFor="includeWordProblems" className="ml-2 text-sm text-white">
                Include word problems
              </label>
            </div>
          </div>

          <div className="flex justify-end gap-2">
            <button
              onClick={onClose}
              className="px-3 py-1.5 text-sm border border-white/20 rounded-md bg-white text-blue-600 hover:bg-white/90"
            >
              Cancel
            </button>
            <button
              onClick={onCreatePractice}
              className="px-3 py-1.5 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              disabled={selectedLessons.length === 0}
            >
              Create
            </button>
          </div>
        </div>
      </div>
    )
  }

  // Add a default export
  return (
    <div className="p-8 bg-[linear-gradient(150deg,#2B6DFE_0%,#2B6DFE_70%,#00F2FF_100%)] text-white h-full overflow-y-auto">
      <div
        className="backdrop-blur-sm rounded-2xl p-4 md:p-6 lg:p-8 shadow-2xl h-full overflow-y-auto w-full bg-gradient-to-br from-blue-700/30 to-cyan-500/30 border-l-[10px] border-amber-300/60 relative"
        style={{
          boxShadow: "0 10px 30px -5px rgba(0, 0, 0, 0.3), 0 0 15px 2px rgba(255, 255, 255, 0.15) inset",
        }}
      >
        {/* Home Tab */}
        {activeTab === "home" && (
          <div className="space-y-6">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 md:mb-10 gap-4 sm:gap-0">
              <div className="flex items-center">
                <div className="flex items-center gap-2">
                  <Home className="h-7 w-7" />
                  <div>
                    <h2 className="text-2xl md:text-3xl font-extrabold text-white font-montserrat">Dashboard</h2>
                    <p className="text-white/80 text-xs mt-0.5">Quick access to teaching tools</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <button
                onClick={() => setActiveTab("scope")}
                className="bg-gradient-to-br from-blue-700/40 to-blue-900/40 rounded-xl p-6 shadow-xl hover:shadow-2xl hover:scale-[1.02] transition-all duration-300 transform w-full text-left cursor-pointer"
              >
                <div className="bg-white/20 text-white rounded-lg p-3 inline-block mb-3">
                  <BookOpen className="h-8 w-8" />
                </div>
                <h4 className="text-white font-bold text-xl">Plan Your Lessons</h4>
                <p className="text-blue-100 text-sm mt-2 mb-4">
                  View our comprehensive lesson plans for short and long-term planning
                </p>
                <div className="bg-white/30 text-white text-sm py-1.5 px-3 rounded-md inline-flex items-center font-medium">
                  Explore Scope & Sequence <ChevronRight className="h-4 w-4 ml-1" />
                </div>
              </button>

              <button
                onClick={() => setActiveTab("quizzes")}
                className="bg-gradient-to-br from-purple-700/40 to-purple-900/40 rounded-xl p-6 shadow-xl hover:shadow-2xl hover:scale-[1.02] transition-all duration-300 transform w-full text-left cursor-pointer"
              >
                <div className="bg-white/20 text-white rounded-lg p-3 inline-block mb-3">
                  <FileText className="h-8 w-8" />
                </div>
                <h4 className="text-white font-bold text-xl">View Assessments</h4>
                <p className="text-purple-100 text-sm mt-2 mb-4">
                  See and print quizzes and tests expertly crafted to measure student progress
                </p>
                <div className="bg-white/30 text-white text-sm py-1.5 px-3 rounded-md inline-flex items-center font-medium">
                  Explore Quizzes & Tests <ChevronRight className="h-4 w-4 ml-1" />
                </div>
              </button>

              <button
                onClick={() => setActiveTab("practice")}
                className="bg-gradient-to-br from-indigo-600/40 to-purple-700/40 rounded-xl p-6 shadow-xl hover:shadow-2xl hover:scale-[1.02] transition-all duration-300 transform w-full text-left cursor-pointer"
              >
                <div className="bg-white/20 text-white rounded-lg p-3 inline-block mb-3">
                  <ClipboardList className="h-8 w-8" />
                </div>
                <h4 className="text-white font-bold text-xl">Build Practice Sheets</h4>
                <p className="text-indigo-100 text-sm mt-2 mb-4">
                  Generate customized practice worksheets for targeted learning
                </p>
                <div className="bg-white/30 text-white text-sm py-1.5 px-3 rounded-md inline-flex items-center font-medium">
                  Explore Practice Builder <ChevronRight className="h-4 w-4 ml-1" />
                </div>
              </button>

              <button
                onClick={() => setActiveTab("elsa")}
                className="bg-gradient-to-br from-sky-600/40 to-cyan-700/40 rounded-xl p-6 shadow-xl hover:shadow-2xl hover:scale-[1.02] transition-all duration-300 transform w-full text-left cursor-pointer"
              >
                <div className="bg-white/20 text-white rounded-lg p-3 inline-block mb-3">
                  <Flame className="h-8 w-8 text-amber-400" />
                </div>
                <h4 className="text-white font-bold text-xl">AI Teaching Assistant</h4>
                <p className="text-cyan-100 text-sm mt-2 mb-4">
                  Get instant insights into class progress and custom recommendations for student learning
                </p>
                <div className="bg-white/30 text-white text-sm py-1.5 px-3 rounded-md inline-flex items-center font-medium">
                  Chat with ELSA <ChevronRight className="h-4 w-4 ml-1" />
                </div>
              </button>
            </div>
          </div>
        )}
        {/* Scope and Sequence Tab */}
        {activeTab === "scope" && (
          <div className="space-y-6">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 md:mb-10 gap-4 sm:gap-0">
              <div className="flex items-center">
                <div className="flex items-center gap-2">
                  <BookOpen className="h-7 w-7" />
                  <div>
                    <h2 className="text-2xl md:text-3xl font-extrabold text-white font-montserrat">
                      Scope and Sequence
                    </h2>
                    <p className="text-white/80 text-xs mt-0.5">Curriculum planning and lesson organization by unit</p>
                  </div>
                </div>
              </div>
              <div className="flex flex-wrap items-center gap-2 md:gap-3 w-full sm:w-auto">
                <button
                  className="flex items-center gap-1 md:gap-2 rounded-md bg-white/10 px-2 md:px-3 py-1 md:py-1.5 text-xs md:text-sm font-medium text-white backdrop-blur-sm border border-white/20"
                  onClick={() => setGridColumns(gridColumns < 4 ? gridColumns + 1 : 1)}
                >
                  <div className="grid grid-cols-2 gap-0.5 h-4 w-4">
                    {Array.from({ length: 4 }).map((_, i) => (
                      <div key={i} className="bg-current rounded-sm" style={{ opacity: i < gridColumns ? 1 : 0.4 }} />
                    ))}
                  </div>
                  <span>
                    {gridColumns} {gridColumns === 1 ? "Column" : "Columns"}
                  </span>
                </button>
                <select className="text-xs md:text-sm border border-gray-200 rounded-md px-2 md:px-3 py-1 md:py-1.5 bg-white text-gray-800">
                  <option className="text-gray-800">Common Core (CCSS)</option>
                  <option className="text-gray-800">Ontario</option>
                  <option className="text-gray-800">Alberta</option>
                  <option className="text-gray-800">British Columbia</option>
                  <option className="text-gray-800">Australia</option>
                  <option className="text-gray-800">UK</option>
                </select>
                <select className="text-xs md:text-sm border border-gray-200 rounded-md px-2 md:px-3 py-1 md:py-1.5 bg-white text-gray-800">
                  <option className="text-gray-800">Grade 5</option>
                  <option className="text-gray-800">Grade 4</option>
                  <option className="text-gray-800">Grade 3</option>
                  <option className="text-gray-800">Grade 2</option>
                  <option className="text-gray-800">Grade 1</option>
                  <option className="text-gray-800">Kindergarten</option>
                </select>
              </div>
            </div>

            <div className="relative mb-6">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search units and lessons..."
                className="w-full pl-9 pr-4 py-2 text-sm border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            <div
              className={`grid grid-cols-1 ${
                gridColumns === 1
                  ? ""
                  : gridColumns === 2
                    ? "sm:grid-cols-1 md:grid-cols-2"
                    : gridColumns === 3
                      ? "sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
                      : "sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
              } gap-2 md:gap-4 transition-all duration-300`}
            >
              {Array.from({ length: 12 }).map((_, index) => (
                <UnitAccordion
                  key={index}
                  unitNumber={index + 1}
                  title={getUnitTitle(index + 1)}
                  index={index}
                  onToggle={() => toggleRowExpansion(index)}
                  getLessonTitle={(lessonNumber) => getLessonTitle(index + 1, lessonNumber)}
                />
              ))}
            </div>
          </div>
        )}

        {/* Quizzes and Tests Tab */}
        {activeTab === "quizzes" && (
          <div className="space-y-6">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 md:mb-10 gap-4 sm:gap-0">
              <div className="flex items-center">
                <div className="flex items-center gap-2">
                  <FileText className="h-7 w-7" />
                  <div>
                    <h2 className="text-2xl md:text-3xl font-extrabold text-white font-montserrat">
                      Quizzes and Tests
                    </h2>
                    <p className="text-white/80 text-xs mt-0.5">Assessment tools to measure student understanding</p>
                  </div>
                </div>
              </div>
              <div className="flex flex-wrap items-center gap-2 md:gap-3 w-full sm:w-auto">
                <button
                  className="flex items-center gap-1 md:gap-2 rounded-md bg-white/10 px-2 md:px-3 py-1 md:py-1.5 text-xs md:text-sm font-medium text-white backdrop-blur-sm border border-white/20"
                  onClick={() => setGridColumns(gridColumns < 4 ? gridColumns + 1 : 1)}
                >
                  <div className="grid grid-cols-2 gap-0.5 h-4 w-4">
                    {Array.from({ length: 4 }).map((_, i) => (
                      <div key={i} className="bg-current rounded-sm" style={{ opacity: i < gridColumns ? 1 : 0.4 }} />
                    ))}
                  </div>
                  <span>
                    {gridColumns} {gridColumns === 1 ? "Column" : "Columns"}
                  </span>
                </button>
                <select className="text-xs md:text-sm border border-gray-200 rounded-md px-2 md:px-3 py-1 md:py-1.5 bg-white text-gray-800">
                  <option className="text-gray-800">Common Core (CCSS)</option>
                  <option className="text-gray-800">Ontario</option>
                  <option className="text-gray-800">Alberta</option>
                  <option className="text-gray-800">British Columbia</option>
                  <option className="text-gray-800">Australia</option>
                  <option className="text-gray-800">UK</option>
                </select>
                <select className="text-xs md:text-sm border border-gray-200 rounded-md px-2 md:px-3 py-1 md:py-1.5 bg-white text-gray-800">
                  <option className="text-gray-800">Grade 5</option>
                  <option className="text-gray-800">Grade 4</option>
                  <option className="text-gray-800">Grade 3</option>
                  <option className="text-gray-800">Grade 2</option>
                  <option className="text-gray-800">Grade 1</option>
                  <option className="text-gray-800">Kindergarten</option>
                </select>
              </div>
            </div>

            <div className="relative mb-6">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search quizzes and tests..."
                className="w-full pl-9 pr-4 py-2 text-sm border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            <div
              className={`grid grid-cols-1 ${
                gridColumns === 1
                  ? ""
                  : gridColumns === 2
                    ? "sm:grid-cols-1 md:grid-cols-2"
                    : gridColumns === 3
                      ? "sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
                      : "sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
              } gap-2 md:gap-4 transition-all duration-300`}
            >
              {Array.from({ length: 12 }).map((_, index) => (
                <QuizTestAccordion
                  key={index}
                  unitNumber={index + 1}
                  title={getUnitTitle(index + 1)}
                  index={index}
                  onToggle={() => toggleRowExpansion(index)}
                />
              ))}
            </div>
          </div>
        )}

        {/* Practice Builder Tab */}
        {activeTab === "practice" && (
          <div className="space-y-6">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 md:mb-10 gap-4 sm:gap-0">
              <div className="flex items-center">
                <div className="flex items-center gap-2">
                  <ClipboardList className="h-7 w-7" />
                  <div>
                    <h2 className="text-2xl md:text-3xl font-extrabold text-white font-montserrat">Practice Builder</h2>
                    <p className="text-white/80 text-xs mt-0.5">
                      Create customized practice worksheets for your students
                    </p>
                  </div>
                </div>
              </div>
              <div className="flex flex-wrap items-center gap-2 md:gap-3 w-full sm:w-auto">
                <button
                  onClick={() => setShowPracticeModal(true)}
                  disabled={selectedLessons.length === 0}
                  className="flex items-center gap-1 md:gap-2 rounded-md bg-blue-600 px-2 md:px-3 py-1 md:py-1.5 text-xs md:text-sm font-medium text-white disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Plus className="h-4 w-4" />
                  <span>Create Practice {selectedLessons.length > 0 ? `(${selectedLessons.length})` : ""}</span>
                </button>
                <button
                  className="flex items-center gap-1 md:gap-2 rounded-md bg-white/10 px-2 md:px-3 py-1 md:py-1.5 text-xs md:text-sm font-medium text-white backdrop-blur-sm border border-white/20"
                  onClick={() => setGridColumns(gridColumns < 4 ? gridColumns + 1 : 1)}
                >
                  <div className="grid grid-cols-2 gap-0.5 h-4 w-4">
                    {Array.from({ length: 4 }).map((_, i) => (
                      <div key={i} className="bg-current rounded-sm" style={{ opacity: i < gridColumns ? 1 : 0.4 }} />
                    ))}
                  </div>
                  <span>
                    {gridColumns} {gridColumns === 1 ? "Column" : "Columns"}
                  </span>
                </button>
                <select className="text-xs md:text-sm border border-gray-200 rounded-md px-2 md:px-3 py-1 md:py-1.5 bg-white text-gray-800">
                  <option className="text-gray-800">Common Core (CCSS)</option>
                  <option className="text-gray-800">Ontario</option>
                  <option className="text-gray-800">Alberta</option>
                  <option className="text-gray-800">British Columbia</option>
                  <option className="text-gray-800">Australia</option>
                  <option className="text-gray-800">UK</option>
                </select>
                <select className="text-xs md:text-sm border border-gray-200 rounded-md px-2 md:px-3 py-1 md:py-1.5 bg-white text-gray-800">
                  <option className="text-gray-800">Grade 5</option>
                  <option className="text-gray-800">Grade 4</option>
                  <option className="text-gray-800">Grade 3</option>
                  <option className="text-gray-800">Grade 2</option>
                  <option className="text-gray-800">Grade 1</option>
                  <option className="text-gray-800">Kindergarten</option>
                </select>
              </div>
            </div>

            {errorMessage && (
              <div className="bg-blue-50 border border-blue-200 text-blue-700 p-4 mb-4 rounded-lg shadow-sm flex items-center gap-3 animate-fadeIn">
                <div className="p-2 bg-blue-100 rounded-full">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 text-blue-500"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm-2-6a2 2 0 114 0 2 2 0 01-4 0zm5-6a1 1 0 10-2 0v1a1 1 0 102 0V6zm-8 1a1 1 0 112 0v1a1 1 0 11-2 0V7z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <p className="font-light text-sm">Maximum of 3 lessons per practice sheet</p>
              </div>
            )}

            <div className="relative mb-6">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search units and lessons..."
                className="w-full pl-9 pr-4 py-2 text-sm border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            <div
              className={`grid grid-cols-1 ${
                gridColumns === 1
                  ? ""
                  : gridColumns === 2
                    ? "sm:grid-cols-1 md:grid-cols-2"
                    : gridColumns === 3
                      ? "sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
                      : "sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
              } gap-2 md:gap-4 transition-all duration-300`}
            >
              {Array.from({ length: 12 }).map((_, index) => (
                <UnitAccordion
                  key={index}
                  unitNumber={index + 1}
                  title={getUnitTitle(index + 1)}
                  index={index}
                  onToggle={() => toggleRowExpansion(index)}
                  getLessonTitle={(lessonNumber) => getLessonTitle(index + 1, lessonNumber)}
                  isPracticeBuilder={true}
                />
              ))}
            </div>

            {/* Practice Modal */}
            <PracticeModal
              isOpen={showPracticeModal}
              onClose={() => setShowPracticeModal(false)}
              selectedLessons={selectedLessons}
              practiceOptions={practiceOptions}
              setPracticeOptions={setPracticeOptions}
              onCreatePractice={createPractice}
            />
          </div>
        )}

        {/* Student Achievement Tab */}
        {activeTab === "achievement" && (
          <div className="space-y-6 relative">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 md:mb-10 gap-4 sm:gap-0">
              <div className="flex items-center">
                <div className="flex items-center gap-2">
                  <BarChart3 className="h-7 w-7" />
                  <div>
                    <h2 className="text-2xl md:text-3xl font-extrabold text-white font-montserrat">
                      Student Achievement
                    </h2>
                    <p className="text-white/80 text-xs mt-0.5">Track progress and identify learning opportunities</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Content with subtle blur */}
            <div className="relative">
              <div className="filter blur-[2px] opacity-90 pointer-events-none">
                <div className="flex flex-wrap items-center gap-2 md:gap-3 w-full sm:w-auto mb-6">
                  <select className="text-xs md:text-sm border border-gray-200 rounded-md px-2 md:px-3 py-1 md:py-1.5 bg-white text-gray-800">
                    <option className="text-gray-800">Class 5A</option>
                    <option className="text-gray-800">Class 4B</option>
                    <option className="text-gray-800">Class 3C</option>
                  </select>
                  <select className="text-xs md:text-sm border border-gray-200 rounded-md px-2 md:px-3 py-1 md:py-1.5 bg-white text-gray-800">
                    <option className="text-gray-800">All Students</option>
                    <option className="text-gray-800">At Risk</option>
                    <option className="text-gray-800">On Track</option>
                    <option className="text-gray-800">Advanced</option>
                  </select>
                </div>

                <div className="relative mb-6">
                  <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search students..."
                    className="w-full pl-9 pr-4 py-2 text-sm border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>

                <div className="w-full">
                  <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th
                            scope="col"
                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                          >
                            Student
                          </th>
                          <th
                            scope="col"
                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                          >
                            Grade
                          </th>
                          <th
                            scope="col"
                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                          >
                            Most Recent
                          </th>
                          <th
                            scope="col"
                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                          >
                            Most Consistent
                          </th>
                          <th
                            scope="col"
                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                          >
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-medium">
                                JD
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900">John Doe</div>
                                <div className="text-sm text-gray-500">Student ID: 12345</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900 font-medium">A (92%)</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center justify-center w-8 h-8 rounded-full bg-green-500 text-white font-bold">
                              4
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center justify-center w-8 h-8 rounded-full bg-green-500 text-white font-bold">
                              4
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="#" className="text-blue-600 hover:text-blue-900">
                              View
                            </a>
                          </td>
                        </tr>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center text-purple-600 font-medium">
                                JS
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900">Jane Smith</div>
                                <div className="text-sm text-gray-500">Student ID: 12346</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900 font-medium">B (85%)</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-500 text-white font-bold">
                              3
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center justify-center w-8 h-8 rounded-full bg-green-500 text-white font-bold">
                              4
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="#" className="text-blue-600 hover:text-blue-900">
                              View
                            </a>
                          </td>
                        </tr>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10 rounded-full bg-pink-100 flex items-center justify-center text-pink-600 font-medium">
                                AW
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900">Alice Williams</div>
                                <div className="text-sm text-gray-500">Student ID: 12348</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900 font-medium">C+ (78%)</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center justify-center w-8 h-8 rounded-full bg-red-500 text-white font-bold">
                              2
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-500 text-white font-bold">
                              3
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="#" className="text-blue-600 hover:text-blue-900">
                              View
                            </a>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>

              {/* Overlay to indicate content is not interactive - taller with less blur */}
              <div className="absolute inset-x-0 top-[40%] bottom-0 bg-gradient-to-t from-white/65 via-white/60 to-transparent backdrop-blur-[3px] z-10 flex flex-col items-center justify-center p-8 text-center">
                <div className="bg-gray-800/85 px-8 py-8 rounded-2xl border border-gray-700 shadow-lg max-w-md">
                  <div className="animate-pulse mb-4">
                    <div className="h-20 w-20 rounded-full bg-amber-400 flex items-center justify-center text-white font-bold shadow-lg shadow-amber-400/30 mx-auto">
                      <BarChart3 className="h-12 w-12" />
                    </div>
                  </div>

                  <h3 className="text-3xl font-black text-white mb-4 tracking-tight font-montserrat">
                    Achievement Data
                  </h3>
                  <p className="text-amber-300 text-lg mb-4 font-montserrat font-black">Student Progress Analytics</p>

                  <h4 className="text-xl font-bold text-white mb-4">Performance Tracking</h4>

                  <div className="bg-gradient-to-r from-amber-400 to-amber-500 text-blue-900 font-semibold py-2 px-6 rounded-full inline-flex items-center gap-2 shadow-lg shadow-amber-400/20 hover:shadow-xl hover:scale-105 transition-all duration-300">
                    <span>Summer 2025</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* ELSA Tab - Facebook Messenger Style */}
        {activeTab === "elsa" && (
          <div className="space-y-6 relative">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 md:mb-10 gap-4 sm:gap-0">
              <div className="flex items-center">
                <div className="flex items-center gap-2">
                  <Flame className="h-7 w-7 text-amber-400" />
                  <div>
                    <h2 className="text-2xl md:text-3xl font-extrabold text-white font-montserrat">ELSA</h2>
                    <p className="text-white/80 text-xs mt-0.5">EMBRS LEARNING Strategic Assistant</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md overflow-hidden flex flex-col h-[calc(100vh-240px)] relative">
              {/* Blurred background - entire interface */}
              <div className="absolute inset-0 filter blur-[2px] opacity-90 pointer-events-none">
                {/* Messenger Header */}
                <div className="bg-blue-600 text-white p-4 flex items-center gap-3 border-b border-blue-700">
                  <div className="h-10 w-10 rounded-full bg-amber-400 flex items-center justify-center text-white font-bold">
                    <Flame className="h-6 w-6" />
                  </div>
                  <div>
                    <h3 className="font-semibold">ELSA</h3>
                  </div>
                </div>

                {/* Messenger Chat Area */}
                <div className="flex-1 p-4 overflow-y-auto bg-gray-100">
                  <div className="space-y-4">
                    {chatMessages.map((message, index) => (
                      <div
                        key={index}
                        className={`flex ${message.role === "user" ? "justify-end" : "justify-start"} items-end gap-2`}
                      >
                        {message.role === "assistant" && (
                          <div className="h-8 w-8 rounded-full bg-amber-400 flex items-center justify-center text-white">
                            <Flame className="h-5 w-5" />
                          </div>
                        )}
                        <div
                          className={`rounded-2xl p-3 max-w-[80%] ${
                            message.role === "user"
                              ? "bg-blue-500 text-white rounded-br-none"
                              : "bg-gray-200 text-gray-800 rounded-bl-none"
                          }`}
                        >
                          {message.content}
                        </div>
                        {message.role === "user" && (
                          <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-medium">
                            TS
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Messenger Input Area */}
                <div className="p-4 border-t border-gray-200 bg-white">
                  <div className="flex items-center gap-2">
                    <input
                      type="text"
                      placeholder="Type a message..."
                      className="w-full border border-gray-300 rounded-full px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          sendMessage()
                        }
                      }}
                    />
                    <button
                      onClick={sendMessage}
                      className="bg-blue-500 text-white p-2 rounded-full hover:bg-blue-600 transition-colors"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-6 w-6"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                        />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>

              {/* Sleek advertisement overlay */}
              <div className="absolute inset-0 bg-white/20 z-10 flex flex-col items-center justify-center p-8 text-center backdrop-blur-sm rounded-3xl">
                <div className="bg-gray-800/85 px-8 py-8 rounded-3xl border border-gray-700 shadow-lg max-w-md">
                  <div className="animate-pulse mb-4">
                    <div className="h-20 w-20 rounded-full bg-amber-400 flex items-center justify-center text-white font-bold shadow-lg shadow-amber-400/30 mx-auto">
                      <Flame className="h-12 w-12" />
                    </div>
                  </div>

                  <h3 className="text-3xl font-black text-white mb-4 tracking-tight font-montserrat">ELSA</h3>
                  <p className="text-amber-300 text-lg mb-4 font-montserrat font-black">
                    EMBRS LEARNING Strategic Assistant
                  </p>

                  <h4 className="text-xl font-bold text-white mb-4">AI Teaching Assistant</h4>

                  <div className="bg-gradient-to-r from-amber-400 to-amber-500 text-blue-900 font-semibold py-2 px-6 rounded-full inline-flex items-center gap-2 shadow-lg shadow-amber-400/20 hover:shadow-xl hover:scale-105 transition-all duration-300">
                    <span>Summer 2025</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

// Export both as named export and default export
export { TabContent }
export default TabContent
