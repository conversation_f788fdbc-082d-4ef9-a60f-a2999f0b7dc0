/**
 * Utility functions for handling lesson data
 */

/**
 * Get the lesson number from localStorage
 * This ensures we always use the correct lesson number for document IDs
 */
export function getLessonNumberFromStorage(): string {
  try {
    // First try to get from lesson_lesson_number (primary source)
    const lessonNumber = localStorage.getItem('lesson_lesson_number');
    if (lessonNumber) {
      return lessonNumber;
    }

    // Fallback to selectedLesson if lesson_lesson_number is not available
    const selectedLesson = localStorage.getItem('selectedLesson');
    if (selectedLesson) {
      // Extract lesson number from string like "Lesson 2: Adding 2"
      const lessonNumberMatch = selectedLesson.match(/Lesson (\d+)/);
      if (lessonNumberMatch && lessonNumberMatch[1]) {
        return lessonNumberMatch[1];
      }
    }

    // Default fallback
    return "1";
  } catch (error) {
    console.error('Error getting lesson number from localStorage:', error);
    return "1";
  }
}

/**
 * Get the unit number from localStorage
 */
export function getUnitNumberFromStorage(): string {
  try {
    // First try to get from lesson_unit_number (primary source)
    const unitNumber = localStorage.getItem('lesson_unit_number');
    if (unitNumber) {
      return unitNumber;
    }

    // Fallback to selectedUnit if lesson_unit_number is not available
    const selectedUnit = localStorage.getItem('selectedUnit');
    if (selectedUnit) {
      // Extract unit number from string like "Unit 3: Addition"
      const unitNumberMatch = selectedUnit.match(/Unit (\d+)/);
      if (unitNumberMatch && unitNumberMatch[1]) {
        return unitNumberMatch[1];
      }
    }

    // Default fallback
    return "1";
  } catch (error) {
    console.error('Error getting unit number from localStorage:', error);
    return "1";
  }
}

/**
 * Get the grade level from localStorage
 */
export function getGradeLevelFromStorage(): string {
  try {
    // First try to get from lesson_grade_level (primary source)
    const gradeLevel = localStorage.getItem('lesson_grade_level');
    if (gradeLevel) {
      return gradeLevel;
    }

    // Fallback to selectedGrade if lesson_grade_level is not available
    const selectedGrade = localStorage.getItem('selectedGrade');
    if (selectedGrade) {
      return selectedGrade;
    }

    // Default fallback
    return "Grade 1";
  } catch (error) {
    console.error('Error getting grade level from localStorage:', error);
    return "Grade 1";
  }
}

/**
 * Get the document ID for the current lesson
 * Format: {unit_number}-{lesson_number}-{grade_level}
 */
export function getDocumentIdFromStorage(): string {
  const unitNumber = getUnitNumberFromStorage();
  const lessonNumber = getLessonNumberFromStorage();
  const gradeLevel = getGradeLevelFromStorage();
  
  return `${unitNumber}-${lessonNumber}-${gradeLevel}`;
}
