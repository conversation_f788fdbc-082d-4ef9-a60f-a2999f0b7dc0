/**
 * Utility function to save HTML content and force a refresh
 * This is used when we need to ensure the page refreshes after saving HTML content
 */

interface SaveHtmlResult {
  success: boolean;
  message: string;
}

export async function saveHtmlAndRefresh(
  slideNumber: number,
  unitNumber: string,
  lessonNumber: string,
  gradeLevel: string,
  lang?: string,
  html?: string,
  commonCore?: string
): Promise<SaveHtmlResult> {
  try {
    if (!html) {
      return {
        success: false,
        message: 'No HTML content provided'
      };
    }

    console.log('Saving HTML content for slide with force refresh:', {
      unitNumber,
      lessonNumber,
      gradeLevel,
      slideNumber,
      htmlLength: html?.length
    });

    // Send the update to the server
    const response = await fetch('/api/update-slide-html', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        slide_number: slideNumber,
        html,
        unit_number: unitNumber,
        lesson_number: lessonNumber,
        grade_level: gradeLevel,
        lang,
        common_core: commonCore
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Error response:', errorData);
      throw new Error(`Failed to update HTML content: ${errorData.message || response.statusText}`);
    }

    const responseData = await response.json();
    console.log('Update response:', responseData);

    // Force a page refresh after a short delay
    setTimeout(() => {
      // Construct the current URL with parameters
      const params = new URLSearchParams(window.location.search);
      
      // Ensure we have all the parameters
      if (unitNumber) params.set('unit', unitNumber);
      if (lessonNumber) params.set('lesson', lessonNumber);
      if (gradeLevel) params.set('grade', gradeLevel);
      if (slideNumber) params.set('slide', slideNumber.toString());
      if (lang) params.set('lang', lang);
      if (commonCore) params.set('curriculum', commonCore);
      
      // Construct the new URL
      const newUrl = `${window.location.pathname}?${params.toString()}`;
      
      // Reload the page with the new URL
      window.location.href = newUrl;
    }, 500);

    return {
      success: true,
      message: 'HTML content updated successfully. Page will refresh.'
    };
  } catch (error) {
    console.error('Error updating HTML content:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}
