/**
 * Utility functions for admin-related functionality
 */

/**
 * Check if the current user is a super admin
 * This checks the user's session role and falls back to localStorage
 */
export function isSuperAdmin(session?: any): boolean {
  try {
    // First check if session is provided and has admin role
    if (session?.user?.role === 'admin') {
      return true;
    }

    // Fallback to localStorage check (for backward compatibility)
    if (typeof window !== 'undefined') {
      const isSuperAdminFlag = localStorage.getItem('isSuperAdmin');
      return isSuperAdminFlag === 'true';
    }

    return false;
  } catch (error) {
    console.error('Error checking super admin status:', error);
    return false;
  }
}
