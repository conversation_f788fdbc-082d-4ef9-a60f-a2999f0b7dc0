# Translation Upload Fix

## Issues Fixed

### 1. DocumentId Format Issue
**Problem**: Spanish documents were creating documentId with translated grade level (`"6-49-Grado 1"` instead of `"6-49-Grade 1"`)

**Solution**: Always use English format for documentId to ensure consistency:
```javascript
const englishGradeLevel = lang === 'esp' ? grade_level.replace('Grado', 'Grade') : grade_level;
const documentId = `${unit_number}-${lesson_number}-${englishGradeLevel}`;
```

### 2. HTML Content Not Copying
**Problem**: HTML content from English version wasn't being copied to Spanish slides

**Solution**: 
- Copy HTML before saving/updating the document
- Update the document again after HTML copying
- Add better logging for debugging

## Steps to Fix Current Database

### 1. Remove Incorrect Spanish Document
```javascript
// In MongoDB, delete the document with wrong documentId
db.jsondocuments.deleteOne({
  documentId: "6-49-Grado 1",
  lang: "esp"
})
```

### 2. Re-upload Spanish Translation
- Upload `Grade_1_Lesson_49_DUMMY_Translated_ES.json` again
- Should now create document with `documentId: "6-49-Grade 1"`
- Should copy HTML content from English version

### 3. Verify Results
After re-upload, check:
- Spanish document has `documentId: "6-49-Grade 1"` (same as English)
- Spanish slides have HTML content in `html_css_description_of_image` fields
- Language button becomes enabled for Grade 1, Unit 6, Lesson 49

## Expected Database State After Fix

### English Document
```javascript
{
  _id: "68404954f99c3cac1487898d",
  documentId: "6-49-Grade 1",
  lang: "en",
  grade_level: "Grade 1",
  content: {
    "Slide 1: Quick_Review": {
      html_css_description_of_image: "<!DOCTYPE html>..." // Has HTML
    }
  }
}
```

### Spanish Document (Fixed)
```javascript
{
  documentId: "6-49-Grade 1", // Same as English
  lang: "esp",
  grade_level: "Grado 1", // Can stay in Spanish for display
  content: {
    "Slide 1: Quick_Review": {
      slide_pedagogical_name: "Repaso Rápido",
      html_css_description_of_image: "<!DOCTYPE html>..." // Copied from English
    }
  }
}
```

## Testing Checklist

After re-upload:
- [ ] Spanish document has correct documentId format
- [ ] HTML content is copied to Spanish slides
- [ ] Language button is enabled for the lesson
- [ ] Language switching works correctly
- [ ] URL parameters work (?lang=esp)
- [ ] Page refresh maintains language selection

## Console Messages to Look For

During upload:
```
Looking for English version of document: 6-49-Grade 1
Found English version, copying HTML content...
Copied HTML content from English to Spanish for Slide 1: Quick_Review
Copied HTML content for X slides from English to Spanish version of 6-49-Grade 1
```

During language check:
```
Translation availability: true
Language button enabled for Grade 1, Unit 6, Lesson 49
```
