<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Math Elements with Clocks</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #FFFFFF;
            font-family: 'Montserrat', sans-serif;
            font-weight: 900;
            height: 100%;
            box-sizing: border-box;
        }

        .grid-container {
            display: grid;
            grid-template-columns: repeat(13, 1fr);
            grid-template-rows: repeat(8, 1fr);
            gap: min(16px, 2vw);
            width: 100%;
            max-width: 100%;
            height: calc(100% - 40px);
            max-height: calc(100% - 40px);
            background: transparent;
            padding: 1rem;
            box-sizing: border-box;
            overflow: hidden;
        }

        .math-element {
            background: white;
            border: 2px solid #000;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #000;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .math-element:hover {
            background: rgba(0, 0, 0, 0.1);
            transform: scale(1.05);
        }

        .clock {
            width: 120px;
            height: 120px;
            border: 3px solid #333;
            border-radius: 50%;
            position: relative;
            background: white;
            margin: 10px;
        }

        .clock-face {
            width: 100%;
            height: 100%;
            position: relative;
        }

        .clock-number {
            position: absolute;
            font-size: 14px;
            font-weight: 900;
            color: #000;
        }

        .clock-hand {
            position: absolute;
            background: #333;
            transform-origin: bottom center;
            border-radius: 2px;
        }

        .hour-hand {
            width: 3px;
            height: 35px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -100%);
        }

        .minute-hand {
            width: 2px;
            height: 45px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -100%);
        }

        .clock-center {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 6px;
            height: 6px;
            background: #333;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            z-index: 10;
        }

        .interactive-area {
            grid-column: span 6;
            grid-row: span 4;
            background: rgba(91, 184, 92, 0.1);
            border: 2px dashed #5CB85C;
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 20px;
        }

        .number-display {
            font-size: 48px;
            color: #000;
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 2px solid #000;
            min-width: 100px;
            text-align: center;
        }

        .click-instruction {
            font-size: clamp(14px, 3vw, 18px);
            color: #333;
            text-align: center;
            margin: 10px 0;
        }

        /* Responsive media queries to prevent overlap */
        @media (max-width: 768px) {
            .grid-container {
                grid-template-columns: repeat(8, 1fr);
                grid-template-rows: repeat(12, 1fr);
                gap: min(12px, 1.5vw);
                height: auto;
                min-height: 600px;
            }

            /* Reposition elements for mobile */
            .math-element[style*="grid-column: 1 / 4"] {
                grid-column: 1 / 5 !important;
                grid-row: 1 / 4 !important;
            }

            .interactive-area[style*="grid-column: 5 / 11"] {
                grid-column: 1 / 9 !important;
                grid-row: 4 / 8 !important;
            }

            .math-element[style*="grid-column: 12 / 14"] {
                grid-column: 5 / 9 !important;
                grid-row: 1 / 4 !important;
            }

            .math-element[style*="grid-column: 1 / 7"] {
                grid-column: 1 / 9 !important;
                grid-row: 8 / 11 !important;
            }

            .math-element[style*="grid-column: 8 / 14"] {
                grid-column: 1 / 9 !important;
                grid-row: 11 / 13 !important;
            }

            .clock {
                width: min(80px, 15vw);
                height: min(80px, 15vw);
            }

            .number-display {
                font-size: clamp(24px, 6vw, 36px);
                padding: 10px;
            }
        }

        @media (max-width: 480px) {
            .grid-container {
                grid-template-columns: repeat(4, 1fr);
                grid-template-rows: repeat(16, 1fr);
                gap: min(8px, 1vw);
                padding: 0.5rem;
            }

            /* Stack elements vertically on very small screens */
            .math-element[style*="grid-column: 1 / 4"],
            .math-element[style*="grid-column: 12 / 14"] {
                grid-column: 1 / 5 !important;
            }

            .interactive-area[style*="grid-column: 5 / 11"] {
                grid-column: 1 / 5 !important;
                grid-row: 5 / 9 !important;
            }

            .math-element[style*="grid-column: 1 / 7"] {
                grid-column: 1 / 5 !important;
                grid-row: 9 / 12 !important;
            }

            .math-element[style*="grid-column: 8 / 14"] {
                grid-column: 1 / 5 !important;
                grid-row: 12 / 15 !important;
            }
        }
    </style>
</head>
<body>
    <div class="grid-container">
        <!-- Clock Examples -->
        <div class="math-element" style="grid-column: 1 / 4; grid-row: 1 / 3;">
            <div class="clock" id="clock1">
                <div class="clock-face">
                    <div class="clock-number" style="top: 5px; left: 50%; transform: translateX(-50%);">12</div>
                    <div class="clock-number" style="top: 50%; right: 5px; transform: translateY(-50%);">3</div>
                    <div class="clock-number" style="bottom: 5px; left: 50%; transform: translateX(-50%);">6</div>
                    <div class="clock-number" style="top: 50%; left: 5px; transform: translateY(-50%);">9</div>
                    <div class="hour-hand" id="hour1"></div>
                    <div class="minute-hand" id="minute1"></div>
                    <div class="clock-center"></div>
                </div>
            </div>
        </div>

        <!-- Interactive Number Area -->
        <div class="interactive-area" style="grid-column: 5 / 11; grid-row: 1 / 5;">
            <div class="click-instruction">Click the elements to interact!</div>
            <div class="number-display" id="numberDisplay">0</div>
            <div style="display: flex; gap: 10px;">
                <button class="math-element" onclick="incrementNumber()" style="padding: 10px 20px;">+1</button>
                <button class="math-element" onclick="decrementNumber()" style="padding: 10px 20px;">-1</button>
                <button class="math-element" onclick="resetNumber()" style="padding: 10px 20px;">Reset</button>
            </div>
        </div>

        <!-- Second Clock -->
        <div class="math-element" style="grid-column: 12 / 14; grid-row: 1 / 3;">
            <div class="clock" id="clock2">
                <div class="clock-face">
                    <div class="clock-number" style="top: 5px; left: 50%; transform: translateX(-50%);">12</div>
                    <div class="clock-number" style="top: 50%; right: 5px; transform: translateY(-50%);">3</div>
                    <div class="clock-number" style="bottom: 5px; left: 50%; transform: translateX(-50%);">6</div>
                    <div class="clock-number" style="top: 50%; left: 5px; transform: translateY(-50%);">9</div>
                    <div class="hour-hand" id="hour2"></div>
                    <div class="minute-hand" id="minute2"></div>
                    <div class="clock-center"></div>
                </div>
            </div>
        </div>

        <!-- Math Problem Area -->
        <div class="math-element" style="grid-column: 1 / 7; grid-row: 4 / 7;">
            <div style="text-align: center;">
                <div style="font-size: 32px; margin-bottom: 20px;">Time Difference</div>
                <div id="timeDifference" style="font-size: 24px; color: #5CB85C;">Click clocks to calculate!</div>
            </div>
        </div>

        <!-- Control Buttons -->
        <div class="math-element" style="grid-column: 8 / 14; grid-row: 6 / 8;">
            <div style="display: flex; flex-wrap: wrap; gap: 10px; justify-content: center;">
                <button onclick="setClock1Time()" class="math-element" style="padding: 8px 16px;">Set Clock 1</button>
                <button onclick="setClock2Time()" class="math-element" style="padding: 8px 16px;">Set Clock 2</button>
                <button onclick="calculateDifference()" class="math-element" style="padding: 8px 16px;">Calculate</button>
            </div>
        </div>
    </div>

    <script>
        let currentNumber = 0;
        let clock1Time = { hours: 3, minutes: 0 };
        let clock2Time = { hours: 7, minutes: 30 };

        // Initialize clocks
        function initClocks() {
            updateClock('clock1', clock1Time.hours, clock1Time.minutes);
            updateClock('clock2', clock2Time.hours, clock2Time.minutes);
        }

        function updateClock(clockId, hours, minutes) {
            const hourHand = document.querySelector(`#${clockId} .hour-hand`);
            const minuteHand = document.querySelector(`#${clockId} .minute-hand`);

            const hourAngle = (hours % 12) * 30 + (minutes * 0.5);
            const minuteAngle = minutes * 6;

            hourHand.style.transform = `translate(-50%, -100%) rotate(${hourAngle}deg)`;
            minuteHand.style.transform = `translate(-50%, -100%) rotate(${minuteAngle}deg)`;
        }

        function incrementNumber() {
            currentNumber++;
            document.getElementById('numberDisplay').textContent = currentNumber;
        }

        function decrementNumber() {
            currentNumber--;
            document.getElementById('numberDisplay').textContent = currentNumber;
        }

        function resetNumber() {
            currentNumber = 0;
            document.getElementById('numberDisplay').textContent = currentNumber;
        }

        function setClock1Time() {
            clock1Time.hours = Math.floor(Math.random() * 12) + 1;
            clock1Time.minutes = Math.floor(Math.random() * 12) * 5;
            updateClock('clock1', clock1Time.hours, clock1Time.minutes);
        }

        function setClock2Time() {
            clock2Time.hours = Math.floor(Math.random() * 12) + 1;
            clock2Time.minutes = Math.floor(Math.random() * 12) * 5;
            updateClock('clock2', clock2Time.hours, clock2Time.minutes);
        }

        function calculateDifference() {
            const time1 = clock1Time.hours * 60 + clock1Time.minutes;
            const time2 = clock2Time.hours * 60 + clock2Time.minutes;
            const diff = Math.abs(time2 - time1);
            const diffHours = Math.floor(diff / 60);
            const diffMinutes = diff % 60;

            document.getElementById('timeDifference').textContent =
                `Difference: ${diffHours}h ${diffMinutes}m`;
        }

        // Initialize on load
        window.onload = function() {
            initClocks();
            calculateDifference();
        };

        // Add click handlers for clocks
        document.getElementById('clock1').addEventListener('click', setClock1Time);
        document.getElementById('clock2').addEventListener('click', setClock2Time);
    </script>
</body>
</html>
