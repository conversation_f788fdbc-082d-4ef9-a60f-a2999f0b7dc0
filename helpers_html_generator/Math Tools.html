<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta http-equiv="Content-Style-Type" content="text/css">
  <title></title>
  <meta name="Generator" content="Cocoa HTML Writer">
  <meta name="CocoaVersion" content="2575.6">
  <style type="text/css">
    p.p1 {margin: 0.0px 0.0px 0.0px 0.0px; font: 12.0px Helvetica}
    p.p2 {margin: 0.0px 0.0px 0.0px 0.0px; font: 12.0px Helvetica; min-height: 14.0px}
  </style>
</head>
<body>
<p class="p1">&lt;!DOCTYPE html&gt;</p>
<p class="p1">&lt;html lang="en"&gt;</p>
<p class="p1">&lt;head&gt;</p>
<p class="p1"><span class="Apple-converted-space">    </span>&lt;meta charset="UTF-8"&gt;</p>
<p class="p1"><span class="Apple-converted-space">    </span>&lt;meta name="viewport" content="width=device-width, initial-scale=1.0"&gt;</p>
<p class="p1"><span class="Apple-converted-space">    </span>&lt;title&gt;Movable Canvas - Consistent Factor Tree Color&lt;/title&gt;</p>
<p class="p1"><span class="Apple-converted-space">    </span>&lt;style&gt;</p>
<p class="p1"><span class="Apple-converted-space">        </span>body {</p>
<p class="p1"><span class="Apple-converted-space">            </span>margin: 0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>overflow: hidden;</p>
<p class="p1"><span class="Apple-converted-space">            </span>background-color: #f0f0f0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>display: flex;</p>
<p class="p1"><span class="Apple-converted-space">            </span>justify-content: center;</p>
<p class="p1"><span class="Apple-converted-space">            </span>align-items: center;</p>
<p class="p1"><span class="Apple-converted-space">            </span>height: 100vh;</p>
<p class="p1"><span class="Apple-converted-space">            </span>font-family: 'Inter', sans-serif;</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p1"><span class="Apple-converted-space">        </span>#myCubeCanvas {</p>
<p class="p1"><span class="Apple-converted-space">            </span>background-color: #ffffff;</p>
<p class="p1"><span class="Apple-converted-space">            </span>cursor: default;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>display: block;</p>
<p class="p1"><span class="Apple-converted-space">            </span>width: 100vw;</p>
<p class="p1"><span class="Apple-converted-space">            </span>height: 100vh;</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p1"><span class="Apple-converted-space">    </span>&lt;/style&gt;</p>
<p class="p1">&lt;/head&gt;</p>
<p class="p1">&lt;body&gt;</p>
<p class="p1"><span class="Apple-converted-space">    </span>&lt;canvas id="myCubeCanvas"&gt;&lt;/canvas&gt;</p>
<p class="p1"><span class="Apple-converted-space">    </span>&lt;script&gt;</p>
<p class="p1"><span class="Apple-converted-space">        </span>const canvas = document.getElementById('myCubeCanvas');</p>
<p class="p1"><span class="Apple-converted-space">        </span>const ctx = canvas.getContext('2d');</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>// --- Base size for a single unit cube's side ---</p>
<p class="p1"><span class="Apple-converted-space">        </span>let baseSize = 9;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>let canvasBgColor = '#f0f0f0';</p>
<p class="p1"><span class="Apple-converted-space">        </span>const baseTenTransparency = 0.75;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const geometricShapeTransparency = 0.75;</p>
<p class="p1"><span class="Apple-converted-space">        </span>const cuisenaireRodTransparency = 0.85;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const threeDShapeTransparency = 0.3;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const netTransparency = 0.70;</p>
<p class="p1"><span class="Apple-converted-space">        </span>const DEPTH_PERSPECTIVE_RATIO = 0.5;<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>// --- Color assignments (Classic Base Ten) ---</p>
<p class="p1"><span class="Apple-converted-space">        </span>const unitCubeColor = '#FFEB3B'; // Yellow</p>
<p class="p1"><span class="Apple-converted-space">        </span>const rodColor = '#4CAF50';<span class="Apple-converted-space">      </span>// Green</p>
<p class="p1"><span class="Apple-converted-space">        </span>const flatColor = '#2196F3'; <span class="Apple-converted-space">    </span>// Blue</p>
<p class="p1"><span class="Apple-converted-space">        </span>const block1000Color = '#F44336';// Red</p>
<p class="p2"><span class="Apple-converted-space">        </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>// --- Pattern Block Colors ---</p>
<p class="p1"><span class="Apple-converted-space">        </span>const patternHexagonColor = '#FFFF00';<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const patternTrapezoidColor = '#FF0000';<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const patternTriangleColor = '#008000'; <span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const patternBlueRhombusColor = '#0000FF';</p>
<p class="p1"><span class="Apple-converted-space">        </span>const patternSquareColor = '#FFA500'; <span class="Apple-converted-space">   </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const patternTanRhombusColor = '#D2B48C'; <span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>// --- Other Shapes Colors ---</p>
<p class="p1"><span class="Apple-converted-space">        </span>const circleColor = '#0040DD';<span class="Apple-converted-space">       </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const clockFaceColor = '#FFFFFF';<span class="Apple-converted-space">   </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const diceColor = '#FFFFFF';<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const dicePipColor = '#000000';<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const diceOverlayColor = 'rgba(255, 0, 0, 0.3)';</p>
<p class="p1"><span class="Apple-converted-space">        </span>const spinnerPointerColor = '#333333';</p>
<p class="p1"><span class="Apple-converted-space">        </span>const spinnerSectionColors = ['#FFC107', '#03A9F4', '#FF5722', '#8BC34A', '#E91E63'];<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>// --- Fraction Tool Colors ---</p>
<p class="p1"><span class="Apple-converted-space">        </span>const fractionStripEighthsColor = '#4DB6AC';<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const fractionCircleQuartersColor = '#F06292';<span class="Apple-converted-space"> </span></p>
<p class="p2"><span class="Apple-converted-space">        </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>// Number Line Colors<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const numLineRegularBaseColor = '#BDBDBD';<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const numLineIntegerBaseColor = '#81C784';<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const numLineFractionBaseColor = '#FFCC80';<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const numLineDecimalBaseColor = '#9575CD';<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>// --- Cuisenaire Rod Colors ---</p>
<p class="p1"><span class="Apple-converted-space">        </span>const cuisenaireColors = {</p>
<p class="p1"><span class="Apple-converted-space">            </span>1: '#FFFFFF', 2: '#FF0000', 3: '#90EE90', 4: '#FFC0CB', 5: '#FFFF00',<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>6: '#006400', 7: '#000000', 8: '#A0522D', 9: '#0000FF', 10: '#FFA500'<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>};</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>// --- 3D Shape and Net Colors ---</p>
<p class="p1"><span class="Apple-converted-space">        </span>const shape3DCubeColor = '#80CBC4';<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const shape3DConeColor = '#F48FB1';<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const shape3DCylinderColor = '#90CAF9';<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const shape3DPyramidColor = '#FFE082';<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const shape3DRectPrismColor = '#A1887F';<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const shape3DTriPrismColor = '#C5E1A5';<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>// --- Algebra Tile Colors ---</p>
<p class="p1"><span class="Apple-converted-space">        </span>const algebraXSquaredColor = '#2196F3'; // Blue</p>
<p class="p1"><span class="Apple-converted-space">        </span>const algebraXColor = '#4CAF50'; <span class="Apple-converted-space">      </span>// Green</p>
<p class="p1"><span class="Apple-converted-space">        </span>const algebraUnitColor = '#FFEB3B';<span class="Apple-converted-space">    </span>// Yellow</p>
<p class="p1"><span class="Apple-converted-space">        </span>const algebraNegativeColor = '#F44336'; // Red</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>// --- New Tool Colors ---</p>
<p class="p1"><span class="Apple-converted-space">        </span>const tenFrameColor = '#E0E0E0';<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const tenFrameDotColor = '#757575';<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const arrayCellColor = '#BDBDBD';<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const hundredsChartGridColor = '#000000';</p>
<p class="p1"><span class="Apple-converted-space">        </span>const hundredsChartTextColor = '#000000';</p>
<p class="p1"><span class="Apple-converted-space">        </span>const barGraphBarColor = '#64B5F6';<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const graphAxisColor = '#333333';</p>
<p class="p1"><span class="Apple-converted-space">        </span>const graphGridColor = '#E0E0E0';</p>
<p class="p1"><span class="Apple-converted-space">        </span>const graphLineColor = '#F44336';<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const scatterPlotPointColor = '#FF7043';<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const numberBondAdditionColor = '#D1C4E9'; // Light Purple for addition</p>
<p class="p1"><span class="Apple-converted-space">        </span>const numberBondMultiplicationColor = '#FFCDD2'; // Light Pink for multiplication</p>
<p class="p1"><span class="Apple-converted-space">        </span>const factorTreeColor = '#BBDEFB'; // Light Blue (e.g., blue lighten-3 from Material Design)</p>
<p class="p2"><br></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>const mainOutlineColor = '#000000';<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const mainOutlineWidth = 1.5;<span class="Apple-converted-space">       </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const internalLineColor = '#000000';<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const internalLineWidth = 1;<span class="Apple-converted-space">         </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const shadowOffsetX = 3;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const shadowOffsetY = 3;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const shadowBlur = 5; <span class="Apple-converted-space">   </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const shadowColor = 'rgba(0, 0, 0, 0.4)';<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>const rodLengthUnits = 10;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const rodDisplaySegmentsCount = 10;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const flatSideUnits = 10;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const flatDisplaySegmentsCount = 10;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const block1000SideUnits = 10;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const block1000GridSegments = 10;<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>const patternBlockUnitSide = baseSize * 2.5;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const rotationHandleSymbol = '⟲';</p>
<p class="p1"><span class="Apple-converted-space">        </span>const rotationHandleSize = baseSize * 2.5;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const rotationHandleClickRadius = baseSize * 1.5;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const rotationHandleOffset = baseSize * 0.8;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const cmScaleFactor = baseSize * 1.2;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const cuisenaireRodHeight = baseSize * 1.5;</p>
<p class="p1"><span class="Apple-converted-space">        </span>const threeDShapeBaseUnit = baseSize * 1.1;<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>// --- Data for shapes ---</p>
<p class="p1"><span class="Apple-converted-space">        </span>let unitCube = { x: 0, y: 0, size: baseSize, color: unitCubeColor, type: 'cube' };</p>
<p class="p1"><span class="Apple-converted-space">        </span>let rod = { x: 0, y: 0, length: 0, height: 0, depth: 0, color: rodColor, type: 'rod' };</p>
<p class="p1"><span class="Apple-converted-space">        </span>let flat = { x: 0, y: 0, sideLength: 0, thickness: 0, color: flatColor, type: 'flat' };</p>
<p class="p1"><span class="Apple-converted-space">        </span>let block1000 = { x: 0, y: 0, sideLength: 0, color: block1000Color, type: 'block1000' };</p>
<p class="p2"><span class="Apple-converted-space">        </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>let circle = { x:0, y:0, radius:0, color: circleColor, type: 'circle', angle: 0 };<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>let clock = { x:0, y:0, radius:0, color: clockFaceColor, type: 'clock'};<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>let dice = {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>x:0, y:0, size: baseSize * 2.5, color: diceColor, pipColor: dicePipColor, type: 'dice',<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>currentFaceValue: 1, isAnimating: false, animationStartTime: 0,<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>displayFaceValue: 1, targetFaceValue: 1, lastFaceChangeTime: 0,</p>
<p class="p1"><span class="Apple-converted-space">            </span>overlayActiveUntil: 0<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>};</p>
<p class="p2"><span class="Apple-converted-space">        </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>let patternHexagon = { x:0, y:0, sideLength: patternBlockUnitSide, color: patternHexagonColor, type: 'patternHexagon', angle: 0 };</p>
<p class="p1"><span class="Apple-converted-space">        </span>let patternTrapezoid = { x:0, y:0, shortBase: patternBlockUnitSide, longBase: patternBlockUnitSide * 2, height:0, color: patternTrapezoidColor, type: 'patternTrapezoid', angle: 0 };<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>let patternTriangle = { x:0, y:0, sideLength: patternBlockUnitSide, color: patternTriangleColor, type: 'patternTriangle', angle: 0 };</p>
<p class="p1"><span class="Apple-converted-space">        </span>let patternBlueRhombus = { x:0, y:0, sideLength: patternBlockUnitSide, angleDeg: 60, color: patternBlueRhombusColor, type: 'patternBlueRhombus', angle: 0 };<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>let patternSquare = { x:0, y:0, sideLength: patternBlockUnitSide, color: patternSquareColor, type: 'patternSquare', angle: 0 };</p>
<p class="p1"><span class="Apple-converted-space">        </span>let patternTanRhombus = { x:0, y:0, sideLength: patternBlockUnitSide, angleDeg: 30, color: patternTanRhombusColor, type: 'patternTanRhombus', angle: 0 };</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>let numLineRegular = { x:0, y:0, length:0, height: baseSize * 0.5, color: numLineRegularBaseColor, type: 'numLineRegular'};</p>
<p class="p1"><span class="Apple-converted-space">        </span>let numLineInteger = { x:0, y:0, length:0, height: baseSize * 0.5, color: numLineIntegerBaseColor, type: 'numLineInteger'};</p>
<p class="p1"><span class="Apple-converted-space">        </span>let numLineFraction = { x:0, y:0, length:0, height: baseSize * 0.5, color: numLineFractionBaseColor, type: 'numLineFraction'};</p>
<p class="p1"><span class="Apple-converted-space">        </span>let numLineDecimal = { x:0, y:0, length:0, height: baseSize * 0.5, color: numLineDecimalBaseColor, type: 'numLineDecimal'};</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>let fractionStripEighths = { x:0, y:0, length:0, height: baseSize * 2, color: fractionStripEighthsColor, type: 'fractionStripEighths'};</p>
<p class="p1"><span class="Apple-converted-space">        </span>let fractionCircleQuarters = { x:0, y:0, radius:0, color: fractionCircleQuartersColor, type: 'fractionCircleQuarters', angle: 0};<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>let spinner = {</p>
<p class="p1"><span class="Apple-converted-space">            </span>x:0, y:0, radius:0, colors: spinnerSectionColors, numSections: 5, type: 'spinner',<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>currentAngle: 0,<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>targetAngle: 0,<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>isSpinning: false,<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>animationStartTime: 0,<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>overlayActiveUntil: 0,<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>spinDuration: 2000,<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>initialAngleForSpin: 0<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>};</p>
<p class="p2"><span class="Apple-converted-space">        </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>// Cuisenaire Rods Data</p>
<p class="p1"><span class="Apple-converted-space">        </span>const cuisenaireRods = [];</p>
<p class="p1"><span class="Apple-converted-space">        </span>for (let i = 1; i &lt;= 10; i++) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>cuisenaireRods.push({</p>
<p class="p1"><span class="Apple-converted-space">                </span>x: 0, y: 0,<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>lengthUnit: i,<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>width: 0,<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>height: cuisenaireRodHeight,<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>color: cuisenaireColors[i],<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>type: 'cuisenaireRod',</p>
<p class="p1"><span class="Apple-converted-space">                </span>value: i<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>});</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>// 3D Shapes and Nets Data</p>
<p class="p1"><span class="Apple-converted-space">        </span>let shape3DCube = { x:0, y:0, size: threeDShapeBaseUnit * 1.5, color: shape3DCubeColor, type: 'shape3DCube'};</p>
<p class="p1"><span class="Apple-converted-space">        </span>let netCube = { x:0, y:0, unitSize: threeDShapeBaseUnit * 0.3, color: shape3DCubeColor, type: 'netCube'};<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>let shape3DCylinder = { x:0, y:0, radius: threeDShapeBaseUnit * 0.75, height: threeDShapeBaseUnit * 2, color: shape3DCylinderColor, type: 'shape3DCylinder'};</p>
<p class="p1"><span class="Apple-converted-space">        </span>let netCylinder = { x:0, y:0, radius: threeDShapeBaseUnit * 0.4, height: threeDShapeBaseUnit * 1.5, color: shape3DCylinderColor, type: 'netCylinder'};</p>
<p class="p1"><span class="Apple-converted-space">        </span>let shape3DPyramid = { x:0, y:0, baseSize: threeDShapeBaseUnit * 1.25, height: threeDShapeBaseUnit * 1.75, color: shape3DPyramidColor, type: 'shape3DPyramid'};</p>
<p class="p1"><span class="Apple-converted-space">        </span>let netPyramid = { x:0, y:0, baseUnitSize: threeDShapeBaseUnit * 0.45, triangleHeight: threeDShapeBaseUnit * 0.55, color: shape3DPyramidColor, type: 'netPyramid'};</p>
<p class="p1"><span class="Apple-converted-space">        </span>let shape3DCone = { x:0, y:0, radius: threeDShapeBaseUnit * 0.75, height: threeDShapeBaseUnit * 2, color: shape3DConeColor, type: 'shape3DCone'};</p>
<p class="p1"><span class="Apple-converted-space">        </span>let netCone = { x:0, y:0, radius: threeDShapeBaseUnit * 0.4, slantHeight: threeDShapeBaseUnit * 1.2, color: shape3DConeColor, type: 'netCone'};</p>
<p class="p1"><span class="Apple-converted-space">        </span>let shape3DRectPrism = { x:0, y:0, width: threeDShapeBaseUnit * 2, height: threeDShapeBaseUnit * 1, depth: threeDShapeBaseUnit * 0.75, color: shape3DRectPrismColor, type: 'shape3DRectPrism'};</p>
<p class="p1"><span class="Apple-converted-space">        </span>let netRectPrism = { x:0, y:0, rectW: threeDShapeBaseUnit * 0.5, rectH: threeDShapeBaseUnit * 0.3, rectD: threeDShapeBaseUnit * 0.7, color: shape3DRectPrismColor, type: 'netRectPrism'};</p>
<p class="p1"><span class="Apple-converted-space">        </span>let shape3DTriPrism = { x:0, y:0, baseSide: threeDShapeBaseUnit * 1, prismLength: threeDShapeBaseUnit * 2, color: shape3DTriPrismColor, type: 'shape3DTriPrism'};<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>let netTriPrism = { x:0, y:0, baseSide: threeDShapeBaseUnit * 0.5, prismLength: threeDShapeBaseUnit * 0.8, color: shape3DTriPrismColor, type: 'netTriPrism'};</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>// Algebra Tiles Data</p>
<p class="p1"><span class="Apple-converted-space">        </span>const algebraUnitActualSize = baseSize * 1.5;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>const algebraXDim = algebraUnitActualSize * 2.2;<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>let algXSquaredPositive = { x: 0, y: 0, width: algebraXDim, height: algebraXDim, color: algebraXSquaredColor, label: "x²", type: "algebraTile" };</p>
<p class="p1"><span class="Apple-converted-space">        </span>let algXPositive = { x: 0, y: 0, width: algebraXDim, height: algebraUnitActualSize, color: algebraXColor, label: "x", type: "algebraTile" };</p>
<p class="p1"><span class="Apple-converted-space">        </span>let algUnitPositive = { x: 0, y: 0, width: algebraUnitActualSize, height: algebraUnitActualSize, color: algebraUnitColor, label: "1", type: "algebraTile" };</p>
<p class="p1"><span class="Apple-converted-space">        </span>let algXSquaredNegative = { x: 0, y: 0, width: algebraXDim, height: algebraXDim, color: algebraNegativeColor, label: "-x²", type: "algebraTile" };</p>
<p class="p1"><span class="Apple-converted-space">        </span>let algXNegative = { x: 0, y: 0, width: algebraXDim, height: algebraUnitActualSize, color: algebraNegativeColor, label: "-x", type: "algebraTile" };</p>
<p class="p1"><span class="Apple-converted-space">        </span>let algUnitNegative = { x: 0, y: 0, width: algebraUnitActualSize, height: algebraUnitActualSize, color: algebraNegativeColor, label: "-1", type: "algebraTile" };</p>
<p class="p2"><span class="Apple-converted-space">    </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>// New Tools Data</p>
<p class="p1"><span class="Apple-converted-space">        </span>let tenFrame = { x:0, y:0, cellWidth: baseSize * 1.8, cellHeight: baseSize * 1.8, rows: 2, cols: 5, type: 'tenFrame'};</p>
<p class="p1"><span class="Apple-converted-space">        </span>let multiplicationArray = { x:0, y:0, cellWidth: baseSize * 1.3, cellHeight: baseSize * 1.3, rows: 4, cols: 5, type: 'multiplicationArray'};</p>
<p class="p1"><span class="Apple-converted-space">        </span>let hundredsChart = { x:0, y:0, cellWidth: baseSize * 1.5, cellHeight: baseSize * 1.5, type: 'hundredsChart'};</p>
<p class="p1"><span class="Apple-converted-space">        </span>let barGraph = {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>x: 0, y: 0, width: baseSize * 18, height: baseSize * 12, type: 'barGraph',</p>
<p class="p1"><span class="Apple-converted-space">            </span>data: [ { label: "A", value: 10 }, { label: "B", value: 15 }, { label: "C", value: 7 }, { label: "D", value: 12 } ],</p>
<p class="p1"><span class="Apple-converted-space">            </span>maxValue: 20, barColor: barGraphBarColor, labelColor: graphAxisColor, axisColor: graphAxisColor</p>
<p class="p1"><span class="Apple-converted-space">        </span>};</p>
<p class="p1"><span class="Apple-converted-space">        </span>let coordinatePlane = {</p>
<p class="p1"><span class="Apple-converted-space">            </span>x: 0, y: 0, width: baseSize * 18, height: baseSize * 15, type: 'coordinatePlane',</p>
<p class="p1"><span class="Apple-converted-space">            </span>xMin: -5, xMax: 5, yMin: -5, yMax: 5,</p>
<p class="p1"><span class="Apple-converted-space">            </span>lineFunction: (x) =&gt; 2 * x + 1,</p>
<p class="p1"><span class="Apple-converted-space">            </span>axisColor: graphAxisColor, gridColor: graphGridColor, lineColor: graphLineColor</p>
<p class="p1"><span class="Apple-converted-space">        </span>};</p>
<p class="p1"><span class="Apple-converted-space">        </span>let scatterPlot = {</p>
<p class="p1"><span class="Apple-converted-space">            </span>x: 0, y: 0, width: baseSize * 18, height: baseSize * 15, type: 'scatterPlot',</p>
<p class="p1"><span class="Apple-converted-space">            </span>dataPoints: [ { x: 2, y: 1 }, { x: 3, y: 2 }, { x: 5, y: 3 }, { x: 6, y: 2 }, { x: 8, y: 5 }, { x: 9, y: 4 }, { x: 10, y: 6}, {x: 4, y:3}, {x:7, y:4}],</p>
<p class="p1"><span class="Apple-converted-space">            </span>xLabel: "Shots Taken", yLabel: "Shots Made", xMax: 12, yMax: 8,</p>
<p class="p1"><span class="Apple-converted-space">            </span>pointColor: scatterPlotPointColor, axisColor: graphAxisColor, gridColor: graphGridColor</p>
<p class="p1"><span class="Apple-converted-space">        </span>};</p>
<p class="p1"><span class="Apple-converted-space">        </span>let additionNumberBond = { x:0, y:0, whole: 12, part1: 5, part2: 7, radius: baseSize * 1.5, type: 'additionNumberBond', color: numberBondAdditionColor };</p>
<p class="p1"><span class="Apple-converted-space">        </span>let multiplicationNumberBond = { x:0, y:0, whole: 15, part1: 3, part2: 5, radius: baseSize * 1.5, type: 'multiplicationNumberBond', color: numberBondMultiplicationColor };</p>
<p class="p1"><span class="Apple-converted-space">        </span>let factorTree = { x:0, y:0, number: 24, nodeRadius: baseSize * 1.2, type: 'factorTree', color: factorTreeColor };</p>
<p class="p2"><br></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>const allDrawableItems = [</p>
<p class="p1"><span class="Apple-converted-space">            </span>unitCube, rod, flat, block1000,<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>circle, clock, dice, spinner,</p>
<p class="p1"><span class="Apple-converted-space">            </span>patternHexagon, patternTrapezoid, patternTriangle, patternBlueRhombus, patternSquare, patternTanRhombus,</p>
<p class="p1"><span class="Apple-converted-space">            </span>numLineRegular, numLineInteger, numLineFraction, numLineDecimal,</p>
<p class="p1"><span class="Apple-converted-space">            </span>fractionStripEighths, fractionCircleQuarters,</p>
<p class="p1"><span class="Apple-converted-space">            </span>...cuisenaireRods,</p>
<p class="p1"><span class="Apple-converted-space">            </span>shape3DCube, netCube, shape3DCylinder, netCylinder,</p>
<p class="p1"><span class="Apple-converted-space">            </span>shape3DPyramid, netPyramid, shape3DCone, netCone,</p>
<p class="p1"><span class="Apple-converted-space">            </span>shape3DRectPrism, netRectPrism, shape3DTriPrism, netTriPrism,</p>
<p class="p1"><span class="Apple-converted-space">            </span>algXSquaredPositive, algXPositive, algUnitPositive,</p>
<p class="p1"><span class="Apple-converted-space">            </span>algXSquaredNegative, algXNegative, algUnitNegative,</p>
<p class="p1"><span class="Apple-converted-space">            </span>tenFrame, multiplicationArray, hundredsChart,</p>
<p class="p1"><span class="Apple-converted-space">            </span>barGraph, coordinatePlane, scatterPlot,</p>
<p class="p1"><span class="Apple-converted-space">            </span>additionNumberBond, multiplicationNumberBond, factorTree</p>
<p class="p1"><span class="Apple-converted-space">        </span>];</p>
<p class="p1"><span class="Apple-converted-space">        </span>const rotatableTypes = ['circle', 'patternHexagon', 'patternTrapezoid', 'patternTriangle', 'patternBlueRhombus', 'patternSquare', 'patternTanRhombus', 'fractionCircleQuarters'];</p>
<p class="p2"><br></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>let isDragging = false;</p>
<p class="p1"><span class="Apple-converted-space">        </span>let isLassoing = false;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>let isRotating = false;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>let rotatingItem = null;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>let rotationStartMouseAngle = 0;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>let rotationStartItemAngle = 0;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>let draggedItem = null;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>let selectedItems = [];<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>let lassoRect = { startX: 0, startY: 0, currentX: 0, currentY: 0, active: false };</p>
<p class="p1"><span class="Apple-converted-space">        </span>let dragStartOffsets = [];<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>let lastMouseX, lastMouseY;</p>
<p class="p1"><span class="Apple-converted-space">        </span>let oldCanvasWidth = 0;</p>
<p class="p1"><span class="Apple-converted-space">        </span>let oldCanvasHeight = 0;</p>
<p class="p1"><span class="Apple-converted-space">        </span>const snappingThreshold = 10;<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>// --- Helper functions for color manipulation (unchanged) ---</p>
<p class="p1"><span class="Apple-converted-space">        </span>function hexToRgb(hex) { const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex); return result ? { r: parseInt(result[1], 16), g: parseInt(result[2], 16), b: parseInt(result[3], 16) } : null;}</p>
<p class="p1"><span class="Apple-converted-space">        </span>function rgbToHex(r, g, b) { return "#" + ((1 &lt;&lt; 24) + (r &lt;&lt; 16) + (g &lt;&lt; 8) + b).toString(16).slice(1).padStart(6, '0');}</p>
<p class="p1"><span class="Apple-converted-space">        </span>function lightenColor(hex, percent) { const rgb = hexToRgb(hex); if (!rgb) return hex; const factor = percent / 100; rgb.r = Math.min(255, Math.max(0, Math.round(rgb.r + (255 - rgb.r) * factor))); rgb.g = Math.min(255, Math.max(0, Math.round(rgb.g + (255 - rgb.g) * factor))); rgb.b = Math.min(255, Math.max(0, Math.round(rgb.b + (255 - rgb.b) * factor))); return rgbToHex(rgb.r, rgb.g, rgb.b);}</p>
<p class="p1"><span class="Apple-converted-space">        </span>function darkenColor(hex, percent) { const rgb = hexToRgb(hex); if (!rgb) return hex; const factor = percent / 100; rgb.r = Math.min(255, Math.max(0, Math.round(rgb.r * (1 - factor)))); rgb.g = Math.min(255, Math.max(0, Math.round(rgb.g * (1 - factor)))); rgb.b = Math.min(255, Math.max(0, Math.round(rgb.b * (1 - factor)))); return rgbToHex(rgb.r, rgb.g, rgb.b);}</p>
<p class="p1"><span class="Apple-converted-space">        </span>function applyTransparency(hex, alpha) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>const rgb = hexToRgb(hex);</p>
<p class="p1"><span class="Apple-converted-space">            </span>if (!rgb) return hex;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>return `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${alpha})`;</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>// --- Render Base Ten Blocks (unchanged) ---</p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderUnitCube(cube) { ctx.save(); ctx.globalAlpha = baseTenTransparency; ctx.translate(cube.x, cube.y); ctx.strokeStyle = mainOutlineColor; ctx.lineWidth = mainOutlineWidth; const faceSize = cube.size; const po = faceSize * DEPTH_PERSPECTIVE_RATIO; ctx.beginPath(); ctx.fillStyle = cube.color; ctx.rect(-faceSize / 2, -faceSize / 2, faceSize, faceSize); ctx.fill(); ctx.stroke(); ctx.beginPath(); ctx.fillStyle = lightenColor(cube.color, 20); ctx.moveTo(-faceSize / 2, -faceSize / 2); ctx.lineTo(-faceSize / 2 + po, -faceSize / 2 - po); ctx.lineTo(faceSize / 2 + po, -faceSize / 2 - po); ctx.lineTo(faceSize / 2, -faceSize / 2); ctx.closePath(); ctx.fill(); ctx.stroke(); ctx.beginPath(); ctx.fillStyle = darkenColor(cube.color, 20); ctx.moveTo(faceSize / 2, -faceSize / 2); ctx.lineTo(faceSize / 2 + po, -faceSize / 2 - po); ctx.lineTo(faceSize / 2 + po, faceSize / 2 - po); ctx.lineTo(faceSize / 2, faceSize / 2); ctx.closePath(); ctx.fill(); ctx.stroke(); ctx.globalAlpha = 1.0; ctx.restore(); }</p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderRod(rodObject) { const x = rodObject.x, y = rodObject.y, w = rodObject.length, h = rodObject.height, c = rodObject.color, d = rodObject.depth; ctx.save(); ctx.globalAlpha = baseTenTransparency; ctx.translate(x,y); ctx.strokeStyle = mainOutlineColor; ctx.lineWidth = mainOutlineWidth; const po = d * DEPTH_PERSPECTIVE_RATIO; ctx.beginPath(); ctx.fillStyle = c; ctx.rect(-w/2, -h/2, w, h); ctx.fill(); ctx.stroke(); ctx.strokeStyle = internalLineColor; ctx.lineWidth = internalLineWidth; let segW = w / rodDisplaySegmentsCount; for (let k=1; k&lt;rodDisplaySegmentsCount; k++) { const lx = -w/2 + k*segW; ctx.beginPath(); ctx.moveTo(lx, -h/2); ctx.lineTo(lx, h/2); ctx.stroke(); } ctx.strokeStyle = mainOutlineColor; ctx.lineWidth = mainOutlineWidth; ctx.beginPath(); ctx.fillStyle = lightenColor(c,20); ctx.moveTo(-w/2, -h/2); ctx.lineTo(-w/2+po, -h/2-po); ctx.lineTo(w/2+po, -h/2-po); ctx.lineTo(w/2, -h/2); ctx.closePath(); ctx.fill(); ctx.stroke(); ctx.strokeStyle = internalLineColor; ctx.lineWidth = internalLineWidth; for (let k=1; k&lt;rodDisplaySegmentsCount; k++) { const p1x = -w/2 + k*segW, p1y = -h/2, p2x = -w/2+po + k*segW, p2y = -h/2-po; ctx.beginPath(); ctx.moveTo(p1x,p1y); ctx.lineTo(p2x,p2y); ctx.stroke(); } ctx.strokeStyle = mainOutlineColor; ctx.lineWidth = mainOutlineWidth; ctx.beginPath(); ctx.fillStyle = darkenColor(c,20); ctx.moveTo(w/2, -h/2); ctx.lineTo(w/2+po, -h/2-po); ctx.lineTo(w/2+po, h/2-po); ctx.lineTo(w/2, h/2); ctx.closePath(); ctx.fill(); ctx.stroke(); ctx.globalAlpha = 1.0; ctx.restore(); }</p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderFlat(flatObject) { const x = flatObject.x, y = flatObject.y, s = flatObject.sideLength, th = flatObject.thickness, c = flatObject.color; ctx.save(); ctx.globalAlpha = baseTenTransparency; ctx.translate(x,y); ctx.strokeStyle = mainOutlineColor; ctx.lineWidth = mainOutlineWidth; const po = th * DEPTH_PERSPECTIVE_RATIO; ctx.beginPath(); ctx.fillStyle = c; ctx.rect(-s/2, -s/2, s, s); ctx.fill(); ctx.stroke(); ctx.strokeStyle = internalLineColor; ctx.lineWidth = internalLineWidth; let segS = s/10; for (let i=1; i&lt;10; i++) { let lx = -s/2+i*segS; ctx.beginPath(); ctx.moveTo(lx, -s/2); ctx.lineTo(lx, s/2); ctx.stroke(); let ly = -s/2+i*segS; ctx.beginPath(); ctx.moveTo(-s/2, ly); ctx.lineTo(s/2, ly); ctx.stroke(); } ctx.strokeStyle = mainOutlineColor; ctx.lineWidth = mainOutlineWidth; ctx.beginPath(); ctx.fillStyle = lightenColor(c,15); ctx.moveTo(-s/2,-s/2); ctx.lineTo(-s/2+po, -s/2-po); ctx.lineTo(s/2+po, -s/2-po); ctx.lineTo(s/2,-s/2); ctx.closePath(); ctx.fill(); ctx.stroke(); ctx.strokeStyle = internalLineColor; ctx.lineWidth = internalLineWidth; segS = s/flatDisplaySegmentsCount; for(let k=1;k&lt;flatDisplaySegmentsCount;k++){const p1x_v=-s/2+k*segS,p1y_v=-s/2,p2x_v=-s/2+po+k*segS,p2y_v=-s/2-po;ctx.beginPath();ctx.moveTo(p1x_v,p1y_v);ctx.lineTo(p2x_v,p2y_v);ctx.stroke();} ctx.strokeStyle = mainOutlineColor; ctx.lineWidth = mainOutlineWidth; ctx.beginPath(); ctx.fillStyle = darkenColor(c,15); ctx.moveTo(s/2,-s/2); ctx.lineTo(s/2+po,-s/2-po); ctx.lineTo(s/2+po,s/2-po); ctx.lineTo(s/2,s/2); ctx.closePath();ctx.fill();ctx.stroke(); ctx.strokeStyle=internalLineColor;ctx.lineWidth=internalLineWidth;segS=s/flatDisplaySegmentsCount;for(let k=1;k&lt;flatDisplaySegmentsCount;k++){const p1x_h_side=s/2,p1y_h_side=-s/2+k*segS,p2x_h_side=s/2+po,p2y_h_side=-s/2-po+k*segS;ctx.beginPath();ctx.moveTo(p1x_h_side,p1y_h_side);ctx.lineTo(p2x_h_side,p2y_h_side);ctx.stroke();} ctx.globalAlpha = 1.0; ctx.restore(); }</p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderBlock1000(blockObject) { const x=blockObject.x,y=blockObject.y,s=blockObject.sideLength,c=blockObject.color,d=s; ctx.save(); ctx.globalAlpha=baseTenTransparency; ctx.translate(x,y); ctx.strokeStyle=mainOutlineColor; ctx.lineWidth=mainOutlineWidth; const po=d*DEPTH_PERSPECTIVE_RATIO; ctx.beginPath(); ctx.fillStyle=c; ctx.rect(-s/2,-s/2,s,s); ctx.fill();ctx.stroke(); ctx.strokeStyle=internalLineColor; ctx.lineWidth=internalLineWidth; let seg=s/block1000GridSegments; for(let i=1;i&lt;block1000GridSegments;i++){ ctx.beginPath();ctx.moveTo(-s/2+i*seg,-s/2);ctx.lineTo(-s/2+i*seg,s/2);ctx.stroke(); ctx.beginPath();ctx.moveTo(-s/2,-s/2+i*seg);ctx.lineTo(s/2,-s/2+i*seg);ctx.stroke(); } ctx.strokeStyle=mainOutlineColor;ctx.lineWidth=mainOutlineWidth;ctx.fillStyle=lightenColor(c,20);ctx.beginPath();const tf_x1=-s/2,tf_y1=-s/2,tf_x2=s/2,tf_y2=-s/2,tf_x3=s/2+po,tf_y3=-s/2-po,tf_x4=-s/2+po,tf_y4=-s/2-po;ctx.moveTo(tf_x1,tf_y1);ctx.lineTo(tf_x4,tf_y4);ctx.lineTo(tf_x3,tf_y3);ctx.lineTo(tf_x2,tf_y2);ctx.closePath();ctx.fill();ctx.stroke();ctx.strokeStyle=internalLineColor;ctx.lineWidth=internalLineWidth;for(let k=1;k&lt;block1000GridSegments;k++){const t=k/block1000GridSegments;ctx.beginPath();ctx.moveTo(tf_x1+t*(tf_x2-tf_x1),tf_y1+t*(tf_y2-tf_y1));ctx.lineTo(tf_x4+t*(tf_x3-tf_x4),tf_y4+t*(tf_y3-tf_y4));ctx.stroke();ctx.beginPath();ctx.moveTo(tf_x1+t*(tf_x4-tf_x1),tf_y1+t*(tf_y4-tf_y1));ctx.lineTo(tf_x2+t*(tf_x3-tf_x2),tf_y2+t*(tf_y3-tf_y2));ctx.stroke();}ctx.strokeStyle=mainOutlineColor;ctx.lineWidth=mainOutlineWidth;ctx.fillStyle=darkenColor(c,20);ctx.beginPath();const rf_x1=s/2,rf_y1=-s/2,rf_x2=s/2,rf_y2=s/2,rf_x3=s/2+po,rf_y3=s/2-po,rf_x4=s/2+po,rf_y4=-s/2-po;ctx.moveTo(rf_x1,rf_y1);ctx.lineTo(rf_x4,rf_y4);ctx.lineTo(rf_x3,rf_y3);ctx.lineTo(rf_x2,rf_y2);ctx.closePath();ctx.fill();ctx.stroke();ctx.strokeStyle=internalLineColor;ctx.lineWidth=internalLineWidth;for(let k=1;k&lt;block1000GridSegments;k++){const t=k/block1000GridSegments;ctx.beginPath();ctx.moveTo(rf_x1+t*(rf_x2-rf_x1),rf_y1+t*(rf_y2-rf_y1));ctx.lineTo(rf_x4+t*(rf_x3-rf_x4),rf_y4+t*(rf_y3-tf_y4));ctx.stroke();ctx.beginPath();ctx.moveTo(rf_x1+t*(rf_x4-rf_x1),rf_y1+t*(rf_y4-rf_y1));ctx.lineTo(rf_x2+t*(rf_x3-rf_x2),rf_y2+t*(rf_y3-rf_y2));ctx.stroke();}ctx.globalAlpha=1.0;ctx.restore();}</p>
<p class="p2"><span class="Apple-converted-space">        </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>function applyShadow() { ctx.shadowColor = shadowColor; ctx.shadowBlur = shadowBlur; ctx.shadowOffsetX = shadowOffsetX; ctx.shadowOffsetY = shadowOffsetY; }</p>
<p class="p1"><span class="Apple-converted-space">        </span>function clearShadow() { ctx.shadowColor = 'transparent'; ctx.shadowBlur = 0; ctx.shadowOffsetX = 0; ctx.shadowOffsetY = 0; }</p>
<p class="p2"><span class="Apple-converted-space">        </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>function draw2DRegularPolygon(radius, sides, startAngle = 0) {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>for (let i = 0; i &lt;= sides; i++) {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>const angle = startAngle + (i * 2 * Math.PI / sides);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>const xPos = radius * Math.cos(angle);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>const yPos = radius * Math.sin(angle);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>if (i === 0) ctx.moveTo(xPos, yPos); else ctx.lineTo(xPos, yPos);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>}<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.closePath();<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><span class="Apple-converted-space">        </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>// --- RENDER NEW 2D PATTERN BLOCK SHAPES ---</p>
<p class="p1"><span class="Apple-converted-space">        </span>function render2DCircle(item) { ctx.save(); ctx.translate(item.x, item.y); ctx.rotate(item.angle); applyShadow(); ctx.globalAlpha = geometricShapeTransparency; ctx.fillStyle = item.color; ctx.strokeStyle = mainOutlineColor; ctx.lineWidth = mainOutlineWidth; ctx.beginPath(); ctx.arc(0, 0, item.radius, 0, 2 * Math.PI); ctx.fill(); clearShadow(); ctx.stroke(); ctx.globalAlpha = 1.0; ctx.restore(); }</p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderPatternSquare(item) { ctx.save(); ctx.translate(item.x, item.y); ctx.rotate(item.angle); applyShadow(); ctx.globalAlpha = geometricShapeTransparency; ctx.fillStyle = item.color; ctx.strokeStyle = mainOutlineColor; ctx.lineWidth = mainOutlineWidth; ctx.beginPath(); ctx.rect(-item.sideLength / 2, -item.sideLength / 2, item.sideLength, item.sideLength); ctx.fill(); clearShadow(); ctx.stroke(); ctx.globalAlpha = 1.0; ctx.restore(); }</p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderPatternTriangle(item) {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save(); ctx.translate(item.x, item.y); ctx.rotate(item.angle); applyShadow(); ctx.globalAlpha = geometricShapeTransparency; ctx.fillStyle = item.color; ctx.strokeStyle = mainOutlineColor; ctx.lineWidth = mainOutlineWidth;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const radius = item.sideLength / Math.sqrt(3);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>draw2DRegularPolygon(radius, 3, -Math.PI/2);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fill(); clearShadow(); ctx.stroke(); ctx.globalAlpha = 1.0; ctx.restore();<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>}<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderPatternHexagon(item) {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save(); ctx.translate(item.x, item.y); ctx.rotate(item.angle); applyShadow(); ctx.globalAlpha = geometricShapeTransparency; ctx.fillStyle = item.color; ctx.strokeStyle = mainOutlineColor; ctx.lineWidth = mainOutlineWidth;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const radius = item.sideLength;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>draw2DRegularPolygon(radius, 6, 0);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fill(); clearShadow(); ctx.stroke(); ctx.globalAlpha = 1.0; ctx.restore();<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderPatternTrapezoid(item) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save(); ctx.translate(item.x, item.y); ctx.rotate(item.angle); applyShadow(); ctx.globalAlpha = geometricShapeTransparency; ctx.fillStyle = item.color; ctx.strokeStyle = mainOutlineColor; ctx.lineWidth = mainOutlineWidth;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const h = item.height;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.moveTo(-item.longBase / 2, h / 2);<span class="Apple-converted-space">   </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(item.longBase / 2, h / 2); <span class="Apple-converted-space">   </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(item.shortBase / 2, -h / 2);<span class="Apple-converted-space">   </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(-item.shortBase / 2, -h / 2); <span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.closePath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fill(); clearShadow(); ctx.stroke(); ctx.globalAlpha = 1.0; ctx.restore();</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderPatternRhombus(item) {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save(); ctx.translate(item.x, item.y); ctx.rotate(item.angle); applyShadow(); ctx.globalAlpha = geometricShapeTransparency; ctx.fillStyle = item.color; ctx.strokeStyle = mainOutlineColor; ctx.lineWidth = mainOutlineWidth;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const s = item.sideLength;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const angleRad = item.angleDeg * Math.PI / 180;<span class="Apple-converted-space"> </span></p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const halfD1 = s * Math.sin(angleRad / 2);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const halfD2 = s * Math.cos(angleRad / 2);<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.moveTo(0, -halfD2); <span class="Apple-converted-space">   </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(halfD1, 0);<span class="Apple-converted-space">     </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(0, halfD2);<span class="Apple-converted-space">     </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(-halfD1, 0); <span class="Apple-converted-space">   </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.closePath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fill(); clearShadow(); ctx.stroke(); ctx.globalAlpha = 1.0; ctx.restore();</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderClock(item) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save(); ctx.translate(item.x, item.y); applyShadow(); ctx.globalAlpha = geometricShapeTransparency;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = item.color; ctx.strokeStyle = mainOutlineColor; ctx.lineWidth = mainOutlineWidth;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath(); ctx.arc(0, 0, item.radius, 0, 2 * Math.PI); ctx.fill(); clearShadow(); ctx.stroke();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = mainOutlineColor; ctx.font = `${item.radius * 0.25}px Arial`; ctx.textAlign = 'center'; ctx.textBaseline = 'middle';</p>
<p class="p1"><span class="Apple-converted-space">            </span>for (let i = 1; i &lt;= 12; i++) { const angle = (i - 3) * (Math.PI * 2) / 12; const numX = Math.cos(angle) * (item.radius*0.8); const numY = Math.sin(angle) * (item.radius*0.8); ctx.fillText(i.toString(), numX, numY); }</p>
<p class="p1"><span class="Apple-converted-space">            </span>const hourAngle = (10 - 3) * (Math.PI * 2) / 12; ctx.beginPath(); ctx.lineWidth = item.radius * 0.07; ctx.strokeStyle = mainOutlineColor; ctx.moveTo(0,0); ctx.lineTo(Math.cos(hourAngle) * item.radius * 0.5, Math.sin(hourAngle) * item.radius * 0.5); ctx.stroke();</p>
<p class="p1"><span class="Apple-converted-space">            </span>const minuteAngle = (10 - 15) * (Math.PI * 2) / 60; ctx.beginPath(); ctx.lineWidth = item.radius * 0.05; ctx.moveTo(0,0); ctx.lineTo(Math.cos(minuteAngle) * item.radius * 0.7, Math.sin(minuteAngle) * item.radius * 0.7); ctx.stroke();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath(); ctx.fillStyle = mainOutlineColor; ctx.arc(0,0, item.radius * 0.05, 0, 2 * Math.PI); ctx.fill();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = 1.0; ctx.restore();</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderDice(diceObj) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>const x = diceObj.x, y = diceObj.y, size = diceObj.size, color = diceObj.color, pipColor = diceObj.pipColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const faceValue = diceObj.isAnimating ? diceObj.displayFaceValue : diceObj.currentFaceValue;</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.translate(x, y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>applyShadow();<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = geometricShapeTransparency;<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = color;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.strokeStyle = mainOutlineColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineWidth = mainOutlineWidth;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.rect(-size / 2, -size / 2, size, size);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fill();</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>if (Date.now() &lt; diceObj.overlayActiveUntil) {</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.fillStyle = diceOverlayColor;</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.fillRect(-size / 2, -size / 2, size, size);</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>clearShadow();<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.stroke();<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>drawPipsOn2DFace(0, 0, size, faceValue, pipColor);<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = 1.0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.restore();</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>function drawPipsOn2DFace(centerX, centerY, faceSize, value, color) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = color;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const pipRadius = faceSize * 0.08;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const q = faceSize / 4;<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const positions = {</p>
<p class="p1"><span class="Apple-converted-space">                </span>1: [[0,0]],</p>
<p class="p1"><span class="Apple-converted-space">                </span>2: [[-q,-q], [q,q]],</p>
<p class="p1"><span class="Apple-converted-space">                </span>3: [[-q,-q], [0,0], [q,q]],</p>
<p class="p1"><span class="Apple-converted-space">                </span>4: [[-q,-q], [q,-q], [-q,q], [q,q]],</p>
<p class="p1"><span class="Apple-converted-space">                </span>5: [[-q,-q], [q,-q], [0,0], [-q,q], [q,q]],</p>
<p class="p1"><span class="Apple-converted-space">                </span>6: [[-q,-q], [q,-q], [-q,0], [q,0], [-q,q], [q,q]]</p>
<p class="p1"><span class="Apple-converted-space">            </span>};</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>if (positions?.[value]) {</p>
<p class="p1"><span class="Apple-converted-space">                </span>positions?.[value].forEach(pos =&gt; {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">                    </span>ctx.arc(centerX + pos?.[0], centerY + pos?.[1], pipRadius, 0, 2 * Math.PI);</p>
<p class="p1"><span class="Apple-converted-space">                    </span>ctx.fill();</p>
<p class="p1"><span class="Apple-converted-space">                </span>});</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>// --- RENDER NUMBER LINES ---</p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderNumberLine(item, rangeStart, rangeEnd, step, labelFn, subTickFn = null, isFractionLine = false) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.translate(item.x, item.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>applyShadow();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = geometricShapeTransparency;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const numDisplaySegments = isFractionLine ? (rangeEnd - rangeStart) * 3 : (rangeEnd - rangeStart) / step;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const segmentRectWidth = item.length / numDisplaySegments;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>for (let i = 0; i &lt; numDisplaySegments; i++) {</p>
<p class="p1"><span class="Apple-converted-space">                </span>const segmentStartX = -item.length / 2 + i * segmentRectWidth;</p>
<p class="p1"><span class="Apple-converted-space">                </span>const currentSegmentColor = (i % 2 === 0) ? '#FFFFFF' : '#000000'; // Alternating colors for segments</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.fillStyle = currentSegmentColor;</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.rect(segmentStartX, -item.height / 2, segmentRectWidth, item.height);</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.fill();</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>clearShadow();<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.strokeStyle = mainOutlineColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineWidth = mainOutlineWidth;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.rect(-item.length / 2, -item.height / 2, item.length, item.height);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.stroke();<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.strokeStyle = mainOutlineColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineWidth = internalLineWidth;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.font = `${baseSize * 0.7}px Arial`;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.textAlign = 'center';</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.textBaseline = 'top';<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const numMainTicks = (rangeEnd - rangeStart) / (isFractionLine ? 1 : step);<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>for (let i = 0; i &lt;= numMainTicks; i++) {</p>
<p class="p1"><span class="Apple-converted-space">                </span>const value = rangeStart + i * (isFractionLine ? 1 : step);</p>
<p class="p1"><span class="Apple-converted-space">                </span>const tickX = -item.length / 2 + (i / numMainTicks) * item.length;</p>
<p class="p2"><span class="Apple-converted-space">                </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.moveTo(tickX, -item.height / 2);</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.lineTo(tickX, -item.height / 2 - baseSize * 0.3);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.stroke();</p>
<p class="p2"><span class="Apple-converted-space">                </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.fillStyle = mainOutlineColor;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.fillText(labelFn(value), tickX, -item.height / 2 - baseSize * 1.1);</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                </span>if (subTickFn) {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>subTickFn(ctx, tickX, item.length / numMainTicks, item.height, value, (isFractionLine ? 1 : step));</p>
<p class="p1"><span class="Apple-converted-space">                </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = 1.0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.restore();</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderNumLineRegular(item) { renderNumberLine(item, 0, 10, 1, val =&gt; val.toString());}</p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderNumLineInteger(item) { renderNumberLine(item, -5, 5, 1, val =&gt; val.toString());}</p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderNumLineFraction(item) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>renderNumberLine(item, 0, 3, 1,<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>val =&gt; val.toString(),<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>(context, mainTickX, mainSegmentLen, lineHeight, mainVal, mainStep) =&gt; {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                    </span>if (mainVal &lt; 3) {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                        </span>for (let j = 1; j &lt;= 2; j++) {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                            </span>const subTickX = mainTickX + (j / 3) * mainSegmentLen;</p>
<p class="p1"><span class="Apple-converted-space">                            </span>context.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">                            </span>context.strokeStyle = mainOutlineColor;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                            </span>context.moveTo(subTickX, -lineHeight / 2);</p>
<p class="p1"><span class="Apple-converted-space">                            </span>context.lineTo(subTickX, -lineHeight / 2 - baseSize * 0.2);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                            </span>context.stroke();</p>
<p class="p2"><span class="Apple-converted-space">                            </span></p>
<p class="p1"><span class="Apple-converted-space">                            </span>let fracLabel = `${j}/3`;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                            </span>const originalFont = context.font;</p>
<p class="p1"><span class="Apple-converted-space">                            </span>context.font = `${baseSize * 0.55}px Arial`;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                            </span>context.fillStyle = mainOutlineColor;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                            </span>context.fillText(fracLabel, subTickX, -lineHeight / 2 - baseSize * 0.9);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                            </span>context.font = originalFont;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                        </span>}</p>
<p class="p1"><span class="Apple-converted-space">                    </span>}</p>
<p class="p1"><span class="Apple-converted-space">                </span>},</p>
<p class="p1"><span class="Apple-converted-space">                </span>true<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>);</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderNumLineDecimal(item) { renderNumberLine(item, 0, 5, 0.5, val =&gt; val.toFixed(1));}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>// --- FRACTION TOOLS RENDERING ---</p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderFractionStripEighths(item) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.translate(item.x, item.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.rotate(item.angle || 0);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>applyShadow();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = geometricShapeTransparency;</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = item.color;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.strokeStyle = mainOutlineColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineWidth = mainOutlineWidth;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.rect(-item.length / 2, -item.height / 2, item.length, item.height);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fill();</p>
<p class="p1"><span class="Apple-converted-space">            </span>clearShadow();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.stroke();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.strokeStyle = internalLineColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineWidth = internalLineWidth;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const segmentWidth = item.length / 8;</p>
<p class="p1"><span class="Apple-converted-space">            </span>for (let i = 1; i &lt; 8; i++) {</p>
<p class="p1"><span class="Apple-converted-space">                </span>const lineX = -item.length / 2 + i * segmentWidth;</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.moveTo(lineX, -item.height / 2);</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.lineTo(lineX, item.height / 2);</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.stroke();</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = mainOutlineColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.font = `${item.height * 0.4}px Arial`;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.textAlign = 'center';</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.textBaseline = 'middle';</p>
<p class="p1"><span class="Apple-converted-space">            </span>for (let i = 0; i &lt; 8; i++) {</p>
<p class="p1"><span class="Apple-converted-space">                </span>const labelX = -item.length / 2 + (i * segmentWidth) + (segmentWidth / 2);</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.fillText("1/8", labelX, 0);</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = 1.0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.restore();</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderFractionCircleQuarters(item) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.translate(item.x, item.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.rotate(item.angle || 0);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>applyShadow();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = geometricShapeTransparency;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = item.color;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.strokeStyle = mainOutlineColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineWidth = mainOutlineWidth;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.arc(0, 0, item.radius, 0, 2 * Math.PI);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fill();</p>
<p class="p1"><span class="Apple-converted-space">            </span>clearShadow();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.stroke();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.strokeStyle = internalLineColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineWidth = internalLineWidth;</p>
<p class="p1"><span class="Apple-converted-space">            </span>for (let i = 0; i &lt; 4; i++) {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>const angle = i * (Math.PI / 2);</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.moveTo(0,0);</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.lineTo(item.radius * Math.cos(angle), item.radius * Math.sin(angle));</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.stroke();</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = mainOutlineColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.font = `${item.radius * 0.25}px Arial`;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.textAlign = 'center';</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.textBaseline = 'middle';</p>
<p class="p1"><span class="Apple-converted-space">            </span>for (let i = 0; i &lt; 4; i++) {</p>
<p class="p1"><span class="Apple-converted-space">                </span>const angle = (i * Math.PI / 2) + (Math.PI / 4);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>const labelX = item.radius * 0.65 * Math.cos(angle);</p>
<p class="p1"><span class="Apple-converted-space">                </span>const labelY = item.radius * 0.65 * Math.sin(angle);</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.fillText("1/4", labelX, labelY);</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = 1.0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.restore();</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><span class="Apple-converted-space">        </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderSpinner(item) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.translate(item.x, item.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>const baseAngle = item.angle || 0;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.rotate(baseAngle);<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>applyShadow();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = geometricShapeTransparency;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.rotate(item.currentAngle);</p>
<p class="p1"><span class="Apple-converted-space">            </span>const anglePerSection = (2 * Math.PI) / item.numSections;</p>
<p class="p1"><span class="Apple-converted-space">            </span>for (let i = 0; i &lt; item.numSections; i++) {</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.moveTo(0, 0);</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.arc(0, 0, item.radius, i * anglePerSection, (i + 1) * anglePerSection);</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.closePath();</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.fillStyle = item.colors[i % item.colors.length];</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.fill();</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.restore();<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>clearShadow();<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.strokeStyle = mainOutlineColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineWidth = mainOutlineWidth;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.arc(0, 0, item.radius, 0, 2 * Math.PI);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.stroke();</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>if (Date.now() &lt; item.overlayActiveUntil) {</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.fillStyle = diceOverlayColor;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.arc(0, 0, item.radius, 0, 2 * Math.PI);</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.fill();</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.restore();<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.translate(item.x, item.y);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = spinnerPointerColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>const pointerBaseWidth = baseSize * 0.6;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const pointerHeight = baseSize * 1.2;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.moveTo(0, -item.radius + pointerHeight * 0.2);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(-pointerBaseWidth / 2, -item.radius - pointerHeight * 0.8);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(pointerBaseWidth / 2, -item.radius - pointerHeight * 0.8);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.closePath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fill();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = 1.0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.restore();</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>// --- RENDER CUISENAIRE RODS ---</p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderCuisenaireRod(item) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.translate(item.x, item.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>applyShadow();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = cuisenaireRodTransparency;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = item.color;</p>
<p class="p1"><span class="Apple-converted-space">            </span>if (item.color === '#FFFFFF') { // White rod needs an outline for visibility</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.strokeStyle = mainOutlineColor;</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.lineWidth = mainOutlineWidth;</p>
<p class="p1"><span class="Apple-converted-space">            </span>} else {</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.strokeStyle = item.color; // Make border same as fill to hide it</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.rect(-item.width / 2, -item.height / 2, item.width, item.height);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fill();</p>
<p class="p1"><span class="Apple-converted-space">            </span>if (item.color === '#FFFFFF') { // Only stroke white rod</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.stroke();</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>clearShadow();<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = 1.0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.restore();</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>// --- 3D SHAPE AND NET RENDERING ---</p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderShape3DCube(item) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.translate(item.x, item.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.strokeStyle = mainOutlineColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineWidth = mainOutlineWidth;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const s = item.size;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const po = s * DEPTH_PERSPECTIVE_RATIO;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const faceColor = applyTransparency(item.color, threeDShapeTransparency);</p>
<p class="p1"><span class="Apple-converted-space">            </span>const topFaceColor = applyTransparency(lightenColor(item.color, 20), threeDShapeTransparency);</p>
<p class="p1"><span class="Apple-converted-space">            </span>const sideFaceColor = applyTransparency(darkenColor(item.color, 20), threeDShapeTransparency);</p>
<p class="p1"><span class="Apple-converted-space">            </span>const backFaceColor = applyTransparency(darkenColor(item.color, 30), threeDShapeTransparency * 0.8);<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = backFaceColor;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.moveTo(-s / 2 + po, -s / 2 - po); ctx.lineTo(s / 2 + po, -s / 2 - po);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(s / 2 + po, s / 2 - po); ctx.lineTo(-s / 2 + po, s / 2 - po);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.closePath(); ctx.fill(); ctx.stroke();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = applyTransparency(darkenColor(item.color, 10), threeDShapeTransparency);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.moveTo(-s / 2, s / 2); ctx.lineTo(s / 2, s / 2);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(s / 2 + po, s / 2 - po); ctx.lineTo(-s / 2 + po, s / 2 - po);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.closePath(); ctx.fill(); ctx.stroke();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = applyTransparency(darkenColor(item.color, 25), threeDShapeTransparency);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.moveTo(-s / 2, -s / 2); ctx.lineTo(-s / 2 + po, -s / 2 - po);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(-s / 2 + po, s / 2 - po); ctx.lineTo(-s / 2, s / 2);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.closePath(); ctx.fill(); ctx.stroke();</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = faceColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath(); ctx.rect(-s / 2, -s / 2, s, s); ctx.fill(); ctx.stroke();</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = topFaceColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath(); ctx.moveTo(-s / 2, -s / 2); ctx.lineTo(-s / 2 + po, -s / 2 - po); ctx.lineTo(s / 2 + po, -s / 2 - po); ctx.lineTo(s / 2, -s / 2); ctx.closePath(); ctx.fill(); ctx.stroke();</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = sideFaceColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath(); ctx.moveTo(s / 2, -s / 2); ctx.lineTo(s / 2 + po, -s / 2 - po); ctx.lineTo(s / 2 + po, s / 2 - po); ctx.lineTo(s / 2, s / 2); ctx.closePath(); ctx.fill(); ctx.stroke();</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = 1.0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.restore();</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderNetCube(item) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = netTransparency;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.translate(item.x, item.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = item.color;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.strokeStyle = mainOutlineColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineWidth = internalLineWidth;<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const u = item.unitSize;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath(); ctx.rect(-u / 2, -u * 2.5, u, u); ctx.fill(); ctx.stroke();<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath(); ctx.rect(-u * 1.5, -u * 1.5, u, u); ctx.fill(); ctx.stroke();<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath(); ctx.rect(-u / 2, -u * 1.5, u, u); ctx.fill(); ctx.stroke();<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath(); ctx.rect(u / 2, -u * 1.5, u, u); ctx.fill(); ctx.stroke();<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath(); ctx.rect(u * 1.5, -u * 1.5, u, u); ctx.fill(); ctx.stroke();<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath(); ctx.rect(-u / 2, -u * 0.5, u, u); ctx.fill(); ctx.stroke();<span class="Apple-converted-space"> </span></p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = 1.0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.restore();</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderShape3DCylinder(item) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.translate(item.x, item.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.strokeStyle = mainOutlineColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineWidth = mainOutlineWidth;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const r = item.radius;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const h = item.height;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const ellipseHeight = r * DEPTH_PERSPECTIVE_RATIO;<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const bodyColor = applyTransparency(item.color, threeDShapeTransparency);</p>
<p class="p1"><span class="Apple-converted-space">            </span>const topFaceColor = applyTransparency(lightenColor(item.color, 15), threeDShapeTransparency);</p>
<p class="p1"><span class="Apple-converted-space">            </span>const bottomFaceColor = applyTransparency(darkenColor(item.color, 15), threeDShapeTransparency * 0.9);<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = bottomFaceColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.ellipse(0, h / 2, r, ellipseHeight, 0, 0, Math.PI, true);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fill();</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = applyTransparency(darkenColor(item.color,10), threeDShapeTransparency);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.ellipse(0, h / 2, r, ellipseHeight, 0, 0, 2 * Math.PI);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fill();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.stroke();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = bodyColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.moveTo(-r, -h / 2);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(-r, h / 2);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.ellipse(0, h / 2, r, ellipseHeight, 0, Math.PI, 0, true);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(r, -h / 2);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.ellipse(0, -h / 2, r, ellipseHeight, 0, 0, Math.PI, false);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.closePath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fill();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath(); ctx.moveTo(-r, -h/2); ctx.lineTo(-r, h/2); ctx.stroke();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath(); ctx.moveTo(r, -h/2); ctx.lineTo(r, h/2); ctx.stroke();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = topFaceColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.ellipse(0, -h / 2, r, ellipseHeight, 0, 0, 2 * Math.PI);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fill();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.stroke();</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = 1.0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.restore();</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderNetCylinder(item) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = netTransparency;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.translate(item.x, item.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = item.color;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.strokeStyle = mainOutlineColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineWidth = internalLineWidth;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const r = item.radius;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const h = item.height;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const rectWidth = 2 * Math.PI * r;</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.rect(-rectWidth / 2, -h / 2, rectWidth, h);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fill();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.stroke();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const topCircleY = -h / 2 - r;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.arc(0, topCircleY, r, 0, 2 * Math.PI);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fill();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.stroke();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const bottomCircleY = h / 2 + r;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.arc(0, bottomCircleY, r, 0, 2 * Math.PI);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fill();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.stroke();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = 1.0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.restore();</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderShape3DPyramid(item) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.translate(item.x, item.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.strokeStyle = mainOutlineColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineWidth = mainOutlineWidth;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineJoin = "round";<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const base = item.baseSize;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const h = item.height;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const po = base * DEPTH_PERSPECTIVE_RATIO * 0.5;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const apexY = -h / 2;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const apexX = po / 2;<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const v1 = { x: -base / 2, y: h / 2 };<span class="Apple-converted-space">         </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const v2 = { x: base / 2, y: h / 2 }; <span class="Apple-converted-space">         </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const v3 = { x: base / 2 + po, y: h / 2 - po };<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const v4 = { x: -base / 2 + po, y: h / 2 - po };<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const apex = { x: apexX, y: apexY };</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const baseColor = applyTransparency(darkenColor(item.color, 25), threeDShapeTransparency);</p>
<p class="p1"><span class="Apple-converted-space">            </span>const faceColors = [</p>
<p class="p1"><span class="Apple-converted-space">                </span>applyTransparency(darkenColor(item.color, 20), threeDShapeTransparency * 0.85),<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>applyTransparency(darkenColor(item.color, 10), threeDShapeTransparency * 0.9), <span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>applyTransparency(lightenColor(item.color, 10), threeDShapeTransparency * 0.95),</p>
<p class="p1"><span class="Apple-converted-space">                </span>applyTransparency(item.color, threeDShapeTransparency)<span class="Apple-converted-space">                         </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>];</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = baseColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.moveTo(v1.x, v1.y); ctx.lineTo(v2.x, v2.y); ctx.lineTo(v3.x, v3.y); ctx.lineTo(v4.x, v4.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.closePath(); ctx.fill(); ctx.stroke();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = faceColors[0];</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath(); ctx.moveTo(v4.x, v4.y); ctx.lineTo(v3.x, v3.y); ctx.lineTo(apex.x, apex.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.closePath(); ctx.fill(); ctx.stroke();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = faceColors[1];</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath(); ctx.moveTo(v1.x, v1.y); ctx.lineTo(v4.x, v4.y); ctx.lineTo(apex.x, apex.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.closePath(); ctx.fill(); ctx.stroke();</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = faceColors[2];</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath(); ctx.moveTo(v2.x, v2.y); ctx.lineTo(v3.x, v3.y); ctx.lineTo(apex.x, apex.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.closePath(); ctx.fill(); ctx.stroke();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = faceColors[3];</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath(); ctx.moveTo(v1.x, v1.y); ctx.lineTo(v2.x, v2.y); ctx.lineTo(apex.x, apex.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.closePath(); ctx.fill(); ctx.stroke();</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = 1.0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineJoin = "miter";<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.restore();</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderNetPyramid(item) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = netTransparency;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.translate(item.x, item.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = item.color;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.strokeStyle = mainOutlineColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineWidth = internalLineWidth;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const base = item.baseUnitSize;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const triH = item.triangleHeight;<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.rect(-base / 2, -base / 2, base, base);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fill(); ctx.stroke();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.moveTo(-base / 2, -base / 2);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(base / 2, -base / 2); <span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(0, -base / 2 - triH); <span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.closePath(); ctx.fill(); ctx.stroke();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.moveTo(-base / 2, base / 2); <span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(base / 2, base / 2);<span class="Apple-converted-space">   </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(0, base / 2 + triH);<span class="Apple-converted-space">   </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.closePath(); ctx.fill(); ctx.stroke();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.moveTo(-base / 2, -base / 2);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(-base / 2, base / 2); <span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(-base / 2 - triH, 0); <span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.closePath(); ctx.fill(); ctx.stroke();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.moveTo(base / 2, -base / 2); <span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(base / 2, base / 2);<span class="Apple-converted-space">   </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(base / 2 + triH, 0);<span class="Apple-converted-space">   </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.closePath(); ctx.fill(); ctx.stroke();</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = 1.0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.restore();</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderShape3DCone(item) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.translate(item.x, item.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.strokeStyle = mainOutlineColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineWidth = mainOutlineWidth;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineJoin = "round";</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const r = item.radius;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const h = item.height;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const ellipseHeight = r * DEPTH_PERSPECTIVE_RATIO;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const apexY = -h / 2;<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const baseColor = applyTransparency(darkenColor(item.color, 15), threeDShapeTransparency * 0.9);</p>
<p class="p1"><span class="Apple-converted-space">            </span>const bodyColor = applyTransparency(item.color, threeDShapeTransparency);</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = baseColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.ellipse(0, h / 2, r, ellipseHeight, 0, 0, Math.PI, true);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fill();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = applyTransparency(darkenColor(item.color,10), threeDShapeTransparency);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.ellipse(0, h / 2, r, ellipseHeight, 0, 0, 2 * Math.PI);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fill();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.stroke();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = bodyColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.moveTo(-r, h / 2);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(0, apexY); <span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(r, h / 2); <span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.ellipse(0, h / 2, r, ellipseHeight, 0, 0, Math.PI, true);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.closePath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fill();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath(); ctx.moveTo(-r, h/2); ctx.lineTo(0, apexY); ctx.stroke();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath(); ctx.moveTo(r, h/2); ctx.lineTo(0, apexY); ctx.stroke();</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = 1.0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineJoin = "miter";</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.restore();</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderNetCone(item) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = netTransparency;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.translate(item.x, item.y);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = item.color;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.strokeStyle = mainOutlineColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineWidth = internalLineWidth;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const r = item.radius;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const sL = item.slantHeight;<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>let sectorAngle = 0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>if (sL &gt; 0) {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                 </span>sectorAngle = (2 * Math.PI * r) / sL;</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>sectorAngle = Math.min(sectorAngle, 2 * Math.PI - 0.001);<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const startAngle = -sectorAngle / 2 - Math.PI / 2;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const endAngle = sectorAngle / 2 - Math.PI / 2;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.moveTo(0, 0);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.arc(0, 0, sL, startAngle, endAngle);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.closePath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fill();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.stroke();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const baseCircleCenterY = -(sL + r);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.arc(0, baseCircleCenterY, r, 0, 2 * Math.PI);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fill();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.stroke();</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = 1.0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.restore();</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderShape3DRectPrism(item) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.translate(item.x, item.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.strokeStyle = mainOutlineColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineWidth = mainOutlineWidth;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineJoin = "round";</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const w = item.width;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const h = item.height;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const d = item.depth;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const po = d * DEPTH_PERSPECTIVE_RATIO;<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const frontFaceColor = applyTransparency(item.color, threeDShapeTransparency);</p>
<p class="p1"><span class="Apple-converted-space">            </span>const topFaceColor = applyTransparency(lightenColor(item.color, 15), threeDShapeTransparency);</p>
<p class="p1"><span class="Apple-converted-space">            </span>const sideFaceColor = applyTransparency(darkenColor(item.color, 15), threeDShapeTransparency);</p>
<p class="p1"><span class="Apple-converted-space">            </span>const backPartsColor = applyTransparency(darkenColor(item.color, 25), threeDShapeTransparency * 0.8);</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = backPartsColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.rect(-w / 2 + po, -h / 2 - po, w, h);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fill(); ctx.stroke();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = applyTransparency(darkenColor(item.color, 20), threeDShapeTransparency);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.moveTo(-w / 2, h / 2); <span class="Apple-converted-space">       </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(w / 2, h / 2);<span class="Apple-converted-space">         </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(w / 2 + po, h / 2 - po);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(-w / 2 + po, h / 2 - po);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.closePath(); ctx.fill(); ctx.stroke();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = sideFaceColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.moveTo(-w / 2, -h / 2);<span class="Apple-converted-space">       </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(-w / 2, h / 2); <span class="Apple-converted-space">       </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(-w / 2 + po, h / 2 - po);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(-w / 2 + po, -h / 2 - po);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.closePath(); ctx.fill(); ctx.stroke();</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = frontFaceColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.rect(-w / 2, -h / 2, w, h);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fill(); ctx.stroke();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = topFaceColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.moveTo(-w / 2, -h / 2);<span class="Apple-converted-space">       </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(w / 2, -h / 2); <span class="Apple-converted-space">       </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(w / 2 + po, -h / 2 - po);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(-w / 2 + po, -h / 2 - po);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.closePath(); ctx.fill(); ctx.stroke();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = sideFaceColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.moveTo(w / 2, -h / 2); <span class="Apple-converted-space">       </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(w / 2, h / 2);<span class="Apple-converted-space">         </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(w / 2 + po, h / 2 - po);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(w / 2 + po, -h / 2 - po);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.closePath(); ctx.fill(); ctx.stroke();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = 1.0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineJoin = "miter";</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.restore();</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderNetRectPrism(item) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = netTransparency;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.translate(item.x, item.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = item.color;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.strokeStyle = mainOutlineColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineWidth = internalLineWidth;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const w = item.rectW;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const h = item.rectH;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const d = item.rectD;<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath(); ctx.rect(-w/2, -h/2, w, h); ctx.fill(); ctx.stroke();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath(); ctx.rect(-w/2, -h/2 - d, w, d); ctx.fill(); ctx.stroke();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath(); ctx.rect(-w/2, h/2, w, d); ctx.fill(); ctx.stroke();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath(); ctx.rect(-w/2 - d, -h/2, d, h); ctx.fill(); ctx.stroke();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath(); ctx.rect(w/2, -h/2, d, h); ctx.fill(); ctx.stroke();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath(); ctx.rect(w/2 + d, -h/2, w, h); ctx.fill(); ctx.stroke();</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = 1.0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.restore();</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderShape3DTriPrism(item) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.translate(item.x, item.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.strokeStyle = mainOutlineColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineWidth = mainOutlineWidth;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineJoin = "round";</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const s = item.baseSide;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const l = item.prismLength;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const triH = (Math.sqrt(3) / 2) * s;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const po = l * DEPTH_PERSPECTIVE_RATIO;<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const frontFaceColor = applyTransparency(item.color, threeDShapeTransparency);</p>
<p class="p1"><span class="Apple-converted-space">            </span>const sideFaceColor = applyTransparency(darkenColor(item.color, 15), threeDShapeTransparency);</p>
<p class="p1"><span class="Apple-converted-space">            </span>const backFaceColor = applyTransparency(darkenColor(item.color, 25), threeDShapeTransparency * 0.8);</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const ft1 = { x: 0, y: -triH * 2/3 }; <span class="Apple-converted-space">     </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const ft2 = { x: -s / 2, y: triH * 1/3 }; <span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const ft3 = { x: s / 2, y: triH * 1/3 };<span class="Apple-converted-space">   </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const bt1 = { x: ft1.x + po, y: ft1.y - po*0.3 };<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const bt2 = { x: ft2.x + po, y: ft2.y - po*0.3 };</p>
<p class="p1"><span class="Apple-converted-space">            </span>const bt3 = { x: ft3.x + po, y: ft3.y - po*0.3 };</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = backFaceColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.moveTo(bt1.x, bt1.y); ctx.lineTo(bt2.x, bt2.y); ctx.lineTo(bt3.x, bt3.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.closePath(); ctx.fill(); ctx.stroke();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = sideFaceColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.moveTo(ft2.x, ft2.y); ctx.lineTo(ft3.x, ft3.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(bt3.x, bt3.y); ctx.lineTo(bt2.x, bt2.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.closePath(); ctx.fill(); ctx.stroke();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = applyTransparency(lightenColor(item.color,10), threeDShapeTransparency);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.moveTo(ft1.x, ft1.y); ctx.lineTo(ft2.x, ft2.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(bt2.x, bt2.y); ctx.lineTo(bt1.x, bt1.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.closePath(); ctx.fill(); ctx.stroke();</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = applyTransparency(darkenColor(item.color,5), threeDShapeTransparency);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.moveTo(ft1.x, ft1.y); ctx.lineTo(ft3.x, ft3.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(bt3.x, bt3.y); ctx.lineTo(bt1.x, bt1.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.closePath(); ctx.fill(); ctx.stroke();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = frontFaceColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.moveTo(ft1.x, ft1.y); ctx.lineTo(ft2.x, ft2.y); ctx.lineTo(ft3.x, ft3.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.closePath(); ctx.fill(); ctx.stroke();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = 1.0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineJoin = "miter";</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.restore();</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderNetTriPrism(item) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = netTransparency;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.translate(item.x, item.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = item.color;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.strokeStyle = mainOutlineColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineWidth = internalLineWidth;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const s = item.baseSide;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const l = item.prismLength;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const triH = (Math.sqrt(3) / 2) * s;<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath(); ctx.rect(-s/2, -l/2, s, l); ctx.fill(); ctx.stroke();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath(); ctx.rect(-s/2 - s, -l/2, s, l); ctx.fill(); ctx.stroke();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath(); ctx.rect(s/2, -l/2, s, l); ctx.fill(); ctx.stroke();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.moveTo(-s/2, -l/2);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(s/2, -l/2); <span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(0, -l/2 - triH);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.closePath(); ctx.fill(); ctx.stroke();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.moveTo(-s/2, l/2); <span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(s/2, l/2);<span class="Apple-converted-space">   </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(0, l/2 + triH);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.closePath(); ctx.fill(); ctx.stroke();</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = 1.0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.restore();</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderAlgebraTile(item) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.translate(item.x, item.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>applyShadow();<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = geometricShapeTransparency;<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = item.color;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.strokeStyle = mainOutlineColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineWidth = mainOutlineWidth;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.rect(-item.width / 2, -item.height / 2, item.width, item.height);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fill();</p>
<p class="p1"><span class="Apple-converted-space">            </span>clearShadow();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.stroke();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = mainOutlineColor;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const fontSize = Math.min(item.width, item.height) * 0.5;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.font = `${fontSize}px Arial`;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.textAlign = 'center';</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.textBaseline = 'middle';</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillText(item.label, 0, 0);</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = 1.0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.restore();</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderTenFrame(item) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.translate(item.x, item.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>applyShadow();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = geometricShapeTransparency;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.strokeStyle = mainOutlineColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineWidth = mainOutlineWidth;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const totalWidth = item.cols * item.cellWidth;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const totalHeight = item.rows * item.cellHeight;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.rect(-totalWidth / 2, -totalHeight / 2, totalWidth, totalHeight);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.stroke();</p>
<p class="p1"><span class="Apple-converted-space">            </span>clearShadow();<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineWidth = internalLineWidth;</p>
<p class="p1"><span class="Apple-converted-space">            </span>for (let r = 0; r &lt; item.rows; r++) {</p>
<p class="p1"><span class="Apple-converted-space">                </span>for (let c = 0; c &lt; item.cols; c++) {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>const cellX = -totalWidth / 2 + c * item.cellWidth;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>const cellY = -totalHeight / 2 + r * item.cellHeight;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">                    </span>ctx.rect(cellX, cellY, item.cellWidth, item.cellHeight);</p>
<p class="p1"><span class="Apple-converted-space">                    </span>ctx.stroke();</p>
<p class="p1"><span class="Apple-converted-space">                </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = 1.0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.restore();</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderMultiplicationArray(item) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.translate(item.x, item.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>applyShadow();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = geometricShapeTransparency;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.strokeStyle = mainOutlineColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineWidth = mainOutlineWidth;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const totalWidth = item.cols * item.cellWidth;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const totalHeight = item.rows * item.cellHeight;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.rect(-totalWidth / 2, -totalHeight / 2, totalWidth, totalHeight);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.stroke();</p>
<p class="p1"><span class="Apple-converted-space">            </span>clearShadow();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineWidth = internalLineWidth;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = arrayCellColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>for (let r = 0; r &lt; item.rows; r++) {</p>
<p class="p1"><span class="Apple-converted-space">                </span>for (let c = 0; c &lt; item.cols; c++) {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>const cellX = -totalWidth / 2 + c * item.cellWidth;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>const cellY = -totalHeight / 2 + r * item.cellHeight;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">                    </span>ctx.rect(cellX, cellY, item.cellWidth, item.cellHeight);</p>
<p class="p1"><span class="Apple-converted-space">                    </span>ctx.fill();</p>
<p class="p1"><span class="Apple-converted-space">                    </span>ctx.stroke();</p>
<p class="p1"><span class="Apple-converted-space">                </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = 1.0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.restore();</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderHundredsChart(item) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.translate(item.x, item.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>applyShadow();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = geometricShapeTransparency;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.strokeStyle = hundredsChartGridColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineWidth = mainOutlineWidth;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const cols = 10;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const rows = 10;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const totalWidth = cols * item.cellWidth;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const totalHeight = rows * item.cellHeight;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.rect(-totalWidth / 2, -totalHeight / 2, totalWidth, totalHeight);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.stroke();</p>
<p class="p1"><span class="Apple-converted-space">            </span>clearShadow();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineWidth = internalLineWidth;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = hundredsChartTextColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const fontSize = item.cellHeight * 0.4;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.font = `${fontSize}px Arial`;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.textAlign = 'center';</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.textBaseline = 'middle';</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>for (let r = 0; r &lt; rows; r++) {</p>
<p class="p1"><span class="Apple-converted-space">                </span>for (let c = 0; c &lt; cols; c++) {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>const number = r * cols + c + 1;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>const cellX = -totalWidth / 2 + c * item.cellWidth;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>const cellY = -totalHeight / 2 + r * item.cellHeight;</p>
<p class="p2"><span class="Apple-converted-space">                    </span></p>
<p class="p1"><span class="Apple-converted-space">                    </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">                    </span>ctx.rect(cellX, cellY, item.cellWidth, item.cellHeight);</p>
<p class="p1"><span class="Apple-converted-space">                    </span>ctx.stroke();</p>
<p class="p2"><span class="Apple-converted-space">                    </span></p>
<p class="p1"><span class="Apple-converted-space">                    </span>ctx.fillText(number.toString(), cellX + item.cellWidth / 2, cellY + item.cellHeight / 2);</p>
<p class="p1"><span class="Apple-converted-space">                </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = 1.0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.restore();</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderBarGraph(item) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.translate(item.x, item.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>applyShadow();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = geometricShapeTransparency;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const padding = baseSize * 1.5;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const graphWidth = item.width - 2 * padding;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const graphHeight = item.height - 2 * padding;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const barCount = item.data.length;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const barSpacing = graphWidth * 0.1 / (barCount -1 &gt; 0 ? barCount -1 : 1) ;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const barWidth = (graphWidth - (barCount - 1) * barSpacing) / barCount;</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.strokeStyle = item.axisColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineWidth = mainOutlineWidth;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.moveTo(-item.width/2 + padding, -item.height/2 + padding);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(-item.width/2 + padding, item.height/2 - padding); <span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(item.width/2 - padding, item.height/2 - padding);<span class="Apple-converted-space">   </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.stroke();</p>
<p class="p1"><span class="Apple-converted-space">            </span>clearShadow();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = item.barColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.textAlign = "center";</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.textBaseline = "top";</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.font = `${baseSize*0.8}px Arial`;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>for (let i = 0; i &lt; barCount; i++) {</p>
<p class="p1"><span class="Apple-converted-space">                </span>const barHeight = (item.data[i].value / item.maxValue) * graphHeight;</p>
<p class="p1"><span class="Apple-converted-space">                </span>const barX = -item.width/2 + padding + i * (barWidth + barSpacing);</p>
<p class="p1"><span class="Apple-converted-space">                </span>const barY = item.height/2 - padding - barHeight;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.rect(barX, barY, barWidth, barHeight);</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.fill();</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.stroke();<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.fillStyle = item.labelColor;</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.fillText(item.data[i].label, barX + barWidth / 2, item.height/2 - padding + baseSize*0.3);</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.textAlign = "right";</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.textBaseline = "middle";</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillText("0", -item.width/2 + padding - baseSize*0.3, item.height/2 - padding);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillText(item.maxValue.toString(), -item.width/2 + padding - baseSize*0.3, -item.height/2 + padding);</p>
<p class="p2"><br></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = 1.0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.restore();</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderCoordinatePlane(item) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.translate(item.x, item.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>applyShadow();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = geometricShapeTransparency;</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const padding = baseSize * 2;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const graphWidth = item.width - 2 * padding;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const graphHeight = item.height - 2 * padding;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.strokeStyle = item.axisColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineWidth = mainOutlineWidth;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.rect(-item.width/2, -item.height/2, item.width, item.height);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.stroke();</p>
<p class="p1"><span class="Apple-converted-space">            </span>clearShadow();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const originX = 0;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const originY = 0;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const xRange = item.xMax - item.xMin;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const yRange = item.yMax - item.yMin;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const xPixelPerUnit = graphWidth / xRange;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const yPixelPerUnit = graphHeight / yRange;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.strokeStyle = item.axisColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineWidth = internalLineWidth;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.moveTo(originX - graphWidth/2, originY); ctx.lineTo(originX + graphWidth/2, originY);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.moveTo(originX, originY - graphHeight/2); ctx.lineTo(originX, originY + graphHeight/2);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.stroke();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.strokeStyle = item.gridColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = item.axisColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.font = `${baseSize*0.7}px Arial`;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.textAlign = "center";</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.textBaseline = "top";</p>
<p class="p1"><span class="Apple-converted-space">            </span>for (let i = item.xMin; i &lt;= item.xMax; i++) {</p>
<p class="p1"><span class="Apple-converted-space">                </span>const xPos = originX + (i - item.xMin - xRange/2) * xPixelPerUnit;</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.beginPath(); ctx.moveTo(xPos, originY - graphHeight/2); ctx.lineTo(xPos, originY + graphHeight/2); ctx.stroke();</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (i !== 0) ctx.fillText(i.toString(), xPos, originY + baseSize*0.3);</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.textAlign = "right";</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.textBaseline = "middle";</p>
<p class="p1"><span class="Apple-converted-space">            </span>for (let i = item.yMin; i &lt;= item.yMax; i++) {</p>
<p class="p1"><span class="Apple-converted-space">                </span>const yPos = originY - (i - item.yMin - yRange/2) * yPixelPerUnit;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.beginPath(); ctx.moveTo(originX - graphWidth/2, yPos); ctx.lineTo(originX + graphWidth/2, yPos); ctx.stroke();</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (i !== 0) ctx.fillText(i.toString(), originX - baseSize*0.3, yPos);</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillText("0", originX - baseSize*0.3, originY + baseSize*0.3);</p>
<p class="p2"><br></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.strokeStyle = item.lineColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineWidth = mainOutlineWidth;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>let firstPoint = true;</p>
<p class="p1"><span class="Apple-converted-space">            </span>for (let gx = item.xMin; gx &lt;= item.xMax; gx += 0.1) {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>const gy = item.lineFunction(gx);</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (gy &gt;= item.yMin &amp;&amp; gy &lt;= item.yMax) {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>const canvasX = originX + (gx - item.xMin - xRange/2) * xPixelPerUnit;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>const canvasY = originY - (gy - item.yMin - yRange/2) * yPixelPerUnit;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                    </span>if (firstPoint) {</p>
<p class="p1"><span class="Apple-converted-space">                        </span>ctx.moveTo(canvasX, canvasY);</p>
<p class="p1"><span class="Apple-converted-space">                        </span>firstPoint = false;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>} else {</p>
<p class="p1"><span class="Apple-converted-space">                        </span>ctx.lineTo(canvasX, canvasY);</p>
<p class="p1"><span class="Apple-converted-space">                    </span>}</p>
<p class="p1"><span class="Apple-converted-space">                </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.stroke();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = 1.0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.restore();</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderScatterPlot(item) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.translate(item.x, item.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>applyShadow();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = geometricShapeTransparency;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const padding = baseSize * 2.5;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const graphWidth = item.width - 2 * padding;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const graphHeight = item.height - 2 * padding;</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.strokeStyle = item.axisColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineWidth = mainOutlineWidth;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.rect(-item.width/2, -item.height/2, item.width, item.height);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.stroke();</p>
<p class="p1"><span class="Apple-converted-space">            </span>clearShadow();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.moveTo(-item.width/2 + padding, -item.height/2 + padding);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(-item.width/2 + padding, item.height/2 - padding); <span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineTo(item.width/2 - padding, item.height/2 - padding);<span class="Apple-converted-space">   </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.stroke();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = item.axisColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.font = `${baseSize*0.9}px Arial`;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.textAlign = "center";</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillText(item.xLabel, 0, item.height/2 - padding + baseSize * 1.2);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.translate(-item.width/2 + padding - baseSize * 1.2, 0);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.rotate(-Math.PI/2);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillText(item.yLabel, 0, 0);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.restore();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = item.pointColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const xPixelPerUnit = graphWidth / item.xMax;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const yPixelPerUnit = graphHeight / item.yMax;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const pointRadius = baseSize * 0.25;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>item.dataPoints.forEach(p =&gt; {</p>
<p class="p1"><span class="Apple-converted-space">                </span>const canvasX = -item.width/2 + padding + p.x * xPixelPerUnit;</p>
<p class="p1"><span class="Apple-converted-space">                </span>const canvasY = item.height/2 - padding - p.y * yPixelPerUnit;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.arc(canvasX, canvasY, pointRadius, 0, 2 * Math.PI);</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.fill();</p>
<p class="p1"><span class="Apple-converted-space">            </span>});</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.font = `${baseSize*0.7}px Arial`;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.textAlign = "center";</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.textBaseline = "top";</p>
<p class="p1"><span class="Apple-converted-space">            </span>for(let i = 0; i &lt;= item.xMax; i+=2) {</p>
<p class="p1"><span class="Apple-converted-space">                </span>const xPos = -item.width/2 + padding + i * xPixelPerUnit;</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.fillText(i.toString(), xPos, item.height/2 - padding + baseSize*0.2);</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.textAlign = "right";</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.textBaseline = "middle";</p>
<p class="p1"><span class="Apple-converted-space">            </span>for(let i = 0; i &lt;= item.yMax; i+=2) {</p>
<p class="p1"><span class="Apple-converted-space">                </span>const yPos = item.height/2 - padding - i * yPixelPerUnit;</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.fillText(i.toString(), -item.width/2 + padding - baseSize*0.2, yPos);</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p2"><br></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = 1.0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.restore();</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderNumberBond(item) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.translate(item.x, item.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>applyShadow();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = geometricShapeTransparency;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.strokeStyle = mainOutlineColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineWidth = mainOutlineWidth;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = item.color;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const r = item.radius;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const partRadius = r * 0.7;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const yOffset = r * 1.8;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const xOffset = r * 1.5;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>// Draw circles</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath(); ctx.arc(0, 0, r, 0, 2 * Math.PI); ctx.fill(); ctx.stroke(); // Whole</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath(); ctx.arc(-xOffset, yOffset, partRadius, 0, 2 * Math.PI); ctx.fill(); ctx.stroke(); // Part 1</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath(); ctx.arc(xOffset, yOffset, partRadius, 0, 2 * Math.PI); ctx.fill(); ctx.stroke(); // Part 2</p>
<p class="p1"><span class="Apple-converted-space">            </span>clearShadow();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>// Draw connecting lines</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath(); ctx.moveTo(0, r); ctx.lineTo(-xOffset, yOffset - partRadius); ctx.stroke();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath(); ctx.moveTo(0, r); ctx.lineTo(xOffset, yOffset - partRadius); ctx.stroke();</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>// Draw numbers and operator</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = mainOutlineColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.font = `${r * 0.8}px Arial`;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.textAlign = 'center';</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.textBaseline = 'middle';</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillText(item.whole.toString(), 0, 0);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.font = `${partRadius * 0.8}px Arial`;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillText(item.part1.toString(), -xOffset, yOffset);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillText(item.part2.toString(), xOffset, yOffset);</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const operator = item.type === 'additionNumberBond' ? '+' : '×';</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.font = `${r * 0.6}px Arial`;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillText(operator, 0, yOffset / 2);</p>
<p class="p2"><br></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = 1.0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.restore();</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>function getPrimeFactors(n) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>const factors = [];</p>
<p class="p1"><span class="Apple-converted-space">            </span>let divisor = 2;</p>
<p class="p1"><span class="Apple-converted-space">            </span>while (n &gt;= 2) {</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (n % divisor === 0) {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>factors.push(divisor);</p>
<p class="p1"><span class="Apple-converted-space">                    </span>n = n / divisor;</p>
<p class="p1"><span class="Apple-converted-space">                </span>} else {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>divisor++;</p>
<p class="p1"><span class="Apple-converted-space">                </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>return factors;</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>function drawFactorTreeNodes(num, currentX, currentY, nodeRadius, level, maxDepth, treeColor) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>if (level &gt; maxDepth) return {minX: currentX, maxX: currentX, minY: currentY, maxY: currentY + nodeRadius*2};<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = treeColor; // Use passed tree color for each node</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.beginPath();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.arc(currentX, currentY, nodeRadius, 0, 2 * Math.PI);</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fill(); ctx.stroke();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillStyle = mainOutlineColor; // Text color</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.fillText(num.toString(), currentX, currentY);</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>let minX = currentX - nodeRadius, maxX = currentX + nodeRadius;</p>
<p class="p1"><span class="Apple-converted-space">            </span>let minY = currentY - nodeRadius, maxY = currentY + nodeRadius;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>if (num &lt;= 1) return {minX, maxX, minY, maxY};<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>let factor1 = -1, factor2 = -1;</p>
<p class="p1"><span class="Apple-converted-space">            </span>for (let i = 2; i * i &lt;= num; i++) {</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (num % i === 0) {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>factor1 = i;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>factor2 = num / i;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>break;</p>
<p class="p1"><span class="Apple-converted-space">                </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>if (factor1 !== -1) {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>const childY = currentY + nodeRadius * 3;</p>
<p class="p1"><span class="Apple-converted-space">                </span>const spread = nodeRadius * 3 * Math.pow(0.8, level);<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.beginPath(); ctx.moveTo(currentX, currentY + nodeRadius); ctx.lineTo(currentX - spread, childY - nodeRadius); ctx.stroke();</p>
<p class="p1"><span class="Apple-converted-space">                </span>const leftChildBounds = drawFactorTreeNodes(factor1, currentX - spread, childY, nodeRadius, level + 1, maxDepth, treeColor);</p>
<p class="p1"><span class="Apple-converted-space">                </span>minX = Math.min(minX, leftChildBounds.minX); maxX = Math.max(maxX, leftChildBounds.maxX);</p>
<p class="p1"><span class="Apple-converted-space">                </span>minY = Math.min(minY, leftChildBounds.minY); maxY = Math.max(maxY, leftChildBounds.maxY);</p>
<p class="p2"><br></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.beginPath(); ctx.moveTo(currentX, currentY + nodeRadius); ctx.lineTo(currentX + spread, childY - nodeRadius); ctx.stroke();</p>
<p class="p1"><span class="Apple-converted-space">                </span>const rightChildBounds = drawFactorTreeNodes(factor2, currentX + spread, childY, nodeRadius, level + 1, maxDepth, treeColor);</p>
<p class="p1"><span class="Apple-converted-space">                </span>minX = Math.min(minX, rightChildBounds.minX); maxX = Math.max(maxX, rightChildBounds.maxX);</p>
<p class="p1"><span class="Apple-converted-space">                </span>minY = Math.min(minY, rightChildBounds.minY); maxY = Math.max(maxY, rightChildBounds.maxY);</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">             </span>return {minX, maxX, minY, maxY};</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>function renderFactorTree(item) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.translate(item.x, item.y);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>applyShadow();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = geometricShapeTransparency;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.strokeStyle = mainOutlineColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.lineWidth = internalLineWidth;</p>
<p class="p1"><span class="Apple-converted-space">            </span>// Note: fillStyle for nodes is now set inside drawFactorTreeNodes</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.font = `${item.nodeRadius * 0.8}px Arial`;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.textAlign = 'center';</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.textBaseline = 'middle';</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const bounds = drawFactorTreeNodes(item.number, 0, -item.nodeRadius * 3, item.nodeRadius, 0, 4, item.color);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>item.aabb = {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>minX: item.x + bounds.minX, maxX: item.x + bounds.maxX,<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>minY: item.y + bounds.minY, maxY: item.y + bounds.maxY<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>};</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>clearShadow();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.globalAlpha = 1.0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.restore();</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p2"><span class="Apple-converted-space">        </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>// --- Main drawing function for all elements ---</p>
<p class="p1"><span class="Apple-converted-space">        </span>function drawScene() {</p>
<p class="p1"><span class="Apple-converted-space">            </span>canvas.style.backgroundColor = canvasBgColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.clearRect(0, 0, canvas.width, canvas.height);</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>allDrawableItems.forEach(item =&gt; {</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (!selectedItems.includes(item)) {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                    </span>drawItem(item);</p>
<p class="p1"><span class="Apple-converted-space">                </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>});</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>selectedItems.forEach(item =&gt; {</p>
<p class="p1"><span class="Apple-converted-space">                </span>drawItem(item);</p>
<p class="p1"><span class="Apple-converted-space">                </span>highlightItem(item);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>});</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>if (lassoRect.active) {</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.save();</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.strokeStyle = 'blue';</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.lineWidth = 1;</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.setLineDash([5, 5]);</p>
<p class="p1"><span class="Apple-converted-space">                </span>const rectX = Math.min(lassoRect.startX, lassoRect.currentX);</p>
<p class="p1"><span class="Apple-converted-space">                </span>const rectY = Math.min(lassoRect.startY, lassoRect.currentY);</p>
<p class="p1"><span class="Apple-converted-space">                </span>const rectWidth = Math.abs(lassoRect.startX - lassoRect.currentX);</p>
<p class="p1"><span class="Apple-converted-space">                </span>const rectHeight = Math.abs(lassoRect.startY - lassoRect.currentY);</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.strokeRect(rectX, rectY, rectWidth, rectHeight);</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.restore();</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>if (dice.isAnimating) {</p>
<p class="p1"><span class="Apple-converted-space">                </span>const now = Date.now();</p>
<p class="p1"><span class="Apple-converted-space">                </span>const elapsedTime = now - dice.animationStartTime;</p>
<p class="p1"><span class="Apple-converted-space">                </span>const animationDuration = 2000;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>const faceChangeInterval = 200;<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                </span>if (elapsedTime &lt; animationDuration) {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>if (now - dice.lastFaceChangeTime &gt; faceChangeInterval) {</p>
<p class="p1"><span class="Apple-converted-space">                        </span>dice.displayFaceValue = Math.floor(Math.random() * 6) + 1;</p>
<p class="p1"><span class="Apple-converted-space">                        </span>dice.lastFaceChangeTime = now;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>}</p>
<p class="p1"><span class="Apple-converted-space">                    </span>requestAnimationFrame(drawScene);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>} else {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>dice.isAnimating = false;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>dice.currentFaceValue = dice.targetFaceValue;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>dice.displayFaceValue = dice.targetFaceValue;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                    </span>drawScene();<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>if (spinner.isSpinning) {</p>
<p class="p1"><span class="Apple-converted-space">                </span>const now = Date.now();</p>
<p class="p1"><span class="Apple-converted-space">                </span>const elapsedTime = now - spinner.animationStartTime;</p>
<p class="p1"><span class="Apple-converted-space">                </span>const overlayDuration = 200;</p>
<p class="p2"><span class="Apple-converted-space">                </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>if (elapsedTime &lt; spinner.spinDuration + overlayDuration) {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>if (elapsedTime &gt; overlayDuration) {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                        </span>const spinElapsedTime = elapsedTime - overlayDuration;</p>
<p class="p1"><span class="Apple-converted-space">                        </span>const progress = spinElapsedTime / spinner.spinDuration;</p>
<p class="p1"><span class="Apple-converted-space">                        </span>const easedProgress = 1 - Math.pow(1 - progress, 4);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                        </span>spinner.currentAngle = spinner.initialAngleForSpin + (spinner.targetAngle - spinner.initialAngleForSpin) * easedProgress;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>}</p>
<p class="p1"><span class="Apple-converted-space">                    </span>requestAnimationFrame(drawScene);</p>
<p class="p1"><span class="Apple-converted-space">                </span>} else {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>spinner.isSpinning = false;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>spinner.currentAngle = spinner.targetAngle;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                    </span>drawScene();<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>function drawItem(item) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>if (!item) return;</p>
<p class="p1"><span class="Apple-converted-space">            </span>if (item.type === 'rod') renderRod(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'flat') renderFlat(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'block1000') renderBlock1000(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'cube') renderUnitCube(item);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'circle') render2DCircle(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'patternTriangle') renderPatternTriangle(item);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'patternHexagon') renderPatternHexagon(item);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'patternSquare') renderPatternSquare(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'patternTrapezoid') renderPatternTrapezoid(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'patternBlueRhombus') renderPatternRhombus(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'patternTanRhombus') renderPatternRhombus(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'clock') renderClock(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'numLineRegular') renderNumLineRegular(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'numLineInteger') renderNumLineInteger(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'numLineFraction') renderNumLineFraction(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'numLineDecimal') renderNumLineDecimal(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'dice') renderDice(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'fractionStripEighths') renderFractionStripEighths(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'fractionCircleQuarters') renderFractionCircleQuarters(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'spinner') renderSpinner(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'cuisenaireRod') renderCuisenaireRod(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'shape3DCube') renderShape3DCube(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'netCube') renderNetCube(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'shape3DCylinder') renderShape3DCylinder(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'netCylinder') renderNetCylinder(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'shape3DPyramid') renderShape3DPyramid(item);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'netPyramid') renderNetPyramid(item);<span class="Apple-converted-space">       </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'shape3DCone') renderShape3DCone(item);<span class="Apple-converted-space">     </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'netCone') renderNetCone(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'shape3DRectPrism') renderShape3DRectPrism(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'netRectPrism') renderNetRectPrism(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'shape3DTriPrism') renderShape3DTriPrism(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'netTriPrism') renderNetTriPrism(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'algebraTile') renderAlgebraTile(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'tenFrame') renderTenFrame(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'multiplicationArray') renderMultiplicationArray(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'hundredsChart') renderHundredsChart(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'barGraph') renderBarGraph(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'coordinatePlane') renderCoordinatePlane(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'scatterPlot') renderScatterPlot(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'additionNumberBond' || item.type === 'multiplicationNumberBond') renderNumberBond(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'factorTree') renderFactorTree(item);</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>function highlightItem(item) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>if (!item) return;</p>
<p class="p1"><span class="Apple-converted-space">            </span>if (['shape3DCube', 'netCube', 'shape3DCylinder', 'netCylinder',<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                 </span>'shape3DPyramid', 'netPyramid', 'shape3DCone', 'netCone',</p>
<p class="p1"><span class="Apple-converted-space">                 </span>'shape3DRectPrism', 'netRectPrism', 'shape3DTriPrism', 'netTriPrism',<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                 </span>'algebraTile', 'tenFrame', 'multiplicationArray', 'hundredsChart',</p>
<p class="p1"><span class="Apple-converted-space">                 </span>'barGraph', 'coordinatePlane', 'scatterPlot',<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                 </span>'additionNumberBond', 'multiplicationNumberBond', 'factorTree' ].includes(item.type)) {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>if (selectedItems.length === 1 &amp;&amp; selectedItems[0] === item &amp;&amp; rotatableTypes.includes(item.type) &amp;&amp; item.type !== 'spinner') {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>ctx.save();</p>
<p class="p1"><span class="Apple-converted-space">                    </span>ctx.translate(item.x, item.y);</p>
<p class="p1"><span class="Apple-converted-space">                    </span>if (item.angle) ctx.rotate(item.angle);</p>
<p class="p1"><span class="Apple-converted-space">                    </span>ctx.rotate(-item.angle);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                    </span>ctx.font = `${rotationHandleSize}px Arial`;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';</p>
<p class="p1"><span class="Apple-converted-space">                    </span>ctx.textAlign = 'center';</p>
<p class="p1"><span class="Apple-converted-space">                    </span>ctx.textBaseline = 'middle';</p>
<p class="p1"><span class="Apple-converted-space">                    </span>let itemHeightForHandle = item.height || (item.radius ? item.radius * 2 : 0) || item.size || 0;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>if(item.type === 'patternTriangle') itemHeightForHandle = item.sideLength * (Math.sqrt(3)/2);</p>
<p class="p1"><span class="Apple-converted-space">                    </span>if(item.type === 'patternHexagon') itemHeightForHandle = item.sideLength * Math.sqrt(3);</p>
<p class="p1"><span class="Apple-converted-space">                     </span>if (item.type === 'patternBlueRhombus' || item.type === 'patternTanRhombus') {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                        </span>const angleRad = (item.angleDeg || 0) * Math.PI / 180;</p>
<p class="p1"><span class="Apple-converted-space">                        </span>const d2 = (item.sideLength || 0) * 2 * Math.cos(angleRad / 2);</p>
<p class="p1"><span class="Apple-converted-space">                        </span>itemHeightForHandle = (item.angleDeg === 60) ? (item.sideLength || 0) * Math.sqrt(3) : d2;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>}</p>
<p class="p1"><span class="Apple-converted-space">                    </span>const handleYPos = -itemHeightForHandle/2 - rotationHandleOffset;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                    </span>ctx.fillText(rotationHandleSymbol, 0, handleYPos);</p>
<p class="p1"><span class="Apple-converted-space">                    </span>ctx.restore();</p>
<p class="p1"><span class="Apple-converted-space">                </span>}</p>
<p class="p1"><span class="Apple-converted-space">                </span>return;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p2"><br></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.save();</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.translate(item.x, item.y);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>if (item.angle &amp;&amp; rotatableTypes.includes(item.type) &amp;&amp; item.type !== 'spinner') {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.rotate(item.angle);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>let itemHeightForHandle = 0;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>if (item.type === 'circle' || item.type === 'clock' || item.type === 'fractionCircleQuarters' || item.type === 'spinner') {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>itemHeightForHandle = (item.radius || 0) * 2;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>} else if (item.type === 'patternSquare' || item.type === 'patternTriangle' || item.type === 'patternHexagon') {</p>
<p class="p1"><span class="Apple-converted-space">                 </span>itemHeightForHandle = item.sideLength || 0;</p>
<p class="p1"><span class="Apple-converted-space">                 </span>if(item.type === 'patternTriangle') itemHeightForHandle = item.sideLength * (Math.sqrt(3)/2);</p>
<p class="p1"><span class="Apple-converted-space">                 </span>if(item.type === 'patternHexagon') itemHeightForHandle = item.sideLength * Math.sqrt(3);</p>
<p class="p1"><span class="Apple-converted-space">            </span>} else if (item.type === 'patternTrapezoid') {</p>
<p class="p1"><span class="Apple-converted-space">                </span>itemHeightForHandle = item.height || 0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>} else if (item.type === 'patternBlueRhombus' || item.type === 'patternTanRhombus') {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>const angleRad = (item.angleDeg || 0) * Math.PI / 180;</p>
<p class="p1"><span class="Apple-converted-space">                </span>const d2 = (item.sideLength || 0) * 2 * Math.cos(angleRad / 2);</p>
<p class="p1"><span class="Apple-converted-space">                </span>itemHeightForHandle = (item.angleDeg === 60) ? (item.sideLength || 0) * Math.sqrt(3) : d2;</p>
<p class="p1"><span class="Apple-converted-space">            </span>} else if (item.type === 'dice') {</p>
<p class="p1"><span class="Apple-converted-space">                </span>itemHeightForHandle = item.size || 0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p2"><br></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>if (selectedItems.length === 1 &amp;&amp; selectedItems[0] === item &amp;&amp; rotatableTypes.includes(item.type) &amp;&amp; item.type !== 'spinner' &amp;&amp; itemHeightForHandle &gt; 0) {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.save();<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.rotate(-item.angle);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.font = `${rotationHandleSize}px Arial`;</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.textAlign = 'center';</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.textBaseline = 'middle';</p>
<p class="p1"><span class="Apple-converted-space">                </span>const handleYPos = -itemHeightForHandle/2 - rotationHandleOffset;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.fillText(rotationHandleSymbol, 0, handleYPos);</p>
<p class="p1"><span class="Apple-converted-space">                </span>ctx.restore();</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>ctx.restore();</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><span class="Apple-converted-space">        </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>// --- Function to initialize or update all shapes for layout ---</p>
<p class="p1"><span class="Apple-converted-space">        </span>function initializeShapesInLayout() {</p>
<p class="p1"><span class="Apple-converted-space">            </span>const baseTenRow = [unitCube, rod, flat, block1000];</p>
<p class="p1"><span class="Apple-converted-space">            </span>const patternBlockRow = [patternHexagon, patternTrapezoid, patternTriangle, patternBlueRhombus, patternSquare, patternTanRhombus];</p>
<p class="p1"><span class="Apple-converted-space">            </span>const utilityRow = [circle, clock, dice, spinner];<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const numLineRow = [numLineRegular, numLineInteger, numLineFraction, numLineDecimal];</p>
<p class="p1"><span class="Apple-converted-space">            </span>const fractionToolsRow = [fractionStripEighths, fractionCircleQuarters];</p>
<p class="p1"><span class="Apple-converted-space">            </span>const cuisenaireRodsRow = [...cuisenaireRods];<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const threeDShapeNetRow1 = [ shape3DCube, netCube, shape3DCylinder, netCylinder, shape3DPyramid, netPyramid];</p>
<p class="p1"><span class="Apple-converted-space">            </span>const threeDShapeNetRow2 = [ shape3DCone, netCone, shape3DRectPrism, netRectPrism, shape3DTriPrism, netTriPrism ];</p>
<p class="p1"><span class="Apple-converted-space">            </span>const algebraTileRow = [ algXSquaredPositive, algXPositive, algUnitPositive, algXSquaredNegative, algXNegative, algUnitNegative];</p>
<p class="p1"><span class="Apple-converted-space">            </span>const otherToolsRow = [tenFrame, multiplicationArray, hundredsChart];<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const graphingToolsRow = [barGraph, coordinatePlane, scatterPlot];<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const numberTheoryToolsRow = [additionNumberBond, multiplicationNumberBond, factorTree];</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const spacing = baseSize * 0.3;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const rowSpacing = baseSize * 3.5;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const numLineWidth = baseSize * 15;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const patternBlockUnit = patternBlockUnitSide;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const fractionToolSize = baseSize * 8;</p>
<p class="p2"><br></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>// Initialize Base Ten Blocks - Row 1</p>
<p class="p1"><span class="Apple-converted-space">            </span>let totalWidthRow1 = 0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>baseTenRow.forEach(shape =&gt; {</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (shape.type === 'cube') { shape.size = baseSize; shape.color = unitCubeColor; totalWidthRow1 += shape.size;}</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (shape.type === 'rod') { shape.length = rodLengthUnits * baseSize; shape.height = baseSize; shape.depth = baseSize; shape.color = rodColor; totalWidthRow1 += shape.length;}</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (shape.type === 'flat') { shape.sideLength = flatSideUnits * baseSize; shape.thickness = baseSize; shape.color = flatColor; totalWidthRow1 += shape.sideLength;}</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (shape.type === 'block1000') { shape.sideLength = block1000SideUnits * baseSize; shape.color = block1000Color; totalWidthRow1 += shape.sideLength;}</p>
<p class="p1"><span class="Apple-converted-space">            </span>});</p>
<p class="p1"><span class="Apple-converted-space">            </span>totalWidthRow1 += (baseTenRow.length - 1) * spacing;</p>
<p class="p1"><span class="Apple-converted-space">            </span>let currentXRow1 = (totalWidthRow1 &gt; canvas.width) ? baseSize * 0.1 : (canvas.width - totalWidthRow1) / 2;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const yPosRow1 = canvas.height * 0.03;<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>baseTenRow.forEach(shape =&gt; {</p>
<p class="p1"><span class="Apple-converted-space">                </span>let w = 0;</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (shape.type === 'cube') w = shape.size; else if (shape.type === 'rod') w = shape.length; else if (shape.type === 'flat' || shape.type === 'block1000') w = shape.sideLength;</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (!shape.hasBeenDragged || oldCanvasWidth !== canvas.width || oldCanvasHeight !== canvas.height) {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>shape.x = currentXRow1 + w / 2; shape.y = yPosRow1;</p>
<p class="p1"><span class="Apple-converted-space">                </span>} currentXRow1 += w + spacing;</p>
<p class="p1"><span class="Apple-converted-space">            </span>});</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>// Initialize New Pattern Blocks - Row 2</p>
<p class="p1"><span class="Apple-converted-space">            </span>patternHexagon.sideLength = patternBlockUnit; patternHexagon.color = patternHexagonColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>patternTrapezoid.shortBase = patternBlockUnit; patternTrapezoid.longBase = patternBlockUnit * 2; patternTrapezoid.height = patternBlockUnit * Math.sqrt(3)/2; patternTrapezoid.color = patternTrapezoidColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>patternTriangle.sideLength = patternBlockUnit; patternTriangle.color = patternTriangleColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>patternBlueRhombus.sideLength = patternBlockUnit; patternBlueRhombus.angleDeg = 60; patternBlueRhombus.color = patternBlueRhombusColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>patternSquare.sideLength = patternBlockUnit; patternSquare.color = patternSquareColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>patternTanRhombus.sideLength = patternBlockUnit; patternTanRhombus.angleDeg = 30; patternTanRhombus.color = patternTanRhombusColor;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>let totalWidthRow2 = 0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>patternBlockRow.forEach(s =&gt; {</p>
<p class="p1"><span class="Apple-converted-space">                </span>if(s.type === 'patternHexagon') totalWidthRow2 += s.sideLength * 2;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>else if(s.type === 'patternTrapezoid') totalWidthRow2 += s.longBase;</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if(s.type === 'patternTriangle') totalWidthRow2 += s.sideLength;</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if(s.type === 'patternBlueRhombus' || s.type === 'patternTanRhombus') totalWidthRow2 += s.sideLength * 2 * Math.cos((s.angleDeg * Math.PI / 180) / 2);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>else if(s.type === 'patternSquare') totalWidthRow2 += s.sideLength;</p>
<p class="p1"><span class="Apple-converted-space">            </span>});</p>
<p class="p1"><span class="Apple-converted-space">            </span>totalWidthRow2 += (patternBlockRow.length - 1) * spacing;</p>
<p class="p1"><span class="Apple-converted-space">            </span>let currentXRow2 = (totalWidthRow2 &gt; canvas.width) ? baseSize * 0.1 : (canvas.width - totalWidthRow2) / 2;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const yPosRow2 = yPosRow1 + rowSpacing;<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>patternBlockRow.forEach(shape =&gt; {</p>
<p class="p1"><span class="Apple-converted-space">                </span>let w = 0;</p>
<p class="p1"><span class="Apple-converted-space">                </span>if(shape.type === 'patternHexagon') w = shape.sideLength * 2;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>else if(shape.type === 'patternTrapezoid') w = shape.longBase;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>else if(shape.type === 'patternTriangle') w = shape.sideLength;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>else if(shape.type === 'patternBlueRhombus' || shape.type === 'patternTanRhombus') w = shape.sideLength * 2 * Math.cos((shape.angleDeg * Math.PI / 180) / 2);</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if(shape.type === 'patternSquare') w = shape.sideLength;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>if (!shape.hasBeenDragged || oldCanvasWidth !== canvas.width || oldCanvasHeight !== canvas.height) {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>shape.x = currentXRow2 + w / 2; shape.y = yPosRow2;</p>
<p class="p1"><span class="Apple-converted-space">                </span>} currentXRow2 += w + spacing;</p>
<p class="p1"><span class="Apple-converted-space">            </span>});</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>// Initialize Utilities (Circle, Clock, Dice, Spinner) - Row 3</p>
<p class="p1"><span class="Apple-converted-space">            </span>const utilShapeDim = baseSize * 3.5;</p>
<p class="p1"><span class="Apple-converted-space">            </span>circle.radius = utilShapeDim / 2; circle.color = circleColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>clock.radius = utilShapeDim / 1.8; clock.color = clockFaceColor;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>dice.size = utilShapeDim * 0.8; dice.color = diceColor; dice.pipColor = dicePipColor;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>spinner.radius = utilShapeDim / 1.5;<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>let totalWidthRow3 = circle.radius*2 + clock.radius*2 + dice.size + spinner.radius*2;</p>
<p class="p1"><span class="Apple-converted-space">            </span>totalWidthRow3 += (utilityRow.length - 1) * spacing;</p>
<p class="p1"><span class="Apple-converted-space">            </span>let currentXRow3 = (totalWidthRow3 &gt; canvas.width) ? baseSize * 0.1 : (canvas.width - totalWidthRow3) / 2;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const yPosRow3 = yPosRow2 + rowSpacing;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>utilityRow.forEach(shape =&gt; {</p>
<p class="p1"><span class="Apple-converted-space">                </span>let w = 0;</p>
<p class="p1"><span class="Apple-converted-space">                </span>if(shape.type === 'circle' || shape.type === 'clock' || shape.type === 'spinner') w = shape.radius * 2;</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if(shape.type === 'dice') w = shape.size;</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (!shape.hasBeenDragged || oldCanvasWidth !== canvas.width || oldCanvasHeight !== canvas.height) {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>shape.x = currentXRow3 + w / 2; shape.y = yPosRow3;</p>
<p class="p1"><span class="Apple-converted-space">                </span>}</p>
<p class="p1"><span class="Apple-converted-space">                </span>currentXRow3 += w + spacing;</p>
<p class="p1"><span class="Apple-converted-space">            </span>});</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>// Initialize Number Lines - Row 4</p>
<p class="p1"><span class="Apple-converted-space">            </span>numLineRow.forEach(nl =&gt; nl.length = numLineWidth);</p>
<p class="p1"><span class="Apple-converted-space">            </span>let totalWidthNumLines = numLineRow.length * numLineWidth + (numLineRow.length - 1) * spacing;</p>
<p class="p1"><span class="Apple-converted-space">            </span>let currentXNumLines = (totalWidthNumLines &gt; canvas.width) ? baseSize * 0.1 : (canvas.width - totalWidthNumLines) / 2;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const yPosNumLines = yPosRow3 + rowSpacing;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>numLineRow.forEach(shape =&gt; {</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (!shape.hasBeenDragged || oldCanvasWidth !== canvas.width || oldCanvasHeight !== canvas.height) {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>shape.x = currentXNumLines + shape.length / 2; shape.y = yPosNumLines;</p>
<p class="p1"><span class="Apple-converted-space">                </span>}</p>
<p class="p1"><span class="Apple-converted-space">                </span>currentXNumLines += shape.length + spacing;</p>
<p class="p1"><span class="Apple-converted-space">            </span>});</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>// Initialize Fraction Tools - Row 5</p>
<p class="p1"><span class="Apple-converted-space">            </span>fractionStripEighths.length = baseSize * 16;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>fractionStripEighths.height = baseSize * 2;</p>
<p class="p1"><span class="Apple-converted-space">            </span>fractionStripEighths.color = fractionStripEighthsColor;</p>
<p class="p1"><span class="Apple-converted-space">            </span>fractionCircleQuarters.radius = baseSize * 3;</p>
<p class="p1"><span class="Apple-converted-space">            </span>fractionCircleQuarters.color = fractionCircleQuartersColor;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>let totalWidthFractionTools = fractionStripEighths.length + fractionCircleQuarters.radius * 2;</p>
<p class="p1"><span class="Apple-converted-space">            </span>totalWidthFractionTools += spacing;</p>
<p class="p1"><span class="Apple-converted-space">            </span>let currentXFractionTools = (totalWidthFractionTools &gt; canvas.width) ? baseSize * 0.1 : (canvas.width - totalWidthFractionTools) / 2;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const yPosFractionTools = yPosNumLines + rowSpacing;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>fractionToolsRow.forEach(shape =&gt; {</p>
<p class="p1"><span class="Apple-converted-space">                </span>let w = 0;</p>
<p class="p1"><span class="Apple-converted-space">                </span>if(shape.type === 'fractionStripEighths') w = shape.length;</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (shape.type === 'fractionCircleQuarters') w = shape.radius * 2;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                </span>if(!shape.hasBeenDragged || oldCanvasWidth !== canvas.width || oldCanvasHeight !== canvas.height) {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>shape.x = currentXFractionTools + w / 2;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>shape.y = yPosFractionTools;</p>
<p class="p1"><span class="Apple-converted-space">                </span>}</p>
<p class="p1"><span class="Apple-converted-space">                </span>currentXFractionTools += w + spacing;</p>
<p class="p1"><span class="Apple-converted-space">            </span>});</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>// Initialize Cuisenaire Rods - Row 6</p>
<p class="p1"><span class="Apple-converted-space">            </span>let totalWidthCuisenaire = 0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>cuisenaireRods.forEach(rod =&gt; {</p>
<p class="p1"><span class="Apple-converted-space">                </span>rod.width = rod.lengthUnit * cmScaleFactor;</p>
<p class="p1"><span class="Apple-converted-space">                </span>rod.height = cuisenaireRodHeight;</p>
<p class="p1"><span class="Apple-converted-space">                </span>totalWidthCuisenaire += rod.width;</p>
<p class="p1"><span class="Apple-converted-space">            </span>});</p>
<p class="p1"><span class="Apple-converted-space">            </span>totalWidthCuisenaire += (cuisenaireRods.length -1) * spacing * 0.5;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>let currentXCuisenaire = (totalWidthCuisenaire &gt; canvas.width) ? baseSize * 0.1 : (canvas.width - totalWidthCuisenaire) / 2;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const yPosCuisenaire = yPosFractionTools + rowSpacing;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>cuisenaireRods.forEach(shape =&gt; {</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (!shape.hasBeenDragged || oldCanvasWidth !== canvas.width || oldCanvasHeight !== canvas.height) {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>shape.x = currentXCuisenaire + shape.width / 2;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>shape.y = yPosCuisenaire;</p>
<p class="p1"><span class="Apple-converted-space">                </span>}</p>
<p class="p1"><span class="Apple-converted-space">                </span>currentXCuisenaire += shape.width + spacing * 0.5;</p>
<p class="p1"><span class="Apple-converted-space">            </span>});</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>// Initialize 3D Shapes and Nets - Row 7a</p>
<p class="p1"><span class="Apple-converted-space">            </span>const yPos3DAndNets1 = yPosCuisenaire + rowSpacing * 1.0;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>let currentX3DAndNets1 = baseSize * 0.25;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const itemSpacing3D = spacing * 0.7;<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>threeDShapeNetRow1.forEach(shape =&gt; {</p>
<p class="p1"><span class="Apple-converted-space">                </span>let w = 0;</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (shape.type === 'shape3DCube') w = shape.size;</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (shape.type === 'netCube') w = shape.unitSize * 4;</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (shape.type === 'shape3DCylinder') w = shape.radius * 2;</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (shape.type === 'netCylinder') w = 2 * Math.PI * shape.radius;</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (shape.type === 'shape3DPyramid') w = shape.baseSize;</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (shape.type === 'netPyramid') w = shape.baseUnitSize + 2 * shape.triangleHeight;</p>
<p class="p2"><span class="Apple-converted-space">                </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>if (!shape.hasBeenDragged || oldCanvasWidth !== canvas.width || oldCanvasHeight !== canvas.height) {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>shape.x = currentX3DAndNets1 + w/2;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>shape.y = yPos3DAndNets1 + (shape.type.includes("Net") ? (shape.unitSize || shape.radius || shape.triangleHeight)/2 : 0);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>}</p>
<p class="p1"><span class="Apple-converted-space">                </span>currentX3DAndNets1 += w + itemSpacing3D;</p>
<p class="p1"><span class="Apple-converted-space">            </span>});</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>// Initialize 3D Shapes and Nets - Row 7b</p>
<p class="p1"><span class="Apple-converted-space">            </span>const yPos3DAndNets2 = yPos3DAndNets1 + rowSpacing * 1.0;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>let currentX3DAndNets2 = baseSize * 0.25;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>threeDShapeNetRow2.forEach(shape =&gt; {</p>
<p class="p1"><span class="Apple-converted-space">                </span>let w = 0;</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (shape.type === 'shape3DCone') w = shape.radius * 2;</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (shape.type === 'netCone') w = shape.slantHeight * 2;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (shape.type === 'shape3DRectPrism') w = shape.width;</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (shape.type === 'netRectPrism') w = shape.rectD * 2 + shape.rectW * 2;</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (shape.type === 'shape3DTriPrism') w = shape.baseSide + (shape.prismLength * DEPTH_PERSPECTIVE_RATIO * 0.5);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (shape.type === 'netTriPrism') w = shape.baseSide * 3;</p>
<p class="p2"><span class="Apple-converted-space">                </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>if (!shape.hasBeenDragged || oldCanvasWidth !== canvas.width || oldCanvasHeight !== canvas.height) {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>shape.x = currentX3DAndNets2 + w/2;</p>
<p class="p1"><span class="Apple-converted-space">                     </span>if (shape.type === 'netCone') {</p>
<p class="p1"><span class="Apple-converted-space">                        </span>shape.y = yPos3DAndNets2 + shape.slantHeight / 2 + shape.radius;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                    </span>} else {</p>
<p class="p1"><span class="Apple-converted-space">                        </span>shape.y = yPos3DAndNets2 + (shape.type.includes("Net") ? (shape.rectH || shape.prismLength || shape.baseSide)/2 : 0);</p>
<p class="p1"><span class="Apple-converted-space">                    </span>}</p>
<p class="p1"><span class="Apple-converted-space">                </span>}</p>
<p class="p1"><span class="Apple-converted-space">                </span>currentX3DAndNets2 += w + itemSpacing3D;</p>
<p class="p1"><span class="Apple-converted-space">            </span>});</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>// Initialize Algebra Tiles - Row 8</p>
<p class="p1"><span class="Apple-converted-space">            </span>const yPosAlgebraTiles = yPos3DAndNets2 + rowSpacing * 1.1;</p>
<p class="p1"><span class="Apple-converted-space">            </span>let totalWidthAlgebra = 0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>algebraTileRow.forEach(tile =&gt; {</p>
<p class="p1"><span class="Apple-converted-space">                </span>totalWidthAlgebra += tile.width;</p>
<p class="p1"><span class="Apple-converted-space">            </span>});</p>
<p class="p1"><span class="Apple-converted-space">            </span>totalWidthAlgebra += (algebraTileRow.length -1) * spacing;</p>
<p class="p1"><span class="Apple-converted-space">            </span>let currentXAlgebra = (totalWidthAlgebra &gt; canvas.width) ? baseSize * 0.1 : (canvas.width - totalWidthAlgebra)/2;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>algebraTileRow.forEach(tile =&gt; {</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (!tile.hasBeenDragged || oldCanvasWidth !== canvas.width || oldCanvasHeight !== canvas.height) {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>tile.x = currentXAlgebra + tile.width / 2;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>tile.y = yPosAlgebraTiles;</p>
<p class="p1"><span class="Apple-converted-space">                </span>}</p>
<p class="p1"><span class="Apple-converted-space">                </span>currentXAlgebra += tile.width + spacing;</p>
<p class="p1"><span class="Apple-converted-space">            </span>});</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>// Initialize Other Tools - Row 9 (Ten Frame, Array, Hundreds Chart)</p>
<p class="p1"><span class="Apple-converted-space">            </span>const yPosOtherTools = yPosAlgebraTiles + rowSpacing * 1.1;</p>
<p class="p1"><span class="Apple-converted-space">            </span>let totalWidthOtherTools = 0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>otherToolsRow.forEach(tool =&gt; {</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (tool.type === 'tenFrame') totalWidthOtherTools += tool.cols * tool.cellWidth;</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (tool.type === 'multiplicationArray') totalWidthOtherTools += tool.cols * tool.cellWidth;</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (tool.type === 'hundredsChart') totalWidthOtherTools += 10 * tool.cellWidth;</p>
<p class="p1"><span class="Apple-converted-space">            </span>});</p>
<p class="p1"><span class="Apple-converted-space">            </span>totalWidthOtherTools += (otherToolsRow.length - 1) * spacing * 1.5;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>let currentXOtherTools = (totalWidthOtherTools &gt; canvas.width) ? baseSize * 0.1 : (canvas.width - totalWidthOtherTools) / 2;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>otherToolsRow.forEach(tool =&gt; {</p>
<p class="p1"><span class="Apple-converted-space">                </span>let w = 0;</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (tool.type === 'tenFrame') w = tool.cols * tool.cellWidth;</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (tool.type === 'multiplicationArray') w = tool.cols * tool.cellWidth;</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (tool.type === 'hundredsChart') w = 10 * tool.cellWidth;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                </span>if (!tool.hasBeenDragged || oldCanvasWidth !== canvas.width || oldCanvasHeight !== canvas.height) {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>tool.x = currentXOtherTools + w / 2;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>tool.y = yPosOtherTools;</p>
<p class="p1"><span class="Apple-converted-space">                </span>}</p>
<p class="p1"><span class="Apple-converted-space">                </span>currentXOtherTools += w + spacing * 1.5;</p>
<p class="p1"><span class="Apple-converted-space">            </span>});</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>// Initialize Graphing Tools - Row 10</p>
<p class="p1"><span class="Apple-converted-space">            </span>const yPosGraphingTools = yPosOtherTools + rowSpacing * 1.2;</p>
<p class="p1"><span class="Apple-converted-space">            </span>let totalWidthGraphing = 0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>graphingToolsRow.forEach(tool =&gt; totalWidthGraphing += tool.width);</p>
<p class="p1"><span class="Apple-converted-space">            </span>totalWidthGraphing += (graphingToolsRow.length - 1) * spacing * 2;</p>
<p class="p1"><span class="Apple-converted-space">            </span>let currentXGraphing = (totalWidthGraphing &gt; canvas.width) ? baseSize * 0.1 : (canvas.width - totalWidthGraphing) / 2;</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>graphingToolsRow.forEach(tool =&gt; {</p>
<p class="p1"><span class="Apple-converted-space">                 </span>if (!tool.hasBeenDragged || oldCanvasWidth !== canvas.width || oldCanvasHeight !== canvas.height) {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>tool.x = currentXGraphing + tool.width / 2;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>tool.y = yPosGraphingTools;</p>
<p class="p1"><span class="Apple-converted-space">                </span>}</p>
<p class="p1"><span class="Apple-converted-space">                </span>currentXGraphing += tool.width + spacing * 2;</p>
<p class="p1"><span class="Apple-converted-space">            </span>});</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>// Initialize Number Theory Tools - Row 11</p>
<p class="p1"><span class="Apple-converted-space">            </span>const yPosNumberTheory = yPosGraphingTools + rowSpacing * 1.3;</p>
<p class="p1"><span class="Apple-converted-space">            </span>let totalWidthNumberTheory = 0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>numberTheoryToolsRow.forEach(tool =&gt; {</p>
<p class="p1"><span class="Apple-converted-space">                </span>if(tool.type.includes('NumberBond')) totalWidthNumberTheory += tool.radius * 4;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (tool.type === 'factorTree') totalWidthNumberTheory += tool.nodeRadius * 6;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>});</p>
<p class="p1"><span class="Apple-converted-space">            </span>totalWidthNumberTheory += (numberTheoryToolsRow.length -1) * spacing * 2;</p>
<p class="p1"><span class="Apple-converted-space">            </span>let currentXNumberTheory = (totalWidthNumberTheory &gt; canvas.width) ? baseSize * 0.1 : (canvas.width - totalWidthNumberTheory) / 2;</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>numberTheoryToolsRow.forEach(tool =&gt; {</p>
<p class="p1"><span class="Apple-converted-space">                </span>let w = 0;</p>
<p class="p1"><span class="Apple-converted-space">                 </span>if(tool.type.includes('NumberBond')) w = tool.radius * 4;</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (tool.type === 'factorTree') w = tool.nodeRadius * 6;<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                </span>if (!tool.hasBeenDragged || oldCanvasWidth !== canvas.width || oldCanvasHeight !== canvas.height) {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>tool.x = currentXNumberTheory + w / 2;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>tool.y = yPosNumberTheory + (tool.type === 'factorTree' ? tool.nodeRadius * 3 : 0) ;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>}</p>
<p class="p1"><span class="Apple-converted-space">                </span>currentXNumberTheory += w + spacing * 2;</p>
<p class="p1"><span class="Apple-converted-space">            </span>});</p>
<p class="p2"><br></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>allDrawableItems.forEach(item =&gt; item.hasBeenDragged = false);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>// --- Function to handle canvas resizing and element position normalization ---</p>
<p class="p1"><span class="Apple-converted-space">        </span>function resizeCanvasAndDraw() {</p>
<p class="p1"><span class="Apple-converted-space">            </span>canvas.width = window.innerWidth;</p>
<p class="p1"><span class="Apple-converted-space">            </span>canvas.height = window.innerHeight;</p>
<p class="p1"><span class="Apple-converted-space">            </span>initializeShapesInLayout();<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>oldCanvasWidth = canvas.width;</p>
<p class="p1"><span class="Apple-converted-space">            </span>oldCanvasHeight = canvas.height;</p>
<p class="p1"><span class="Apple-converted-space">            </span>drawScene();<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>// --- Mouse Event Listeners for Dragging and Lasso ---</p>
<p class="p1"><span class="Apple-converted-space">        </span>canvas.addEventListener('mousedown', (e) =&gt; {</p>
<p class="p1"><span class="Apple-converted-space">            </span>lastMouseX = e.offsetX;</p>
<p class="p1"><span class="Apple-converted-space">            </span>lastMouseY = e.offsetY;</p>
<p class="p1"><span class="Apple-converted-space">            </span>let clickedOnExistingSelection = false;</p>
<p class="p1"><span class="Apple-converted-space">            </span>let itemClicked = null;</p>
<p class="p1"><span class="Apple-converted-space">            </span>let clickedRotationHandle = false;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>if (selectedItems.length === 1 &amp;&amp; rotatableTypes.includes(selectedItems[0].type)) {</p>
<p class="p1"><span class="Apple-converted-space">                </span>const item = selectedItems[0];</p>
<p class="p1"><span class="Apple-converted-space">                </span>let itemHeightForHandle = item.sideLength || item.height || item.radius * 2 || item.size;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>if (item.type === 'patternTriangle') itemHeightForHandle = item.sideLength * (Math.sqrt(3)/2);</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (item.type === 'patternBlueRhombus' || item.type === 'patternTanRhombus') {</p>
<p class="p1"><span class="Apple-converted-space">                     </span>const angleRad = item.angleDeg * Math.PI / 180;</p>
<p class="p1"><span class="Apple-converted-space">                     </span>const d2 = item.sideLength * 2 * Math.cos(angleRad / 2);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                     </span>itemHeightForHandle = (item.angleDeg === 60) ? item.sideLength * Math.sqrt(3) : d2;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>}</p>
<p class="p2"><span class="Apple-converted-space">                </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>const handleRelX = 0;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>const handleRelY = -itemHeightForHandle/2 - rotationHandleOffset;<span class="Apple-converted-space"> </span></p>
<p class="p2"><span class="Apple-converted-space">                </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>const rotatedHandleX = item.x + (handleRelX * Math.cos(item.angle) - handleRelY * Math.sin(item.angle));</p>
<p class="p1"><span class="Apple-converted-space">                </span>const rotatedHandleY = item.y + (handleRelX * Math.sin(item.angle) + handleRelY * Math.cos(item.angle));</p>
<p class="p2"><span class="Apple-converted-space">                </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>if (Math.sqrt(Math.pow(lastMouseX - rotatedHandleX, 2) + Math.pow(lastMouseY - rotatedHandleY, 2)) &lt;= rotationHandleClickRadius) {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>isRotating = true;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>rotatingItem = item;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>rotationStartMouseAngle = Math.atan2(lastMouseY - item.y, lastMouseX - item.x);</p>
<p class="p1"><span class="Apple-converted-space">                    </span>rotationStartItemAngle = item.angle;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>clickedRotationHandle = true;</p>
<p class="p1"><span class="Apple-converted-space">                </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p2"><br></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>if (!clickedRotationHandle) {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>if (isPointOverItem(lastMouseX, lastMouseY, dice) &amp;&amp; !dice.isAnimating) {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>dice.isAnimating = true;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>dice.animationStartTime = Date.now();</p>
<p class="p1"><span class="Apple-converted-space">                    </span>dice.lastFaceChangeTime = Date.now();<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                    </span>dice.targetFaceValue = Math.floor(Math.random() * 6) + 1;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>dice.overlayActiveUntil = Date.now() + 200;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                    </span>requestAnimationFrame(drawScene);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                    </span>return;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>}</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (isPointOverItem(lastMouseX, lastMouseY, spinner) &amp;&amp; !spinner.isSpinning) {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>spinner.isSpinning = true;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>spinner.animationStartTime = Date.now();</p>
<p class="p1"><span class="Apple-converted-space">                    </span>spinner.overlayActiveUntil = Date.now() + 200;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>spinner.initialAngleForSpin = spinner.currentAngle;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>const randomSpins = Math.floor(Math.random() * 3) + 2;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                    </span>const targetSection = Math.floor(Math.random() * spinner.numSections);</p>
<p class="p1"><span class="Apple-converted-space">                    </span>const anglePerSection = (2 * Math.PI) / spinner.numSections;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>spinner.targetAngle = spinner.initialAngleForSpin + (randomSpins * 2 * Math.PI) - (targetSection * anglePerSection + anglePerSection / 2) + Math.PI / 2;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                    </span>requestAnimationFrame(drawScene);</p>
<p class="p1"><span class="Apple-converted-space">                    </span>return;</p>
<p class="p1"><span class="Apple-converted-space">                </span>}</p>
<p class="p2"><br></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                </span>if (selectedItems.length &gt; 0) {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>for (const item of selectedItems) {</p>
<p class="p1"><span class="Apple-converted-space">                        </span>if (isPointOverItem(lastMouseX, lastMouseY, item)) {</p>
<p class="p1"><span class="Apple-converted-space">                            </span>clickedOnExistingSelection = true;</p>
<p class="p1"><span class="Apple-converted-space">                            </span>itemClicked = item;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                            </span>break;</p>
<p class="p1"><span class="Apple-converted-space">                        </span>}</p>
<p class="p1"><span class="Apple-converted-space">                    </span>}</p>
<p class="p1"><span class="Apple-converted-space">                </span>}</p>
<p class="p2"><span class="Apple-converted-space">                </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>if (clickedOnExistingSelection) {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>isDragging = true;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>draggedItem = itemClicked;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                    </span>dragStartOffsets = selectedItems.map(item =&gt; ({</p>
<p class="p1"><span class="Apple-converted-space">                        </span>item: item,</p>
<p class="p1"><span class="Apple-converted-space">                        </span>dx: item.x - lastMouseX,</p>
<p class="p1"><span class="Apple-converted-space">                        </span>dy: item.y - lastMouseY</p>
<p class="p1"><span class="Apple-converted-space">                    </span>}));</p>
<p class="p1"><span class="Apple-converted-space">                </span>} else {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>itemClicked = null;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>const itemsToCheck = [...allDrawableItems].reverse();<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                    </span>for (const item of itemsToCheck) {</p>
<p class="p1"><span class="Apple-converted-space">                        </span>if (isPointOverItem(lastMouseX, lastMouseY, item)) {</p>
<p class="p1"><span class="Apple-converted-space">                            </span>itemClicked = item;</p>
<p class="p1"><span class="Apple-converted-space">                            </span>break;</p>
<p class="p1"><span class="Apple-converted-space">                        </span>}</p>
<p class="p1"><span class="Apple-converted-space">                    </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                    </span>if (itemClicked) {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                        </span>selectedItems = [itemClicked];<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                        </span>isDragging = true;</p>
<p class="p1"><span class="Apple-converted-space">                        </span>draggedItem = itemClicked;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                        </span>dragStartOffsets = [{ item: itemClicked, dx: itemClicked.x - lastMouseX, dy: itemClicked.y - lastMouseY }];</p>
<p class="p1"><span class="Apple-converted-space">                    </span>} else {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                        </span>selectedItems = [];<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                        </span>draggedItem = null;</p>
<p class="p1"><span class="Apple-converted-space">                        </span>isLassoing = true;</p>
<p class="p1"><span class="Apple-converted-space">                        </span>lassoRect.startX = lastMouseX;</p>
<p class="p1"><span class="Apple-converted-space">                        </span>lassoRect.startY = lastMouseY;</p>
<p class="p1"><span class="Apple-converted-space">                        </span>lassoRect.currentX = lastMouseX;</p>
<p class="p1"><span class="Apple-converted-space">                        </span>lassoRect.currentY = lastMouseY;</p>
<p class="p1"><span class="Apple-converted-space">                        </span>lassoRect.active = true;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>}</p>
<p class="p1"><span class="Apple-converted-space">                </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>if(draggedItem &amp;&amp; !isRotating) draggedItem.hasBeenDragged = true;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>if(selectedItems.length &gt; 0 &amp;&amp; !draggedItem &amp;&amp; clickedOnExistingSelection &amp;&amp; !isRotating) {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>draggedItem = selectedItems[0];<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>draggedItem.hasBeenDragged = true;</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>if (!isRotating) {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>selectedItems.forEach(item =&gt; item.hasBeenDragged = true);</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p2"><br></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>canvas.style.cursor = isRotating ? 'grabbing' : (isDragging ? 'grabbing' : 'default');<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>drawScene();</p>
<p class="p1"><span class="Apple-converted-space">        </span>});</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>canvas.addEventListener('mousemove', (e) =&gt; {</p>
<p class="p1"><span class="Apple-converted-space">            </span>const currentX = e.offsetX;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const currentY = e.offsetY;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>if (isRotating &amp;&amp; rotatingItem) {</p>
<p class="p1"><span class="Apple-converted-space">                </span>const mouseAngle = Math.atan2(currentY - rotatingItem.y, currentX - rotatingItem.x);</p>
<p class="p1"><span class="Apple-converted-space">                </span>rotatingItem.angle = rotationStartItemAngle + (mouseAngle - rotationStartMouseAngle);</p>
<p class="p1"><span class="Apple-converted-space">            </span>} else if (isDragging) {</p>
<p class="p1"><span class="Apple-converted-space">                </span>const masterDx = currentX - lastMouseX;</p>
<p class="p1"><span class="Apple-converted-space">                </span>const masterDy = currentY - lastMouseY;</p>
<p class="p1"><span class="Apple-converted-space">                </span>selectedItems.forEach(selItem =&gt; {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>selItem.x += masterDx;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>selItem.y += masterDy;</p>
<p class="p1"><span class="Apple-converted-space">                </span>});</p>
<p class="p1"><span class="Apple-converted-space">                </span>lastMouseX = currentX;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>lastMouseY = currentY;</p>
<p class="p1"><span class="Apple-converted-space">            </span>} else if (isLassoing) {</p>
<p class="p1"><span class="Apple-converted-space">                </span>lassoRect.currentX = currentX;</p>
<p class="p1"><span class="Apple-converted-space">                </span>lassoRect.currentY = currentY;</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>if(!dice.isAnimating &amp;&amp; !spinner.isSpinning) {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>drawScene();</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">        </span>});</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>canvas.addEventListener('mouseup', (e) =&gt; {</p>
<p class="p1"><span class="Apple-converted-space">            </span>if (isRotating) {</p>
<p class="p1"><span class="Apple-converted-space">                </span>isRotating = false;</p>
<p class="p1"><span class="Apple-converted-space">                </span>rotatingItem = null;</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>if (isDragging) {</p>
<p class="p1"><span class="Apple-converted-space">                </span>isDragging = false;</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (selectedItems.length &gt; 0) {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>const primaryDragged = selectedItems[0];<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                    </span>let bestSnapDx = 0;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>let bestSnapDy = 0;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>let minSnapDist = Infinity;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>let snapped = false;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                    </span>allDrawableItems.forEach(staticItem =&gt; {</p>
<p class="p1"><span class="Apple-converted-space">                        </span>if (!selectedItems.includes(staticItem)) {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                            </span>const draggedVertices = getRotatedVertices(primaryDragged);</p>
<p class="p1"><span class="Apple-converted-space">                            </span>const staticVertices = getRotatedVertices(staticItem);</p>
<p class="p2"><span class="Apple-converted-space">                            </span></p>
<p class="p1"><span class="Apple-converted-space">                            </span>if (!draggedVertices || !staticVertices) return;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                            </span>for (const dv of draggedVertices) {</p>
<p class="p1"><span class="Apple-converted-space">                                </span>for (const sv of staticVertices) {</p>
<p class="p1"><span class="Apple-converted-space">                                    </span>const dist = Math.sqrt(Math.pow(dv.x - sv.x, 2) + Math.pow(dv.y - sv.y, 2));</p>
<p class="p1"><span class="Apple-converted-space">                                    </span>if (dist &lt; snappingThreshold &amp;&amp; dist &lt; minSnapDist) {</p>
<p class="p1"><span class="Apple-converted-space">                                        </span>minSnapDist = dist;</p>
<p class="p1"><span class="Apple-converted-space">                                        </span>bestSnapDx = sv.x - dv.x;</p>
<p class="p1"><span class="Apple-converted-space">                                        </span>bestSnapDy = sv.y - dv.y;</p>
<p class="p1"><span class="Apple-converted-space">                                        </span>snapped = true;</p>
<p class="p1"><span class="Apple-converted-space">                                    </span>}</p>
<p class="p1"><span class="Apple-converted-space">                                </span>}</p>
<p class="p1"><span class="Apple-converted-space">                            </span>}</p>
<p class="p1"><span class="Apple-converted-space">                        </span>}</p>
<p class="p1"><span class="Apple-converted-space">                    </span>});</p>
<p class="p1"><span class="Apple-converted-space">                    </span>if(snapped){</p>
<p class="p1"><span class="Apple-converted-space">                        </span>selectedItems.forEach(selItem =&gt; {</p>
<p class="p1"><span class="Apple-converted-space">                            </span>selItem.x += bestSnapDx;</p>
<p class="p1"><span class="Apple-converted-space">                            </span>selItem.y += bestSnapDy;</p>
<p class="p1"><span class="Apple-converted-space">                        </span>});</p>
<p class="p1"><span class="Apple-converted-space">                    </span>}</p>
<p class="p1"><span class="Apple-converted-space">                </span>}</p>
<p class="p2"><br></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>} else if (isLassoing) {</p>
<p class="p1"><span class="Apple-converted-space">                </span>isLassoing = false;</p>
<p class="p1"><span class="Apple-converted-space">                </span>lassoRect.active = false;</p>
<p class="p1"><span class="Apple-converted-space">                </span>selectedItems = [];<span class="Apple-converted-space"> </span></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                </span>const rX = Math.min(lassoRect.startX, lassoRect.currentX);</p>
<p class="p1"><span class="Apple-converted-space">                </span>const rY = Math.min(lassoRect.startY, lassoRect.currentY);</p>
<p class="p1"><span class="Apple-converted-space">                </span>const rW = Math.abs(lassoRect.startX - lassoRect.currentX);</p>
<p class="p1"><span class="Apple-converted-space">                </span>const rH = Math.abs(lassoRect.startY - lassoRect.currentY);</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                </span>allDrawableItems.forEach(item =&gt; {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>const itemAABB = getItemAABB(item);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                    </span>if (itemAABB.minX &lt; rX + rW &amp;&amp; itemAABB.maxX &gt; rX &amp;&amp; itemAABB.minY &lt; rY + rH &amp;&amp; itemAABB.maxY &gt; rY) {</p>
<p class="p1"><span class="Apple-converted-space">                        </span>selectedItems.push(item);</p>
<p class="p1"><span class="Apple-converted-space">                        </span>item.hasBeenDragged = true;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                    </span>}</p>
<p class="p1"><span class="Apple-converted-space">                </span>});</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>canvas.style.cursor = 'default';</p>
<p class="p1"><span class="Apple-converted-space">            </span>if(!dice.isAnimating &amp;&amp; !spinner.isSpinning){<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>drawScene();</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">        </span>});</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>canvas.addEventListener('mouseleave', () =&gt; {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>if (isDragging) isDragging = false;</p>
<p class="p1"><span class="Apple-converted-space">            </span>if (isLassoing) { isLassoing = false; lassoRect.active = false; }</p>
<p class="p1"><span class="Apple-converted-space">            </span>if (isRotating) { isRotating = false; rotatingItem = null; }</p>
<p class="p1"><span class="Apple-converted-space">            </span>canvas.style.cursor = 'default';</p>
<p class="p1"><span class="Apple-converted-space">            </span>if(!dice.isAnimating &amp;&amp; !spinner.isSpinning){</p>
<p class="p1"><span class="Apple-converted-space">                </span>drawScene();</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">        </span>});</p>
<p class="p2"><span class="Apple-converted-space">        </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>function getRotatedVertices(item) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>if (!item) return null;</p>
<p class="p1"><span class="Apple-converted-space">            </span>let w, h, po = 0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const angle = item.angle || 0;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const cosA = Math.cos(angle);</p>
<p class="p1"><span class="Apple-converted-space">            </span>const sinA = Math.sin(angle);</p>
<p class="p1"><span class="Apple-converted-space">            </span>let localCorners = [];</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>if (item.type === 'cube' || item.type === 'dice' || item.type === 'shape3DCube') { w = item.size; h = item.size; if(item.type === 'cube' || item.type === 'shape3DCube') po = item.size * DEPTH_PERSPECTIVE_RATIO; }</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'rod') { w = item.length; h = item.height; po = item.depth * DEPTH_PERSPECTIVE_RATIO; }</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'flat') { w = item.sideLength; h = item.sideLength; po = item.thickness * DEPTH_PERSPECTIVE_RATIO; }</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'block1000') { w = item.sideLength; h = item.sideLength; po = item.sideLength * DEPTH_PERSPECTIVE_RATIO; }</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'circle' || item.type === 'clock' || item.type === 'fractionCircleQuarters' || item.type === 'spinner') { w = item.radius * 2; h = item.radius * 2; }<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'patternSquare') { w = item.sideLength; h = item.sideLength;}</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'patternTriangle') { w = item.sideLength; h = item.sideLength * (Math.sqrt(3) / 2); }<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'patternHexagon') { w = item.sideLength * 2; h = item.sideLength * Math.sqrt(3); }<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'patternTrapezoid') { w = item.longBase; h = item.height; }</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'patternBlueRhombus' || item.type === 'patternTanRhombus') {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>const angleRad = item.angleDeg * Math.PI / 180;</p>
<p class="p1"><span class="Apple-converted-space">                </span>const d1 = item.sideLength * 2 * Math.sin(angleRad / 2);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>const d2 = item.sideLength * 2 * Math.cos(angleRad / 2);</p>
<p class="p1"><span class="Apple-converted-space">                </span>w = (item.angleDeg === 60) ? d1 : d2;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>h = (item.angleDeg === 60) ? d2 : d1;</p>
<p class="p1"><span class="Apple-converted-space">            </span>}<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type.startsWith('numLine') || item.type === 'fractionStripEighths') { w = item.length; h = item.height; }<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'cuisenaireRod') { w = item.width; h = item.height; }</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'shape3DCylinder') { w = item.radius * 2; h = item.height; }</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'shape3DPyramid') { w = item.baseSize; h = item.height; po = item.baseSize * DEPTH_PERSPECTIVE_RATIO * 0.5; }</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'shape3DCone') { w = item.radius * 2; h = item.height; }</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'shape3DRectPrism') { w = item.width; h = item.height; po = item.depth * DEPTH_PERSPECTIVE_RATIO; }</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'shape3DTriPrism') { w = item.baseSide; h = (Math.sqrt(3)/2) * item.baseSide; po = item.prismLength * DEPTH_PERSPECTIVE_RATIO; }</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'netCube') { w = item.unitSize * 4; h = item.unitSize * 3; }<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'netCylinder') { w = 2 * Math.PI * item.radius; h = item.height + 2 * item.radius; }<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'netPyramid') { w = item.baseUnitSize + 2 * item.triangleHeight; h = item.baseUnitSize + 2 * item.triangleHeight; }<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'netCone') { w = item.slantHeight * 2; h = item.slantHeight + 2 * item.radius; }<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'netRectPrism') { w = item.rectD * 2 + item.rectW * 2; h = item.rectH + 2 * item.rectD; }</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'netTriPrism') { w = item.baseSide * 3; h = item.prismLength + 2 * (Math.sqrt(3)/2 * item.baseSide); }</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'algebraTile') { w = item.width; h = item.height; }</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'tenFrame') { w = item.cols * item.cellWidth; h = item.rows * item.cellHeight; }</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'multiplicationArray') { w = item.cols * item.cellWidth; h = item.rows * item.cellHeight; }</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'hundredsChart') { w = 10 * item.cellWidth; h = 10 * item.cellHeight; }</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'barGraph' || item.type === 'coordinatePlane' || item.type === 'scatterPlot') { w = item.width; h = item.height; }</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'additionNumberBond' || item.type === 'multiplicationNumberBond') { w = item.radius * 3; h = item.radius * 3.5; } // Approximate AABB</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'factorTree') { // AABB for factor tree is dynamic, use stored or estimate</p>
<p class="p1"><span class="Apple-converted-space">                </span>if(item.aabb) {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>w = item.aabb.maxX - item.aabb.minX;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>h = item.aabb.maxY - item.aabb.minY;</p>
<p class="p1"><span class="Apple-converted-space">                </span>} else { // Estimate if aabb not yet calculated</p>
<p class="p1"><span class="Apple-converted-space">                    </span>w = item.nodeRadius * 8; h = item.nodeRadius * 10;</p>
<p class="p1"><span class="Apple-converted-space">                </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>else { return null; }</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const is3DBlock = ['cube', 'rod', 'flat', 'block1000', 'shape3DCube', 'shape3DCylinder', 'shape3DPyramid', 'shape3DCone', 'shape3DRectPrism', 'shape3DTriPrism'].includes(item.type);</p>
<p class="p1"><span class="Apple-converted-space">            </span>const visualWidth = (['shape3DCylinder', 'shape3DCone'].includes(item.type)) ? item.radius * 2 : (w + (is3DBlock &amp;&amp; !['shape3DCylinder', 'shape3DCone'].includes(item.type) ? po : 0));</p>
<p class="p1"><span class="Apple-converted-space">            </span>const visualHeight = (['shape3DCylinder', 'shape3DCone'].includes(item.type)) ? item.height : (h + (is3DBlock &amp;&amp; !['shape3DCylinder', 'shape3DCone'].includes(item.type) ? po : 0));</p>
<p class="p2"><br></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>// Define corners relative to item's center (0,0) before rotation</p>
<p class="p1"><span class="Apple-converted-space">            </span>if (item.type === 'patternTriangle') {</p>
<p class="p1"><span class="Apple-converted-space">                 </span>const triH = item.sideLength * (Math.sqrt(3)/2);</p>
<p class="p1"><span class="Apple-converted-space">                 </span>localCorners = [ {x: 0, y: -triH * 2/3}, {x: -item.sideLength/2, y: triH/3}, {x: item.sideLength/2, y: triH/3} ];</p>
<p class="p1"><span class="Apple-converted-space">            </span>} else if (item.type === 'patternHexagon') {</p>
<p class="p1"><span class="Apple-converted-space">                </span>const r = item.sideLength;</p>
<p class="p1"><span class="Apple-converted-space">                </span>localCorners = [];</p>
<p class="p1"><span class="Apple-converted-space">                </span>for(let i=0; i&lt;6; i++) {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>localCorners.push({x: r * Math.cos(i * Math.PI/3), y: r * Math.sin(i * Math.PI/3)});</p>
<p class="p1"><span class="Apple-converted-space">                </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>} else if (item.type === 'patternTrapezoid') {</p>
<p class="p1"><span class="Apple-converted-space">                </span>const trapH = item.height;</p>
<p class="p1"><span class="Apple-converted-space">                </span>localCorners = [ {x: -item.longBase/2, y: trapH/2}, {x: item.longBase/2, y: trapH/2}, {x: item.shortBase/2, y: -trapH/2}, {x: -item.shortBase/2, y: -trapH/2} ];</p>
<p class="p1"><span class="Apple-converted-space">            </span>} else if (item.type === 'patternBlueRhombus' || item.type === 'patternTanRhombus') {</p>
<p class="p1"><span class="Apple-converted-space">                </span>const s = item.sideLength; const angleR = item.angleDeg * Math.PI / 180;</p>
<p class="p1"><span class="Apple-converted-space">                </span>const halfD1 = s * Math.sin(angleR / 2); const halfD2 = s * Math.cos(angleR / 2);</p>
<p class="p1"><span class="Apple-converted-space">                </span>localCorners = [ {x:0, y: -halfD2}, {x:halfD1, y:0}, {x:0, y:halfD2}, {x:-halfD1, y:0} ];</p>
<p class="p1"><span class="Apple-converted-space">            </span>} else if (item.type === 'netCylinder') {</p>
<p class="p1"><span class="Apple-converted-space">                </span>const rectW = 2 * Math.PI * item.radius;</p>
<p class="p1"><span class="Apple-converted-space">                </span>const totalH = item.height + 2 * item.radius;</p>
<p class="p1"><span class="Apple-converted-space">                </span>localCorners = [<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                    </span>{x: -rectW/2, y: -totalH/2}, {x: rectW/2, y: -totalH/2},</p>
<p class="p1"><span class="Apple-converted-space">                    </span>{x: rectW/2, y: totalH/2}, <span class="Apple-converted-space">  </span>{x: -rectW/2, y: totalH/2}</p>
<p class="p1"><span class="Apple-converted-space">                </span>];</p>
<p class="p1"><span class="Apple-converted-space">            </span>} else if (item.type === 'netPyramid') {</p>
<p class="p1"><span class="Apple-converted-space">                </span>const b = item.baseUnitSize; const tH = item.triangleHeight;</p>
<p class="p1"><span class="Apple-converted-space">                </span>const totalW = b + 2 * tH;</p>
<p class="p1"><span class="Apple-converted-space">                </span>const totalH = b + 2 * tH;</p>
<p class="p1"><span class="Apple-converted-space">                </span>localCorners = [<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                    </span>{x: -totalW/2, y: -totalH/2}, {x: totalW/2, y: -totalH/2},</p>
<p class="p1"><span class="Apple-converted-space">                    </span>{x: totalW/2, y: totalH/2}, <span class="Apple-converted-space">  </span>{x: -totalW/2, y: totalH/2}</p>
<p class="p1"><span class="Apple-converted-space">                </span>];</p>
<p class="p1"><span class="Apple-converted-space">            </span>} else if (item.type === 'netCone') {</p>
<p class="p1"><span class="Apple-converted-space">                </span>const sL = item.slantHeight; const rBase = item.radius;</p>
<p class="p1"><span class="Apple-converted-space">                </span>const maxY = 0;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>const minY = -(sL + 2*rBase);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>const halfWidthSector = sL * Math.sin( ( (2 * Math.PI * rBase) / sL ) / 2 );<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>const maxAbsX = Math.max(halfWidthSector, rBase);</p>
<p class="p1"><span class="Apple-converted-space">                </span>localCorners = [</p>
<p class="p1"><span class="Apple-converted-space">                    </span>{x: -maxAbsX, y: minY}, {x: maxAbsX, y: minY},</p>
<p class="p1"><span class="Apple-converted-space">                    </span>{x: maxAbsX, y: maxY}, {x: -maxAbsX, y: maxY}</p>
<p class="p1"><span class="Apple-converted-space">                </span>];</p>
<p class="p1"><span class="Apple-converted-space">            </span>} else if (item.type === 'netRectPrism') {</p>
<p class="p1"><span class="Apple-converted-space">                </span>const netW = item.rectD * 2 + item.rectW * 2;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>const netH = item.rectH + 2 * item.rectD;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>localCorners = [</p>
<p class="p1"><span class="Apple-converted-space">                    </span>{x: -netW/2, y: -netH/2}, {x: netW/2, y: -netH/2},</p>
<p class="p1"><span class="Apple-converted-space">                    </span>{x: netW/2, y: netH/2}, {x: -netW/2, y: netH/2}</p>
<p class="p1"><span class="Apple-converted-space">                </span>];</p>
<p class="p1"><span class="Apple-converted-space">            </span>} else if (item.type === 'netTriPrism') {</p>
<p class="p1"><span class="Apple-converted-space">                </span>const side = item.baseSide; const len = item.prismLength;</p>
<p class="p1"><span class="Apple-converted-space">                </span>const triH = (Math.sqrt(3)/2) * side;</p>
<p class="p1"><span class="Apple-converted-space">                </span>const netW = side * 3;</p>
<p class="p1"><span class="Apple-converted-space">                </span>const netH = len + 2 * triH;</p>
<p class="p1"><span class="Apple-converted-space">                </span>localCorners = [</p>
<p class="p1"><span class="Apple-converted-space">                    </span>{x: -netW/2, y: -netH/2}, {x: netW/2, y: -netH/2},</p>
<p class="p1"><span class="Apple-converted-space">                    </span>{x: netW/2, y: netH/2}, {x: -netW/2, y: netH/2}</p>
<p class="p1"><span class="Apple-converted-space">                </span>];</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>else { // Default rectangular bounding box corners (includes algebraTile, tenFrame, etc.)</p>
<p class="p1"><span class="Apple-converted-space">                </span>localCorners = [</p>
<p class="p1"><span class="Apple-converted-space">                    </span>{x: -visualWidth/2, y: -visualHeight/2}, {x: visualWidth/2, y: -visualHeight/2},</p>
<p class="p1"><span class="Apple-converted-space">                    </span>{x: visualWidth/2, y: visualHeight/2}, {x: -visualWidth/2, y: visualHeight/2}</p>
<p class="p1"><span class="Apple-converted-space">                </span>];</p>
<p class="p1"><span class="Apple-converted-space">                 </span>if(is3DBlock &amp;&amp; !['shape3DCylinder','shape3DCone'].includes(item.type)){<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                    </span>localCorners.push({x: -w/2 + po, y: -h/2 - po});</p>
<p class="p1"><span class="Apple-converted-space">                    </span>localCorners.push({x:<span class="Apple-converted-space">  </span>w/2 + po, y: -h/2 - po});</p>
<p class="p1"><span class="Apple-converted-space">                    </span>localCorners.push({x:<span class="Apple-converted-space">  </span>w/2 + po, y:<span class="Apple-converted-space">  </span>h/2 - po});</p>
<p class="p1"><span class="Apple-converted-space">                    </span>localCorners.push({x: -w/2 + po, y:<span class="Apple-converted-space">  </span>h/2 - po});</p>
<p class="p1"><span class="Apple-converted-space">                </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p2"><br></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>return localCorners.map(corner =&gt; ({</p>
<p class="p1"><span class="Apple-converted-space">                </span>x: item.x + (corner.x * cosA - corner.y * sinA),</p>
<p class="p1"><span class="Apple-converted-space">                </span>y: item.y + (corner.x * sinA + corner.y * cosA)</p>
<p class="p1"><span class="Apple-converted-space">            </span>}));</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>function getItemAABB(item) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>const vertices = getRotatedVertices(item);</p>
<p class="p1"><span class="Apple-converted-space">            </span>if (!vertices || vertices.length === 0) {</p>
<p class="p1"><span class="Apple-converted-space">                </span>let w, h, po = 0;</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (item.type === 'circle' || item.type === 'clock' || item.type === 'fractionCircleQuarters' || item.type === 'spinner') { w = item.radius * 2; h = item.radius * 2; }</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (item.type === 'dice') { w = item.size; h = item.size; }</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (item.type.startsWith('numLine') || item.type === 'fractionStripEighths') { w = item.length; h = item.height; }</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (item.type === 'cube' || item.type === 'shape3DCube') { w = item.size; h = item.size; po = item.size * DEPTH_PERSPECTIVE_RATIO; }</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (item.type === 'cuisenaireRod') { w = item.width; h = item.height; }</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (item.type === 'shape3DCylinder') { w = item.radius * 2; h = item.height; }</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (item.type === 'shape3DPyramid') { w = item.baseSize; h = item.height; po = item.baseSize * DEPTH_PERSPECTIVE_RATIO * 0.5; }</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (item.type === 'shape3DCone') { w = item.radius * 2; h = item.height; }</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (item.type === 'shape3DRectPrism') { w = item.width; h = item.height; po = item.depth * DEPTH_PERSPECTIVE_RATIO; }</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (item.type === 'shape3DTriPrism') { w = item.baseSide; h = (Math.sqrt(3)/2) * item.baseSide; po = item.prismLength * DEPTH_PERSPECTIVE_RATIO; }</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (item.type === 'netCube') { w = item.unitSize * 4; h = item.unitSize * 3; }</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (item.type === 'netCylinder') { w = 2 * Math.PI * item.radius; h = item.height + 2 * item.radius; }</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (item.type === 'netPyramid') { w = item.baseUnitSize + 2 * item.triangleHeight; h = item.baseUnitSize + 2 * item.triangleHeight; }</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (item.type === 'netCone') { w = item.slantHeight * 2; h = item.slantHeight + 2 * item.radius; }</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (item.type === 'netRectPrism') { w = item.rectD * 2 + item.rectW * 2; h = item.rectH + 2 * item.rectD; }</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (item.type === 'netTriPrism') { w = item.baseSide * 3; h = item.prismLength + 2 * (Math.sqrt(3)/2 * item.baseSide); }</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (item.type === 'algebraTile') { w = item.width; h = item.height; }</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (item.type === 'tenFrame') { w = item.cols * item.cellWidth; h = item.rows * item.cellHeight; }</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (item.type === 'multiplicationArray') { w = item.cols * item.cellWidth; h = item.rows * item.cellHeight; }</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (item.type === 'hundredsChart') { w = 10 * item.cellWidth; h = 10 * item.cellHeight; }</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (item.type === 'barGraph' || item.type === 'coordinatePlane' || item.type === 'scatterPlot') { w = item.width; h = item.height; }</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (item.type === 'additionNumberBond' || item.type === 'multiplicationNumberBond') { w = item.radius * 3; h = item.radius * 3.5; }</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (item.type === 'factorTree') { w = item.nodeRadius * 8; h = item.nodeRadius * 10; } // Estimate</p>
<p class="p1"><span class="Apple-converted-space">                </span>else { return { minX: item.x, minY: item.y, maxX: item.x, maxY: item.y }; }<span class="Apple-converted-space"> </span></p>
<p class="p2"><span class="Apple-converted-space">                </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>const is3DBlock = ['cube', 'rod', 'flat', 'block1000', 'shape3DCube', 'shape3DCylinder', 'shape3DPyramid', 'shape3DCone', 'shape3DRectPrism', 'shape3DTriPrism'].includes(item.type);</p>
<p class="p1"><span class="Apple-converted-space">                </span>const visualWidth = (['shape3DCylinder', 'shape3DCone'].includes(item.type)) ? item.radius * 2 : (w + (is3DBlock &amp;&amp; !['shape3DCylinder', 'shape3DCone'].includes(item.type) ? po : 0));</p>
<p class="p1"><span class="Apple-converted-space">                </span>const visualHeight = (['shape3DCylinder', 'shape3DCone'].includes(item.type)) ? item.height : (h + (is3DBlock &amp;&amp; !['shape3DCylinder', 'shape3DCone'].includes(item.type) ? po : 0));</p>
<p class="p1"><span class="Apple-converted-space">                </span>let yOffsetFor3D = (is3DBlock &amp;&amp; !['shape3DCylinder', 'shape3DCone'].includes(item.type)) ? po / 2 : 0;</p>
<p class="p2"><span class="Apple-converted-space">                </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>let specificMinY = item.y - visualHeight / 2 - yOffsetFor3D;</p>
<p class="p1"><span class="Apple-converted-space">                </span>let specificMaxY = item.y + visualHeight / 2 - yOffsetFor3D;</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (item.type === 'netCone') {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>specificMinY = item.y - (item.slantHeight + 2 * item.radius);</p>
<p class="p1"><span class="Apple-converted-space">                    </span>specificMaxY = item.y;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>}</p>
<p class="p1"><span class="Apple-converted-space">                 </span>if (item.type === 'factorTree' &amp;&amp; item.aabb) { // Use calculated AABB if available</p>
<p class="p1"><span class="Apple-converted-space">                    </span>return {minX: item.aabb.minX, minY: item.aabb.minY, maxX: item.aabb.maxX, maxY: item.aabb.maxY};</p>
<p class="p1"><span class="Apple-converted-space">                </span>}</p>
<p class="p2"><br></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">                </span>return {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>minX: item.x - visualWidth / 2,</p>
<p class="p1"><span class="Apple-converted-space">                    </span>minY: specificMinY,</p>
<p class="p1"><span class="Apple-converted-space">                    </span>maxX: item.x + visualWidth / 2,</p>
<p class="p1"><span class="Apple-converted-space">                    </span>maxY: specificMaxY</p>
<p class="p1"><span class="Apple-converted-space">                </span>};</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>let minX = Infinity, maxX = -Infinity, minY = Infinity, maxY = -Infinity;</p>
<p class="p1"><span class="Apple-converted-space">            </span>vertices.forEach(v =&gt; {</p>
<p class="p1"><span class="Apple-converted-space">                </span>minX = Math.min(minX, v.x);</p>
<p class="p1"><span class="Apple-converted-space">                </span>maxX = Math.max(maxX, v.x);</p>
<p class="p1"><span class="Apple-converted-space">                </span>minY = Math.min(minY, v.y);</p>
<p class="p1"><span class="Apple-converted-space">                </span>maxY = Math.max(maxY, v.y);</p>
<p class="p1"><span class="Apple-converted-space">            </span>});</p>
<p class="p1"><span class="Apple-converted-space">            </span>return { minX, minY, maxX, maxY };</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>function isPointOverItem(mouseX, mouseY, item) {</p>
<p class="p1"><span class="Apple-converted-space">            </span>if (!item) return false;</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const localMouseX = (mouseX - item.x) * Math.cos(-(item.angle || 0)) - (mouseY - item.y) * Math.sin(-(item.angle || 0));</p>
<p class="p1"><span class="Apple-converted-space">            </span>const localMouseY = (mouseX - item.x) * Math.sin(-(item.angle || 0)) + (mouseY - item.y) * Math.cos(-(item.angle || 0));</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>let halfWidth, halfHeight, po = 0;</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>if (item.type === 'cube' || item.type === 'dice' || item.type === 'shape3DCube') { halfWidth = item.size / 2; halfHeight = item.size / 2; if(item.type === 'cube' || item.type === 'shape3DCube') po = item.size * DEPTH_PERSPECTIVE_RATIO; }</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'rod') { halfWidth = item.length / 2; halfHeight = item.height / 2; po = item.depth * DEPTH_PERSPECTIVE_RATIO; }</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'flat') { halfWidth = item.sideLength / 2; halfHeight = item.sideLength / 2; po = item.thickness * DEPTH_PERSPECTIVE_RATIO; }</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'block1000') { halfWidth = item.sideLength / 2; halfHeight = item.sideLength / 2; po = item.sideLength * DEPTH_PERSPECTIVE_RATIO; }</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'circle' || item.type === 'clock' || item.type === 'fractionCircleQuarters' || item.type === 'spinner') { return Math.sqrt(localMouseX*localMouseX + localMouseY*localMouseY) &lt;= item.radius; }<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'patternSquare') { halfWidth = item.sideLength/2; halfHeight = item.sideLength/2; }</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'patternTriangle') { const h = item.sideLength * (Math.sqrt(3)/2); const p0x=0, p0y=-h*2/3, p1x=-item.sideLength/2, p1y=h/3, p2x=item.sideLength/2, p2y=h/3; const areaOrig=Math.abs(p0x*(p1y-p2y)+p1x*(p2y-p0y)+p2x*(p0y-p1y))/2; const area1=Math.abs(localMouseX*(p1y-p2y)+p1x*(p2y-localMouseY)+p2x*(localMouseY-p1y))/2; const area2=Math.abs(p0x*(localMouseY-p2y)+localMouseX*(p2y-p0y)+p2x*(p0y-localMouseY))/2; const area3=Math.abs(p0x*(p1y-localMouseY)+p1x*(localMouseY-p0y)+localMouseX*(p0y-p1y))/2; return Math.abs(area1+area2+area3 - areaOrig) &lt; 0.1; }<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'patternHexagon') { const r = item.sideLength; return Math.sqrt(localMouseX*localMouseX + localMouseY*localMouseY) &lt;= r; }<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'patternTrapezoid') {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>const h = item.height;</p>
<p class="p1"><span class="Apple-converted-space">                </span>const vertices = [ {x: -item.longBase / 2, y: h / 2}, {x: item.longBase / 2, y: h / 2}, {x: item.shortBase / 2, y: -h / 2}, {x: -item.shortBase / 2, y: -h / 2} ];</p>
<p class="p1"><span class="Apple-converted-space">                </span>let inside = false; for (let i = 0, j = vertices.length - 1; i &lt; vertices.length; j = i++) { const xi = vertices[i].x, yi = vertices[i].y; const xj = vertices[j].x, yj = vertices[j].y; const intersect = ((yi &gt; localMouseY) !== (yj &gt; localMouseY)) &amp;&amp; (localMouseX &lt; (xj - xi) * (localMouseY - yi) / (yj - yi) + xi); if (intersect) inside = !inside; } return inside;</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'patternBlueRhombus' || item.type === 'patternTanRhombus') {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>const s = item.sideLength; const angleRad = item.angleDeg * Math.PI / 180;</p>
<p class="p1"><span class="Apple-converted-space">                </span>const hD1 = s * Math.sin(angleRad / 2); const hD2 = s * Math.cos(angleRad / 2);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>return (Math.abs(localMouseX) / hD1 + Math.abs(localMouseY) / hD2 &lt;= 1);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type.startsWith('numLine') || item.type === 'fractionStripEighths') { halfWidth = item.length / 2; halfHeight = item.height / 2 + baseSize * 0.4; }<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'cuisenaireRod') { halfWidth = item.width / 2; halfHeight = item.height / 2; }</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'shape3DCylinder' || item.type === 'shape3DCone') {</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (Math.abs(localMouseX) &lt;= item.radius &amp;&amp; Math.abs(localMouseY) &lt;= item.height / 2) return true;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>const ellipseH = item.radius * DEPTH_PERSPECTIVE_RATIO;</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (Math.pow(localMouseX / item.radius, 2) + Math.pow((localMouseY - item.height/2) / ellipseH, 2) &lt;= 1) return true;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>if (item.type === 'shape3DCylinder' &amp;&amp; Math.pow(localMouseX / item.radius, 2) + Math.pow((localMouseY + item.height/2) / ellipseH, 2) &lt;= 1) return true;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>return false;</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">             </span>else if (item.type === 'shape3DPyramid') {</p>
<p class="p1"><span class="Apple-converted-space">                </span>halfWidth = item.baseSize / 2; halfHeight = item.height / 2;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>po = item.baseSize * DEPTH_PERSPECTIVE_RATIO * 0.5;</p>
<p class="p1"><span class="Apple-converted-space">                </span>const xMinPy = -halfWidth;</p>
<p class="p1"><span class="Apple-converted-space">                </span>const xMaxPy = halfWidth + po;</p>
<p class="p1"><span class="Apple-converted-space">                </span>const yMinPy = -halfHeight;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>const yMaxPy = halfHeight - po;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>return (localMouseX &gt;= xMinPy &amp;&amp; localMouseX &lt;= xMaxPy &amp;&amp; localMouseY &gt;= yMinPy &amp;&amp; localMouseY &lt;= yMaxPy);</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'shape3DRectPrism') {</p>
<p class="p1"><span class="Apple-converted-space">                </span>halfWidth = item.width / 2; halfHeight = item.height / 2;</p>
<p class="p1"><span class="Apple-converted-space">                </span>po = item.depth * DEPTH_PERSPECTIVE_RATIO;</p>
<p class="p1"><span class="Apple-converted-space">                </span>const xMinRP = -halfWidth;</p>
<p class="p1"><span class="Apple-converted-space">                </span>const xMaxRP = halfWidth + po;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>const yMinRP = -halfHeight -po;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>const yMaxRP = halfHeight;</p>
<p class="p1"><span class="Apple-converted-space">                </span>return (localMouseX &gt;= xMinRP &amp;&amp; localMouseX &lt;= xMaxRP &amp;&amp; localMouseY &gt;= yMinRP &amp;&amp; localMouseY &lt;= yMaxRP);</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'shape3DTriPrism') {</p>
<p class="p1"><span class="Apple-converted-space">                </span>const s = item.baseSide;</p>
<p class="p1"><span class="Apple-converted-space">                </span>const triH = (Math.sqrt(3)/2) * s;</p>
<p class="p1"><span class="Apple-converted-space">                </span>halfWidth = s/2;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>halfHeight = triH/2;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>po = item.prismLength * DEPTH_PERSPECTIVE_RATIO;</p>
<p class="p1"><span class="Apple-converted-space">                </span>const xMinTP = -halfWidth;</p>
<p class="p1"><span class="Apple-converted-space">                </span>const xMaxTP = halfWidth + po;</p>
<p class="p1"><span class="Apple-converted-space">                </span>const yMinTP = -triH * 2/3 - po*0.3;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>const yMaxTP = triH * 1/3; <span class="Apple-converted-space">   </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>return (localMouseX &gt;= xMinTP &amp;&amp; localMouseX &lt;= xMaxTP &amp;&amp; localMouseY &gt;= yMinTP &amp;&amp; localMouseY &lt;= yMaxTP);</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'netCube') {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>halfWidth = item.unitSize * 2;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>halfHeight = item.unitSize * 1.5;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>}<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'netCylinder') {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>const rectW = 2 * Math.PI * item.radius;</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (Math.abs(localMouseX) &lt;= rectW / 2 &amp;&amp; Math.abs(localMouseY) &lt;= item.height / 2) return true;</p>
<p class="p1"><span class="Apple-converted-space">                </span>const topCircleY = -item.height / 2 - item.radius;</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (Math.sqrt(localMouseX*localMouseX + (localMouseY - topCircleY)*(localMouseY - topCircleY)) &lt;= item.radius) return true;</p>
<p class="p1"><span class="Apple-converted-space">                </span>const bottomCircleY = item.height / 2 + item.radius;</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (Math.sqrt(localMouseX*localMouseX + (localMouseY - bottomCircleY)*(localMouseY - bottomCircleY)) &lt;= item.radius) return true;</p>
<p class="p1"><span class="Apple-converted-space">                </span>return false;</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">             </span>else if (item.type === 'netPyramid') {</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (Math.abs(localMouseX) &lt;= item.baseUnitSize/2 &amp;&amp; Math.abs(localMouseY) &lt;= item.baseUnitSize/2) return true;</p>
<p class="p1"><span class="Apple-converted-space">                </span>const b = item.baseUnitSize; const tH = item.triangleHeight;</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (localMouseX &gt;= -b/2 &amp;&amp; localMouseX &lt;= b/2 &amp;&amp; localMouseY &lt;= -b/2 &amp;&amp; localMouseY &gt;= -b/2 - tH) return true;</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (localMouseX &gt;= -b/2 &amp;&amp; localMouseX &lt;= b/2 &amp;&amp; localMouseY &gt;= b/2 &amp;&amp; localMouseY &lt;= b/2 + tH) return true;</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (localMouseY &gt;= -b/2 &amp;&amp; localMouseY &lt;= b/2 &amp;&amp; localMouseX &lt;= -b/2 &amp;&amp; localMouseX &gt;= -b/2 - tH) return true;</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (localMouseY &gt;= -b/2 &amp;&amp; localMouseY &lt;= b/2 &amp;&amp; localMouseX &gt;= b/2 &amp;&amp; localMouseX &lt;= b/2 + tH) return true;</p>
<p class="p1"><span class="Apple-converted-space">                </span>return false;</p>
<p class="p1"><span class="Apple-converted-space">            </span>} else if (item.type === 'netCone') {</p>
<p class="p1"><span class="Apple-converted-space">                </span>const sL = item.slantHeight; const rBase = item.radius;</p>
<p class="p1"><span class="Apple-converted-space">                </span>const distToApex = Math.sqrt(localMouseX*localMouseX + localMouseY*localMouseY);</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (distToApex &lt;= sL) {</p>
<p class="p1"><span class="Apple-converted-space">                    </span>let angle = Math.atan2(localMouseY, localMouseX);</p>
<p class="p1"><span class="Apple-converted-space">                    </span>const sectorAngle = (2 * Math.PI * rBase) / sL;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>const startAngle = -sectorAngle / 2 - Math.PI / 2;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>const endAngle = sectorAngle / 2 - Math.PI / 2;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>while (angle &lt; startAngle) angle += 2 * Math.PI;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>while (angle &gt; endAngle) angle -= 2 * Math.PI;</p>
<p class="p1"><span class="Apple-converted-space">                    </span>if (angle &gt;= startAngle &amp;&amp; angle &lt;= endAngle) return true;</p>
<p class="p1"><span class="Apple-converted-space">                </span>}</p>
<p class="p1"><span class="Apple-converted-space">                </span>const baseCircleCenterY = -(sL + rBase);</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (Math.sqrt(localMouseX*localMouseX + (localMouseY - baseCircleCenterY)*(localMouseY - baseCircleCenterY)) &lt;= rBase) return true;</p>
<p class="p1"><span class="Apple-converted-space">                </span>return false;</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'netRectPrism') {</p>
<p class="p1"><span class="Apple-converted-space">                </span>const w = item.rectW; const h = item.rectH; const d = item.rectD;</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (localMouseX &gt;= -w/2 &amp;&amp; localMouseX &lt;= w/2 &amp;&amp; localMouseY &gt;= -h/2 &amp;&amp; localMouseY &lt;= h/2) return true;</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (localMouseX &gt;= -w/2 &amp;&amp; localMouseX &lt;= w/2 &amp;&amp; localMouseY &gt;= -h/2 - d &amp;&amp; localMouseY &lt;= -h/2) return true;</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (localMouseX &gt;= -w/2 &amp;&amp; localMouseX &lt;= w/2 &amp;&amp; localMouseY &gt;= h/2 &amp;&amp; localMouseY &lt;= h/2 + d) return true;</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (localMouseX &gt;= -w/2 - d &amp;&amp; localMouseX &lt;= -w/2 &amp;&amp; localMouseY &gt;= -h/2 &amp;&amp; localMouseY &lt;= h/2) return true;</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (localMouseX &gt;= w/2 &amp;&amp; localMouseX &lt;= w/2 + d &amp;&amp; localMouseY &gt;= -h/2 &amp;&amp; localMouseY &lt;= h/2) return true;</p>
<p class="p1"><span class="Apple-converted-space">                </span>if (localMouseX &gt;= w/2 + d &amp;&amp; localMouseX &lt;= w/2 + d + w &amp;&amp; localMouseY &gt;= -h/2 &amp;&amp; localMouseY &lt;= h/2) return true;</p>
<p class="p1"><span class="Apple-converted-space">                </span>return false;</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'netTriPrism') {</p>
<p class="p1"><span class="Apple-converted-space">                </span>const s = item.baseSide; const l = item.prismLength; const triH = (Math.sqrt(3)/2)*s;</p>
<p class="p1"><span class="Apple-converted-space">                </span>if(localMouseX &gt;= -s/2 &amp;&amp; localMouseX &lt;= s/2 &amp;&amp; localMouseY &gt;= -l/2 &amp;&amp; localMouseY &lt;= l/2) return true;</p>
<p class="p1"><span class="Apple-converted-space">                </span>if(localMouseX &gt;= -s/2-s &amp;&amp; localMouseX &lt;= -s/2 &amp;&amp; localMouseY &gt;= -l/2 &amp;&amp; localMouseY &lt;= l/2) return true;</p>
<p class="p1"><span class="Apple-converted-space">                </span>if(localMouseX &gt;= s/2 &amp;&amp; localMouseX &lt;= s/2+s &amp;&amp; localMouseY &gt;= -l/2 &amp;&amp; localMouseY &lt;= l/2) return true;</p>
<p class="p1"><span class="Apple-converted-space">                </span>let p0={x:-s/2, y:-l/2}, p1={x:s/2, y:-l/2}, p2={x:0, y:-l/2-triH};</p>
<p class="p1"><span class="Apple-converted-space">                </span>let A = 0.5 * (-p1.y * p2.x + p0.y * (-p1.x + p2.x) + p0.x * (p1.y - p2.y) + p1.x * p2.y);</p>
<p class="p1"><span class="Apple-converted-space">                </span>let sign = A &lt; 0 ? -1 : 1;</p>
<p class="p1"><span class="Apple-converted-space">                </span>let s_ = (p0.y * p2.x - p0.x * p2.y + (p2.y - p0.y) * localMouseX + (p0.x - p2.x) * localMouseY) * sign;</p>
<p class="p1"><span class="Apple-converted-space">                </span>let t_ = (p0.x * p1.y - p0.y * p1.x + (p0.y - p1.y) * localMouseX + (p1.x - p0.x) * localMouseY) * sign;</p>
<p class="p1"><span class="Apple-converted-space">                </span>if(s_ &gt; 0 &amp;&amp; t_ &gt; 0 &amp;&amp; (s_ + t_) &lt; (2 * A * sign)) return true;</p>
<p class="p1"><span class="Apple-converted-space">                </span>p0={x:-s/2, y:l/2}; p1={x:s/2, y:l/2}; p2={x:0, y:l/2+triH};</p>
<p class="p1"><span class="Apple-converted-space">                </span>A = 0.5 * (-p1.y * p2.x + p0.y * (-p1.x + p2.x) + p0.x * (p1.y - p2.y) + p1.x * p2.y);</p>
<p class="p1"><span class="Apple-converted-space">                </span>sign = A &lt; 0 ? -1 : 1;</p>
<p class="p1"><span class="Apple-converted-space">                </span>s_ = (p0.y * p2.x - p0.x * p2.y + (p2.y - p0.y) * localMouseX + (p0.x - p2.x) * localMouseY) * sign;</p>
<p class="p1"><span class="Apple-converted-space">                </span>t_ = (p0.x * p1.y - p0.y * p1.x + (p0.y - p1.y) * localMouseX + (p1.x - p0.x) * localMouseY) * sign;</p>
<p class="p1"><span class="Apple-converted-space">                </span>if(s_ &gt; 0 &amp;&amp; t_ &gt; 0 &amp;&amp; (s_ + t_) &lt; (2 * A * sign)) return true;</p>
<p class="p1"><span class="Apple-converted-space">                </span>return false;</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>else if (item.type === 'algebraTile' || item.type === 'tenFrame' || item.type === 'multiplicationArray' || item.type === 'hundredsChart' || item.type === 'barGraph' || item.type === 'coordinatePlane' || item.type === 'scatterPlot' || item.type.includes('NumberBond') || item.type === 'factorTree') {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>let w = 0, h = 0;</p>
<p class="p1"><span class="Apple-converted-space">                </span>if(item.type === 'algebraTile') { w = item.width; h = item.height; }</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if(item.type === 'tenFrame') { w = item.cols * item.cellWidth; h = item.rows * item.cellHeight; }</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if(item.type === 'multiplicationArray') { w = item.cols * item.cellWidth; h = item.rows * item.cellHeight; }</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if(item.type === 'hundredsChart') { w = 10 * item.cellWidth; h = 10 * item.cellHeight; }</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (item.type === 'barGraph' || item.type === 'coordinatePlane' || item.type === 'scatterPlot') { w = item.width; h = item.height; }</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (item.type.includes('NumberBond')) { w = item.radius * 3; h = item.radius * 3.5; }</p>
<p class="p1"><span class="Apple-converted-space">                </span>else if (item.type === 'factorTree' &amp;&amp; item.aabb) { // Use pre-calculated AABB for factor tree</p>
<p class="p1"><span class="Apple-converted-space">                    </span>return mouseX &gt;= item.aabb.minX &amp;&amp; mouseX &lt;= item.aabb.maxX &amp;&amp; mouseY &gt;= item.aabb.minY &amp;&amp; mouseY &lt;= item.aabb.maxY;</p>
<p class="p1"><span class="Apple-converted-space">                </span>} else if (item.type === 'factorTree') { // Fallback if AABB not yet set</p>
<p class="p1"><span class="Apple-converted-space">                     </span>w = item.nodeRadius * 8; h = item.nodeRadius * 10;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">                </span>}</p>
<p class="p1"><span class="Apple-converted-space">                </span>halfWidth = w / 2;</p>
<p class="p1"><span class="Apple-converted-space">                </span>halfHeight = h / 2;</p>
<p class="p1"><span class="Apple-converted-space">            </span>}</p>
<p class="p1"><span class="Apple-converted-space">            </span>else { return false; }</p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">            </span>const is3DBlock = ['cube', 'rod', 'flat', 'block1000', 'shape3DCube'].includes(item.type);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const xMin = -halfWidth;</p>
<p class="p1"><span class="Apple-converted-space">            </span>const xMax = halfWidth + (is3DBlock ? po : 0);<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>const yMin = -halfHeight - (is3DBlock ? po : 0);</p>
<p class="p1"><span class="Apple-converted-space">            </span>const yMax = halfHeight;</p>
<p class="p2"><span class="Apple-converted-space">            </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>return (localMouseX &gt;= xMin &amp;&amp; localMouseX &lt;= xMax &amp;&amp; localMouseY &gt;= yMin &amp;&amp; localMouseY &lt;= yMax);</p>
<p class="p1"><span class="Apple-converted-space">        </span>}</p>
<p class="p2"><br></p>
<p class="p2"><br></p>
<p class="p1"><span class="Apple-converted-space">        </span>// --- Initial setup ---</p>
<p class="p1"><span class="Apple-converted-space">        </span>window.onload = () =&gt; {<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>oldCanvasWidth = window.innerWidth;<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">            </span>oldCanvasHeight = window.innerHeight;</p>
<p class="p1"><span class="Apple-converted-space">            </span>resizeCanvasAndDraw();<span class="Apple-converted-space"> </span></p>
<p class="p1"><span class="Apple-converted-space">        </span>};</p>
<p class="p1"><span class="Apple-converted-space">        </span>// --- Handle window resize ---</p>
<p class="p1"><span class="Apple-converted-space">        </span>window.addEventListener('resize', resizeCanvasAndDraw);</p>
<p class="p1"><span class="Apple-converted-space">    </span>&lt;/script&gt;</p>
<p class="p1">&lt;/body&gt;</p>
<p class="p1">&lt;/html&gt;</p>
</body>
</html>
