<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clock Example</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
            font-family: Arial, sans-serif;
            height: 100%;
            max-width: 100%;
            overflow-x: hidden;
            box-sizing: border-box;
        }
        .clock-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: calc(100% - 40px);
            max-width: 100%;
            padding: 1rem;
            box-sizing: border-box;
        }
        .clock {
            width: min(200px, 80vw);
            height: min(200px, 80vw);
            max-width: 300px;
            max-height: 300px;
            border: 4px solid #333;
            border-radius: 50%;
            position: relative;
            background: white;
            aspect-ratio: 1;
        }
        .clock-center {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 8px;
            height: 8px;
            background: #333;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            z-index: 10;
        }
        .hour-hand {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 3px;
            height: 60px;
            background: #333;
            transform-origin: bottom center;
            transform: translate(-50%, -100%);
        }
        .minute-hand {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 2px;
            height: 80px;
            background: #666;
            transform-origin: bottom center;
            transform: translate(-50%, -100%);
        }
        .clock-numbers {
            position: absolute;
            width: 100%;
            height: 100%;
        }
        .number {
            position: absolute;
            font-size: clamp(12px, 3vw, 16px);
            font-weight: bold;
            color: #333;
        }

        /* Responsive media queries */
        @media (max-width: 768px) {
            .clock-container {
                min-height: 300px;
                padding: 0.5rem;
            }
            .clock {
                width: min(150px, 70vw);
                height: min(150px, 70vw);
            }
        }

        @media (max-width: 480px) {
            .clock-container {
                min-height: 250px;
            }
            .clock {
                width: min(120px, 60vw);
                height: min(120px, 60vw);
                border-width: 2px;
            }
            .hour-hand {
                height: 40px;
                width: 2px;
            }
            .minute-hand {
                height: 50px;
                width: 1px;
            }
        }
    </style>
</head>
<body>
    <div class="clock-container">
        <div class="clock">
            <div class="clock-numbers">
                <div class="number" style="top: 10px; left: 50%; transform: translateX(-50%);">12</div>
                <div class="number" style="top: 50%; right: 10px; transform: translateY(-50%);">3</div>
                <div class="number" style="bottom: 10px; left: 50%; transform: translateX(-50%);">6</div>
                <div class="number" style="top: 50%; left: 10px; transform: translateY(-50%);">9</div>
            </div>
            <div class="hour-hand" style="transform: translate(-50%, -100%) rotate(90deg);"></div>
            <div class="minute-hand" style="transform: translate(-50%, -100%) rotate(180deg);"></div>
            <div class="clock-center"></div>
        </div>
    </div>

    <script>
        // Example of interactive clock functionality
        function setClock(hours, minutes) {
            const hourHand = document.querySelector('.hour-hand');
            const minuteHand = document.querySelector('.minute-hand');

            const hourAngle = (hours % 12) * 30 + (minutes * 0.5); // 30 degrees per hour + minute adjustment
            const minuteAngle = minutes * 6; // 6 degrees per minute

            hourHand.style.transform = `translate(-50%, -100%) rotate(${hourAngle}deg)`;
            minuteHand.style.transform = `translate(-50%, -100%) rotate(${minuteAngle}deg)`;
        }

        // Set clock to 3:30
        setClock(3, 30);
    </script>
</body>
</html>
