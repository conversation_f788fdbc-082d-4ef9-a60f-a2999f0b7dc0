"use client"

import React from 'react';
import ReactMarkdown from 'react-markdown';
import rehypeRaw from 'rehype-raw';

interface MarkdownRendererProps {
  markdown: string;
  className?: string;
  style?: React.CSSProperties;
}

export default function MarkdownRenderer({ markdown, className = "", style = {} }: MarkdownRendererProps) {
  return (
    <div className={`markdown-content ${className}`} style={style}>
      <ReactMarkdown 
        rehypePlugins={[rehypeRaw]} 
        components={{
          // Customize heading styles
          h1: ({ node, ...props }) => <h1 className="text-2xl font-bold mb-4" {...props} />,
          h2: ({ node, ...props }) => <h2 className="text-xl font-bold mb-3" {...props} />,
          h3: ({ node, ...props }) => <h3 className="text-lg font-bold mb-2" {...props} />,
          h4: ({ node, ...props }) => <h4 className="text-base font-bold mb-2" {...props} />,
          
          // Customize paragraph styles
          p: ({ node, ...props }) => <p className="mb-4" {...props} />,
          
          // Customize list styles
          ul: ({ node, ...props }) => <ul className="list-disc pl-5 mb-4" {...props} />,
          ol: ({ node, ...props }) => <ol className="list-decimal pl-5 mb-4" {...props} />,
          li: ({ node, ...props }) => <li className="mb-1" {...props} />,
          
          // Customize link styles
          a: ({ node, ...props }) => <a className="text-blue-500 hover:underline" {...props} />,
          
          // Customize code block styles
          code: ({ node, inline, ...props }) => 
            inline 
              ? <code className="bg-gray-100 px-1 py-0.5 rounded text-sm" {...props} />
              : <code className="block bg-gray-100 p-2 rounded text-sm overflow-x-auto my-4" {...props} />,
          
          // Customize blockquote styles
          blockquote: ({ node, ...props }) => 
            <blockquote className="border-l-4 border-gray-300 pl-4 italic my-4" {...props} />,
          
          // Customize table styles
          table: ({ node, ...props }) => <table className="min-w-full border-collapse my-4" {...props} />,
          thead: ({ node, ...props }) => <thead className="bg-gray-100" {...props} />,
          tbody: ({ node, ...props }) => <tbody className="divide-y divide-gray-200" {...props} />,
          tr: ({ node, ...props }) => <tr className="hover:bg-gray-50" {...props} />,
          th: ({ node, ...props }) => <th className="px-4 py-2 text-left font-medium text-gray-700" {...props} />,
          td: ({ node, ...props }) => <td className="px-4 py-2" {...props} />,
        }}
      >
        {markdown}
      </ReactMarkdown>
    </div>
  );
}
