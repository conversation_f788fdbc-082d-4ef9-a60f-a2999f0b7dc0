"use client"

import type React from "react"

import { useState } from "react"
import {
  Home,
  BookOpen,
  FileText,
  ClipboardList,
  BarChart3,
  Flame,
  ChevronLeft,
  ChevronDown,
  Plus,
  CheckCircle,
  AlertCircle,
} from "lucide-react"
import { cn } from "@/lib/utils"

interface SidebarProps {
  currentSlide: number
  setCurrentSlide: (slide: number) => void
  setSidebarOpen: (open: boolean) => void
  activeTab: string
  setActiveTab: (tab: string) => void
}

export function Sidebar({ currentSlide, setCurrentSlide, setSidebarOpen, activeTab, setActiveTab }: SidebarProps) {
  const [expandedUnit, setExpandedUnit] = useState<number | null>(null)
  const [expandedGrade, setExpandedGrade] = useState<string | null>(null)

  return (
    <div className="w-72 flex-shrink-0 border-r border-r-gray-200 bg-white shadow-lg flex flex-col">
      <div className="flex items-center justify-between bg-[#2B6DFE] p-5 text-white border-r border-r-[#2B6DFE] relative">
        <h2 className="text-lg font-montserrat font-black">Dashboard</h2>

        {/* Tab to toggle sidebar */}
        <div
          className="absolute -right-6 top-0 h-full w-6 bg-[#fadb9a]/80 flex items-center justify-center cursor-pointer rounded-r-md z-50"
          onClick={(e) => {
            e.stopPropagation()
            setSidebarOpen(false)
          }}
        >
          <ChevronLeft size={16} className="text-[#2B6DFE] stroke-[3]" />
        </div>
      </div>

      <div className="flex flex-col flex-1 overflow-hidden">
        {/* Navigation Tabs */}
        <div className="p-3 space-y-2">
          <NavItem
            icon={<Home size={20} />}
            label="Home"
            active={activeTab === "home"}
            onClick={() => setActiveTab("home")}
          />

          <NavItem
            icon={<BookOpen size={20} />}
            label="Scope and Sequence"
            active={activeTab === "scope"}
            onClick={() => setActiveTab("scope")}
          />

          <NavItem
            icon={<FileText size={20} />}
            label="Quizzes and Tests"
            active={activeTab === "quizzes"}
            onClick={() => setActiveTab("quizzes")}
          />

          <NavItem
            icon={<ClipboardList size={20} />}
            label="Practice Builder"
            active={activeTab === "practice"}
            onClick={() => setActiveTab("practice")}
          />

          <NavItem
            icon={<BarChart3 size={20} />}
            label="Student Achievement"
            active={activeTab === "achievement"}
            onClick={() => setActiveTab("achievement")}
          />

          <NavItem
            icon={<Flame size={20} />}
            label="ELSA"
            active={activeTab === "elsa"}
            onClick={() => setActiveTab("elsa")}
            badge="AI"
          />
        </div>

        {/* Tab Content */}
        <div className="flex-1 overflow-y-auto p-3">{/* Tab content has been moved to the main content area */}</div>
      </div>
    </div>
  )
}

// Helper function to get unit titles
function getUnitTitle(unitNumber: number): string {
  const unitTitles = [
    "Numbers and Operations",
    "Addition and Subtraction",
    "Multiplication and Division",
    "Fractions",
    "Decimals",
    "Geometry",
    "Measurement",
    "Data and Statistics",
    "Algebra",
    "Probability",
    "Ratios and Proportions",
    "Financial Literacy",
  ]
  return unitTitles[unitNumber - 1] || `Unit ${unitNumber}`
}

// Component for navigation items
interface NavItemProps {
  icon: React.ReactNode
  label: string
  active?: boolean
  onClick?: () => void
  badge?: string
}

function NavItem({ icon, label, active = false, onClick, badge }: NavItemProps) {
  return (
    <div
      className={cn(
        "flex cursor-pointer items-center gap-3 px-4 py-3.5 rounded-lg transition-colors",
        active
          ? "bg-blue-50 text-[#2B6DFE] border-l-4 border-[#2B6DFE] font-medium"
          : "text-gray-700 hover:bg-gray-100 border-l-4 border-transparent",
      )}
      onClick={onClick}
    >
      <span className={cn("text-gray-500", active && "text-[#2B6DFE]")}>{icon}</span>
      <span className={cn("text-base", active && "font-medium text-[#2B6DFE]")}>{label}</span>
      {badge && (
        <span className="ml-auto text-xs font-medium px-2 py-1 rounded-full bg-blue-100 text-blue-800">{badge}</span>
      )}
    </div>
  )
}

// Component for quick access cards on home tab
interface QuickAccessCardProps {
  icon: React.ReactNode
  title: string
  count: number
}

function QuickAccessCard({ icon, title, count }: QuickAccessCardProps) {
  return (
    <div className="bg-gray-50 rounded-lg p-3 hover:bg-gray-100 transition-colors cursor-pointer">
      <div className="flex items-center mb-2">
        {icon}
        <span className="ml-auto text-lg font-semibold text-gray-700">{count}</span>
      </div>
      <h4 className="text-xs font-medium text-gray-600">{title}</h4>
    </div>
  )
}

// Component for upcoming lessons on home tab
interface UpcomingLessonProps {
  title: string
  grade: string
  time: string
}

function UpcomingLesson({ title, grade, time }: UpcomingLessonProps) {
  return (
    <div className="bg-white border border-gray-200 rounded-lg p-3 hover:border-blue-300 transition-colors cursor-pointer">
      <h4 className="text-sm font-medium text-gray-800">{title}</h4>
      <div className="flex justify-between mt-1">
        <span className="text-xs text-gray-500">{grade}</span>
        <span className="text-xs text-blue-600">{time}</span>
      </div>
    </div>
  )
}

// Component for activity items on home tab
interface ActivityItemProps {
  icon: React.ReactNode
  title: string
  description: string
  time: string
}

function ActivityItem({ icon, title, description, time }: ActivityItemProps) {
  return (
    <div className="flex items-start">
      <div className="bg-gray-100 rounded-full p-2 mr-3">{icon}</div>
      <div className="flex-1">
        <h4 className="text-sm font-medium text-gray-800">{title}</h4>
        <p className="text-xs text-gray-600">{description}</p>
      </div>
      <span className="text-xs text-gray-500">{time}</span>
    </div>
  )
}

// Component for unit accordions on scope tab
interface UnitAccordionProps {
  unitNumber: number
  title: string
  isExpanded: boolean
  onToggle: () => void
}

function UnitAccordion({ unitNumber, title, isExpanded, onToggle }: UnitAccordionProps) {
  return (
    <div className="border border-gray-200 rounded-lg overflow-hidden">
      <div
        className={`flex items-center justify-between p-3 cursor-pointer ${isExpanded ? "bg-blue-50" : "bg-white"}`}
        onClick={onToggle}
      >
        <div className="flex items-center">
          <span className="flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 text-blue-700 text-xs font-medium mr-2">
            {unitNumber}
          </span>
          <h3 className="text-sm font-medium text-gray-800">{title}</h3>
        </div>
        <ChevronDown
          className={`h-4 w-4 text-gray-500 transition-transform ${isExpanded ? "transform rotate-180" : ""}`}
        />
      </div>

      {isExpanded && (
        <div className="p-3 bg-white border-t border-gray-200">
          <ul className="space-y-2">
            {Array.from({ length: 5 }).map((_, index) => (
              <li key={index} className="flex items-center text-sm text-gray-700 hover:text-blue-600 cursor-pointer">
                <span className="w-6 text-center text-xs text-gray-500 mr-2">{index + 1}</span>
                <span>
                  Lesson {index + 1}: {getLessonTitle(unitNumber, index + 1)}
                </span>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  )
}

// Helper function to get lesson titles
function getLessonTitle(unitNumber: number, lessonNumber: number): string {
  // This is a simplified version - in a real app, you'd have actual lesson titles
  const unitTitles = {
    1: ["Counting", "Place Value", "Comparing Numbers", "Rounding", "Number Patterns"],
    2: ["Addition Facts", "Addition Strategies", "Subtraction Facts", "Subtraction Strategies", "Word Problems"],
    3: ["Multiplication Facts", "Multiplication Strategies", "Division Facts", "Division Strategies", "Word Problems"],
    4: ["Fraction Basics", "Equivalent Fractions", "Comparing Fractions", "Adding Fractions", "Subtracting Fractions"],
    5: ["Decimal Basics", "Comparing Decimals", "Adding Decimals", "Subtracting Decimals", "Decimal Word Problems"],
    6: ["2D Shapes", "3D Shapes", "Angles", "Symmetry", "Coordinate Plane"],
    7: ["Length", "Weight", "Capacity", "Time", "Area and Perimeter"],
    8: ["Collecting Data", "Representing Data", "Mean, Median, Mode", "Interpreting Data", "Creating Graphs"],
    9: ["Patterns", "Variables", "Expressions", "Equations", "Functions"],
    10: [
      "Basic Probability",
      "Experimental Probability",
      "Theoretical Probability",
      "Compound Events",
      "Probability Games",
    ],
    11: ["Understanding Ratios", "Equivalent Ratios", "Rates", "Unit Rates", "Proportional Relationships"],
    12: ["Money Basics", "Saving", "Spending", "Budgeting", "Financial Decisions"],
  }

  return unitTitles[unitNumber as keyof typeof unitTitles]?.[lessonNumber - 1] || `Lesson ${lessonNumber}`
}

// Component for grade accordions on quizzes tab
interface GradeAccordionProps {
  grade: string
  isExpanded: boolean
  onToggle: () => void
}

function GradeAccordion({ grade, isExpanded, onToggle }: GradeAccordionProps) {
  return (
    <div className="border border-gray-200 rounded-lg overflow-hidden">
      <div
        className={`flex items-center justify-between p-3 cursor-pointer ${isExpanded ? "bg-blue-50" : "bg-white"}`}
        onClick={onToggle}
      >
        <h3 className="text-sm font-medium text-gray-800">{grade === "K" ? "Kindergarten" : `Grade ${grade}`}</h3>
        <ChevronDown
          className={`h-4 w-4 text-gray-500 transition-transform ${isExpanded ? "transform rotate-180" : ""}`}
        />
      </div>

      {isExpanded && (
        <div className="p-3 bg-white border-t border-gray-200">
          <div className="mb-2">
            <h4 className="text-xs font-medium text-gray-500 mb-1">QUIZZES</h4>
            <ul className="space-y-1">
              <li className="flex items-center text-sm text-gray-700 hover:text-blue-600 cursor-pointer">
                <FileText className="h-3 w-3 mr-2 text-blue-600" />
                <span>Number Recognition Quiz</span>
              </li>
              <li className="flex items-center text-sm text-gray-700 hover:text-blue-600 cursor-pointer">
                <FileText className="h-3 w-3 mr-2 text-blue-600" />
                <span>Addition Facts Quiz</span>
              </li>
              <li className="flex items-center text-sm text-gray-700 hover:text-blue-600 cursor-pointer">
                <FileText className="h-3 w-3 mr-2 text-blue-600" />
                <span>Shapes and Patterns Quiz</span>
              </li>
            </ul>
          </div>

          <div>
            <h4 className="text-xs font-medium text-gray-500 mb-1">TESTS</h4>
            <ul className="space-y-1">
              <li className="flex items-center text-sm text-gray-700 hover:text-blue-600 cursor-pointer">
                <FileText className="h-3 w-3 mr-2 text-purple-600" />
                <span>Mid-Year Assessment</span>
              </li>
              <li className="flex items-center text-sm text-gray-700 hover:text-blue-600 cursor-pointer">
                <FileText className="h-3 w-3 mr-2 text-purple-600" />
                <span>End-of-Year Assessment</span>
              </li>
            </ul>
          </div>
        </div>
      )}
    </div>
  )
}

// Component for practice items on practice tab
interface PracticeItemProps {
  title: string
  type: string
  status: string
  date: string
}

function PracticeItem({ title, type, status, date }: PracticeItemProps) {
  return (
    <div className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors cursor-pointer">
      <div>
        <h4 className="text-sm font-medium text-gray-800">{title}</h4>
        <div className="flex items-center mt-1">
          <span className="text-xs text-gray-500 mr-2">{type}</span>
          <span
            className={`text-xs px-1.5 py-0.5 rounded-full ${
              status === "Published" ? "bg-green-100 text-green-800" : "bg-amber-100 text-amber-800"
            }`}
          >
            {status}
          </span>
        </div>
      </div>
      <span className="text-xs text-gray-500">{date}</span>
    </div>
  )
}

// Component for template cards on practice tab
interface TemplateCardProps {
  title: string
}

function TemplateCard({ title }: TemplateCardProps) {
  return (
    <div className="flex flex-col items-center justify-center p-3 border border-dashed border-gray-300 rounded-lg hover:border-blue-400 hover:bg-blue-50 transition-colors cursor-pointer">
      <Plus className="h-5 w-5 text-blue-600 mb-1" />
      <span className="text-xs font-medium text-gray-700">{title}</span>
    </div>
  )
}

// Component for student items on achievement tab
interface StudentItemProps {
  name: string
  grade: string
  status: "advanced" | "ontrack" | "atrisk"
}

function StudentItem({ name, grade, status }: StudentItemProps) {
  return (
    <div className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors cursor-pointer">
      <div className="flex items-center">
        <div className="w-7 h-7 rounded-full bg-gray-200 flex items-center justify-center text-xs font-medium text-gray-700 mr-3">
          {name
            .split(" ")
            .map((n) => n[0])
            .join("")}
        </div>
        <span className="text-sm text-gray-800">{name}</span>
      </div>
      <div className="flex items-center">
        <span
          className={`text-sm font-medium mr-2 ${
            grade.startsWith("A")
              ? "text-green-600"
              : grade.startsWith("B")
                ? "text-blue-600"
                : grade.startsWith("C")
                  ? "text-amber-600"
                  : "text-red-600"
          }`}
        >
          {grade}
        </span>
        {status === "advanced" && <CheckCircle className="h-4 w-4 text-green-600" />}
        {status === "ontrack" && <CheckCircle className="h-4 w-4 text-blue-600" />}
        {status === "atrisk" && <AlertCircle className="h-4 w-4 text-red-600" />}
      </div>
    </div>
  )
}
