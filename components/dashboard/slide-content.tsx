"use client"

import { useState, useEffect } from "react"
import { ChevronDown, ChevronUp, Eye } from "lucide-react"
import React from "react"

interface SlideContentProps {
  slideNumber: number
  highContrast?: boolean
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
  setRevealedItems?: React.Dispatch<React.SetStateAction<number[]>>
}

export function SlideContent({
  slideNumber,
  highContrast = false,
  revealedItems = [],
  registerRevealableItems = () => {},
  setRevealedItems = (_items: number[]) => {},
}: SlideContentProps) {
  // Return different content based on the slide number
  switch (slideNumber) {
    case 1:
      return (
        <QuickReviewSlide
          key={`slide-${slideNumber}`}
          highContrast={highContrast}
          revealedItems={revealedItems}
          registerRevealableItems={registerRevealableItems}
          setRevealedItems={setRevealedItems}
        />
      )
    case 2:
      return (
        <LearningGoalsSlide
          key={`slide-${slideNumber}`}
          highContrast={highContrast}
          revealedItems={revealedItems}
          registerRevealableItems={registerRevealableItems}
        />
      )
    case 3:
      return (
        <VocabularySlide
          key={`slide-${slideNumber}`}
          revealedItems={revealedItems}
          registerRevealableItems={registerRevealableItems}
          setRevealedItems={setRevealedItems}
        />
      )
    default:
      return (
        <div className="p-8 text-white">
          <h2 className="text-4xl font-bold mb-6">Slide {slideNumber}</h2>
          <div className="bg-white/10 rounded-lg p-6">
            <p className="text-2xl">Content for slide {slideNumber} will be displayed here.</p>
            <p className="text-lg mt-4 opacity-80">
              This is a placeholder slide. In a real implementation, this would contain the actual lesson content.
            </p>
          </div>
        </div>
      )
  }
}

// Individual slide components
interface QuickReviewSlideProps {
  highContrast?: boolean
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
  setRevealedItems?: React.Dispatch<React.SetStateAction<number[]>>
}

function QuickReviewSlide({
  highContrast = false,
  revealedItems = [],
  registerRevealableItems = () => {},
  setRevealedItems,
}: QuickReviewSlideProps) {
  // Register 3 revealable items (one for each card)
  useEffect(() => {
    registerRevealableItems(3)
  }, [registerRevealableItems])

  return (
    <div className={`p-8 ${highContrast ? "text-black" : "text-white"}`}>
      <h2 className={`text-4xl font-bold mb-6 ${highContrast ? "text-black" : ""}`}>Quick Review</h2>
      <div className="space-y-6">
        <QuickReviewCard
          number={1}
          question="Which number has more tens: 35 or 62?"
          answer="62"
          isVisible={revealedItems.includes(0)}
          onToggleVisibility={() => {
            if (setRevealedItems) {
              if (revealedItems.includes(0)) {
                setRevealedItems(revealedItems.filter((item) => item !== 0))
              } else {
                setRevealedItems([...revealedItems, 0])
              }
            }
          }}
        />

        <QuickReviewCard
          number={2}
          question="What is the value of the 4 in 47?"
          answer="40"
          isVisible={revealedItems.includes(1)}
          onToggleVisibility={() => {
            if (setRevealedItems) {
              if (revealedItems.includes(1)) {
                setRevealedItems(revealedItems.filter((item) => item !== 1))
              } else {
                setRevealedItems([...revealedItems, 1])
              }
            }
          }}
        />

        <QuickReviewCard
          number={3}
          question="Is 70 greater than or less than 20?"
          answer="Greater than"
          isVisible={revealedItems.includes(2)}
          onToggleVisibility={() => {
            if (setRevealedItems) {
              if (revealedItems.includes(2)) {
                setRevealedItems(revealedItems.filter((item) => item !== 2))
              } else {
                setRevealedItems([...revealedItems, 2])
              }
            }
          }}
        />
      </div>
    </div>
  )
}

interface QuickReviewCardProps {
  number: number
  question: string
  answer: string
  isVisible: boolean
  onToggleVisibility: () => void
}

function QuickReviewCard({ number, question, answer, isVisible, onToggleVisibility }: QuickReviewCardProps) {
  const [showAnswer, setShowAnswer] = useState(false)

  useEffect(() => {
    if (!isVisible) {
      setShowAnswer(false)
    }
  }, [isVisible])

  return (
    <div className="rounded-lg bg-white/10 overflow-hidden">
      <div className="p-4 flex items-center justify-between gap-4 cursor-pointer" onClick={onToggleVisibility}>
        <div className="flex items-center gap-4">
          <div className="w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold">
            {number}
          </div>
          <span className="text-xl font-medium">{question}</span>
        </div>
        {isVisible ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
      </div>

      {isVisible && (
        <div className="p-4 pt-0">
          <div className="mt-4 border-t border-white/20 pt-4">
            <div
              className="flex items-center justify-between cursor-pointer mb-2"
              onClick={(e) => {
                e.stopPropagation()
                setShowAnswer(!showAnswer)
              }}
            >
              <span className="font-medium">Answer</span>
              <Eye size={18} className={showAnswer ? "text-yellow-400" : ""} />
            </div>

            {showAnswer && (
              <div className="bg-white/10 p-4 rounded-md text-xl font-medium">{answer}</div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

interface LearningGoalsSlideProps {
  highContrast?: boolean
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
}

function LearningGoalsSlide({
  highContrast = false,
  revealedItems = [],
  registerRevealableItems = () => {},
}: LearningGoalsSlideProps) {
  // Register 3 revealable items (one for each learning goal)
  useEffect(() => {
    registerRevealableItems(3)
  }, [registerRevealableItems])

  const [showAll, setShowAll] = useState(false)

  return (
    <div className={`p-8 ${highContrast ? "text-black" : "text-white"}`}>
      <h2 className={`text-4xl font-bold mb-6 ${highContrast ? "text-black" : ""}`}>Learning Goals</h2>
      
      <div className="flex justify-end mb-4">
        <button onClick={() => setShowAll(!showAll)} className="flex items-center gap-2 text-sm cursor-pointer">
          <span>{showAll ? "Hide All" : "Show All"}</span>
          <Eye size={18} className={showAll ? "text-yellow-400" : ""} />
        </button>
      </div>

      <div className="space-y-6">
        <LearningGoalCard
          number={1}
          title="Compare Two-Digit Numbers"
          content="I will be able to compare two two-digit numbers when the tens digits are the same."
          isVisible={revealedItems.includes(0) || showAll}
        />
        <LearningGoalCard
          number={2}
          title="Use Ones Digit"
          content="I will be able to use the ones digit to tell which number is greater or less."
          isVisible={revealedItems.includes(1) || showAll}
        />
        <LearningGoalCard
          number={3}
          title="Use Comparison Symbols"
          content="I will be able to use the symbols >, =, and < to show how numbers compare."
          isVisible={revealedItems.includes(2) || showAll}
        />
      </div>
    </div>
  )
}

interface LearningGoalCardProps {
  number: number
  title: string
  content: string
  isVisible: boolean
}

function LearningGoalCard({ number, title, content, isVisible }: LearningGoalCardProps) {
  return (
    <div className="rounded-lg bg-white/10 p-4">
      <div className="flex items-start gap-4">
        <div className={`w-8 h-8 rounded-full flex items-center justify-center font-bold ${
          isVisible ? "bg-yellow-400 text-blue-900" : "bg-white/20 text-white"
        }`}>
          {number}
        </div>
        <div className="flex-1">
          {isVisible && (
            <div>
              <h3 className="text-xl font-semibold mb-2">{title}</h3>
              <p className="text-lg opacity-90">{content}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

function VocabularySlide({
  revealedItems = [],
  registerRevealableItems = () => {},
  setRevealedItems = (_items: number[]) => {},
}: {
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
  setRevealedItems?: React.Dispatch<React.SetStateAction<number[]>>
}) {
  const vocabularyTerms = [
    {
      term: "Compare",
      definition: "To look at two numbers and see how they are different, like which is bigger or smaller. 👀",
    },
    {
      term: "Tens Place",
      definition: "The spot on the left in a two-digit number that tells you how many groups of ten there are. 📦📦📦",
    },
    {
      term: "Ones Place",
      definition: "The spot on the right in a two-digit number that tells you how many single ones there are. 🪙",
    },
    {
      term: "Less Than (<)",
      definition: "A symbol that shows the first number is smaller than the second number. The symbol points to the smaller number! 👇",
    },
  ]

  // Register 4 revealable items (one for each vocabulary term)
  useEffect(() => {
    registerRevealableItems(vocabularyTerms.length)
  }, [registerRevealableItems, vocabularyTerms.length])

  return (
    <div className="p-8 text-white">
      <h2 className="text-4xl font-bold mb-6">Vocabulary</h2>
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        {vocabularyTerms.map((item, index) => (
          <div
            key={item.term}
            className={`rounded-lg ${revealedItems.includes(index) ? "bg-white/20" : "bg-white/10"} p-4 cursor-pointer`}
            onClick={() => {
              if (revealedItems.includes(index)) {
                setRevealedItems((prev) => prev.filter((i) => i !== index))
              } else {
                setRevealedItems((prev) => [...prev, index])
              }
            }}
          >
            <div className="flex items-center gap-4 mb-2">
              <div className="w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold">
                {index + 1}
              </div>
              <h3 className="text-2xl font-bold">{item.term}</h3>
            </div>

            {revealedItems.includes(index) && (
              <p className="text-lg mt-2">{item.definition}</p>
            )}

            <div className="mt-2 text-center">
              {revealedItems.includes(index) ? (
                <ChevronUp size={16} className="inline-block" />
              ) : (
                <ChevronDown size={16} className="inline-block" />
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
