"use client"

import React from 'react'
import { X, Home } from 'lucide-react'

interface ComingSoonModalProps {
  isOpen: boolean
  onClose: () => void
}

export default function ComingSoonModal({ isOpen, onClose }: ComingSoonModalProps) {
  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-[1000] flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="relative bg-gray-800/85 px-8 py-8 rounded-3xl border border-gray-700 shadow-lg max-w-md mx-4">
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 p-2 text-gray-400 hover:text-white transition-colors"
        >
          <X size={20} />
        </button>

        {/* Icon */}
        <div className="animate-pulse mb-4">
          <div className="h-20 w-20 rounded-full bg-amber-400 flex items-center justify-center text-white font-bold shadow-lg shadow-amber-400/30 mx-auto">
            <Home className="h-12 w-12" />
          </div>
        </div>

        {/* Title */}
        <h3 className="text-3xl font-black text-white mb-4 tracking-tight font-montserrat text-center">
          Dashboard
        </h3>
        
        {/* Subtitle */}
        <p className="text-amber-300 text-lg mb-4 font-montserrat font-black text-center">
          Teacher Analytics & Tools
        </p>

        {/* Description */}
        <h4 className="text-xl font-bold text-white mb-4 text-center">
          Enhanced Teaching Experience
        </h4>

        {/* Coming Soon Badge */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-amber-400 to-amber-500 text-blue-900 font-semibold py-2 px-6 rounded-full inline-flex items-center gap-2 shadow-lg shadow-amber-400/20 hover:shadow-xl hover:scale-105 transition-all duration-300">
            <span>Coming Soon</span>
          </div>
        </div>
      </div>
    </div>
  )
}
