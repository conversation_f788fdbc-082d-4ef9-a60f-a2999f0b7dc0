"use client"

import { useEffect, useState } from "react"
import { usePresenter } from "./presenter-context"
import { SlideContent } from "./slide-content"
import { ChevronLeft, ChevronRight, Play, Pause, RefreshCw, Maximize2, Clock, ChevronUp, ChevronDown } from "lucide-react"

export function PresenterView() {
  const {
    currentSlide,
    setCurrentSlide,
    revealedItems,
    setRevealedItems,
    presenterNotes,
    elapsedTime,
    startTimer,
    pauseTimer,
    resetTimer,
  } = usePresenter()

  const [totalSlides] = useState(27)
  const [totalRevealableItems, setTotalRevealableItems] = useState(0)
  const [isTimerRunning, setIsTimerRunning] = useState(false)
  const [nextSlideRevealableItems, setNextSlideRevealableItems] = useState(0)
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [totalQuestions, setTotalQuestions] = useState(3) // Example: 3 questions per slide

  // Format elapsed time as mm:ss
  const formatTime = (ms: number) => {
    const seconds = Math.floor(ms / 1000)
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes.toString().padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`
  }

  // Handle timer controls
  const handleTimerToggle = () => {
    if (isTimerRunning) {
      pauseTimer()
      setIsTimerRunning(false)
    } else {
      startTimer()
      setIsTimerRunning(true)
    }
  }

  // Handle slide navigation
  const handlePrevious = () => {
    const newSlide = Math.max(1, currentSlide - 1)
    console.log("Navigating to previous slide:", newSlide)
    setCurrentSlide(newSlide)
  }

  const handleNext = () => {
    const newSlide = Math.min(totalSlides, currentSlide + 1)
    console.log("Navigating to next slide:", newSlide)
    setCurrentSlide(newSlide)
  }

  // Handle question navigation
  const handlePreviousQuestion = () => {
    setCurrentQuestion(Math.max(0, currentQuestion - 1))
  }

  const handleNextQuestion = () => {
    setCurrentQuestion(Math.min(totalQuestions - 1, currentQuestion + 1))
  }

  // Function to reveal the next item
  const revealNextItem = () => {
    if (revealedItems.length < totalRevealableItems) {
      const nextItemIndex = revealedItems.length
      console.log("Revealing item:", nextItemIndex)
      setRevealedItems([...revealedItems, nextItemIndex])
      return true // Item was revealed
    }
    return false // No more items to reveal
  }

  // Function to hide the last revealed item
  const hideLastItem = () => {
    if (revealedItems.length > 0) {
      console.log("Hiding last item")
      setRevealedItems(revealedItems.slice(0, -1))
      return true // Item was hidden
    }
    return false // No items to hide
  }

  // Function to be called from slide components to register their revealable items
  const registerRevealableItems = (count: number) => {
    console.log(`Presenter: registered ${count} revealable items for slide ${currentSlide}`)
    setTotalRevealableItems(count)
  }

  // Function to register revealable items for the next slide
  const registerNextSlideRevealableItems = (count: number) => {
    setNextSlideRevealableItems(count)
  }

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Get the currently focused element
      const target = e.target as HTMLElement
      const isEditableElement = target.tagName === "INPUT" || target.tagName === "TEXTAREA" || target.isContentEditable

      // Only prevent default for navigation keys when not in editable elements
      if ((e.code === "ArrowLeft" || e.code === "ArrowRight") && !isEditableElement) {
        e.preventDefault()
      }

      // Only prevent spacebar default when not in editable elements and not a button
      if (e.code === "Space" && !isEditableElement && target.tagName !== "BUTTON") {
        e.preventDefault()
      }

      // Only process arrow keys for navigation when not in editable elements
      if (!isEditableElement) {
        if (e.code === "ArrowRight") {
          // Try to reveal next item, if none left, go to next slide
          const revealed = revealNextItem()
          if (!revealed) {
            handleNext()
          }
        } else if (e.code === "ArrowLeft") {
          // Try to hide last item, if none left, go to previous slide
          const hidden = hideLastItem()
          if (!hidden) {
            handlePrevious()
          }
        } else if (e.code === "ArrowUp") {
          // Navigate to previous question
          handlePreviousQuestion()
        } else if (e.code === "ArrowDown") {
          // Navigate to next question
          handleNextQuestion()
        }
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => {
      window.removeEventListener("keydown", handleKeyDown)
    }
  }, [revealedItems, totalRevealableItems, currentQuestion])

  // Reset revealed items when changing slides
  useEffect(() => {
    console.log("Slide changed to", currentSlide, "- resetting revealed items")
    setRevealedItems([])
    setCurrentQuestion(0) // Reset current question when changing slides
  }, [currentSlide, setRevealedItems])

  return (
    <div className="min-h-screen bg-gray-100 font-public-sans">
      {/* Header with lesson info */}
      <header className="bg-[linear-gradient(150deg,#2B6DFE_0%,#2B6DFE_70%,#00F2FF_100%)] text-white p-4 shadow-md">
        <div className="container mx-auto flex justify-between items-center">
          <div className="flex items-center gap-4">
            <div className="text-2xl font-bold">EMBRS Presenter</div>
            <div className="text-sm bg-white/20 px-3 py-1 rounded-full">
              Grade 5 • Unit 3 • Lesson 2
            </div>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2 bg-white/20 rounded-md px-3 py-1">
              <Clock size={16} className="text-white" />
              <span className="text-sm font-mono">{formatTime(elapsedTime)}</span>
              <button onClick={handleTimerToggle} className="p-1 rounded hover:bg-white/10">
                {isTimerRunning ? <Pause size={14} /> : <Play size={14} />}
              </button>
              <button onClick={resetTimer} className="p-1 rounded hover:bg-white/10">
                <RefreshCw size={14} />
              </button>
            </div>
            <button
              onClick={() => {
                if (document.fullscreenElement) {
                  document.exitFullscreen()
                } else {
                  document.documentElement.requestFullscreen()
                }
              }}
              className="bg-white/20 p-2 rounded-md hover:bg-white/30"
              title="Toggle fullscreen"
            >
              <Maximize2 size={18} />
            </button>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="container mx-auto p-4 grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
        {/* Left panel: Current slide */}
        <div className="md:col-span-2 space-y-4">
          {/* Current slide */}
          <div className="rounded-lg border text-card-foreground h-3/5 p-4 bg-white shadow-lg flex flex-col senior-friendly-card">
            <h2 className="text-xl font-bold mb-2">Current Slide</h2>
            <div className="flex-grow bg-[linear-gradient(150deg,#2B6DFE_0%,#2B6DFE_70%,#00F2FF_100%)] rounded-lg overflow-hidden">
              <SlideContent
                slideNumber={currentSlide}
                highContrast={false}
                revealedItems={revealedItems}
                registerRevealableItems={registerRevealableItems}
                setRevealedItems={setRevealedItems}
              />
            </div>
            <div className="flex justify-between items-center mt-4">
              <button
                onClick={handlePrevious}
                className="senior-friendly-button bg-gray-200 text-gray-800 hover:bg-gray-300"
              >
                <ChevronLeft size={20} />
                <span>Previous</span>
              </button>
              <div className="text-sm font-medium">
                Slide {currentSlide} of {totalSlides}
                {totalRevealableItems > 0 && (
                  <span className="ml-2 text-blue-500">
                    ({revealedItems.length}/{totalRevealableItems} items)
                  </span>
                )}
              </div>
              <button
                onClick={handleNext}
                className="senior-friendly-button bg-blue-600 text-white hover:bg-blue-700"
              >
                <span>Next</span>
                <ChevronRight size={20} />
              </button>
            </div>
          </div>

          {/* Questions panel */}
          <div className="rounded-lg border text-card-foreground h-2/5 p-4 bg-white shadow-lg flex flex-col senior-friendly-card">
            <div className="flex justify-between items-center mb-2">
              <h2 className="text-xl font-bold">Questions</h2>
              <div className="text-sm font-medium">
                Question {currentQuestion + 1} of {totalQuestions}
              </div>
            </div>
            <div className="flex-grow bg-gray-50 rounded-lg p-4 overflow-auto">
              {/* Question content would go here */}
              <div className="prose max-w-none">
                <h3 className="text-lg font-medium text-blue-600">Question {currentQuestion + 1}</h3>
                <p className="text-gray-700">
                  {currentQuestion === 0 && "What is the sum of 25 and 17?"}
                  {currentQuestion === 1 && "How would you solve this problem using mental math?"}
                  {currentQuestion === 2 && "What strategy did you use to solve this problem?"}
                </p>
              </div>
            </div>
            <div className="flex justify-between items-center mt-4">
              <button
                onClick={handlePreviousQuestion}
                className="senior-friendly-button bg-gray-200 text-gray-800 hover:bg-gray-300"
                disabled={currentQuestion === 0}
              >
                <ChevronUp size={20} />
                <span>Previous Question</span>
              </button>
              <button
                onClick={handleNextQuestion}
                className="senior-friendly-button bg-blue-600 text-white hover:bg-blue-700"
                disabled={currentQuestion === totalQuestions - 1}
              >
                <span>Next Question</span>
                <ChevronDown size={20} />
              </button>
            </div>
          </div>
        </div>

        {/* Right panel: Notes & next slide */}
        <div className="space-y-4">
          {/* Presenter notes */}
          <div className="rounded-lg border text-card-foreground h-3/5 p-4 bg-white shadow-lg flex flex-col senior-friendly-card">
            <h2 className="text-xl font-bold mb-2">Presenter Notes</h2>
            <div className="flex-grow bg-gray-50 rounded-lg p-4 overflow-auto">
              <div className="prose max-w-none">
                <h4 className="text-lg font-medium text-blue-600">Notes for Slide {currentSlide}</h4>
                {presenterNotes[currentSlide] ? (
                  <p className="text-gray-700">{presenterNotes[currentSlide]}</p>
                ) : (
                  <p className="text-gray-400 italic">No notes for this slide</p>
                )}
              </div>
            </div>
          </div>

          {/* Next slide preview */}
          <div className="rounded-lg border text-card-foreground h-2/5 p-4 bg-white shadow-lg flex flex-col senior-friendly-card">
            <h2 className="text-xl font-bold mb-2">Next Slide</h2>
            <div className="flex-grow bg-gray-50 rounded-lg overflow-hidden">
              {currentSlide < totalSlides ? (
                <div className="w-full h-full opacity-70 scale-90 pointer-events-none">
                  <SlideContent
                    slideNumber={currentSlide + 1}
                    highContrast={false}
                    revealedItems={[]}
                    registerRevealableItems={registerNextSlideRevealableItems}
                  />
                </div>
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">End of presentation</div>
              )}
            </div>
            {currentSlide < totalSlides && nextSlideRevealableItems > 0 && (
              <div className="mt-2 text-sm text-gray-500">
                Next slide has {nextSlideRevealableItems} revealable{" "}
                {nextSlideRevealableItems === 1 ? "item" : "items"}
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  )
}
