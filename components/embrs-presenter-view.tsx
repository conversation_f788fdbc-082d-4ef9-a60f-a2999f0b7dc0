"use client"

import { useState, useRef, useEffect } from "react"
import { usePresenter } from "./presenter-context"
import { useLessonContext } from "./lesson-context"
import { ChevronLeft, ChevronRight, Users, FileText, Lightbulb, Tv, Maximize, Monitor } from "lucide-react"
import { ControlPanel } from './ControlPanel'
import { Button } from "@/components/ui/button"

import { SlideContent } from "@/components/slide-content"
import NotesPanel from "@/components/notes-panel"
import ClassList from "@/components/class-list"
import ElsaChat from "@/components/elsa-chat"
import TextSizeControl from "@/components/text-size-control"
import Link from "next/link"

type LayoutMode = "class-list" | "notes" | "current-slide"

export function EmbrsPresenterView() {
  const { currentSlide, setCurrentSlide, revealedItems, setRevealedItems, isShown, setIsShown } = usePresenter()
  const { unitNumber, lessonNumber, gradeLevel, lang, totalSlides } = useLessonContext()
  const [layoutMode, setLayoutMode] = useState<LayoutMode>("notes")
  const [textSize, setTextSize] = useState(1) // 1 = 100%, 1.25 = 125%, 1.5 = 150%, 1.75 = 175%
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [totalQuestions, setTotalQuestions] = useState(3) // Example: 3 questions per slide
  const [activeTab, setActiveTab] = useState<"script" | "tips" | "elsa">("script")
  const [maxRevealableItems, setMaxRevealableItems] = useState(0)
  const [nextSlideRevealedItems, setNextSlideRevealedItems] = useState<number[]>([])
  const [nextSlideMaxRevealableItems, setNextSlideMaxRevealableItems] = useState(0)

  // Next slide popup show/hide state (local only)
  const [nextSlidePopupVisible, setNextSlidePopupVisible] = useState(false)

  // Next slide isShown state (will be applied when navigating to next slide)
  const [nextSlideIsShown, setNextSlideIsShown] = useState(true)

  // Simple rule: only auto-hide when no items, never auto-show
  useEffect(() => {
    // if (revealedItems.length === 0) {
    //   setIsShown(false)
    // }
    // Never auto-show - only manual control
  }, [revealedItems.length, setIsShown])

  // Function to register revealable items (used by SlideContent)
  const registerRevealableItems = (count: number) => {
    // This function is called by slide components to register how many items can be revealed
    setMaxRevealableItems(count)
  }

  // Wrapper function to match the expected type for SlideContent
  const handleSetRevealedItems = (value: React.SetStateAction<number[]>) => {
    if (typeof value === 'function') {
      setRevealedItems(value(revealedItems))
    } else {
      setRevealedItems(value)
    }
  }

  // Reset revealed items when changing slides
  useEffect(() => {
    // console.log("Slide changed to", currentSlide, "- resetting revealed items")
    // setRevealedItems([]) // Reset revealed items when changing slides
    setMaxRevealableItems(0) // Reset max revealable items for current slide
    setCurrentQuestion(0) // Reset current question when changing slides
    setNextSlideRevealedItems([]) // Reset next slide revealed items
    setNextSlideMaxRevealableItems(0) // Reset next slide max revealable items
  }, [currentSlide, setRevealedItems])
  useEffect(() => {
    setRevealedItems([]) // Reset revealed items when changing slides
  }, [currentSlide])

  // Switch to Script tab when entering Notes Focus mode if ELSA is active
  useEffect(() => {
    if (layoutMode === "notes" && activeTab === "elsa") {
      setActiveTab("script")
    }
  }, [layoutMode, activeTab])

  const handlePrevSlide = () => {
    if (currentSlide > 1) {
      setCurrentSlide(currentSlide - 1)
    }
  }

  // Manual show/hide handler
  const handleToggleShow = () => {
    setIsShown(!isShown)
  }

  // Handler to show first item when no items are revealed
  const handleShowFirst = () => {
    if (revealedItems.length === 0) {
      setRevealedItems([0]) // Reveal first item
      setIsShown(true) // Show sidebar
    }
  }


  const handleNextSlide = () => {
    if (currentSlide < totalSlides) {
      // Apply next slide isShown state when navigating
      setIsShown(nextSlideIsShown)
      setCurrentSlide(currentSlide + 1)
    }
  }

  const increaseTextSize = () => {
    if (textSize < 3) {
      setTextSize((prev) => Math.min(prev + 0.25, 3))
    }
  }

  const decreaseTextSize = () => {
    if (textSize > 1) {
      setTextSize((prev) => Math.max(prev - 0.25, 1))
    }
  }

  const resetTextSize = () => {
    setTextSize(1)
  }

  const handlePrevQuestion = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(currentQuestion - 1)
    }
  }

  const handleNextQuestion = () => {
    if (currentQuestion < totalQuestions - 1) {
      setCurrentQuestion(currentQuestion + 1)
    }
  }

  // Functions for revealing items navigation
  const handlePrevReveal = () => {
    if (revealedItems.length > 0) {
      const newRevealed = revealedItems.slice(0, -1)
      setRevealedItems(newRevealed)
    }
  }

  const handleNextReveal = () => {
    if (revealedItems.length < maxRevealableItems) {
      const nextIndex = revealedItems.length
      setRevealedItems([...revealedItems, nextIndex])
    }
  }

  // Functions for Next Slide revealing items navigation
  const registerNextSlideRevealableItems = (count: number) => {
    setNextSlideMaxRevealableItems(count)
  }

  const handlePrevNextSlideReveal = () => {
    if (nextSlideRevealedItems.length > 0) {
      const newRevealed = nextSlideRevealedItems.slice(0, -1)
      setNextSlideRevealedItems(newRevealed)
    }
  }

  const handleNextNextSlideReveal = () => {
    if (nextSlideRevealedItems.length < nextSlideMaxRevealableItems) {
      const nextIndex = nextSlideRevealedItems.length
      setNextSlideRevealedItems([...nextSlideRevealedItems, nextIndex])
    }
  }

  const handleNextSlideSetRevealedItems = (value: React.SetStateAction<number[]>) => {
    if (typeof value === 'function') {
      setNextSlideRevealedItems(value(nextSlideRevealedItems))
    } else {
      setNextSlideRevealedItems(value)
    }
  }



  const toggleFullScreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen().catch((e) => {
        console.error(`Error attempting to enable full-screen mode: ${e.message}`)
      })
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen()
      }
    }
  }

  return (
    <div className="min-h-screen p-1 sm:p-2 md:p-4 flex flex-col bg-gradient-to-br from-blue-600 via-blue-500 to-cyan-400">
      {/* Header with Layout Toggle */}
      <header className="flex flex-col sm:flex-row items-center justify-between py-1 sm:py-2 px-2 sm:px-4 mb-1 sm:mb-2 md:mb-4 bg-gradient-to-r from-blue-600 via-blue-500 to-cyan-400 rounded-xl shadow-md text-white gap-2 sm:gap-0 flex-shrink-0">
        <Link href={"/"} className="flex items-center gap-1 sm:gap-2">
          <div className="h-6 w-6 sm:h-8 sm:w-8 relative">
            <img
              alt="EMBRS Logo"
              loading="lazy"
              decoding="async"
              data-nimg="fill"
              src="/images/embrs-logo.png"
              style={{ position: "absolute", height: "100%", width: "100%", inset: 0, objectFit: "contain", color: "transparent" }}
            />
          </div>
          <h1 className="text-white logo-text text-lg sm:text-xl md:text-2xl" style={{ fontFamily: 'var(--font-montserrat), sans-serif', fontWeight: 900 }}>EMBRS</h1>
        </Link>

        {/* Layout Toggle */}
        <div className="order-2 sm:order-none">
          <div className="p-1 bg-white rounded-lg shadow-sm border border-gray-100 flex items-center justify-center gap-1 sm:gap-2">
            <button
              onClick={() => setLayoutMode("class-list")}
              className={`inline-flex items-center justify-center text-xs sm:text-sm font-medium ring-offset-background hover:bg-muted hover:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-transparent h-8 sm:h-10 border-none rounded-lg min-h-8 sm:min-h-12 data-[state=on]:bg-gradient-to-r data-[state=on]:from-blue-500 data-[state=on]:to-blue-600 transition-all duration-200 ease-in-out px-2 sm:px-4 py-1 sm:py-2 ${layoutMode === "class-list"
                ? "data-[state=on]:bg-blue-700 data-[state=on]:text-white"
                : "text-gray-600"
                } senior-friendly-button`}
              aria-label="Show class list as main panel"
              data-state={layoutMode === "class-list" ? "on" : "off"}
            >
              <Users className="h-4 w-4 sm:h-6 sm:w-6 mr-1 sm:mr-2 senior-friendly-icon" />
              <span className="font-bold hidden sm:inline" style={{ fontFamily: 'Inter, sans-serif' }}>
                Class Focus
              </span>
              <span className="senior-friendly-text sm:hidden" style={{ fontFamily: 'Inter, sans-serif' }}>
                Class
              </span>
            </button>
            <button
              onClick={() => setLayoutMode("notes")}
              className={`inline-flex items-center justify-center text-xs sm:text-sm font-medium ring-offset-background hover:bg-muted hover:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-transparent h-8 sm:h-10 border-none rounded-lg min-h-8 sm:min-h-12 data-[state=on]:bg-gradient-to-r data-[state=on]:from-blue-500 data-[state=on]:to-blue-600 transition-all duration-200 ease-in-out px-2 sm:px-4 py-1 sm:py-2 ${layoutMode === "notes"
                ? "data-[state=on]:bg-blue-700 data-[state=on]:text-white"
                : "text-gray-600"
                } senior-friendly-button`}
              aria-label="Show notes as main panel"
              data-state={layoutMode === "notes" ? "on" : "off"}
            >
              <FileText className="h-4 w-4 sm:h-6 sm:w-6 mr-1 sm:mr-2 senior-friendly-icon" />
              <span className="font-bold hidden sm:inline" style={{ fontFamily: 'Inter, sans-serif' }}>
                Notes Focus
              </span>
              <span className="senior-friendly-text sm:hidden" style={{ fontFamily: 'Inter, sans-serif' }}>
                Notes
              </span>
            </button>
            <button
              onClick={() => setLayoutMode("current-slide")}
              className={`inline-flex items-center justify-center text-xs sm:text-sm font-medium ring-offset-background hover:bg-muted hover:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-transparent h-8 sm:h-10 border-none rounded-lg min-h-8 sm:min-h-12 data-[state=on]:bg-gradient-to-r data-[state=on]:from-blue-500 data-[state=on]:to-blue-600 transition-all duration-200 ease-in-out px-2 sm:px-4 py-1 sm:py-2 ${layoutMode === "current-slide"
                ? "data-[state=on]:bg-blue-700 data-[state=on]:text-white"
                : "text-gray-600"
                } senior-friendly-button`}
              aria-label="Show slide in focus mode"
              data-state={layoutMode === "current-slide" ? "on" : "off"}
            >
              <Monitor className="h-4 w-4 sm:h-6 sm:w-6 mr-1 sm:mr-2 senior-friendly-icon" />
              <span className="hidden sm:inline text-sm sm:text-lg md:text-xl font-bold" style={{ fontFamily: 'Inter, sans-serif' }}>
                Slide Focus
              </span>
              <span className="senior-friendly-text sm:hidden" style={{ fontFamily: 'Inter, sans-serif' }}>
                Current
              </span>
            </button>
          </div>
        </div>

        <div className="flex items-center gap-2 sm:gap-4 order-1 sm:order-none">
          <Button
            onClick={toggleFullScreen}
            className="justify-center whitespace-nowrap text-xs sm:text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 bg-primary hover:bg-primary/90 h-8 sm:h-9 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-2 sm:px-3 py-1 sm:py-1.5 rounded-md flex items-center gap-1 sm:gap-1.5 shadow-sm"
            title="Toggle full screen"
          >
            <Maximize className="h-3 w-3 sm:h-4 sm:w-4" />
            <span className="text-xs sm:text-sm font-bold hidden sm:inline" style={{ fontFamily: 'Inter, sans-serif' }}>
              Fullscreen
            </span>
          </Button>
          <button
            onClick={() => {
              // Save current state to localStorage before navigating
              try {
                localStorage.setItem('lesson_unit_number', unitNumber || '');
                localStorage.setItem('lesson_lesson_number', lessonNumber || '');
                localStorage.setItem('lesson_grade_level', gradeLevel || '');
                localStorage.setItem('lesson_current_slide', currentSlide.toString());
                if (lang) {
                  localStorage.setItem('lesson_lang', lang);
                }

                console.log('Saved lesson state to localStorage before closing presenter:', {
                  unitNumber, lessonNumber, gradeLevel, currentSlide, lang
                });
              } catch (error) {
                console.error('Error saving to localStorage:', error);
              }

              // Try to close any audience window that might be open
              try {
                // First approach: Try to access audience window through localStorage
                // Store a message in localStorage to tell the audience window to close
                localStorage.setItem('closeAudienceWindow', Date.now().toString());

                // Second approach: Try to broadcast a message to close audience windows
                // This uses BroadcastChannel API which works across tabs/windows of the same origin
                const bc = new BroadcastChannel('presenter-audience-channel');
                bc.postMessage({ action: 'close-audience' });
                setTimeout(() => bc.close(), 1000); // Close the channel after a delay

                // Third approach: Try to find any windows opened by this window
                // This is a more generic approach that doesn't rely on specific window names
                const windowReferences = (window as any)._openedWindows || [];
                for (const winRef of windowReferences) {
                  if (winRef && !winRef.closed) {
                    try {
                      winRef.close();
                    } catch (err) {
                      console.error("Error closing window:", err);
                    }
                  }
                }
              } catch (e) {
                console.error("Error closing audience window:", e);
              }

              // Construct query parameters for returning to home page
              const queryParams = new URLSearchParams({
                unit: unitNumber || '',
                lesson: lessonNumber || '',
                grade: gradeLevel || '',
                slide: currentSlide.toString()
              });

              if (lang) {
                queryParams.append('lang', lang);
              }

              // Navigate back to home page with query parameters
              window.location.href = `/?${queryParams.toString()}`;
            }}
            className="flex items-center hidden gap-1 sm:gap-2 hover:bg-white/10 rounded-md px-1 sm:px-2 py-1 transition-colors"
            title="Close presenter and audience views and return to home"
          >
            <Tv className="h-4 w-4 sm:h-5 sm:w-5 text-white opacity-90" />
            <span className="text-white text-sm sm:text-base hidden sm:inline" style={{ fontFamily: 'Inter, sans-serif' }}>
              Presenter View
            </span>
            <span className="text-white text-xs sm:hidden" style={{ fontFamily: 'Inter, sans-serif' }}>
              Exit
            </span>
          </button>
        </div>
      </header>

      {/* Main Content - Dynamic Layout */}
      <div className="flex flex-col lg:flex-row flex-1 gap-1 max-h-[calc(100vh-130px)] sm:gap-2 md:gap-4 overflow-hidden min-h-0">
        {/* Left Column - Always has Slide Preview */}
        <div
          className={`${layoutMode === "class-list" ? "lg:w-1/3" :
              layoutMode === "current-slide" ? "lg:w-1/4" :
                "lg:w-1/4"
            } w-full lg:flex-none flex flex-col gap-1 sm:gap-2 md:gap-4 transition-all duration-300 senior-friendly-transition min-h-0`}
        >
          {/* Top Left - Script/Tips/ELSA Controls or Slide Preview */}
          {layoutMode === "current-slide" ? (
            <div className="rounded-lg border text-card-foreground h-40 sm:h-48 lg:h-2/5 p-1 sm:p-2 md:p-4 bg-white shadow-lg flex flex-col senior-friendly-card flex-shrink-0">
              <div className="flex w-full mb-2 sm:mb-4 justify-between gap-1 sm:gap-2 bg-white p-0.5 sm:p-1 rounded-lg border border-gray-100 shadow-sm flex-shrink-0">
                <button
                  onClick={() => setActiveTab("script")}
                  className={`flex-1 px-1 sm:px-2 py-1 flex items-center justify-center rounded-lg ${activeTab === "script"
                    ? "bg-gradient-to-r from-blue-500 to-blue-600 text-white"
                    : "text-gray-600 hover:bg-gray-100"
                    }`}
                >
                  <FileText className="h-3 w-3 sm:h-4 sm:w-4 mr-0.5 sm:mr-1" />
                  <span className="text-xs" style={{ fontWeight: 500, fontFamily: 'Inter, sans-serif' }}>Script</span>
                </button>
                <button
                  onClick={() => setActiveTab("tips")}
                  className={`flex-1 px-1 sm:px-2 py-1 flex items-center justify-center rounded-lg ${activeTab === "tips"
                    ? "bg-gradient-to-r from-blue-500 to-blue-600 text-white"
                    : "text-gray-600 hover:bg-gray-100"
                    }`}
                >
                  <Lightbulb className="h-3 w-3 sm:h-4 sm:w-4 mr-0.5 sm:mr-1" />
                  <span className="text-xs" style={{ fontWeight: 500, fontFamily: 'Inter, sans-serif' }}>Tips</span>
                </button>
                {/* <button
                  onClick={() => setActiveTab("elsa")}
                  className={`flex-1 px-1 sm:px-2 py-1 flex items-center justify-center rounded-lg ${activeTab === "elsa"
                    ? "bg-gradient-to-r from-blue-500 to-blue-600 text-white"
                    : "text-gray-600 hover:bg-gray-100"
                    }`}
                >
                  <div className="h-3 w-3 sm:h-4 sm:w-4 mr-0.5 sm:mr-1 relative flex items-center justify-center bg-amber-100 rounded-full">
                    <span className="text-amber-600 font-bold text-xs">E</span>
                  </div>
                  <span className="text-xs" style={{ fontWeight: 500, fontFamily: 'Inter, sans-serif' }}>ELSA</span>
                </button> */}
              </div>
              <div className="flex-1 flex flex-col min-h-0">
                <div className="flex items-center justify-between gap-1 mb-2 p-1 bg-blue-50 rounded-md border border-blue-100 shadow-sm">
                  <div className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-type h-3 w-3 mr-1 text-blue-600">
                      <polyline points="4 7 4 4 20 4 20 7"></polyline>
                      <line x1="9" x2="15" y1="20" y2="20"></line>
                      <line x1="12" x2="12" y1="4" y2="20"></line>
                    </svg>
                    <span className="text-xs text-blue-700 font-bold">Size: {Math.round(textSize * 100)}%</span>
                  </div>
                  <div className="flex items-center gap-0.5">
                    <button
                      onClick={decreaseTextSize}
                      disabled={textSize <= 1}
                      className="h-5 w-5 p-0 flex items-center justify-center bg-white border border-input rounded text-xs hover:bg-gray-50"
                      title="Decrease text size"
                    >
                      -
                    </button>
                    <button
                      onClick={resetTextSize}
                      className="h-5 px-1 text-xs bg-white border border-input rounded hover:bg-gray-50 font-bold"
                      title="Reset to default text size"
                    >
                      Reset
                    </button>
                    <button
                      onClick={increaseTextSize}
                      disabled={textSize >= 3}
                      className="h-5 w-5 p-0 flex items-center justify-center bg-white border border-input rounded text-xs hover:bg-gray-50"
                      title="Increase text size"
                    >
                      +
                    </button>
                  </div>
                </div>
                <div className="flex-1 min-h-0 overflow-hidden">
                  {activeTab === "script" && (
                    <NotesPanel
                      title="Presentation Script"
                      type="script"
                      currentSlide={currentSlide}
                      textSize={textSize}
                      isNotesFocus={false}
                    />
                  )}
                  {activeTab === "tips" && (
                    <NotesPanel
                      title="Teaching Tips"
                      type="misconceptions"
                      currentSlide={currentSlide}
                      textSize={textSize}
                      isNotesFocus={false}
                    />
                  )}
                  {activeTab === "elsa" && (
                    <ElsaChat textSize={textSize} />
                  )}
                </div>
              </div>
            </div>
          ) : (
            <div className="rounded-lg border text-card-foreground h-40 sm:h-48 lg:h-2/5 p-1 sm:p-2 md:p-4 bg-white shadow-lg flex flex-col senior-friendly-card flex-shrink-0">
              <div className="flex items-center gap-1 sm:gap-2 mb-1 sm:mb-2 flex-shrink-0">
                <Monitor className="h-3 w-3 sm:h-4 sm:w-4 md:h-5 md:w-5 text-blue-500" />
                <h2 className="text-sm sm:text-lg md:text-xl font-bold" style={{ fontFamily: 'Inter, sans-serif' }}>Current Slide</h2>
                <div className="ml-auto text-xs font-medium" style={{ fontFamily: 'Inter, sans-serif' }}>
                  Slide {currentSlide} of {totalSlides}
                </div>
              </div>
              <div className="flex-1 relative mb-2">
                <div className="absolute inset-0 bg-white border border-gray-200 rounded-lg overflow-hidden shadow-md">
                  <div className="absolute inset-0 bg-[linear-gradient(150deg,#2B6DFE_0%,#2B6DFE_70%,#00F2FF_100%)] overflow-hidden">
                    <div className="absolute top-2 right-2 text-white text-sm font-medium">
                      {currentSlide} of {totalSlides}
                    </div>
                    <div className="absolute inset-0 flex items-center justify-center overflow-hidden">
                      <div className="transform origin-center" style={{ transform: 'scale(0.3)', minWidth: '1280px', height: '768px', margin: '0 auto' }}>
                        <div style={{ transform: 'scale(0.7)', transformOrigin: 'center center' }} className="p-8 slide-content-wrapper">
                          <SlideContent
                            slideNumber={currentSlide}
                            highContrast={false}
                            revealedItems={revealedItems}
                            registerRevealableItems={registerRevealableItems}
                            setRevealedItems={handleSetRevealedItems}
                            scale={100}
                            unitNumber={unitNumber}
                            lessonNumber={lessonNumber}
                            gradeLevel={gradeLevel}
                            lang={lang}
                          />
                        </div>
                      </div>
                    </div>

                    {/* Navigation arrows for revealing items */}
                    <div className="absolute bottom-2 left-0 right-0 flex justify-between items-center px-4">
                      <button
                        onClick={handlePrevReveal}
                        disabled={revealedItems.length === 0}
                        className={`w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center ${
                          revealedItems.length === 0 ? "opacity-50 cursor-not-allowed" : "hover:from-blue-600 hover:to-blue-700"
                        }`}
                        aria-label="Previous item"
                        title="Reveal previous item"
                      >
                        <ChevronLeft className="h-4 w-4 text-white" />
                      </button>

                      {/* Reveal state indicator and Show/Hide button */}
                      <div className="flex items-center gap-2">
                        {maxRevealableItems > 0 && (
                          <div className="bg-white/20 px-2 py-1 rounded-full text-white text-xs">
                            {revealedItems.length} / {maxRevealableItems} Items
                          </div>
                        )}
                        {revealedItems.length > 0 ? (
                          <button
                            onClick={handleToggleShow}
                            className={`px-3 py-1 rounded-full text-white text-xs font-medium transition-colors ${
                              isShown
                                ? "bg-red-500/30 hover:bg-red-500/40"
                                : "bg-green-500/30 hover:bg-green-500/40"
                            }`}
                            title={isShown ? "Hide Questions" : "Show Questions"}
                          >
                            {isShown ? "Hide" : "Show"}
                          </button>
                        ) : maxRevealableItems > 0 && (
                          <button
                            onClick={handleShowFirst}
                            className="bg-blue-500/30 hover:bg-blue-500/40 px-3 py-1 rounded-full text-white text-xs font-medium transition-colors"
                            title="Show first question"
                          >
                            Show
                          </button>
                        )}
                      </div>

                      <button
                        onClick={handleNextReveal}
                        disabled={revealedItems.length >= maxRevealableItems}
                        className={`w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center ${
                          revealedItems.length >= maxRevealableItems ? "opacity-50 cursor-not-allowed" : "hover:from-blue-600 hover:to-blue-700"
                        }`}
                        aria-label="Next item"
                        title="Reveal next item"
                      >
                        <ChevronRight className="h-4 w-4 text-white" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex justify-between items-center flex-shrink-0">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePrevSlide}
                  disabled={currentSlide === 1}
                  className="border border-gray-200 hover:bg-gray-50 text-gray-600 px-1 sm:px-2 md:px-4 py-1 rounded-md text-xs"
                >
                  <ChevronLeft className="h-3 w-3 sm:h-4 sm:w-4 mr-0.5 sm:mr-1" />
                  <span className="hidden md:inline" style={{ fontFamily: 'Inter, sans-serif' }}>Previous</span>
                  <span className="md:hidden" style={{ fontFamily: 'Inter, sans-serif' }}>Prev</span>
                </Button>
                <div className="text-xs sm:text-sm md:text-lg font-medium" style={{ fontFamily: 'Inter, sans-serif' }}>
                  {currentSlide} / {totalSlides}
                </div>
                <Button
                  variant="default"
                  size="sm"
                  onClick={handleNextSlide}
                  disabled={currentSlide === totalSlides}
                  className="bg-blue-500 hover:bg-blue-600 text-white px-1 sm:px-2 md:px-4 py-1 rounded-md text-xs"
                >
                  <span className="hidden md:inline" style={{ fontFamily: 'Inter, sans-serif' }}>Next</span>
                  <span className="md:hidden" style={{ fontFamily: 'Inter, sans-serif' }}>Next</span>
                  <ChevronRight className="h-3 w-3 sm:h-4 sm:w-4 ml-0.5 sm:ml-1" />
                </Button>
              </div>
            </div>
          )}

          {/* Notes or Class List - Bottom Left (depending on layout) */}
          <div className="rounded-lg p-0.5 bg-white text-card-foreground flex-1 lg:h-3/5 shadow-lg senior-friendly-card min-h-0 overflow-hidden">
            {layoutMode === "class-list" ? (
              <>
                {/* Control Panel for Class Focus */}
                <ControlPanel
                  revealedItems={revealedItems}
                  setRevealedItems={setRevealedItems}
                  maxRevealableItems={maxRevealableItems}
                  isShown={isShown}
                  setIsShown={setIsShown}
                  onPrevSlide={handlePrevSlide}
                  onNextSlide={handleNextSlide}
                  currentSlide={currentSlide}
                  totalSlides={totalSlides}
                />

              </>
            ) : (
              <>
                {/* Control Panel for Notes Focus */}
                <ControlPanel
                  revealedItems={revealedItems}
                  setRevealedItems={setRevealedItems}
                  maxRevealableItems={maxRevealableItems}
                  isShown={isShown}
                  setIsShown={setIsShown}
                  onPrevSlide={handlePrevSlide}
                  onNextSlide={handleNextSlide}
                  currentSlide={currentSlide}
                  totalSlides={totalSlides}
                />
              </>
            )}
          </div>
        </div>

        {/* Right Column - Dynamic Main Window */}
        <div
          className={`${layoutMode === "class-list" ? "lg:w-2/3" :
              layoutMode === "current-slide" ? "lg:w-3/4" :
                "lg:w-3/4"
            } w-full flex flex-col gap-1 sm:gap-2 md:gap-4 transition-all duration-300 senior-friendly-transition min-h-0`}
        >
          {layoutMode === "class-list" ? (
            <>
              {/* Class List View */}
              <div className="rounded-lg border text-card-foreground p-1 sm:p-2 md:p-4 bg-white shadow-lg flex-1 senior-friendly-card min-h-0 overflow-auto">
                <ClassList expanded={true} />
              </div>
            </>
          ) : layoutMode === "current-slide" ? (
            <>
              {/* Current Slide View - Full Size Slide */}
              <div className="rounded-lg border text-card-foreground p-1 sm:p-2 md:p-4 bg-white shadow-lg flex-1 senior-friendly-card min-h-0 overflow-hidden flex flex-col">
                <div className="flex items-center gap-1 sm:gap-2 mb-1 sm:mb-2 flex-shrink-0">
                  <Monitor className="h-4 w-4 sm:h-5 sm:w-5 text-blue-500" />
                  <h2 className="text-lg sm:text-xl md:text-2xl font-bold" style={{ fontFamily: 'Inter, sans-serif' }}>
                    Current Slide
                  </h2>
                  <div className="ml-auto text-sm font-medium" style={{ fontFamily: 'Inter, sans-serif' }}>
                    Slide {currentSlide} of {totalSlides}
                  </div>
                </div>
                <div className="flex-1 relative bg-[linear-gradient(150deg,#2B6DFE_0%,#2B6DFE_70%,#00F2FF_100%)] rounded-lg overflow-hidden mb-2">
                  <SlideContent
                    slideNumber={currentSlide}
                    highContrast={false}
                    revealedItems={revealedItems}
                    registerRevealableItems={registerRevealableItems}
                    setRevealedItems={handleSetRevealedItems}
                    scale={100}
                    unitNumber={unitNumber}
                    lessonNumber={lessonNumber}
                    gradeLevel={gradeLevel}
                    lang={lang}
                  />

                  {/* Navigation arrows for revealing items in full-size slide */}
                  <div className="absolute bottom-4 left-0 right-0 flex justify-between items-center px-8">
                    <button
                      onClick={handlePrevReveal}
                      disabled={revealedItems.length === 0}
                      className={`w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center ${
                        revealedItems.length === 0 ? "opacity-50 cursor-not-allowed" : "hover:from-blue-600 hover:to-blue-700"
                      }`}
                      aria-label="Previous item"
                      title="Reveal previous item"
                    >
                      <ChevronLeft className="h-6 w-6 text-white" />
                    </button>

                    {/* Reveal state indicator and Show/Hide button */}
                    <div className="flex items-center gap-3">
                      {maxRevealableItems > 0 && (
                        <div className="bg-white/20 px-4 py-2 rounded-full text-white text-sm font-medium">
                          {revealedItems.length} / {maxRevealableItems} Items
                        </div>
                      )}
                      {revealedItems.length > 0 ? (
                        <button
                          onClick={handleToggleShow}
                          className={`px-4 py-2 rounded-full text-white text-sm font-medium transition-colors ${
                            isShown
                              ? "bg-red-500/30 hover:bg-red-500/40"
                              : "bg-green-500/30 hover:bg-green-500/40"
                          }`}
                          title={isShown ? "Hide Questions" : "Show Questions"}
                        >
                          {isShown ? "Hide" : "Show"}
                        </button>
                      ) : maxRevealableItems > 0 && (
                        <button
                          onClick={handleShowFirst}
                          className="bg-blue-500/30 hover:bg-blue-500/40 px-4 py-2 rounded-full text-white text-sm font-medium transition-colors"
                          title="Show first question"
                        >
                          Show
                        </button>
                      )}
                    </div>

                    <button
                      onClick={handleNextReveal}
                      disabled={revealedItems.length >= maxRevealableItems}
                      className={`w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center ${
                        revealedItems.length >= maxRevealableItems ? "opacity-50 cursor-not-allowed" : "hover:from-blue-600 hover:to-blue-700"
                      }`}
                      aria-label="Next item"
                      title="Reveal next item"
                    >
                      <ChevronRight className="h-6 w-6 text-white" />
                    </button>
                  </div>
                </div>
                {/* Navigation Controls */}
                <div className="flex justify-between items-center flex-shrink-0">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handlePrevSlide}
                    disabled={currentSlide === 1}
                    className="border border-gray-200 hover:bg-gray-50 text-gray-600 px-1 sm:px-2 md:px-4 py-1 rounded-md text-xs"
                  >
                    <ChevronLeft className="h-3 w-3 sm:h-4 sm:w-4 mr-0.5 sm:mr-1" />
                    <span className="hidden md:inline" style={{ fontFamily: 'Inter, sans-serif' }}>Previous</span>
                    <span className="md:hidden" style={{ fontFamily: 'Inter, sans-serif' }}>Prev</span>
                  </Button>
                  <div className="text-xs sm:text-sm md:text-lg font-medium" style={{ fontFamily: 'Inter, sans-serif' }}>
                    {currentSlide} / {totalSlides}
                  </div>
                  <Button
                    variant="default"
                    size="sm"
                    onClick={handleNextSlide}
                    disabled={currentSlide === totalSlides}
                    className="bg-blue-500 hover:bg-blue-600 text-white px-1 sm:px-2 md:px-4 py-1 rounded-md text-xs"
                  >
                    <span className="hidden md:inline" style={{ fontFamily: 'Inter, sans-serif' }}>Next</span>
                    <span className="md:hidden" style={{ fontFamily: 'Inter, sans-serif' }}>Next</span>
                    <ChevronRight className="h-3 w-3 sm:h-4 sm:w-4 ml-0.5 sm:ml-1" />
                  </Button>
                </div>
              </div>
            </>
          ) : (
            <>
              {/* Top section - Script/Tips/ELSA tabs */}
              <div className="rounded-lg border text-card-foreground flex-1 p-1 sm:p-2 md:p-4 bg-white shadow-lg flex flex-col senior-friendly-card min-h-0">
                <div className="flex w-full mb-2 sm:mb-4 justify-between gap-1 sm:gap-2 bg-white p-0.5 sm:p-1 rounded-lg border border-gray-100 shadow-sm flex-shrink-0">
                  <button
                    onClick={() => setActiveTab("script")}
                    className={`flex-1 px-2 sm:px-4 py-1 sm:py-2 flex items-center justify-center rounded-lg ${activeTab === "script"
                      ? "bg-gradient-to-r from-blue-500 to-blue-600 text-white"
                      : "text-gray-600 hover:bg-gray-100"
                      }`}
                  >
                    <FileText className="h-4 w-4 sm:h-6 sm:w-6 mr-1 sm:mr-2" />
                    <span style={{ fontWeight: 700, fontFamily: 'Inter, sans-serif' }} className="text-xs sm:text-sm">
                      Script
                    </span>
                  </button>
                  <button
                    onClick={() => setActiveTab("tips")}
                    className={`flex-1 px-2 sm:px-4 py-1 sm:py-2 flex items-center justify-center rounded-lg ${activeTab === "tips"
                      ? "bg-gradient-to-r from-blue-500 to-blue-600 text-white"
                      : "text-gray-600 hover:bg-gray-100"
                      }`}
                  >
                    <Lightbulb className="h-4 w-4 sm:h-6 sm:w-6 mr-1 sm:mr-2" />
                    <span style={{ fontWeight: 700, fontFamily: 'Inter, sans-serif' }} className="text-xs sm:text-sm">
                      Tips
                    </span>
                  </button>
                  {/* Hide ELSA tab in Notes Focus mode */}
                  {layoutMode !== "notes" && (
                    <button
                      onClick={() => setActiveTab("elsa")}
                      className={`flex-1 px-2 sm:px-4 py-1 sm:py-2 flex items-center justify-center rounded-lg ${activeTab === "elsa"
                        ? "bg-gradient-to-r from-blue-500 to-blue-600 text-white"
                        : "text-gray-600 hover:bg-gray-100"
                        }`}
                    >
                      <div className="h-4 w-4 sm:h-6 sm:w-6 mr-1 sm:mr-2 relative flex items-center justify-center bg-amber-100 rounded-full">
                        <span className="text-amber-600 font-bold text-xs sm:text-sm">E</span>
                      </div>
                      <span style={{ fontWeight: 700, fontFamily: 'Inter, sans-serif' }} className="text-xs sm:text-sm">
                        ELSA
                      </span>
                    </button>
                  )}
                </div>
                <div className="flex-1 flex flex-col min-h-0">
                  <TextSizeControl
                    textSize={textSize}
                    onIncrease={increaseTextSize}
                    onDecrease={decreaseTextSize}
                    onReset={resetTextSize}
                  />
                  <div className="flex-1 min-h-0">
                    {activeTab === "script" && (
                      <NotesPanel
                        title="Presentation Script"
                        type="script"
                        currentSlide={currentSlide}
                        textSize={textSize}
                        isNotesFocus={layoutMode === "notes"}
                      />
                    )}
                    {activeTab === "tips" && (
                      <NotesPanel
                        title="Teaching Tips"
                        type="misconceptions"
                        currentSlide={currentSlide}
                        textSize={textSize}
                        isNotesFocus={layoutMode === "notes"}
                      />
                    )}
                    {activeTab === "elsa" && layoutMode !== "notes" && (
                      <ElsaChat textSize={textSize} />
                    )}
                  </div>
                </div>
              </div>

              {/* Bottom section - Questions hidden*/}
              {/* <div className="rounded-lg border text-card-foreground h-1/2 p-4 bg-white shadow-lg flex flex-col senior-friendly-card">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-bold">Questions</h2>
                  <div className="text-sm font-medium">
                    Question {currentQuestion + 1} of {totalQuestions}
                  </div>
                </div>
                <div className="flex-1 bg-gray-50 rounded-lg p-4 overflow-auto">
                  <div className="prose max-w-none">
                    <h3 className="text-lg font-medium text-blue-600">Question {currentQuestion + 1}</h3>
                    <p className="text-gray-700">
                      {currentQuestion === 0 && "What is the sum of 25 and 17?"}
                      {currentQuestion === 1 && "How would you solve this problem using mental math?"}
                      {currentQuestion === 2 && "What strategy did you use to solve this problem?"}
                    </p>
                  </div>
                </div>
                <div className="flex justify-between items-center mt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handlePrevQuestion}
                    disabled={currentQuestion === 0}
                    className="border border-gray-200 hover:bg-gray-50 text-gray-600"
                  >
                    <ChevronLeft className="h-4 w-4 mr-1" /> Previous Question
                  </Button>
                  <Button
                    variant="default"
                    size="sm"
                    onClick={handleNextQuestion}
                    disabled={currentQuestion === totalQuestions - 1}
                    className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700"
                  >
                    Next Question <ChevronRight className="h-4 w-4 ml-1" />
                  </Button>
                </div>
              </div> */}
            </>
          )}
        </div>
      </div>
    </div>
  )
}
