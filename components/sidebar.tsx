"use client"

import type React from "react"

import { useState, useEffect } from "react"
import {
  BookOpen,
  ChevronRight,
  ChevronLeft,
  LightbulbIcon,
  MessageSquare,
  Pencil,
  RotateCcw,
  FileText,
} from "lucide-react"
import { cn } from "@/lib/utils"
import { SlideType } from "@/types/slideTypes"
import { loadSlideData, getSlideTypeEnum } from "@/services/slideService"

interface SidebarProps {
  sidebarOpen: boolean
  setSidebarOpen: (open: boolean) => void
  currentSlide: number
  setCurrentSlide: (slide: number) => void
  unitNumber?: string // Unit number to load specific unit data
  lessonNumber?: string // Lesson number to load specific lesson data
  gradeLevel?: string // Grade level to load specific grade data
  lang?: string // Language code (en, esp) to load specific language data
}

export function Sidebar({
  sidebarOpen,
  setSidebarOpen,
  currentSlide,
  setCurrentSlide,
  unitNumber = "2",
  lessonNumber = "1",
  gradeLevel = "Grade 1",
  lang
}: SidebarProps) {
  const [expandedSections, setExpandedSections] = useState({
    introduction: false,
    hook: false,
    teach_1: false,
    talk_1: false,
    try_1: false,
    teach_2: false,
    talk_2: false,
    try_2: false,
    practice: false,
    on_ramp: false,
    printables: false,
  })

  const [slideData, setSlideData] = useState<Record<string, any>>({})
  const [slidesByType, setSlidesByType] = useState<Record<string, any[]>>({})
  const [loading, setLoading] = useState(true)
  const [lessonTitle, setLessonTitle] = useState<string>("Lesson Title")

  // Function to fetch lesson title from database
  const fetchLessonTitle = async () => {
    try {
      const params = new URLSearchParams()
      params.append('unit_number', unitNumber)
      params.append('lesson_number', lessonNumber)
      params.append('grade_level', gradeLevel)
      if (lang && lang !== 'en') params.append('lang', lang)

      const response = await fetch(`/api/json-documents?${params.toString()}`)

      if (response.ok) {
        const data = await response.json()
        // Check if the response has lesson_title in metadata
        if (data.lesson_title) {
          setLessonTitle(data.lesson_title)
        } else {
          // Fallback to default
          setLessonTitle("Lesson Title")
        }
      }
    } catch (error) {
      console.error('Error fetching lesson title:', error)
      setLessonTitle("Lesson Title")
    }
  }

  // Load slide data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)

        // Fetch lesson title
        await fetchLessonTitle()

        const data = await loadSlideData(unitNumber, lessonNumber, gradeLevel, lang)
        setSlideData(data)

        // Group slides by type
        const slidesByTypeMap: Record<string, any[]> = {}

        Object.entries(data).forEach(([key, slide]) => {
          // Make sure slide is an object with properties
          if (typeof slide !== 'object' || slide === null) return;

          // Extract slide number from key (e.g., "Slide 4: hook_1" -> 4)
          const slideNumber = parseInt(key.split(":")[0].replace("Slide ", "").trim())

          // Skip if we can't extract a valid slide number
          if (isNaN(slideNumber)) return;

          // Determine slide type based on slide number (language-independent)
          const slideType = getSlideTypeEnum(slideNumber)

          if (!slidesByTypeMap[slideType]) {
            slidesByTypeMap[slideType] = []
          }

          // Make sure we have a valid slide_pedagogical_name
          const slideName = slide.slide_pedagogical_name || key.split(":")[1]?.trim() || `Slide ${slideNumber}`

          slidesByTypeMap[slideType].push({
            ...slide,
            slideNumber,
            slide_pedagogical_name: slideName,
            type: slideType, // Ensure the type is set correctly
            key
          })
        })

        // Filter out invalid slides and sort by slide number within each type
        Object.keys(slidesByTypeMap).forEach(type => {
          // Filter out slides without valid slide numbers or names
          slidesByTypeMap[type] = slidesByTypeMap[type].filter(slide =>
            !isNaN(slide.slideNumber) &&
            slide.slide_pedagogical_name &&
            !slide.slide_pedagogical_name.includes('NaN')
          );

          // Sort slides by slide number
          slidesByTypeMap[type].sort((a, b) => a.slideNumber - b.slideNumber);
        })

        setSlidesByType(slidesByTypeMap)
        setLoading(false)
      } catch (error) {
        console.error("Error loading slide data:", error)
        setLoading(false)
      }
    }

    fetchData()
  }, [unitNumber, lessonNumber, gradeLevel, lang])

  // Determine which section should be expanded based on current slide
  useEffect(() => {
    if (loading || Object.keys(slideData).length === 0) return

    // Determine slide type based on slide number (language-independent)
    const slideType = getSlideTypeEnum(currentSlide)

    // Convert type to the corresponding section key
    const sectionKey = slideType.replace('-', '_') as keyof typeof expandedSections

    // Only expand the current section if it's not already expanded
    setExpandedSections((prev) => ({
      ...prev,
      [sectionKey]: true, // Set the current section to expanded, but don't close others
    }))
  }, [currentSlide, sidebarOpen, slideData, loading])

  // Toggle section expansion
  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section], // Toggle just this section without affecting others
    }))
  }

  if (!sidebarOpen) return null

  return (
    <div className="w-64 flex-shrink-0 border-r border-r-gray-50 bg-gray-50">
      <div className="flex items-center justify-between bg-[#2B6DFE] p-4 text-white border-r border-r-[#2B6DFE] relative">
        <h2 className="text-sm font-medium truncate pr-2 max-w-[180px]" title={lessonTitle}>
          {lessonTitle}
        </h2>

        {/* Tab to toggle sidebar */}
        <div
          className="absolute -right-6 top-0 h-full w-6 bg-[#fadb9a]/80 flex items-center justify-center cursor-pointer rounded-r-md z-50"
          onClick={(e) => {
            e.stopPropagation()
            setSidebarOpen(false)
          }}
        >
          <ChevronLeft size={16} className="text-[#2B6DFE] stroke-[3]" />
        </div>
      </div>

      <nav className="h-[calc(100vh-4rem-3rem-4rem)] overflow-y-auto pb-16">
        {loading ? (
          <div className="flex items-center justify-center h-full">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : (
          <>
            {/* Introduction Section */}
            {slidesByType[SlideType.INTRODUCTION]?.length > 0 && (
              <SidebarSection
                icon={<BookOpen size={18} />}
                label="Introduction"
                expanded={expandedSections.introduction}
                onToggle={() => toggleSection("introduction")}
                highlight={slidesByType[SlideType.INTRODUCTION]?.some(slide => slide.slideNumber === currentSlide) ? "bg-blue-100" : ""}
              >
                {slidesByType[SlideType.INTRODUCTION]
                  .filter(slide => slide.slideNumber && typeof slide.slideNumber === 'number')
                  .map(slide => (
                    <SidebarItem
                      key={slide.slideNumber}
                      label={slide.slide_pedagogical_name}
                      number={slide.slideNumber}
                      active={currentSlide === slide.slideNumber}
                      onClick={() => {
                        setCurrentSlide(slide.slideNumber)
                        // Update URL parameter for slide
                        try {
                          const url = new URL(window.location.href);
                          url.searchParams.set('slide', slide.slideNumber.toString());
                          window.history.replaceState({}, '', url.toString());
                          console.log('Updated slide in URL (sidebar):', slide.slideNumber);
                        } catch (error) {
                          console.error("Error updating slide in URL:", error);
                        }
                      }}
                    />
                  ))}
              </SidebarSection>
            )}

            {/* Hook Section */}
            {slidesByType[SlideType.HOOK]?.length > 0 && (
              <SidebarSection
                icon={<LightbulbIcon size={18} />}
                label="Hook"
                expanded={expandedSections.hook}
                onToggle={() => toggleSection("hook")}
                highlight={slidesByType[SlideType.HOOK]?.some(slide => slide.slideNumber === currentSlide) ? "bg-blue-100" : ""}
              >
                {slidesByType[SlideType.HOOK]
                  .filter(slide => slide.slideNumber && typeof slide.slideNumber === 'number')
                  .map(slide => (
                    <SidebarItem
                      key={slide.slideNumber}
                      label={slide.slide_pedagogical_name}
                      number={slide.slideNumber}
                      active={currentSlide === slide.slideNumber}
                      onClick={() => {
                        setCurrentSlide(slide.slideNumber)
                        // Update URL parameter for slide
                        try {
                          const url = new URL(window.location.href);
                          url.searchParams.set('slide', slide.slideNumber.toString());
                          window.history.replaceState({}, '', url.toString());
                          console.log('Updated slide in URL (sidebar):', slide.slideNumber);
                        } catch (error) {
                          console.error("Error updating slide in URL:", error);
                        }
                      }}
                    />
                  ))}
              </SidebarSection>
            )}

            {/* Teach 1 Section */}
            {slidesByType[SlideType.TEACH_1]?.length > 0 && (
              <SidebarSection
                icon={<LightbulbIcon size={18} />}
                label="Teach 1"
                expanded={expandedSections.teach_1}
                onToggle={() => toggleSection("teach_1")}
                highlight={slidesByType[SlideType.TEACH_1]?.some(slide => slide.slideNumber === currentSlide) ? "bg-blue-100" : ""}
              >
                {slidesByType[SlideType.TEACH_1]
                  .filter(slide => slide.slideNumber && typeof slide.slideNumber === 'number')
                  .map(slide => (
                    <SidebarItem
                      key={slide.slideNumber}
                      label={slide.slide_pedagogical_name}
                      number={slide.slideNumber}
                      active={currentSlide === slide.slideNumber}
                      onClick={() => {
                        setCurrentSlide(slide.slideNumber)
                        // Update URL parameter for slide
                        try {
                          const url = new URL(window.location.href);
                          url.searchParams.set('slide', slide.slideNumber.toString());
                          window.history.replaceState({}, '', url.toString());
                          console.log('Updated slide in URL (sidebar):', slide.slideNumber);
                        } catch (error) {
                          console.error("Error updating slide in URL:", error);
                        }
                      }}
                    />
                  ))}
              </SidebarSection>
            )}

            {/* Talk 1 Section */}
            {slidesByType[SlideType.TALK_1]?.length > 0 && (
              <SidebarSection
                icon={<MessageSquare size={18} />}
                label="Talk 1"
                expanded={expandedSections.talk_1}
                onToggle={() => toggleSection("talk_1")}
                highlight={slidesByType[SlideType.TALK_1]?.some(slide => slide.slideNumber === currentSlide) ? "bg-blue-100" : ""}
              >
                {slidesByType[SlideType.TALK_1]
                  .filter(slide => slide.slideNumber && typeof slide.slideNumber === 'number')
                  .map(slide => (
                    <SidebarItem
                      key={slide.slideNumber}
                      label={slide.slide_pedagogical_name}
                      number={slide.slideNumber}
                      active={currentSlide === slide.slideNumber}
                      onClick={() => {
                        setCurrentSlide(slide.slideNumber)
                        // Update URL parameter for slide
                        try {
                          const url = new URL(window.location.href);
                          url.searchParams.set('slide', slide.slideNumber.toString());
                          window.history.replaceState({}, '', url.toString());
                          console.log('Updated slide in URL (sidebar):', slide.slideNumber);
                        } catch (error) {
                          console.error("Error updating slide in URL:", error);
                        }
                      }}
                    />
                  ))}
              </SidebarSection>
            )}

            {/* Try 1 Section */}
            {slidesByType[SlideType.TRY_1]?.length > 0 && (
              <SidebarSection
                icon={<Pencil size={18} />}
                label="Try 1"
                expanded={expandedSections.try_1}
                onToggle={() => toggleSection("try_1")}
                highlight={slidesByType[SlideType.TRY_1]?.some(slide => slide.slideNumber === currentSlide) ? "bg-blue-100" : ""}
              >
                {slidesByType[SlideType.TRY_1]
                  .filter(slide => slide.slideNumber && typeof slide.slideNumber === 'number')
                  .map(slide => (
                    <SidebarItem
                      key={slide.slideNumber}
                      label={slide.slide_pedagogical_name}
                      number={slide.slideNumber}
                      active={currentSlide === slide.slideNumber}
                      onClick={() => {
                        setCurrentSlide(slide.slideNumber)
                        // Update URL parameter for slide
                        try {
                          const url = new URL(window.location.href);
                          url.searchParams.set('slide', slide.slideNumber.toString());
                          window.history.replaceState({}, '', url.toString());
                          console.log('Updated slide in URL (sidebar):', slide.slideNumber);
                        } catch (error) {
                          console.error("Error updating slide in URL:", error);
                        }
                      }}
                    />
                  ))}
              </SidebarSection>
            )}

            {/* Teach 2 Section */}
            {slidesByType[SlideType.TEACH_2]?.length > 0 && (
              <SidebarSection
                icon={<LightbulbIcon size={18} />}
                label="Teach 2"
                expanded={expandedSections.teach_2}
                onToggle={() => toggleSection("teach_2")}
                highlight={slidesByType[SlideType.TEACH_2]?.some(slide => slide.slideNumber === currentSlide) ? "bg-blue-100" : ""}
              >
                {slidesByType[SlideType.TEACH_2]
                  .filter(slide => slide.slideNumber && typeof slide.slideNumber === 'number')
                  .map(slide => (
                    <SidebarItem
                      key={slide.slideNumber}
                      label={slide.slide_pedagogical_name}
                      number={slide.slideNumber}
                      active={currentSlide === slide.slideNumber}
                      onClick={() => {
                        setCurrentSlide(slide.slideNumber)
                        // Update URL parameter for slide
                        try {
                          const url = new URL(window.location.href);
                          url.searchParams.set('slide', slide.slideNumber.toString());
                          window.history.replaceState({}, '', url.toString());
                          console.log('Updated slide in URL (sidebar):', slide.slideNumber);
                        } catch (error) {
                          console.error("Error updating slide in URL:", error);
                        }
                      }}
                    />
                  ))}
              </SidebarSection>
            )}

            {/* Talk 2 Section */}
            {slidesByType[SlideType.TALK_2]?.length > 0 && (
              <SidebarSection
                icon={<MessageSquare size={18} />}
                label="Talk 2"
                expanded={expandedSections.talk_2}
                onToggle={() => toggleSection("talk_2")}
                highlight={slidesByType[SlideType.TALK_2]?.some(slide => slide.slideNumber === currentSlide) ? "bg-blue-100" : ""}
              >
                {slidesByType[SlideType.TALK_2]
                  .filter(slide => slide.slideNumber && typeof slide.slideNumber === 'number')
                  .map(slide => (
                    <SidebarItem
                      key={slide.slideNumber}
                      label={slide.slide_pedagogical_name}
                      number={slide.slideNumber}
                      active={currentSlide === slide.slideNumber}
                      onClick={() => {
                        setCurrentSlide(slide.slideNumber)
                        // Update URL parameter for slide
                        try {
                          const url = new URL(window.location.href);
                          url.searchParams.set('slide', slide.slideNumber.toString());
                          window.history.replaceState({}, '', url.toString());
                          console.log('Updated slide in URL (sidebar):', slide.slideNumber);
                        } catch (error) {
                          console.error("Error updating slide in URL:", error);
                        }
                      }}
                    />
                  ))}
              </SidebarSection>
            )}

            {/* Try 2 Section */}
            {slidesByType[SlideType.TRY_2]?.length > 0 && (
              <SidebarSection
                icon={<Pencil size={18} />}
                label="Try 2"
                expanded={expandedSections.try_2}
                onToggle={() => toggleSection("try_2")}
                highlight={slidesByType[SlideType.TRY_2]?.some(slide => slide.slideNumber === currentSlide) ? "bg-blue-100" : ""}
              >
                {slidesByType[SlideType.TRY_2]
                  .filter(slide => slide.slideNumber && typeof slide.slideNumber === 'number')
                  .map(slide => (
                    <SidebarItem
                      key={slide.slideNumber}
                      label={slide.slide_pedagogical_name}
                      number={slide.slideNumber}
                      active={currentSlide === slide.slideNumber}
                      onClick={() => {
                        setCurrentSlide(slide.slideNumber)
                        // Update URL parameter for slide
                        try {
                          const url = new URL(window.location.href);
                          url.searchParams.set('slide', slide.slideNumber.toString());
                          window.history.replaceState({}, '', url.toString());
                          console.log('Updated slide in URL (sidebar):', slide.slideNumber);
                        } catch (error) {
                          console.error("Error updating slide in URL:", error);
                        }
                      }}
                    />
                  ))}
              </SidebarSection>
            )}

            {/* Practice Section */}
            {slidesByType[SlideType.PRACTICE]?.length > 0 && (
              <SidebarSection
                icon={<Pencil size={18} />}
                label="Practice"
                expanded={expandedSections.practice}
                onToggle={() => toggleSection("practice")}
                highlight={slidesByType[SlideType.PRACTICE]?.some(slide => slide.slideNumber === currentSlide) ? "bg-blue-100" : ""}
              >
                {slidesByType[SlideType.PRACTICE]
                  .filter(slide => slide.slideNumber && typeof slide.slideNumber === 'number')
                  .map(slide => (
                    <SidebarItem
                      key={slide.slideNumber}
                      label={slide.slide_pedagogical_name}
                      number={slide.slideNumber}
                      active={currentSlide === slide.slideNumber}
                      onClick={() => {
                        setCurrentSlide(slide.slideNumber)
                        // Update URL parameter for slide
                        try {
                          const url = new URL(window.location.href);
                          url.searchParams.set('slide', slide.slideNumber.toString());
                          window.history.replaceState({}, '', url.toString());
                          console.log('Updated slide in URL (sidebar):', slide.slideNumber);
                        } catch (error) {
                          console.error("Error updating slide in URL:", error);
                        }
                      }}
                    />
                  ))}
              </SidebarSection>
            )}

            {/* On-Ramp Section */}
            {slidesByType[SlideType.ON_RAMP]?.length > 0 && (
              <SidebarSection
                icon={<RotateCcw size={18} />}
                label="On-Ramp"
                expanded={expandedSections.on_ramp}
                onToggle={() => toggleSection("on_ramp")}
                highlight={slidesByType[SlideType.ON_RAMP]?.some(slide => slide.slideNumber === currentSlide) ? "bg-blue-100" : ""}
              >
                {slidesByType[SlideType.ON_RAMP]
                  .filter(slide => slide.slideNumber && typeof slide.slideNumber === 'number')
                  .map(slide => (
                    <SidebarItem
                      key={slide.slideNumber}
                      label={slide.slide_pedagogical_name}
                      number={slide.slideNumber}
                      active={currentSlide === slide.slideNumber}
                      onClick={() => {
                        setCurrentSlide(slide.slideNumber)
                        // Update URL parameter for slide
                        try {
                          const url = new URL(window.location.href);
                          url.searchParams.set('slide', slide.slideNumber.toString());
                          window.history.replaceState({}, '', url.toString());
                          console.log('Updated slide in URL (sidebar):', slide.slideNumber);
                        } catch (error) {
                          console.error("Error updating slide in URL:", error);
                        }
                      }}
                    />
                  ))}
              </SidebarSection>
            )}

            {/* Printables Section */}
            {slidesByType[SlideType.PRINTABLES]?.length > 0 && (
              <SidebarSection
                icon={<FileText size={18} />}
                label="Printables"
                expanded={expandedSections.printables}
                onToggle={() => toggleSection("printables")}
                highlight={slidesByType[SlideType.PRINTABLES]?.some(slide => slide.slideNumber === currentSlide) ? "bg-blue-100" : ""}
              >
                {slidesByType[SlideType.PRINTABLES]
                  .filter(slide => slide.slideNumber && typeof slide.slideNumber === 'number')
                  .map(slide => (
                    <SidebarItem
                      key={slide.slideNumber}
                      label={slide.slide_pedagogical_name}
                      number={slide.slideNumber}
                      active={currentSlide === slide.slideNumber}
                      onClick={() => setCurrentSlide(slide.slideNumber)}
                    />
                  ))}
              </SidebarSection>
            )}
          </>
        )}
      </nav>
    </div>
  )
}

interface SidebarSectionProps {
  icon: React.ReactNode
  label: string
  expanded?: boolean
  onToggle: () => void
  highlight?: string
  children?: React.ReactNode
}

function SidebarSection({ icon, label, expanded = false, onToggle, highlight, children }: SidebarSectionProps) {
  return (
    <div className="last:mb-16">
      <div
        className={cn("flex cursor-pointer items-center gap-2 px-4 py-3 hover:bg-blue-50", highlight)}
        onClick={onToggle}
      >
        <span className={cn("text-gray-600", highlight && "text-[#2B6DFE]")}>{icon}</span>
        <span className={cn("text-sm", highlight ? "text-[#2B6DFE] font-medium" : "text-gray-700")}>{label}</span>
        <ChevronRight
          className={cn(
            "ml-auto h-4 w-4 text-gray-400 transition-transform",
            expanded ? "rotate-90" : "",
            highlight && "text-[#2B6DFE]",
          )}
        />
      </div>
      {expanded && children && <div className="border-l border-blue-100 pl-4 ml-8 py-1">{children}</div>}
    </div>
  )
}

interface SidebarItemProps {
  label: string
  number?: number
  active?: boolean
  onClick?: () => void
  highlight?: boolean
}

function SidebarItem({ label, number, active, onClick, highlight }: SidebarItemProps) {
  return (
    <div
      className={cn(
        "flex cursor-pointer items-center gap-2 py-2 text-sm hover:text-[#2B6DFE]",
        active ? "text-[#2B6DFE]" : highlight ? "text-white" : "text-gray-600",
      )}
      onClick={onClick}
    >
      {number && (
        <span
          className={cn(
            "flex h-5 w-5 items-center justify-center rounded-sm text-xs",
            active ? "bg-blue-100 text-[#2B6DFE]" : highlight ? "bg-blue-400 text-white" : "bg-gray-100 text-gray-600",
          )}
        >
          {number}
        </span>
      )}
      {label}
    </div>
  )
}
