"use client"

import { SlideContent } from "@/components/slide-content"

interface SlidePreviewProps {
  currentSlide: number
  totalSlides: number
}

export default function SlidePreview({ currentSlide, totalSlides }: SlidePreviewProps) {
  return (
    <div className="absolute inset-0 bg-white border border-gray-200 rounded-md overflow-hidden">
      <div className="absolute inset-0 flex items-center justify-center bg-blue-400 p-2">
        <div className="text-center text-white">
          <h3 className="text-lg font-heading">Slide {currentSlide}</h3>
          <p className="text-xs opacity-80">EMBRS MATH Curriculum</p>
          <div className="mt-2 p-1 bg-white/20 rounded-md">
            <p className="text-xs">Mathematical concepts and visualizations</p>
          </div>
        </div>
      </div>
    </div>
  )
}
