"use client"

import React, { useState } from 'react'
import { Maximize2, Code, Wand2 } from 'lucide-react'
import HtmlContent from './HtmlContent'
import HtmlFullscreenModal from './HtmlFullscreenModal'
import { isSuperAdmin } from '@/utils/adminUtils'

interface HtmlContentWrapperProps {
  html: string
  className?: string
  onEditClick?: () => void
  onRegenerateClick?: () => void
  showEditButton?: boolean
  showRegenerateButton?: boolean
  slideNumber?: number
  session?: any
}

export default function HtmlContentWrapper({
  html,
  className = '',
  onEditClick,
  onRegenerateClick,
  showEditButton = true,
  showRegenerateButton = true,
  slideNumber,
  session
}: HtmlContentWrapperProps) {
  const [isHovered, setIsHovered] = useState(false)
  const [isFullscreenModalOpen, setIsFullscreenModalOpen] = useState(false)

  // Only show buttons when content is hovered
  const handleMouseEnter = () => setIsHovered(true)
  const handleMouseLeave = () => setIsHovered(false)

  // Open fullscreen modal
  const openFullscreenModal = () => setIsFullscreenModalOpen(true)

  // Close fullscreen modal
  const closeFullscreenModal = () => setIsFullscreenModalOpen(false)

  return (
    <div
      className={`relative w-full h-full ${className}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      style={{
        width: "100%",
        height: "100%",
        maxWidth: "100%",
        maxHeight: "100%",
        overflow: "hidden",
        display: "flex",
        flexDirection: "column",
        position: "relative",
        boxSizing: "border-box"
      }}
    >
      {/* Buttons - only visible on hover */}
      {isHovered && (
        <div className="absolute top-2 right-2 z-10 flex space-x-2">
          <button
            onClick={openFullscreenModal}
            className="p-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            title="View Fullscreen"
            style={{ zIndex: 1000 }}
          >
            <Maximize2 size={16} />
          </button>

          {/* Edit button - only shown if onEditClick is provided AND user is super admin */}
          {showEditButton && onEditClick && isSuperAdmin(session) && (
            <button
              onClick={onEditClick}
              className="p-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              title="Edit HTML"
              style={{ zIndex: 1000 }}
            >
              <Code size={16} />
            </button>
          )}
        </div>
      )}

      {/* Render the HTML content with proper sizing */}
      <div
        className="w-full h-full"
        style={{
          width: "100%",
          height: "100%",
          maxWidth: "100%",
          maxHeight: "100%",
          overflow: "hidden",
          display: "flex",
          flexDirection: "column",
          boxSizing: "border-box"
        }}
      >
        <HtmlContent html={html} className="w-full h-full" />
      </div>

      {/* Fullscreen modal */}
      <HtmlFullscreenModal
        isOpen={isFullscreenModalOpen}
        onClose={closeFullscreenModal}
        html={html}
        title={`${slideNumber ? `Slide ${slideNumber}` : ''}`}
      />
    </div>
  )
  
}
