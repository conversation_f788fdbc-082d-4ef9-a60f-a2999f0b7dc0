"use client"

import React from "react"

// Helper function to render images with proper styling
interface SlideImageProps {
  src: string
  alt: string
  className?: string
}

export function SlideImage({ src, alt, className = "" }: SlideImageProps) {
  return (
    <div className="w-full h-full flex items-center justify-center overflow-hidden">
      <img
        src={src || "/placeholder.svg"}
        alt={alt}
        className={`max-w-full max-h-full object-contain ${className}`}
        onError={(e: any) => {
          console.error("Image failed to load:", e.target.src)
          e.currentTarget.src = "/place-value-comparison.png"
        }}
      />
    </div>
  )
}
