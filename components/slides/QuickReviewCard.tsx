"use client"

import React, { useState, useEffect } from "react"
import { ChevronDown, ChevronUp, Eye } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import HtmlContent from "../HtmlContent"

// Simple QuickReviewCard component
interface QuickReviewCardProps {
  number: number
  question: string
  answer: React.ReactNode
  isVisible: boolean
  isAnswerVisible?: boolean
  onToggleVisibility: () => void
  onToggleAnswer?: () => void
}

export function QuickReviewCard({
  number,
  question,
  answer,
  isVisible,
  isAnswerVisible = false,
  onToggleVisibility,
  onToggleAnswer
}: QuickReviewCardProps) {
  // Use external answer visibility control if provided, otherwise use internal state
  const [internalShowAnswer, setInternalShowAnswer] = useState(false)
  const showAnswer = onToggleAnswer ? isAnswerVisible : internalShowAnswer

  useEffect(() => {
    if (!isVisible) {
      setInternalShowAnswer(false)
    }
  }, [isVisible])

  return (
    <div className="rounded-lg bg-white/10 overflow-hidden">
      <div className="p-4 flex items-center justify-between gap-4 cursor-pointer" onClick={onToggleVisibility}>
        <div className="concept-number bg-[#2B6DFE] text-white">{number}</div>
        {isVisible ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
      </div>

      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ height: 0, opacity: 0, y: -10 }}
            animate={{ height: "auto", opacity: 1, y: 0 }}
            exit={{ height: 0, opacity: 0, y: -10 }}
            transition={{
              duration: 0.3,
              ease: "easeInOut",
              opacity: { duration: 0.2 },
            }}
            className="overflow-hidden"
          >
            <div className="p-4 pt-0">
              <div className="mt-4 text-3xl">
                <HtmlContent html={question} />
              </div>

              <div className="mt-4 border-t border-white/20 pt-4">
                <div
                  className="flex items-center justify-between cursor-pointer mb-2"
                  onClick={(e) => {
                    e.stopPropagation()
                    if (onToggleAnswer) {
                      onToggleAnswer()
                    } else {
                      setInternalShowAnswer(!internalShowAnswer)
                    }
                  }}
                >
                  <span className="font-medium">Answer</span>
                  <Eye size={18} className={showAnswer ? "text-[#fadb9a]" : ""} />
                </div>

                <AnimatePresence>
                  {showAnswer && (
                    <motion.div
                      initial={{ height: 0, opacity: 0, y: -5 }}
                      animate={{ height: "auto", opacity: 1, y: 0 }}
                      exit={{ height: 0, opacity: 0, y: -5 }}
                      transition={{ duration: 0.25 }}
                      className="overflow-hidden"
                    >
                      <div className="bg-white/10 p-4 rounded-md text-3xl">
                        <HtmlContent html={answer as string} />
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
