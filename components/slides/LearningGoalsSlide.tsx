"use client"

import React, { useState, useEffect } from "react"
import { ChevronDown, ChevronUp, Eye, Code } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { getSlideTitle, loadSlideData, getSlide, getSlideImageUrl } from "@/services/slideService"
import { RevealPoint } from "./RevealPoint"
import HtmlContent from "../HtmlContent"
import HtmlEditorModal from "../HtmlEditorModal"
import { saveHtmlAndRefresh } from "@/utils/forceRefresh"
import { getLessonNumberFromStorage, getDocumentIdFromStorage } from "@/utils/lessonUtils"
import { isSuperAdmin } from "@/utils/adminUtils"
import { useLessonContext } from "../lesson-context"
import { useSession } from "next-auth/react"

interface LearningGoalsSlideProps {
  highContrast?: boolean
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
  slideNumber: number
}

export function LearningGoalsSlide({
  highContrast = false,
  revealedItems = [],
  registerRevealableItems = () => {},
  slideNumber
}: LearningGoalsSlideProps) {
  const { data: session } = useSession()
  const [slideData, setSlideData] = useState<any>(null);
  // Add state for showing all points
  const [showAll, setShowAll] = useState(false);
  const [standardsOpen, setStandardsOpen] = useState(false);

  // Add state for HTML editor modal
  const [isHtmlEditorOpen, setIsHtmlEditorOpen] = useState(false);
  const [currentHtml, setCurrentHtml] = useState("");

  // Load slide data
  useEffect(() => {
    const fetchData = async () => {
      try {
        const currentSlideData = await getSlide(slideNumber);
        setSlideData(currentSlideData);
      } catch (error) {
        console.error("Error loading slide data:", error);
      }
    };

    fetchData();
  }, [slideNumber]);

  // Register revealable items (one for each learning goal)
  useEffect(() => {
    if (slideData) {
      const goals = [
        { title: slideData.lg_1, content: slideData.lg_1_content || slideData.lg_1 },
        { title: slideData.lg_2, content: slideData.lg_2_content || slideData.lg_2 },
        { title: slideData.lg_3, content: slideData.lg_3_content || slideData.lg_3 }
      ].filter(goal => goal.title);

      registerRevealableItems(goals.length);
    }
  }, [registerRevealableItems, slideData]);

  // If slide data is not loaded yet, return loading state
  if (!slideData) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <div className="text-center text-white">
          <p>Loading learning goals...</p>
        </div>
      </div>
    );
  }

  // Get the learning goals from the slide data
  const learningGoals = [
    { title: slideData.lg_1, content: slideData.lg_1_content || slideData.lg_1 },
    { title: slideData.lg_2, content: slideData.lg_2_content || slideData.lg_2 },
    { title: slideData.lg_3, content: slideData.lg_3_content || slideData.lg_3 }
  ].filter(goal => goal.title); // Filter out any undefined goals

  // Get the slide title from the slide data
  const title = getSlideTitle(slideData);

  // Get the image URL from the slide data
  const imageUrl = getSlideImageUrl(slideData);

  // Function to refresh slide data after save
  const refreshSlideData = async () => {
    console.log('LearningGoalsSlide: *** REFRESHING SLIDE DATA ***');

    // Update URL with current slide before reload
    const url = new URL(window.location.href);
    url.searchParams.set('unit', unitNumber);
    url.searchParams.set('lesson', lessonNumber);
    url.searchParams.set('grade', gradeLevel);
    url.searchParams.set('slide', slideNumber.toString());
    if (lang) {
      url.searchParams.set('lang', lang);
    }

    // Update URL and reload
    window.history.replaceState({}, '', url.toString());
    window.location.reload();
  };

  // Function to open HTML editor
  const openHtmlEditor = () => {
    setCurrentHtml(slideData?.html_css_description_of_image || "");
    setIsHtmlEditorOpen(true);
  };

    const { unitNumber, lessonNumber, gradeLevel, lang, curriculum } = useLessonContext();


  // Function to save HTML content
  const saveHtmlContent = async (html: string) => {
    try {
      console.log('Saving HTML content for slide:', {
        unitNumber,
        lessonNumber,
        gradeLevel,
        slideNumber,
        curriculum,
        lang,
        htmlLength: html?.length
      });

      const result = await saveHtmlAndRefresh(
        slideNumber,
        unitNumber,
        lessonNumber,
        gradeLevel,
        lang,
        html,
        curriculum
      );

      if (!result.success) {
        throw new Error(result.message);
      }

      console.log('HTML content updated successfully');
    } catch (error) {
      console.error('Error updating HTML content:', error);
      alert('Failed to update HTML content. Please try again.');
    }
  };

  return (
    <div className={`p-8 ${highContrast ? "text-black" : "text-white"}`}>
      <h2 className={`slide-title ${highContrast ? "text-black" : ""}`}>{title}</h2>

      {/* HTML Editor Modal */}
      <HtmlEditorModal
        isOpen={isHtmlEditorOpen}
        onClose={() => setIsHtmlEditorOpen(false)}
        initialHtml={currentHtml}
        onSave={saveHtmlContent}
        onSaveComplete={refreshSlideData}
        slideNumber={slideNumber}
        documentId={getDocumentIdFromStorage()}
        unitNumber={unitNumber}
        lessonNumber={lessonNumber}
        gradeLevel={gradeLevel}
        curriculum={curriculum}
        lang={lang}
        session={session}
      />

      <div className="concept-slide">
        <div
          className={`space-y-6 ${
            highContrast ? "border-2 border-black rounded-xl" : "rounded-xl"
          } p-6 relative bg-white/10`}
          style={{ minHeight: "400px" }}
        >
          {/* Add show all button */}
          <div className="flex justify-end mb-4">
            <button onClick={() => setShowAll(!showAll)} className="flex items-center gap-2 text-sm cursor-pointer">
              <span>{showAll ? "Hide All" : "Show All"}</span>
              <Eye size={18} className={showAll ? "text-[#fadb9a]" : ""} />
            </button>
          </div>

          {learningGoals.map((goal, index) => (
            <RevealPoint
              key={`goal-${index}`}
              number={index + 1}
              title={goal.title}
              content={goal.content}
              forceReveal={revealedItems.includes(index) || showAll}
            />
          ))}

          {slideData.standards_alignment && (
            <div className="mt-6">
              <div
                className={`rounded-lg ${standardsOpen ? "bg-white/20" : "bg-white/10"} p-3 backdrop-blur-sm cursor-pointer`}
                onClick={() => setStandardsOpen(!standardsOpen)}
              >
                <div className="flex items-center gap-3">
                  <div
                    className="concept-number bg-[#2B6DFE] text-white text-sm"
                    style={{ width: "2.5rem", height: "2.5rem" }}
                  >
                    S
                  </div>
                  <h3 className="text-2xl font-montserrat font-extrabold text-white">Standards Alignment</h3>
                </div>

                <AnimatePresence>
                  {standardsOpen && (
                    <motion.div
                      initial={{ height: 0, opacity: 0, y: -10 }}
                      animate={{ height: "auto", opacity: 1, y: 0 }}
                      exit={{ height: 0, opacity: 0, y: -10 }}
                      transition={{
                        duration: 0.3,
                        ease: "easeInOut",
                      }}
                      className="overflow-hidden"
                    >
                      <div className="mt-3 text-xl">
                        <p className="mb-2">{slideData.standards_alignment}</p>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>

                <div className="mt-1 text-center">
                  {standardsOpen ? (
                    <ChevronUp size={14} className="inline-block" />
                  ) : (
                    <ChevronDown size={14} className="inline-block" />
                  )}
                </div>
              </div>
            </div>
          )}

          {/* We no need show this in the slide view */}
          {/* {slideData.teacher_tips && (
            <div className="mt-8 p-4 bg-white/10 rounded-lg w-full h-full">
              <h3 className="text-xl font-semibold mb-2">Teacher Tips</h3>
              {slideData.teacher_tips.general_tip && (
                <div className="mb-4">
                  <h4 className="text-lg font-medium text-[#fadb9a]">General Tip:</h4>
                  <div className="mt-1 text-sm" dangerouslySetInnerHTML={{ __html: slideData.teacher_tips.general_tip }} />
                </div>
              )}
              {slideData.teacher_tips.misconception_tip && (
                <div>
                  <h4 className="text-lg font-medium text-[#fadb9a]">Common Misconceptions:</h4>
                  <div className="mt-1 text-sm" dangerouslySetInnerHTML={{ __html: slideData.teacher_tips.misconception_tip }} />
                </div>
              )}
            </div>
          )} */}
        </div>

        {/* Add image section */}
        <div className="illustration-box" style={{ minHeight: "400px" }}>
          <div className="slide-image-container">
            <div className="w-full h-full flex justify-center items-center overflow-hidden">
              {slideData?.html_css_description_of_image ? (
                <div className="w-full h-full flex justify-center items-center">
                  {/* Check if the HTML content is a full HTML document */}
                  {slideData.html_css_description_of_image.includes('<!DOCTYPE html>') ||
                   slideData.html_css_description_of_image.includes('<html') ? (
                    <div className="w-full h-full relative">
                      {/* Edit HTML button */}
                      {isSuperAdmin(session) && (
                        <button
                          onClick={openHtmlEditor}
                          className="absolute top-2 right-2 p-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors z-10"
                          title="Edit HTML"
                        >
                          <Code size={16} />
                        </button>
                      )}
                      <div className="w-full h-full">
                        <div
                          className="generated-html-container w-full h-full"
                          style={{ width: "100%", height: "100%" }}
                        >
                          <style jsx global>{`
                            .generated-html-container {
                              display: flex;
                              align-items: center;
                              justify-content: center;
                              width: 100%;
                              height: 100%;
                              flex: 1;
                            }
                            .generated-html-container iframe {
                              border: none;
                              width: 100% !important;
                              height: 100% !important;
                              background: white;
                            }
                            .iframe-container {
                              width: 100% !important;
                              height: 100% !important;
                              position: relative !important;
                              overflow: visible !important;
                            }
                            .html-fragment-container {
                              width: 100% !important;
                              height: 100% !important;
                            }
                          `}</style>
                          <HtmlContent
                            key={`html-learning-goals-${slideData?.html_css_description_of_image?.length || 0}-${slideData?._timestamp || Date.now()}`}
                            html={slideData.html_css_description_of_image}
                            className="w-full"
                          />
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center w-full h-full flex flex-col justify-center items-center">
                      <div className="text-lg font-medium text-white mb-2">Image Not Available</div>
                      <div className="text-sm text-white/70 mb-4">
                        The image for this slide has not been added yet.
                      </div>
                      <div className="mt-2 p-3 bg-white/5 rounded-md w-full">
                        <div className="text-sm text-white/90 font-medium mb-1">Image Description:</div>
                        <div className="text-sm text-white/80">
                          {slideData.html_css_description_of_image}
                        </div>
                      </div>
                      <div className="mt-4">
                        {isSuperAdmin(session) && (
                          <button
                            onClick={openHtmlEditor}
                            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors mx-auto"
                          >
                            <Code size={16} />
                            Create HTML Content
                          </button>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="w-full h-full flex flex-col items-center justify-center text-center">
                  <div className="text-lg font-medium text-white mb-2">Image Not Available</div>
                  <div className="text-sm text-white/70 mb-4">
                    The image for this slide has not been added yet.
                  </div>
                  {isSuperAdmin(session) && (
                    <button
                      onClick={openHtmlEditor}
                      className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                    >
                      <Code size={16} />
                      Add HTML Content
                    </button>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
