"use client"

import React, { useEffect, useState } from "react"
import { getQuickReviewQuestions, getSlideImageUrl, getSlideTitle, getSlide } from "@/services/slideService"
import { useLessonContext } from "../lesson-context"
import { QuickReviewCard } from "./QuickReviewCard"
import HtmlContent from "../HtmlContent"
import { Code } from "lucide-react"
import HtmlEditorModal from "../HtmlEditorModal"
import HtmlContentWrapper from "../HtmlContentWrapper"
import { getLessonNumberFromStorage, getDocumentIdFromStorage } from "@/utils/lessonUtils"
import { isSuperAdmin } from "@/utils/adminUtils"
import { useSession } from "next-auth/react"
import { saveHtmlAndRefresh } from "@/utils/forceRefresh"

interface QuickReviewSlideProps {
  highContrast?: boolean
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
  setRevealedItems?: React.Dispatch<React.SetStateAction<number[]>>
  slideData: any
}

export function QuickReviewSlide({
  highContrast = false,
  revealedItems = [],
  registerRevealableItems = () => {},
  setRevealedItems,
  slideData,
}: QuickReviewSlideProps) {
  const { data: session } = useSession()

  // Get lesson context for current unit, lesson, grade
  const { unitNumber, lessonNumber, gradeLevel, lang, curriculum } = useLessonContext();

  // Add state for loaded slide data
  const [loadedSlideData, setLoadedSlideData] = useState<any>(slideData);

  // Load slide data if not provided
  useEffect(() => {
    // Only fetch new data when we don't have data
    if (!slideData || Object.keys(slideData).length === 0) {
      const fetchData = async () => {
        try {
          console.log(`QuickReviewSlide: Fetching data for slide 1`);
          const currentSlideData = await getSlide(1);
          setLoadedSlideData(currentSlideData);
        } catch (error) {
          console.error("Error loading slide data:", error);
        }
      };

      fetchData();
    } else if (slideData) {
      // If we already have slide data, use it
      setLoadedSlideData(slideData);
    }
  }, [slideData]); // Only depend on slideData to ensure it always updates

  // Use the loaded slide data or the provided slide data
  const effectiveSlideData = loadedSlideData || slideData;

  // Get the questions from the slide data
  const questions = getQuickReviewQuestions(effectiveSlideData);

  // Register revealable items (one for each card)
  useEffect(() => {
    registerRevealableItems(questions.length)
  }, [registerRevealableItems, questions.length])

  // Get the image URL from the slide data
  const imageUrl = getSlideImageUrl(effectiveSlideData);

  // Get the slide title from the slide data
  const title = getSlideTitle(effectiveSlideData);

  // Add state for HTML editor modal
  const [isHtmlEditorOpen, setIsHtmlEditorOpen] = useState(false);
  const [currentHtml, setCurrentHtml] = useState("");

  // Function to open HTML editor
  const openHtmlEditor = () => {
    setCurrentHtml(effectiveSlideData?.html_css_description_of_image || "");
    setIsHtmlEditorOpen(true);
  };

  // Function to refresh slide data after save
  const refreshSlideData = async () => {
    try {
      console.log('QuickReviewSlide: *** REFRESHING SLIDE DATA ***');

      // Fetch fresh data from server
      const freshSlideData = await getSlide(1, unitNumber, lessonNumber, gradeLevel, lang);
      console.log('QuickReviewSlide: Fresh data fetched:', freshSlideData?.html_css_description_of_image?.length);

      // Update local slide data state
      setLoadedSlideData(freshSlideData);

      console.log('QuickReviewSlide: *** REFRESH COMPLETE ***');

    } catch (error) {
      console.error("Error refreshing slide data:", error);
    }
  };

  // Function to save HTML content
  const saveHtmlContent = async (html: string) => {
    try {
      console.log('Saving HTML content for slide:', {
        unitNumber,
        lessonNumber,
        gradeLevel,
        slideNumber: 1,
        curriculum,
        lang,
        htmlLength: html?.length
      });

      const result = await saveHtmlAndRefresh(
        1,
        unitNumber,
        lessonNumber,
        gradeLevel,
        lang,
        html,
        curriculum
      );

      if (!result.success) {
        throw new Error(result.message);
      }

      console.log('HTML content updated successfully');
    } catch (error) {
      console.error('Error updating HTML content:', error);
      alert('Failed to update HTML content. Please try again.');
    }
  };

  return (
    <div className={`p-8 ${highContrast ? "text-black" : "text-white"}`}>
      <h2 className={`slide-title ${highContrast ? "text-black" : ""}`}>{title}</h2>

      {/* HTML Editor Modal */}
      <HtmlEditorModal
        isOpen={isHtmlEditorOpen}
        onClose={() => setIsHtmlEditorOpen(false)}
        initialHtml={currentHtml}
        onSave={saveHtmlContent}
        onSaveComplete={refreshSlideData}
        slideNumber={1}
        documentId={getDocumentIdFromStorage()}
        unitNumber={unitNumber}
        lessonNumber={lessonNumber}
        gradeLevel={gradeLevel}
        curriculum={curriculum}
        lang={lang}
        session={session}
      />
      <div className="concept-slide">
        <div
          className={`space-y-8 ${
            highContrast ? "border-2 border-black rounded-xl" : "rounded-xl"
          } p-6 relative bg-white/10`}
          style={{ minHeight: "400px" }}
        >
          <div className="space-y-8">
            {questions.map((item, index) => (
              <QuickReviewCard
                key={`question-${index}`}
                number={index + 1}
                question={item.question}
                answer={
                  <div className="space-y-2">
                    <p>{item.answer}</p>
                  </div>
                }
                isVisible={revealedItems.includes(index)}
                onToggleVisibility={() => {
                  if (setRevealedItems) {
                    if (revealedItems.includes(index)) {
                      setRevealedItems(revealedItems.filter((item) => item !== index))
                    } else {
                      setRevealedItems([...revealedItems, index])
                    }
                  }
                }}
              />
            ))}

            {/* we no need show this thips here only in the presenter view, in tips tap  */}
            {/* {slideData.teacher_tips && (
              <div className="mt-8 p-4 bg-white/10 rounded-lg w-full h-full">
                <h3 className="text-xl font-semibold mb-2">Teacher Tips</h3>
                {slideData.teacher_tips.general_tip && (
                  <div className="mb-4">
                    <h4 className="text-lg font-medium text-[#fadb9a]">General Tip:</h4>
                    <div className="mt-1 text-sm" dangerouslySetInnerHTML={{ __html: slideData.teacher_tips.general_tip }} />
                  </div>
                )}
                {slideData.teacher_tips.misconception_tip && (
                  <div>
                    <h4 className="text-lg font-medium text-[#fadb9a]">Common Misconceptions:</h4>
                    <div className="mt-1 text-sm" dangerouslySetInnerHTML={{ __html: slideData.teacher_tips.misconception_tip }} />
                  </div>
                )}
              </div>
            )} */}
          </div>
        </div>
        <div className="illustration-box" style={{ minHeight: "400px" }}>
          <div className="slide-image-container">
            <div className="w-full h-full flex justify-center items-center overflow-hidden">
              {imageUrl ? (
                <img
                  src={imageUrl}
                  alt={`Illustration for ${title}`}
                  style={{
                    maxWidth: "100%",
                    maxHeight: "100%",
                    objectFit: "contain",
                    backgroundColor: "transparent",
                    border: "none",
                  }}
                  onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                    console.error("Image failed to load:", e.currentTarget.src)
                    e.currentTarget.src = "/place-value-comparison.png"
                  }}
                />
              ) : effectiveSlideData?.html_css_description_of_image ? (
                <div className="w-full h-full flex justify-center items-center">
                  {/* Check if the HTML content is a full HTML document */}
                  {effectiveSlideData.html_css_description_of_image.includes('<!DOCTYPE html>') ||
                   effectiveSlideData.html_css_description_of_image.includes('<html') ? (
                    <div className="w-full h-full relative">
                      {/* Edit HTML button */}
                      {isSuperAdmin(session) && (
                        <button
                          onClick={openHtmlEditor}
                          className="absolute top-2 right-2 p-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors z-10"
                          title="Edit HTML"
                        >
                          <Code size={16} />
                        </button>
                      )}
                      <div className="w-full h-full">
                        <div
                          className="generated-html-container w-full h-full"
                          style={{ width: "100%", height: "100%" }}
                        >
                          <style jsx global>{`
                            .generated-html-container {
                              display: flex;
                              align-items: center;
                              justify-content: center;
                              width: 100%;
                              height: 100%;
                              flex: 1;
                            }
                            .generated-html-container iframe {
                              border: none;
                              width: 100% !important;
                              height: 100% !important;
                              background: white;
                            }
                            .iframe-container {
                              width: 100% !important;
                              height: 100% !important;
                              position: relative !important;
                              overflow: visible !important;
                            }
                            .html-fragment-container {
                              width: 100% !important;
                              height: 100% !important;
                            }
                          `}</style>
                          <HtmlContentWrapper
                            key={`html-quick-review-${effectiveSlideData?.html_css_description_of_image?.length || 0}-${effectiveSlideData?._timestamp || Date.now()}`}
                            html={effectiveSlideData.html_css_description_of_image}
                            className="w-full"
                            onEditClick={openHtmlEditor}
                            slideNumber={1}
                          />
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center w-full h-full flex flex-col justify-center items-center">
                      <div className="text-lg font-medium text-white mb-2">Image Not Available</div>
                      <div className="text-sm text-white/70 mb-4">
                        The image for this slide has not been added yet.
                      </div>
                      <div className="mt-2 p-3 bg-white/5 rounded-md w-full">
                        <div className="text-sm text-white/90 font-medium mb-1">Image Description:</div>
                        <div className="text-sm text-white/80">
                          {effectiveSlideData.html_css_description_of_image}
                        </div>
                      </div>
                      <div className="mt-4">
                        {isSuperAdmin(session) && (
                          <button
                            onClick={openHtmlEditor}
                            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors mx-auto"
                          >
                            <Code size={16} />
                            Create HTML Content
                          </button>
                        )}
                      </div>
                    </div>
                  )}
                </div>

              ) : (
                <div className="w-full h-full flex flex-col items-center justify-center text-center">
                  <div className="text-lg font-medium text-white mb-2">Image Not Available</div>
                  <div className="text-sm text-white/70 mb-4">
                    The image for this slide has not been added yet.
                  </div>
                  {isSuperAdmin(session) && (
                    <button
                      onClick={openHtmlEditor}
                      className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                    >
                      <Code size={16} />
                      Add HTML Content
                    </button>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
