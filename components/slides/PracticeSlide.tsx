"use client"

import React, { useState, useEffect } from "react"
import { getSlideTitle, getSlideActivities, getSlide } from "@/services/slideService"
import { PracticeCard } from "./PracticeCard"

interface PracticeSlideProps {
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
  setRevealedItems?: React.Dispatch<React.SetStateAction<number[]>>
  slideData?: any
  slideNumber?: number
}

export function PracticeSlide({
  revealedItems = [],
  registerRevealableItems = () => {},
  setRevealedItems = () => {},
  slideData,
  slideNumber,
}: PracticeSlideProps) {
  const [loadedSlideData, setLoadedSlideData] = useState<any>(slideData);
  const [gridCols, setGridCols] = useState<number>(4); // Default to 4 columns
  // Removed showAnswers state as we now use revealedItems for both questions and answers

  // Load slide data if not provided
  useEffect(() => {
    // Always fetch new data when slideNumber changes
    if (slideNumber) {
      const fetchData = async () => {
        try {
          console.log(`PracticeSlide: Fetching data for slide ${slideNumber}`);
          const currentSlideData = await getSlide(slideNumber);
          setLoadedSlideData(currentSlideData);
        } catch (error) {
          console.error("Error loading slide data:", error);
        }
      };

      fetchData();
    }
  }, [slideNumber]); // Only depend on slideNumber to ensure it always updates

  // Use the loaded slide data or the provided slide data
  const effectiveSlideData = loadedSlideData || slideData;

  // Get practice problems from the slide data
  const practiceProblems = effectiveSlideData ? getSlideActivities(effectiveSlideData) : [];

  // Check if we have instructions (first item with question "Instructions")
  const hasInstructions = practiceProblems.length > 0 && practiceProblems[0].question === 'Instructions';

  // Separate instructions from practice problems
  const problems = hasInstructions ? practiceProblems.slice(1) : practiceProblems;

  // Register revealable items (2 actions per practice problem: question + answer)
  useEffect(() => {
    registerRevealableItems(problems.length * 2) // 2 actions per problem (question + answer)
  }, [registerRevealableItems, problems.length])

  // Get the slide title from the slide data
  const title = effectiveSlideData ? getSlideTitle(effectiveSlideData) : "Practice";

  // Grid class mapping
  const getGridClass = () => {
    switch (gridCols) {
      case 1: return "grid grid-cols-1 gap-4";
      case 2: return "grid grid-cols-1 gap-4 sm:grid-cols-2";
      case 3: return "grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3";
      case 4: return "grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4";
      default: return "grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4";
    }
  };

  // Now using revealedItems for both questions and answers, no separate toggle needed

  return (
    <div className="text-white">
      <div className="flex items-center justify-between mb-6">
        <h2 className="slide-title">{title}</h2>

        {/* Grid columns toggle */}
        <div className="flex mr-20 items-center gap-2">
          <span className="text-sm text-white/70">Columns:</span>
          <div className="flex gap-1">
            {[1, 2, 3, 4].map((cols) => (
              <button
                key={cols}
                onClick={() => setGridCols(cols)}
                className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                  gridCols === cols
                    ? 'bg-[#fadb9a] text-[#4169E1]'
                    : 'bg-white/10 text-white hover:bg-white/20'
                }`}
              >
                {cols}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Instructions section */}
      {/* {instructions && (
        <div className="mb-6 p-4 bg-white/10 rounded-lg w-full h-full">
          <h3 className="text-xl font-semibold mb-2 text-[#fadb9a]">Instructions</h3>
          <div className="text-white" dangerouslySetInnerHTML={{ __html: instructions.answer }} />
        </div>
      )} */}

      {/* Practice problems grid */}
      <div className={getGridClass()}>
        {problems.map((problem, index) => {
          // Each problem has 2 reveal items: question (index*2) and answer (index*2+1)
          const questionRevealIndex = index * 2;
          const answerRevealIndex = index * 2 + 1;

          return (
            <PracticeCard
              key={`practice-${index}`}
              number={index + 1}
              question={problem.question}
              answer={problem.answer}
              isVisible={revealedItems.includes(questionRevealIndex)}
              showAnswer={revealedItems.includes(answerRevealIndex)}
              onToggleVisibility={() => {
                if (revealedItems.includes(questionRevealIndex)) {
                  setRevealedItems(revealedItems.filter((item) => item !== questionRevealIndex))
                } else {
                  setRevealedItems([...revealedItems, questionRevealIndex])
                }
              }}
              onToggleAnswer={() => {
                if (revealedItems.includes(answerRevealIndex)) {
                  setRevealedItems(revealedItems.filter((item) => item !== answerRevealIndex))
                } else {
                  setRevealedItems([...revealedItems, answerRevealIndex])
                }
              }}
            />
          )
        })}
      </div>
    </div>
  )
}
