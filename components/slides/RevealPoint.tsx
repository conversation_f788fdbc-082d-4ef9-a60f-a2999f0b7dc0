"use client"

import React, { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import HtmlContent from "../HtmlContent"

// Simple RevealPoint component
interface RevealPointProps {
  number: number | string
  title: string
  content: string
  forceReveal?: boolean
  isQuestion?: boolean
  isAnswer?: boolean
  hasAnswer?: boolean
  questionIndex?: number
  onQuestionClick?: (index: number) => void
  revealedAnswers?: number[]
}

export function RevealPoint({
  number,
  title,
  content,
  forceReveal = false,
  isQuestion = false,
  isAnswer = false,
  hasAnswer = false,
  questionIndex = -1,
  onQuestionClick,
  revealedAnswers = []
}: RevealPointProps) {
  const [isVisible, setIsVisible] = useState(false)

  // For answers, visibility is controlled by revealedAnswers
  const shouldShowAnswer = isAnswer && revealedAnswers.includes(questionIndex)

  // For questions or regular points, visibility is controlled by forceReveal
  const shouldShow = isAnswer ? shouldShowAnswer : forceReveal

  React.useEffect(() => {
    if (shouldShow !== undefined) {
      setIsVisible(shouldShow)
    }
  }, [shouldShow])

  return (
    <div className="concept-point">
      <div
        className={`concept-number cursor-pointer ${
          isVisible ? "bg-[#fadb9a]/20 text-[#2B6DFE]" : "bg-white/10 text-white"
        } ${isQuestion && hasAnswer ? "has-answer" : ""} ${isAnswer ? "is-answer" : ""}`}
        onClick={() => {
          console.log('RevealPoint clicked:', { number, isQuestion, isAnswer, questionIndex, hasAnswer });

          if (isQuestion && onQuestionClick && questionIndex >= 0) {
            // If this is a question with an answer and onQuestionClick is provided, call it
            console.log('Calling onQuestionClick for question index:', questionIndex);
            onQuestionClick(questionIndex);
          } else {
            // Otherwise, toggle visibility as usual
            console.log('Toggling visibility for point:', number);
            setIsVisible(!isVisible);
          }
        }}
        style={{ fontSize: "1.25rem" }}
      >
        {number}
      </div>
      <div className="flex-1">
        <AnimatePresence>
          {isVisible && (
            <motion.div
              initial={{ height: 0, opacity: 0, y: -10 }}
              animate={{ height: "auto", opacity: 1, y: 0 }}
              exit={{ height: 0, opacity: 0, y: -10 }}
              transition={{
                duration: 0.3,
                ease: "easeInOut",
              }}
              className="overflow-hidden"
            >
              <div className="concept-content ml-4 text-white text-3xl" style={{ lineHeight: "2.25rem" }}>
                <HtmlContent html={content} />
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}
