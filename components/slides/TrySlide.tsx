"use client"

import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react"
import { ChevronDown, ChevronUp, Code } from "lucide-react"
import { getSlideTitle, getSlideActivities, getSlideImageUrl, getSlide } from "@/services/slideService"
import { motion, AnimatePresence } from "framer-motion"
import HtmlContent from "../HtmlContent"
import HtmlEditorModal from "../HtmlEditorModal"
import HtmlContentWrapper from "../HtmlContentWrapper"
import { saveHtmlAndRefresh } from "@/utils/forceRefresh"
import { getLessonNumberFromStorage, getDocumentIdFromStorage } from "@/utils/lessonUtils"
import { isSuperAdmin } from "@/utils/adminUtils"
import { useLessonContext } from "../lesson-context"
import { useSession } from "next-auth/react"
// Or create the file at 'utils/forceRefresh.ts' if it does not exist.

interface TrySlideProps {
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
  setRevealedItems?: React.Dispatch<React.SetStateAction<number[]>>
  slideData?: any
  slideNumber?: number
}

interface TryCardProps {
  number: number
  question: string
  answer: string
  isOpen: boolean
  onToggle: () => void
}

// TryCard component for individual practice items
const TryCard = React.memo(function TryCard({
  number,
  question,
  answer,
  isOpen,
  onToggle
}: TryCardProps) {
  const [showAnswer, setShowAnswer] = useState(false);

  // Reset showAnswer when card is closed
  useEffect(() => {
    if (!isOpen) {
      setShowAnswer(false);
    }
  }, [isOpen]);

  return (
    <div className="rounded-lg bg-white/10 overflow-hidden h-full flex flex-col">
      <div className="p-4 flex items-center justify-between gap-4 cursor-pointer" onClick={onToggle}>
        <div className="concept-number bg-[#2B6DFE] text-white">{number}</div>
        {isOpen ?
          <ChevronUp size={20} /> :
          <ChevronDown size={20} />
        }
      </div>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden flex flex-col flex-grow"
            style={{ height: "auto" }}
          >
            <div className="p-4 pt-0 flex flex-col flex-grow">
              <div className="text-3xl mb-4">
                <HtmlContentWrapper html={question} />
              </div>

              <div className="mt-auto">
                <div
                  className="flex items-center justify-between p-3 bg-white/10 rounded-md cursor-pointer"
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowAnswer(!showAnswer);
                  }}
                >
                  <span className="font-medium">Answer</span>
                  {showAnswer ?
                    <ChevronUp size={18} /> :
                    <ChevronDown size={18} />
                  }
                </div>

                <AnimatePresence>
                  {showAnswer && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: "auto", opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="overflow-hidden"
                    >
                      <div className="mt-3 p-3 bg-white/10 rounded-md">
                        <HtmlContentWrapper html={answer} />
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
});

// Main TrySlide component
export function TrySlide({
  revealedItems = [],
  registerRevealableItems = () => {},
  setRevealedItems = () => {},
  slideData,
  slideNumber,
}: TrySlideProps) {
  const [loadedSlideData, setLoadedSlideData] = useState<any>(slideData);
  const [isHtmlEditorOpen, setIsHtmlEditorOpen] = useState(false);
  const [currentHtml, setCurrentHtml] = useState("");
  const { data: session } = useSession();

  // Load slide data if not provided
  useEffect(() => {
    if (slideNumber && (!slideData || Object.keys(slideData).length === 0)) {
      const fetchData = async () => {
        try {
          console.log(`TrySlide: Fetching data for slide ${slideNumber}`);
          const currentSlideData = await getSlide(slideNumber);
          setLoadedSlideData(currentSlideData);
        } catch (error) {
          console.error("Error loading slide data:", error);
        }
      };

      fetchData();
    } else if (slideData) {
      setLoadedSlideData(slideData);
    }
  }, [slideNumber, slideData]);

  // Use the loaded slide data or the provided slide data
  const effectiveSlideData = loadedSlideData || slideData;

  // Get practice problems from the slide data
  const practiceProblems = useMemo(() => {
    return effectiveSlideData ? getSlideActivities(effectiveSlideData) : [];
  }, [effectiveSlideData]);

  // Register revealable items (one for each practice problem)
  useEffect(() => {
    registerRevealableItems(practiceProblems.length);
  }, [registerRevealableItems, practiceProblems.length]);

  // Get the slide title from the slide data
  const title = useMemo(() => {
    return effectiveSlideData ? getSlideTitle(effectiveSlideData) : "Try";
  }, [effectiveSlideData]);

  // Function to refresh slide data after save
  const refreshSlideData = async () => {
    console.log('TrySlide: *** REFRESHING SLIDE DATA ***');

    // Update URL with current slide before reload
    const url = new URL(window.location.href);
    url.searchParams.set('unit', unitNumber);
    url.searchParams.set('lesson', lessonNumber);
    url.searchParams.set('grade', gradeLevel);
    if (slideNumber !== undefined) {
      url.searchParams.set('slide', slideNumber.toString());
    }
    if (lang) {
      url.searchParams.set('lang', lang);
    }

    // Update URL and reload
    window.history.replaceState({}, '', url.toString());
    window.location.reload();
  };

  // Function to open HTML editor
  const openHtmlEditor = useCallback(() => {
    setCurrentHtml(effectiveSlideData?.html_css_description_of_image || "");
    setIsHtmlEditorOpen(true);
  }, [effectiveSlideData]);

    const { unitNumber, lessonNumber, gradeLevel, lang, curriculum } = useLessonContext();


  // Function to save HTML content
  const saveHtmlContent = async (html: string) => {
    try {
      console.log('Saving HTML content for slide:', {
        unitNumber,
        lessonNumber,
        gradeLevel,
        slideNumber,
        curriculum,
        lang,
        htmlLength: html?.length
      });

      const result = await saveHtmlAndRefresh(
        slideNumber as number,
        unitNumber,
        lessonNumber,
        gradeLevel,
        lang,
        html,
        curriculum
      );

      if (!result.success) {
        throw new Error(result.message);
      }

      console.log('HTML content updated successfully');
    } catch (error) {
      console.error('Error updating HTML content:', error);
      alert('Failed to update HTML content. Please try again.');
    }
  };

  return (
    <div className="text-white">
      <h2 className="slide-title">{title}</h2>

      {/* HTML Editor Modal */}
      <HtmlEditorModal
        isOpen={isHtmlEditorOpen}
        onClose={() => setIsHtmlEditorOpen(false)}
        initialHtml={currentHtml}
        onSave={saveHtmlContent}
        onSaveComplete={refreshSlideData}
        slideNumber={slideNumber}
        documentId={getDocumentIdFromStorage()}
        unitNumber={unitNumber}
        lessonNumber={lessonNumber}
        gradeLevel={gradeLevel}
        curriculum={curriculum}
        lang={lang}
        session={session}
      />

      <div className="relative">
        <div className="grid grid-cols-3 gap-6" style={{ minHeight: "400px" }}>
          {practiceProblems.map((problem, index) => (
            <TryCard
              key={`try-${index}`}
              number={index + 1}
              question={problem.question}
              answer={problem.answer}
              isOpen={revealedItems.includes(index)}
              onToggle={() => {
                if (revealedItems.includes(index)) {
                  setRevealedItems(revealedItems.filter(item => item !== index));
                } else {
                  setRevealedItems([...revealedItems, index]);
                }
              }}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
