"use client"

import React from "react"
import { motion, AnimatePresence } from "framer-motion"
import HtmlContent from "../HtmlContent"

interface PracticeCardProps {
  number: number
  question: string
  answer: string
  isVisible: boolean
  showAnswer: boolean
  onToggleVisibility: () => void
  onToggleAnswer: () => void
}

export function PracticeCard({
  number,
  question,
  answer,
  isVisible,
  showAnswer,
  onToggleVisibility,
  onToggleAnswer,
}: PracticeCardProps) {
  return (
    <div className="rounded-lg bg-white/10 overflow-hidden">
      {/* Header with number and chevron */}
      <div className="p-4 flex items-center gap-2 cursor-pointer" onClick={onToggleVisibility}>
        <div className="concept-number bg-[#fadb9a]/30 text-[#4169E1]">{number}</div>
        <div className="flex-1"></div>
        {isVisible ? (
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-chevron-up">
            <path d="m18 15-6-6-6 6"></path>
          </svg>
        ) : (
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-chevron-down">
            <path d="m6 9 6 6 6-6"></path>
          </svg>
        )}
      </div>

      {/* Content area - appears when card is expanded */}
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{
              duration: 0.3,
              ease: "easeInOut",
            }}
            className="overflow-hidden"
            style={{ height: "auto", opacity: 1, transform: "none" }}
          >
            <div className="p-4 pt-0">
              {/* Question */}
              <div className="text-3xl mb-4">
                <HtmlContent html={question} />
              </div>

              {/* Answer toggle button */}
              <div
                className="flex items-center justify-between p-2 bg-white/10 rounded-md cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation()
                  onToggleAnswer()
                }}
              >
                <span className="text-sm font-medium text-[#fadb9a]">Answer</span>
                {showAnswer ? (
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-chevron-up">
                    <path d="m18 15-6-6-6 6"></path>
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-chevron-down">
                    <path d="m6 9 6 6 6-6"></path>
                  </svg>
                )}
              </div>

              {/* Answer content */}
              <AnimatePresence>
                {showAnswer && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: "auto", opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.25 }}
                    className="overflow-hidden"
                    style={{ height: "auto", opacity: 1, transform: "none" }}
                  >
                    <div className="mt-2 p-3 bg-[#fadb9a]/10 rounded-md text-3xl">
                      <HtmlContent html={answer} />
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
