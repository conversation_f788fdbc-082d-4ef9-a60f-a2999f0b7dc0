"use client"

import React, { useState, useEffect } from "react"
import { usePathname } from "next/navigation"
import { Eye, ChevronDown, ChevronUp, Code } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { getQuickReviewQuestions, getSlideImageUrl, getSlideTitle, getSlide } from "@/services/slideService"
import { useLessonContext } from "../lesson-context"
import { QuickReviewCard } from "./QuickReviewCard"
import { RevealPoint } from "./RevealPoint"
import HtmlContent from "../HtmlContent"
import HtmlEditorModal from "../HtmlEditorModal"
import HtmlContentWrapper from "../HtmlContentWrapper"
import { getLessonNumberFromStorage, getDocumentIdFromStorage } from "@/utils/lessonUtils"
import { isSuperAdmin } from "@/utils/adminUtils"
import { useSession } from "next-auth/react"
import { saveHtmlAndRefresh } from "@/utils/forceRefresh"

interface IntroductionSlideProps {
  slideNumber: number
  slideData: any
  title?: string
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
  setRevealedItems?: React.Dispatch<React.SetStateAction<number[]>>
}

export const IntroductionSlide: React.FC<IntroductionSlideProps> = ({
  slideNumber,
  slideData,
  title,
  revealedItems = [],
  registerRevealableItems = () => {},
  setRevealedItems,
}) => {
  // Get lesson context for current unit, lesson, grade
  const { unitNumber, lessonNumber, gradeLevel, lang, curriculum } = useLessonContext();

  const [showAll, setShowAll] = useState(false)
  const [standardsOpen, setStandardsOpen] = useState(false)
  const pathname = usePathname()
  const isPresenterView = pathname?.includes('/presenter')

  // Add state for HTML editor modal
  const [isHtmlEditorOpen, setIsHtmlEditorOpen] = useState(false);
  const [currentHtml, setCurrentHtml] = useState("");
    const { data: session } = useSession()
  

  // Get the questions from the slide data for Quick Review
  const questions = slideNumber === 1 ? getQuickReviewQuestions(slideData) : []

  // Get the learning goals from the slide data
  const learningGoals = slideData?.lg_1 ? [
    { title: slideData.lg_1, content: slideData.lg_1_content || slideData.lg_1 },
    { title: slideData.lg_2, content: slideData.lg_2_content || slideData.lg_2 },
    { title: slideData.lg_3, content: slideData.lg_3_content || slideData.lg_3 }
  ].filter(goal => goal.title) : []; // Filter out any undefined goals

  // Get the vocabulary terms from the slide data
  const vocabularyTerms = slideData?.term_1 ? [
    { term: slideData.term_1, definition: slideData.definition_1_with_emoji },
    { term: slideData.term_2, definition: slideData.definition_2_with_emoji },
    { term: slideData.term_3, definition: slideData.definition_3_with_emoji },
    { term: slideData.term_4, definition: slideData.definition_4_with_emoji }
  ].filter(term => term.term && term.definition) : []; // Filter out any undefined terms

  // Register revealable items
  useEffect(() => {
    if (slideNumber === 1 && questions.length > 0) {
      // For Quick Review: each question + answer = 2 items per question
      registerRevealableItems(questions.length * 2)
    } else if (slideNumber === 2 && learningGoals.length > 0) {
      registerRevealableItems(learningGoals.length)
    } else if (slideNumber === 3 && vocabularyTerms.length > 0) {
      // For Vocabulary: each term + definition = 2 items per term
      registerRevealableItems(vocabularyTerms.length * 2)
    }
  }, [registerRevealableItems, questions.length, learningGoals.length, vocabularyTerms.length, slideNumber])

  // Get the slide title from props or slideData
  const slideTitle = title || slideData?.slide_pedagogical_name || "Introduction"

  // Get the image URL from slideData if available
  const imageUrl = slideData?.html_css_description_of_image?.startsWith('http')
    ? slideData.html_css_description_of_image
    : undefined

  // Check if we have generated HTML content
  const hasGeneratedHtml = !!slideData?.generated_html_content

  // Function to refresh slide data after save
  const refreshSlideData = async () => {
    console.log('IntroductionSlide: *** REFRESHING SLIDE DATA ***');

    // Update URL with current slide before reload
    const url = new URL(window.location.href);
    url.searchParams.set('unit', unitNumber);
    url.searchParams.set('lesson', lessonNumber);
    url.searchParams.set('grade', gradeLevel);
    url.searchParams.set('slide', slideNumber.toString());
    if (lang) {
      url.searchParams.set('lang', lang);
    }

    // Update URL and reload
    window.history.replaceState({}, '', url.toString());
    window.location.reload();
  };

  // Function to open HTML editor
  const openHtmlEditor = () => {
    setCurrentHtml(slideData?.html_css_description_of_image || "");
    setIsHtmlEditorOpen(true);
  };

  // Function to save HTML content
  const saveHtmlContent = async (html: string) => {
    try {
      console.log('Saving HTML content for slide:', {
        unitNumber,
        lessonNumber,
        gradeLevel,
        slideNumber,
        curriculum,
        lang,
        htmlLength: html?.length
      });

      const result = await saveHtmlAndRefresh(
        slideNumber,
        unitNumber,
        lessonNumber,
        gradeLevel,
        lang,
        html,
        curriculum
      );

      if (!result.success) {
        throw new Error(result.message);
      }

      console.log('HTML content updated successfully');
    } catch (error) {
      console.error('Error updating HTML content:', error);
      alert('Failed to update HTML content. Please try again.');
    }
  };

  // Determine the slide subtype based on the slide number (language-independent)
  const getSlideSubtype = () => {
    switch (slideNumber) {
      case 1:
        return "quick-review"
      case 2:
        return "learning-goals"
      case 3:
        return "vocabulary"
      default:
        return "quick-review" // default
    }
  }

  const slideSubtype = getSlideSubtype()

  // Render different content based on the slide subtype
  const renderSlideContent = () => {
    switch (slideSubtype) {
      case "quick-review":
        return (
          <div className="text-white">
            <h2 className="slide-title">{slideTitle}</h2>
            <div className="concept-slide">
              <div
                className="space-y-8 rounded-xl p-6 relative bg-white/10"
                style={{ minHeight: "400px" }}
              >
                <div className="space-y-8">
                  {questions.map((item, index) => {
                    const questionIndex = index * 2      // Question indices: 0, 2, 4
                    const answerIndex = index * 2 + 1    // Answer indices: 1, 3, 5
                    const isQuestionVisible = revealedItems.includes(questionIndex)
                    const isAnswerVisible = revealedItems.includes(answerIndex)

                    return (
                      <QuickReviewCard
                        key={`question-${index}`}
                        number={index + 1}
                        question={item.question}
                        answer={
                          <div className="space-y-2">
                            <p>{item.answer}</p>
                          </div>
                        }
                        isVisible={isQuestionVisible}
                        isAnswerVisible={isAnswerVisible}
                        onToggleVisibility={() => {
                          if (setRevealedItems) {
                            if (isQuestionVisible) {
                              // Hide question (and answer if visible)
                              setRevealedItems(revealedItems.filter((item) => item !== questionIndex && item !== answerIndex))
                            } else {
                              // Show question
                              setRevealedItems([...revealedItems, questionIndex])
                            }
                          }
                        }}
                        onToggleAnswer={() => {
                          if (setRevealedItems) {
                            if (isAnswerVisible) {
                              // Hide answer
                              setRevealedItems(revealedItems.filter((item) => item !== answerIndex))
                            } else {
                              // Show answer
                              setRevealedItems([...revealedItems, answerIndex])
                            }
                          }
                        }}
                      />
                    )
                  })}
                </div>
              </div>
              <div className="illustration-box" style={{ minHeight: "400px" }}>
                <div className="slide-image-container">
                  <div className="w-full h-full flex justify-center items-center overflow-hidden">
                    {imageUrl ? (
                      <img
                        src={imageUrl}
                        alt={`Illustration for ${slideTitle}`}
                        style={{
                          maxWidth: "100%",
                          maxHeight: "100%",
                          objectFit: "contain",
                          backgroundColor: "transparent",
                          border: "none",
                        }}
                        onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                          console.error("Image failed to load:", e.currentTarget.src)
                          e.currentTarget.src = "/place-value-comparison.png"
                        }}
                      />
                    ) : slideData?.generated_html_content ? (
                      <div className="p-4 bg-white/10 rounded-lg w-full h-full">
                        <div className="w-full h-full flex justify-center items-center">
                          <div className="generated-html-container w-full">
                            <HtmlContentWrapper
                              html={slideData.generated_html_content}
                              className="w-full"
                              slideNumber={slideNumber}
                            />
                          </div>
                        </div>
                      </div>
                    ) : slideData?.html_css_description_of_image ? (
                      <div className="p-4 bg-white/10 h-full rounded-lg w-full">
                        {slideData.html_css_description_of_image.includes('<!DOCTYPE html>') ||
                         slideData.html_css_description_of_image.includes('<html') ? (
                          <div className="w-full h-full flex justify-center items-center">
                            <div
                              className="generated-html-container w-full overflow-hidden"
                              style={{ height: "100%", maxHeight: "100%" }}
                            >
                              <HtmlContentWrapper
                                key={`html-${slideNumber}-${slideData?.html_css_description_of_image?.length || 0}`}
                                html={slideData.html_css_description_of_image}
                                className="w-full"
                                onEditClick={openHtmlEditor}
                                slideNumber={slideNumber}
                                session={session}
                              />
                            </div>
                          </div>
                        ) : (
                          <div className="text-center">
                            <div className="text-lg font-medium text-white mb-2">Image Not Available</div>
                            <div className="text-sm text-white/70 mb-4">
                              The image for this slide has not been added yet.
                            </div>
                            <div className="mt-2 p-3 bg-white/5 rounded-md">
                              <div className="text-sm text-white/90 font-medium mb-1">Image Description:</div>
                              <div className="text-sm text-white/80">
                                {slideData.html_css_description_of_image}
                              </div>
                            </div>
                            <div className="mt-4">
                              {isSuperAdmin(session) && (
                                <button
                                  onClick={openHtmlEditor}
                                  className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors mx-auto"
                                >
                                  <Code size={16} />
                                  Create HTML Content
                                </button>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="flex flex-col items-center justify-center text-center p-4 bg-white/10 rounded-lg w-full h-full">
                        <div className="text-lg font-medium text-white mb-2">Image Not Available</div>
                        <div className="text-sm text-white/70 mb-4">
                          The image for this slide has not been added yet.
                        </div>
                        {isSuperAdmin(session) && (
                          <button
                            onClick={openHtmlEditor}
                            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                          >
                            <Code size={16} />
                            Create HTML Content
                          </button>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )
      case "learning-goals":
        return (
          <div className="text-white">
            <h2 className="slide-title">{slideTitle}</h2>
            <div
              className="space-y-6 rounded-xl p-6 relative bg-white/10"
              style={{ minHeight: "400px" }}
            >
              {/* Add show all button */}
              <div className="flex justify-end mb-4">
                <button onClick={() => setShowAll(!showAll)} className="flex items-center gap-2 text-sm cursor-pointer">
                  <span>{showAll ? "Hide All" : "Show All"}</span>
                  <Eye size={18} className={showAll ? "text-[#fadb9a]" : ""} />
                </button>
              </div>

              {learningGoals.map((goal, index) => (
                <RevealPoint
                  key={`goal-${index}`}
                  number={index + 1}
                  title={goal.title}
                  content={goal.content}
                  forceReveal={revealedItems.includes(index) || showAll}
                />
              ))}

              {slideData.standards_alignment && (
                <div className="mt-6">
                  <div
                    className={`rounded-lg ${standardsOpen ? "bg-white/20" : "bg-white/10"} p-3 backdrop-blur-sm cursor-pointer`}
                    onClick={() => setStandardsOpen(!standardsOpen)}
                  >
                    <div className="flex items-center gap-3">
                      <div
                        className="concept-number bg-[#2B6DFE] text-white text-sm"
                        style={{ width: "2.5rem", height: "2.5rem" }}
                      >
                        S
                      </div>
                      <h3 className="text-2xl font-montserrat font-extrabold text-white">Standards Alignment</h3>
                    </div>

                    <AnimatePresence>
                      {standardsOpen && (
                        <motion.div
                          initial={{ height: 0, opacity: 0, y: -10 }}
                          animate={{ height: "auto", opacity: 1, y: 0 }}
                          exit={{ height: 0, opacity: 0, y: -10 }}
                          transition={{
                            duration: 0.3,
                            ease: "easeInOut",
                          }}
                          className="overflow-hidden"
                        >
                          <div className="mt-3 text-xl">
                            <p className="mb-2">{slideData.standards_alignment}</p>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>

                    <div className="mt-1 text-center">
                      {standardsOpen ? (
                        <ChevronUp size={14} className="inline-block" />
                      ) : (
                        <ChevronDown size={14} className="inline-block" />
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )
      case "vocabulary":
        return (
          <div className="rounded-xl overflow-hidden shadow-lg blue-gradient">
            <div className="text-white">
              <h2 className="slide-title text-3xl mb-6 text-center">{slideTitle}</h2>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {vocabularyTerms.map((item, index) => {
                  const termIndex = index * 2      // Term indices: 0, 2, 4
                  const definitionIndex = index * 2 + 1    // Definition indices: 1, 3, 5
                  const isTermVisible = revealedItems.includes(termIndex)
                  const isDefinitionVisible = revealedItems.includes(definitionIndex)

                  return (
                    <div
                      key={item.term}
                      className={`rounded-lg ${isTermVisible ? "bg-white/20" : "bg-white/10"} p-4 backdrop-blur-sm cursor-pointer`}
                      onClick={() => {
                        // Toggle term visibility
                        if (setRevealedItems) {
                          if (isTermVisible) {
                            // Hide term (and definition if visible)
                            setRevealedItems((prev) => prev.filter((i) => i !== termIndex && i !== definitionIndex))
                          } else {
                            // Show term
                            setRevealedItems((prev) => [...prev, termIndex])
                          }
                        }
                      }}
                    >
                      <div className="flex items-center gap-4">
                        <div className="concept-number bg-[#2B6DFE] text-white">{index + 1}</div>
                        <h3 className="mb-3 text-4xl font-montserrat font-extrabold text-white">{item.term}</h3>
                      </div>

                      <AnimatePresence>
                        {isTermVisible && (
                          <motion.div
                            initial={{ height: 0, opacity: 0, y: -10 }}
                            animate={{ height: "auto", opacity: 1, y: 0 }}
                            exit={{ height: 0, opacity: 0, y: -10 }}
                            transition={{
                              duration: 0.3,
                              ease: "easeInOut",
                            }}
                            className="overflow-hidden"
                          >
                            <div className="mt-4 border-t border-white/20 pt-4">
                              <div
                                className="flex items-center justify-between cursor-pointer mb-2"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  if (setRevealedItems) {
                                    if (isDefinitionVisible) {
                                      // Hide definition
                                      setRevealedItems((prev) => prev.filter((i) => i !== definitionIndex))
                                    } else {
                                      // Show definition
                                      setRevealedItems((prev) => [...prev, definitionIndex])
                                    }
                                  }
                                }}
                              >
                                <span className="font-medium">Definition</span>
                                <Eye size={18} className={isDefinitionVisible ? "text-[#fadb9a]" : ""} />
                              </div>

                              <AnimatePresence>
                                {isDefinitionVisible && (
                                  <motion.div
                                    initial={{ height: 0, opacity: 0, y: -5 }}
                                    animate={{ height: "auto", opacity: 1, y: 0 }}
                                    exit={{ height: 0, opacity: 0, y: -5 }}
                                    transition={{ duration: 0.25 }}
                                    className="overflow-hidden"
                                  >
                                    <div className="bg-white/10 p-4 rounded-md">
                                      <p className="text-3xl" dangerouslySetInnerHTML={{ __html: item.definition }} />
                                    </div>
                                  </motion.div>
                                )}
                              </AnimatePresence>
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>

                      <div className="mt-2 text-center">
                        {isTermVisible ? (
                          <ChevronUp size={16} className="inline-block" />
                        ) : (
                          <ChevronDown size={16} className="inline-block" />
                        )}
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          </div>
        )
      default:
        return (
          <div className="flex flex-col space-y-4">
            <h2 className="text-2xl font-bold mb-4">{slideTitle}</h2>
            <div className="bg-white/10 p-4 rounded-lg">
              <div>No specific content available for this introduction slide type.</div>
            </div>
          </div>
        )
    }
  }

  // For Quick Review, Learning Goals, and Vocabulary, we use a different layout
  if (slideSubtype === "quick-review" || slideSubtype === "learning-goals" || slideSubtype === "vocabulary") {
    return (
      <div className="w-full h-full bg-gradient-to-br from-blue-600 via-blue-500 to-cyan-400 text-white rounded-lg shadow-lg">
        {/* HTML Editor Modal */}
        <HtmlEditorModal
          isOpen={isHtmlEditorOpen}
          onClose={() => setIsHtmlEditorOpen(false)}
          initialHtml={currentHtml}
          onSave={saveHtmlContent}
          onSaveComplete={refreshSlideData}
          slideNumber={slideNumber}
          documentId={getDocumentIdFromStorage()}
          unitNumber={unitNumber}
          lessonNumber={lessonNumber}
          gradeLevel={gradeLevel}
          curriculum={curriculum}
          lang={lang}
          session={session}
        />

        {renderSlideContent()}

        {/* Teacher Tips - Only shown in presenter view */}
        {/* {isPresenterView && slideData?.teacher_tips && (
          <div className="mt-8 p-4 bg-white/10 rounded-lg w-full h-full mx-8">
            <h3 className="text-xl font-semibold mb-2">Teacher Tips</h3>
            <div className="mb-4">
              <h4 className="text-lg font-medium text-[#fadb9a]">General Tip:</h4>
              <div className="mt-1 text-sm">
                <HtmlContent
                  html={slideData.teacher_tips.general_tip || "No general tip available."}
                />
              </div>
            </div>
            <div>
              <h4 className="text-lg font-medium text-[#fadb9a]">Common Misconceptions:</h4>
              <div className="mt-1 text-sm">
                <HtmlContent
                  html={slideData.teacher_tips.misconception_tip || "No misconception tip available."}
                />
              </div>
            </div>
          </div>
        )} */}
      </div>
    )
  }

  // For other introduction slide types, use the standard layout
  return (
    <div className="w-full h-full flex flex-col p-6 bg-gradient-to-br from-blue-600 via-blue-500 to-cyan-400 text-white rounded-lg shadow-lg">
      {/* HTML Editor Modal */}
      <HtmlEditorModal
        isOpen={isHtmlEditorOpen}
        onClose={() => setIsHtmlEditorOpen(false)}
        initialHtml={currentHtml}
        onSave={saveHtmlContent}
        onSaveComplete={refreshSlideData}
        slideNumber={slideNumber}
        documentId={getDocumentIdFromStorage()}
        unitNumber={unitNumber}
        lessonNumber={lessonNumber}
        gradeLevel={gradeLevel}
        curriculum={curriculum}
        lang={lang}
        session={session}
      />

      <div className="flex-1 overflow-auto">
        {renderSlideContent()}
      </div>

      {/* Teacher Tips - Only shown in presenter view */}
      {/* {isPresenterView && slideData?.teacher_tips && (
        <div className="mt-8 p-4 bg-white/10 rounded-lg w-full h-full">
          <h3 className="text-xl font-semibold mb-2">Teacher Tips</h3>
          <div className="mb-4">
            <h4 className="text-lg font-medium text-[#fadb9a]">General Tip:</h4>
            <div className="mt-1 text-sm">
              <HtmlContent
                html={slideData.teacher_tips.general_tip || "No general tip available."}
              />
            </div>
          </div>
          <div>
            <h4 className="text-lg font-medium text-[#fadb9a]">Common Misconceptions:</h4>
            <div className="mt-1 text-sm">
              <HtmlContent
                html={slideData.teacher_tips.misconception_tip || "No misconception tip available."}
              />
            </div>
          </div>
        </div>
      )} */}
    </div>
  )
}

export default IntroductionSlide
