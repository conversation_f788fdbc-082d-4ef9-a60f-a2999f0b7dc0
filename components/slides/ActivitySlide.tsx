"use client"

import React, { useState, useEffect } from "react"
import { getSlideTitle, getSlideActivities, getSlideImageUrl, getSlide, loadSlideData } from "@/services/slideService"
import { ActivityCard } from "./ActivityCard"
import HtmlContent from "../HtmlContent"
import HtmlContentWrapper from "../HtmlContentWrapper"
import { Code } from "lucide-react"
import { isSuperAdmin } from "@/utils/adminUtils"

interface ActivitySlideProps {
  title?: string
  activities?: { question: string; answer: string }[]
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
  setRevealedItems?: React.Dispatch<React.SetStateAction<number[]>>
  slideData?: any
  slideNumber?: number
}

export function ActivitySlide({
  title,
  activities,
  revealedItems = [],
  registerRevealableItems = () => {},
  setRevealedItems = () => {},
  slideData,
  slideNumber,
}: ActivitySlideProps) {
  const [loadedSlideData, setLoadedSlideData] = useState<any>(slideData);
  const [showAnswers, setShowAnswers] = useState<number[]>([]);

  // Reset showAnswers when slide number changes
  useEffect(() => {
    console.log('ActivitySlide: Slide number changed to', slideNumber, '- resetting showAnswers');
    setShowAnswers([]);
  }, [slideNumber]);

  // Load slide data if not provided
  useEffect(() => {
    if (!slideData && slideNumber) {
      const fetchData = async () => {
        try {
          const currentSlideData = await getSlide(slideNumber);
          setLoadedSlideData(currentSlideData);
        } catch (error) {
          console.error("Error loading slide data:", error);
        }
      };

      fetchData();
    }
  }, [slideData, slideNumber]);

  // Use the loaded slide data or the provided slide data
  const effectiveSlideData = loadedSlideData || slideData;

  // Use activities from props if provided, otherwise get from slideData
  const slideActivities = activities || (effectiveSlideData ? getSlideActivities(effectiveSlideData) : []);

  // Register revealable items (one for each activity)
  useEffect(() => {
    registerRevealableItems(slideActivities.length)
  }, [registerRevealableItems, slideActivities.length])

  // Get the slide title from props or slideData
  const slideTitle = title || (effectiveSlideData ? getSlideTitle(effectiveSlideData) : "Activity");

  // Get the image URL from slideData if available
  const imageUrl = effectiveSlideData ? getSlideImageUrl(effectiveSlideData) : null;

  // Toggle answer visibility for a specific activity
  const toggleAnswer = (index: number) => {
    if (showAnswers.includes(index)) {
      setShowAnswers(showAnswers.filter((i) => i !== index))
    } else {
      setShowAnswers([...showAnswers, index])
    }
  }

  return (
    <div className="text-white">
      <h2 className="slide-title">{slideTitle}</h2>
      <div className="concept-slide">
        <div className="space-y-8 rounded-xl p-6 relative bg-white/10" style={{ minHeight: "400px" }}>
          <div className="grid grid-cols-1 gap-6">
            {slideActivities.map((activity, index) => (
              <ActivityCard
                key={`activity-${index}`}
                number={index + 1}
                question={activity.question}
                answer={activity.answer}
                isVisible={revealedItems.includes(index)}
                showAnswer={showAnswers.includes(index)}
                onToggleVisibility={() => {
                  if (revealedItems.includes(index)) {
                    setRevealedItems(revealedItems.filter((item) => item !== index))
                  } else {
                    setRevealedItems([...revealedItems, index])
                  }
                }}
                onToggleAnswer={() => toggleAnswer(index)}
              />
            ))}
          </div>
        </div>
        <div className="illustration-box" style={{ minHeight: "400px" }}>
          <div className="slide-image-container">
            <div className="w-full h-full flex justify-center items-center overflow-hidden">
              {imageUrl ? (
                <img
                  src={imageUrl}
                  alt={`Illustration for ${slideTitle}`}
                  style={{
                    maxWidth: "100%",
                    maxHeight: "100%",
                    objectFit: "contain",
                    backgroundColor: "transparent",
                    border: "none",
                  }}
                  onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                    console.error("Image failed to load:", e.currentTarget.src)
                    e.currentTarget.src = "/place-value-comparison.png"
                  }}
                />
              ) : effectiveSlideData?.generated_html_content ? (
                <div className="p-4 bg-white/10 rounded-lg w-full h-full">
                  <div className="w-full h-full flex justify-center items-center">
                    <div className="generated-html-container w-full">
                      <HtmlContentWrapper
                        html={effectiveSlideData.generated_html_content}
                        className="w-full"
                        slideNumber={slideNumber}
                      />
                    </div>
                  </div>
                </div>
              ) : effectiveSlideData?.html_css_description_of_image ? (
                <div className="p-4 bg-white/10 rounded-lg w-full h-full text-center">
                  <div className="text-lg font-medium text-white mb-2">Image Not Available</div>
                  <div className="text-sm text-white/70 mb-4">
                    The image for this slide has not been added yet.
                  </div>
                  <div className="mt-2 p-3 bg-white/5 rounded-md">
                    <div className="text-sm text-white/90 font-medium mb-1">Image Description:</div>
                    <div className="text-sm text-white/80">
                      {effectiveSlideData.html_css_description_of_image}
                    </div>
                  </div>
                  <div className="mt-4">
                    {isSuperAdmin() && (
                      <button
                        onClick={() => {
                          // Navigate to HTML generator with context
                          const unitNumber = localStorage.getItem('lesson_unit_number')
                          const lessonNumber = localStorage.getItem('lesson_lesson_number')
                          const gradeLevel = localStorage.getItem('lesson_grade_level')

                          const params = new URLSearchParams({
                            prompt: effectiveSlideData.html_css_description_of_image,
                            slideNumber: slideNumber?.toString() || '',
                            unitNumber: unitNumber || '',
                            lessonNumber: lessonNumber || '',
                            gradeLevel: gradeLevel || ''
                          })

                          window.open(`/html-generator?${params.toString()}`, '_blank')
                        }}
                        className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors mx-auto"
                      >
                        <Code size={16} />
                        Create HTML Content
                      </button>
                    )}
                  </div>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center text-center p-4 bg-white/10 rounded-lg w-full h-full">
                  <div className="text-lg font-medium text-white mb-2">Image Not Available</div>
                  <div className="text-sm text-white/70">
                    The image for this slide has not been added yet.
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
