"use client"

import React, { useState, useEffect } from "react"
import { ChevronDown, ChevronUp } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"

interface OnRampTrySlideProps {
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
  setRevealedItems?: React.Dispatch<React.SetStateAction<number[]>>
}

export function OnRampTrySlide({
  revealedItems = [],
  registerRevealableItems = () => {},
  setRevealedItems = () => {},
}: OnRampTrySlideProps) {
  const [showAnswers, setShowAnswers] = useState<number[]>([])

  const tryProblems = [
    {
      question: "Compare 45 and 42 using the correct symbol.",
      answer: "45 > 42",
    },
    {
      question: "Compare 37 and 73 using the correct symbol.",
      answer: "37 < 73",
    },
    {
      question: "Compare 28 and 82 using the correct symbol.",
      answer: "28 < 82",
    },
    {
      question: "Compare 56 and 65 using the correct symbol.",
      answer: "56 < 65",
    },
  ]

  // Register revealable items (one for each problem)
  useEffect(() => {
    registerRevealableItems(tryProblems.length)
  }, [registerRevealableItems])

  // Toggle answer visibility for a specific problem
  const toggleAnswer = (index: number) => {
    if (showAnswers.includes(index)) {
      setShowAnswers(showAnswers.filter((i) => i !== index))
    } else {
      setShowAnswers([...showAnswers, index])
    }
  }

  return (
    <div className="text-white">
      <h2 className="slide-title">On-Ramp Try 1</h2>
      <div className="relative">
        <div className="grid grid-cols-2 gap-6" style={{ minHeight: "400px" }}>
          {tryProblems.map((problem, index) => (
            <div key={`try-${index}`} className="rounded-lg bg-white/10 overflow-hidden h-full flex flex-col">
              <div className="p-4 flex items-center justify-between gap-4 cursor-pointer" onClick={() => {
                if (revealedItems.includes(index)) {
                  setRevealedItems(revealedItems.filter((item) => item !== index))
                } else {
                  setRevealedItems([...revealedItems, index])
                }
              }}>
                <div className="concept-number bg-[#2B6DFE] text-white">{index + 1}</div>
                {revealedItems.includes(index) ?
                  <ChevronUp size={20} /> :
                  <ChevronDown size={20} />
                }
              </div>

              <AnimatePresence>
                {revealedItems.includes(index) && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: "auto", opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="overflow-hidden flex flex-col flex-grow"
                    style={{ height: "auto" }}
                  >
                    <div className="p-4 pt-0 flex flex-col flex-grow">
                      <div className="text-3xl mb-4">{problem.question}</div>

                      <div className="mt-auto">
                        <div
                          className="flex items-center justify-between p-3 bg-white/10 rounded-md cursor-pointer"
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleAnswer(index);
                          }}
                        >
                          <span className="font-medium">Answer</span>
                          {showAnswers.includes(index) ?
                            <ChevronUp size={18} /> :
                            <ChevronDown size={18} />
                          }
                        </div>

                        <AnimatePresence>
                          {showAnswers.includes(index) && (
                            <motion.div
                              initial={{ height: 0, opacity: 0 }}
                              animate={{ height: "auto", opacity: 1 }}
                              exit={{ height: 0, opacity: 0 }}
                              transition={{ duration: 0.3 }}
                              className="overflow-hidden"
                            >
                              <div className="mt-3 p-3 bg-white/10 rounded-md">
                                {problem.answer}
                              </div>
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
