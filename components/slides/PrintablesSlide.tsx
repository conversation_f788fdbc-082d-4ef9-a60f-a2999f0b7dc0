"use client"

import React, { useEffect, useState } from "react"
import { usePathname } from "next/navigation"
import { Download } from "lucide-react"
import { useSession } from "next-auth/react"
import HtmlContent from "../HtmlContent"
import HtmlContentWrapper from "../HtmlContentWrapper"
import { PdfUploader } from "../PdfUploader"

interface PrintablesSlideProps {
  slideNumber: number
  slideData: any
  title?: string
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
  documentId?: string
  unitNumber?: string
  lessonNumber?: string
  gradeLevel?: string
}

export const PrintablesSlide: React.FC<PrintablesSlideProps> = ({
  slideNumber,
  slideData,
  title,
  revealedItems = [],
  registerRevealableItems = () => {},
  documentId,
  unitNumber,
  lessonNumber,
  gradeLevel,
}) => {
  const pathname = usePathname()
  const isPresenterView = pathname?.includes('/presenter')
  const { data: session } = useSession()
  const [currentPdfUrl, setCurrentPdfUrl] = useState<string>('')

  // Check if user is admin
  const isAdmin = (session?.user as any)?.role === 'admin'



  // Register revealable items (just one for the download button)
  useEffect(() => {
    registerRevealableItems(1)
  }, [registerRevealableItems])

  // Get the slide title from props or slideData
  const slideTitle = title || slideData?.slide_pedagogical_name || "Printables"

  // Determine the type of printable and get the appropriate PDF link and description
  const getPrintableInfo = () => {
    if (slideTitle.includes("Lesson Guide")) {
      return {
        pdfLink: currentPdfUrl || slideData?.link_to_lesson_guide_pdf || "/printable-lesson-guide.pdf",
        description: "This guide includes all the key concepts, vocabulary, and practice problems from this lesson.",
        buttonText: "Download Lesson Guide",
        slideType: "lesson_guide" as const
      };
    } else if (slideTitle.includes("Practice")) {
      return {
        pdfLink: currentPdfUrl || slideData?.link_to_practice_pdf || "/printable-practice.pdf",
        description: "This worksheet contains additional practice problems for students to work on independently or for homework.",
        buttonText: "Download Practice Problems",
        slideType: "practice" as const
      };
    } else {
      return {
        pdfLink: currentPdfUrl || slideData?.link_to_accelerator_pdf || "/accelerator-activities.pdf",
        description: "These activities are designed for students who have mastered the basic concepts and are ready for more challenging work.",
        buttonText: "Download Accelerator Activities",
        slideType: "accelerator" as const
      };
    }
  }

  const { pdfLink, description, buttonText, slideType } = getPrintableInfo();

  // Initialize current PDF URL from slide data
  useEffect(() => {
    const info = getPrintableInfo()
    if (slideType === "lesson_guide" && slideData?.link_to_lesson_guide_pdf) {
      setCurrentPdfUrl(slideData.link_to_lesson_guide_pdf)
    } else if (slideType === "practice" && slideData?.link_to_practice_pdf) {
      setCurrentPdfUrl(slideData.link_to_practice_pdf)
    } else if (slideType === "accelerator" && slideData?.link_to_accelerator_pdf) {
      setCurrentPdfUrl(slideData.link_to_accelerator_pdf)
    }
  }, [slideData, slideType])

  // Generate document ID if not provided
  const effectiveDocumentId = documentId || (unitNumber && lessonNumber && gradeLevel ?
    `${unitNumber}-${lessonNumber}-${gradeLevel}` : '')

  // Handle PDF upload success
  const handlePdfUploadSuccess = (url: string) => {
    setCurrentPdfUrl(url)
  }

  return (
    <div className="text-white">
      <h2 className="slide-title">{slideTitle}</h2>
      <div className="flex flex-col items-center justify-center h-[500px] bg-white/10 rounded-xl p-8">
        <div className="text-center mb-8">
          <p className="text-3xl mb-4">Download the {slideTitle.replace("PDF Link", "").toLowerCase()} for this lesson.</p>
          <p className="text-xl">
            {description}
          </p>
        </div>

        <a
          href={pdfLink}
          download
          target="_blank"
          rel="noopener noreferrer"
          className={`flex items-center gap-2 px-6 py-3 rounded-full ${
            revealedItems.includes(0) ? "bg-[#fadb9a] text-[#4169E1]" : "bg-white/20 text-white"
          } transition-colors duration-300`}
        >
          <Download size={24} />
          <span className="text-xl font-medium">{buttonText}</span>
        </a>

        {/* Admin PDF Upload Section */}
        {isAdmin && effectiveDocumentId && (
          <div className="mt-6 max-w-md mx-auto">
            <PdfUploader
              documentId={effectiveDocumentId}
              slideType={slideType}
              currentPdfUrl={currentPdfUrl}
              onUploadSuccess={handlePdfUploadSuccess}
            />
          </div>
        )}
      </div>

      {/* Teacher Tips - Only shown in presenter view */}
      {/* {isPresenterView && slideData?.teacher_tips && (
        <div className="mt-8 p-4 bg-white/10 rounded-lg w-full h-full">
          <h3 className="text-xl font-semibold mb-2">Teacher Tips</h3>
          <div className="mb-4">
            <h4 className="text-lg font-medium text-[#fadb9a]">General Tip:</h4>
            <div className="mt-1 text-sm">
              <HtmlContentWrapper
                html={slideData.teacher_tips.general_tip || "No general tip available."}
                slideNumber={slideNumber}
              />
            </div>
          </div>
          {slideData.teacher_tips.misconception_tip && (
            <div>
              <h4 className="text-lg font-medium text-[#fadb9a]">Common Misconceptions:</h4>
              <div className="mt-1 text-sm">
                <HtmlContentWrapper
                  html={slideData.teacher_tips.misconception_tip}
                  slideNumber={slideNumber}
                />
              </div>
            </div>
          )}
        </div>
      )} */}
    </div>
  )
}

export default PrintablesSlide
