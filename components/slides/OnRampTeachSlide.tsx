"use client"

import React, { useEffect } from "react"
import { RevealPoint } from "./RevealPoint"

interface OnRampTeachSlideProps {
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
  slideData?: any
}

export function OnRampTeachSlide({ revealedItems = [], registerRevealableItems = () => {}, slideData }: OnRampTeachSlideProps) {
  const teachPoints = [
    {
      title: "The left digit is the tens place.",
      content: "In a two-digit number, the left digit tells us how many tens.",
    },
    {
      title: "The right digit is the ones place.",
      content: "In a two-digit number, the right digit tells us how many ones.",
    },
    {
      title: "Compare tens first.",
      content: "When comparing two-digit numbers, look at the tens place first.",
    },
    {
      title: "If tens are equal, compare ones.",
      content: "If the tens digits are the same, then compare the ones digits.",
    },
    {
      title: "Use symbols to show comparison.",
      content: "Use > for 'greater than', < for 'less than', and = for 'equal to'.",
    },
  ]

  // Register revealable items (one for each point)
  useEffect(() => {
    registerRevealableItems(teachPoints.length)
  }, [registerRevealableItems])

  return (
    <div className="text-white">
      <h2 className="slide-title">On-Ramp: Teach</h2>
      <div className="concept-slide">
        <div className="space-y-6 rounded-xl p-6 relative bg-white/10" style={{ minHeight: "400px" }}>
          {teachPoints.map((point, index) => (
            <RevealPoint
              key={index}
              number={index + 1}
              title={point.title}
              content={point.content}
              forceReveal={revealedItems.includes(index)}
            />
          ))}
        </div>
        <div className="illustration-box" style={{ minHeight: "400px" }}>
          <div className="slide-image-container">
            <div className="w-full h-full flex justify-center items-center overflow-hidden">
              <img
                src="/place-value-chart.png"
                alt="Place value chart showing tens and ones"
                style={{
                  maxWidth: "100%",
                  maxHeight: "100%",
                  objectFit: "contain",
                  backgroundColor: "transparent",
                  border: "none",
                }}
                onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                  console.error("Image failed to load:", e.currentTarget.src)
                  e.currentTarget.src = "/place-value-comparison.png"
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
