"use client"

import React, { useState, useEffect } from "react"
import { ChevronDown, ChevronUp, Code } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { getSlideTitle, getVocabularyTerms, loadSlideData, getSlide, getSlideImageUrl } from "@/services/slideService"
import { useLessonContext } from "../lesson-context"
import HtmlContent from "../HtmlContent"
import HtmlEditorModal from "../HtmlEditorModal"
import HtmlContentWrapper from "../HtmlContentWrapper"
import { getLessonNumberFromStorage, getDocumentIdFromStorage } from "@/utils/lessonUtils"
import { isSuperAdmin } from "@/utils/adminUtils"
import { useSession } from "next-auth/react"
import { saveHtmlAndRefresh } from "@/utils/forceRefresh"

interface VocabularySlideProps {
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
  setRevealedItems?: React.Dispatch<React.SetStateAction<number[]>>
  slideNumber: number
}

export function VocabularySlide({
  revealedItems = [],
  registerRevealableItems = () => {},
  setRevealedItems = () => {},
  slideNumber
}: VocabularySlideProps) {
  const { data: session } = useSession()

  // Get lesson context for current unit, lesson, grade
  const { unitNumber, lessonNumber, gradeLevel, lang, curriculum } = useLessonContext();

  const [slideData, setSlideData] = useState<any>(null);

  // Add state for HTML editor modal
  const [isHtmlEditorOpen, setIsHtmlEditorOpen] = useState(false);
  const [currentHtml, setCurrentHtml] = useState("");

  // Load slide data
  useEffect(() => {
    const fetchData = async () => {
      try {
        const currentSlideData = await getSlide(slideNumber);
        setSlideData(currentSlideData);
      } catch (error) {
        console.error("Error loading slide data:", error);
      }
    };

    fetchData();
  }, [slideNumber]);

  // Register revealable items (one for each vocabulary term)
  useEffect(() => {
    if (slideData) {
      const terms = getVocabularyTerms(slideData);
      registerRevealableItems(terms.length);
    }
  }, [registerRevealableItems, slideData]);

  // If slide data is not loaded yet, return loading state
  if (!slideData) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <div className="text-center text-white">
          <p>Loading vocabulary terms...</p>
        </div>
      </div>
    );
  }

  // Get the vocabulary terms from the slide data
  const vocabularyTerms = getVocabularyTerms(slideData);

  // Get the slide title from the slide data
  const title = getSlideTitle(slideData);

  // Get the image URL from the slide data
  const imageUrl = getSlideImageUrl(slideData);

  // Function to refresh slide data after save
  const refreshSlideData = async () => {
    console.log('VocabularySlide: *** REFRESHING SLIDE DATA ***');

    // Update URL with current slide before reload
    const url = new URL(window.location.href);
    url.searchParams.set('unit', unitNumber);
    url.searchParams.set('lesson', lessonNumber);
    url.searchParams.set('grade', gradeLevel);
    url.searchParams.set('slide', slideNumber.toString());
    if (lang) {
      url.searchParams.set('lang', lang);
    }

    // Update URL and reload
    window.history.replaceState({}, '', url.toString());
    window.location.reload();
  };

  // Function to open HTML editor
  const openHtmlEditor = () => {
    setCurrentHtml(slideData?.html_css_description_of_image || "");
    setIsHtmlEditorOpen(true);
  };

  // Function to save HTML content
  const saveHtmlContent = async (html: string) => {
    try {
      console.log('Saving HTML content for slide:', {
        unitNumber,
        lessonNumber,
        gradeLevel,
        slideNumber,
        curriculum,
        lang,
        htmlLength: html?.length
      });

      const result = await saveHtmlAndRefresh(
        slideNumber,
        unitNumber,
        lessonNumber,
        gradeLevel,
        lang,
        html,
        curriculum
      );

      if (!result.success) {
        throw new Error(result.message);
      }

      console.log('HTML content updated successfully');
    } catch (error) {
      console.error('Error updating HTML content:', error);
      alert('Failed to update HTML content. Please try again.');
    }
  };

  return (
    <div className="rounded-xl overflow-hidden shadow-lg blue-gradient">
      {/* HTML Editor Modal */}
      <HtmlEditorModal
        isOpen={isHtmlEditorOpen}
        onClose={() => setIsHtmlEditorOpen(false)}
        initialHtml={currentHtml}
        onSave={saveHtmlContent}
        onSaveComplete={refreshSlideData}
        slideNumber={slideNumber}
        documentId={getDocumentIdFromStorage()}
        unitNumber={unitNumber}
        lessonNumber={lessonNumber}
        gradeLevel={gradeLevel}
        curriculum={curriculum}
        lang={lang}
        session={session}
      />

      <div className="text-white">
        <h2 className="slide-title text-3xl mb-6 text-center">{title}</h2>

        <div className="concept-slide">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            {vocabularyTerms.map((item, index) => (
              <div
                key={item.term}
                className={`rounded-lg ${revealedItems.includes(index) ? "bg-white/20" : "bg-white/10"} p-4 backdrop-blur-sm cursor-pointer`}
                onClick={() => {
                  // Toggle this item in the revealedItems array
                  if (revealedItems.includes(index)) {
                    setRevealedItems((prev) => prev.filter((i) => i !== index))
                  } else {
                    setRevealedItems((prev) => [...prev, index])
                  }
                }}
              >
                <div className="flex items-center gap-4">
                  <div className="concept-number bg-[#2B6DFE] text-white">{index + 1}</div>
                  <h3 className="mb-3 text-4xl font-montserrat font-extrabold text-white">{item.term}</h3>
                </div>

                <AnimatePresence>
                  {revealedItems.includes(index) && (
                    <motion.div
                      initial={{ height: 0, opacity: 0, y: -10 }}
                      animate={{ height: "auto", opacity: 1, y: 0 }}
                      exit={{ height: 0, opacity: 0, y: -10 }}
                      transition={{
                        duration: 0.3,
                        ease: "easeInOut",
                      }}
                      className="overflow-hidden"
                    >
                      <p className="mt-2 text-3xl" dangerouslySetInnerHTML={{ __html: item.definition }} />
                    </motion.div>
                  )}
                </AnimatePresence>

                <div className="mt-2 text-center">
                  {revealedItems.includes(index) ? (
                    <ChevronUp size={16} className="inline-block" />
                  ) : (
                    <ChevronDown size={16} className="inline-block" />
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* SWe no need show this in the slide view  */}
          {/* {slideData.manipulative_used && (
            <div className="mt-8 p-4 bg-white/10 rounded-lg w-full h-full">
              <h3 className="text-xl font-semibold mb-2">Manipulative Used</h3>
              <p className="text-lg">{slideData.manipulative_used}</p>
            </div>
          )} */}

          {/* We no need show this in the slide view  */}
          {/* {slideData.teacher_tips && (
            <div className="mt-8 p-4 bg-white/10 rounded-lg w-full h-full">
              <h3 className="text-xl font-semibold mb-2">Teacher Tips</h3>
              {slideData.teacher_tips.general_tip && (
                <div className="mb-4">
                  <h4 className="text-lg font-medium text-[#fadb9a]">General Tip:</h4>
                  <div className="mt-1 text-sm" dangerouslySetInnerHTML={{ __html: slideData.teacher_tips.general_tip }} />
                </div>
              )}
              {slideData.teacher_tips.misconception_tip && (
                <div>
                  <h4 className="text-lg font-medium text-[#fadb9a]">Common Misconceptions:</h4>
                  <div className="mt-1 text-sm" dangerouslySetInnerHTML={{ __html: slideData.teacher_tips.misconception_tip }} />
                </div>
              )}
            </div>
          )} */}

          {/* Add image section */}
          <div className="illustration-box mt-8" style={{ minHeight: "400px" }}>
            <div className="slide-image-container">
              <div className="w-full h-full flex justify-center items-center overflow-hidden">
                {slideData?.html_css_description_of_image ? (
                  <div className="w-full h-full flex justify-center items-center">
                    {/* Check if the HTML content is a full HTML document */}
                    {slideData.html_css_description_of_image.includes('<!DOCTYPE html>') ||
                     slideData.html_css_description_of_image.includes('<html') ? (
                      <div className="w-full h-full relative">
                        {/* Edit HTML button */}
                        {isSuperAdmin(session) && (
                          <button
                            onClick={openHtmlEditor}
                            className="absolute top-2 right-2 p-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors z-10"
                            title="Edit HTML"
                          >
                            <Code size={16} />
                          </button>
                        )}
                        <div className="w-full h-full">
                          <div
                            className="generated-html-container w-full h-full"
                            style={{ width: "100%", height: "100%" }}
                          >
                            <style jsx global>{`
                              .generated-html-container {
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                width: 100%;
                                height: 100%;
                                flex: 1;
                              }
                              .generated-html-container iframe {
                                border: none;
                                width: 100% !important;
                                height: 100% !important;
                                background: white;
                              }
                              .iframe-container {
                                width: 100% !important;
                                height: 100% !important;
                                position: relative !important;
                                overflow: visible !important;
                              }
                              .html-fragment-container {
                                width: 100% !important;
                                height: 100% !important;
                              }
                            `}</style>
                            <HtmlContentWrapper
                              key={`html-vocabulary-${slideData?.html_css_description_of_image?.length || 0}-${slideData?._timestamp || Date.now()}`}
                              html={slideData.html_css_description_of_image}
                              className="w-full"
                              onEditClick={openHtmlEditor}
                              slideNumber={slideNumber}
                            />
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center w-full h-full flex flex-col justify-center items-center">
                        <div className="text-lg font-medium text-white mb-2">Image Not Available</div>
                        <div className="text-sm text-white/70 mb-4">
                          The image for this slide has not been added yet.
                        </div>
                        <div className="mt-2 p-3 bg-white/5 rounded-md w-full">
                          <div className="text-sm text-white/90 font-medium mb-1">Image Description:</div>
                          <div className="text-sm text-white/80">
                            {slideData.html_css_description_of_image}
                          </div>
                        </div>
                        <div className="mt-4">
                          <button
                            onClick={openHtmlEditor}
                            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors mx-auto"
                          >
                            <Code size={16} />
                            Create HTML Content
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="w-full h-full flex flex-col items-center justify-center text-center">
                    <div className="text-lg font-medium text-white mb-2">Image Not Available</div>
                    <div className="text-sm text-white/70 mb-4">
                      The image for this slide has not been added yet.
                    </div>
                    <button
                      onClick={openHtmlEditor}
                      className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                    >
                      <Code size={16} />
                      Add HTML Content
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
