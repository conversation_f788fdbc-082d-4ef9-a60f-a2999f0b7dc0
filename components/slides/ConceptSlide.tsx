"use client"

import React, { useState, useEffect } from "react"
import { Eye, Code } from "lucide-react"
import { getSlideTitle, getSlidePoints, getSlideImageUrl, getSlide, clearSlideCache } from "@/services/slideService"
import { RevealPoint } from "./RevealPoint"
import { usePathname } from "next/navigation"

import { SlidePoint } from "@/types/slideTypes"
import HtmlEditorModal from "../HtmlEditorModal"
import HtmlContentWrapper from "../HtmlContentWrapper"
import { getLessonNumberFromStorage, getDocumentIdFromStorage } from "@/utils/lessonUtils"
import { isSuperAdmin } from "@/utils/adminUtils"
import { useLessonContext } from "../lesson-context"
import { useSession } from "next-auth/react"
import { saveHtmlAndRefresh } from "@/utils/forceRefresh"


// Add a custom style for the illustration container
const illustrationStyle = {
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  width: "100%",
  height: "100%",
  overflow: "hidden",
  borderRadius: "0.5rem",
  backgroundColor: "transparent",
}

interface ConceptSlideProps {
  title?: string
  points?: SlidePoint[]
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
  slideNumber?: number
  slideData?: any
}

export function ConceptSlide({
  title,
  points,
  revealedItems = [],
  registerRevealableItems = () => { },
  slideNumber = 0,
  slideData,
}: ConceptSlideProps) {
  const { data: session } = useSession()
  const [loadedSlideData, setLoadedSlideData] = useState<any>(slideData);

  // Clear slide 4 cache on component mount to prevent blinking
  useEffect(() => {
    if (slideNumber === 4) {
      console.log('Clearing cache for slide 4 to prevent blinking');
      clearSlideCache(4);
    }
  }, []);

  // Load slide data if not provided
  useEffect(() => {
    // Only fetch new data when slideNumber changes and we don't have data
    if (slideNumber > 0 && (!slideData || Object.keys(slideData).length === 0)) {
      const fetchData = async () => {
        try {
          console.log(`ConceptSlide: Fetching data for slide ${slideNumber}`);
          const currentSlideData = await getSlide(slideNumber);
          setLoadedSlideData(currentSlideData);
        } catch (error) {
          console.error("Error loading slide data:", error);
        }
      };

      fetchData();
    } else if (slideData) {
      // If we already have slide data, use it
      setLoadedSlideData(slideData);
    }
  }, [slideNumber, slideData]); // Only depend on slideNumber to ensure it always updates

  // Use the loaded slide data or the provided slide data
  const effectiveSlideData = loadedSlideData || slideData;

  // Use points from props if provided, otherwise get from slideData
  const slidePoints = points || (effectiveSlideData ? getSlidePoints({ ...effectiveSlideData, slideNumber }) : []);

  // Register revealable items (one for each point)
  useEffect(() => {
    registerRevealableItems(slidePoints.length)
  }, [registerRevealableItems, slidePoints.length])

  // Add state for showing all points
  const [showAll, setShowAll] = useState(false)

  // Add state for revealed answers
  const [revealedAnswers, setRevealedAnswers] = useState<number[]>([])

  // Add state for HTML editor modal
  const [isHtmlEditorOpen, setIsHtmlEditorOpen] = useState(false)
  const [currentHtml, setCurrentHtml] = useState("")

  // Reset revealed answers and showAll when slide number changes
  useEffect(() => {
    console.log('ConceptSlide: Slide number changed to', slideNumber, '- resetting revealed answers and showAll');
    setRevealedAnswers([]);
    setShowAll(false);
  }, [slideNumber]);

  // Handle revealed answers based on revealed items
  useEffect(() => {
    console.log('ConceptSlide: revealedItems changed:', revealedItems);
    console.log('ConceptSlide: slidePoints:', slidePoints);

    // For Talk slides, we want to show answers only when their specific index is revealed
    // For example: if revealedItems = [0, 1], show question 0 and answer for question 0
    const newRevealedAnswers: number[] = [];

    revealedItems.forEach(itemIndex => {
      const point = slidePoints[itemIndex];
      console.log(`ConceptSlide: Checking revealed item ${itemIndex}:`, point);

      if (point && point.isAnswer && point.questionIndex !== undefined) {
        // This is an answer that has been revealed via next-item button
        console.log(`ConceptSlide: Answer revealed at index ${itemIndex}, questionIndex: ${point.questionIndex}`);
        if (!newRevealedAnswers.includes(point.questionIndex)) {
          newRevealedAnswers.push(point.questionIndex);
        }
      }
    });

    // Only update if there are changes
    if (JSON.stringify(newRevealedAnswers.sort()) !== JSON.stringify(revealedAnswers.sort())) {
      console.log('ConceptSlide: Updating revealedAnswers to:', newRevealedAnswers);
      setRevealedAnswers(newRevealedAnswers);
    }
  }, [revealedItems, slidePoints, revealedAnswers]);

  // Function to handle question click
  const handleQuestionClick = (questionIndex: number) => {
    console.log('Question clicked:', questionIndex, 'Current revealed answers:', revealedAnswers);

    if (revealedAnswers.includes(questionIndex)) {
      // If the answer is already revealed, hide it
      const newRevealedAnswers = revealedAnswers.filter(index => index !== questionIndex);
      setRevealedAnswers(newRevealedAnswers);
      console.log('Answer hidden, new revealed answers:', newRevealedAnswers);
    } else {
      // Otherwise, reveal it
      const newRevealedAnswers = [...revealedAnswers, questionIndex];
      setRevealedAnswers(newRevealedAnswers);
      console.log('Answer revealed, new revealed answers:', newRevealedAnswers);
    }
  }

  // Function to refresh slide data after save
  const refreshSlideData = async () => {
    console.log('ConceptSlide: *** REFRESHING SLIDE DATA ***');

    // Update URL with current slide before reload
    const url = new URL(window.location.href);
    url.searchParams.set('unit', unitNumber);
    url.searchParams.set('lesson', lessonNumber);
    url.searchParams.set('grade', gradeLevel);
    url.searchParams.set('slide', slideNumber.toString());
    if (lang) {
      url.searchParams.set('lang', lang);
    }

    // Update URL and reload
    window.history.replaceState({}, '', url.toString());
    window.location.reload();
  };

  // Function to open HTML editor
  const openHtmlEditor = () => {
    setCurrentHtml(effectiveSlideData?.html_css_description_of_image || "")
    setIsHtmlEditorOpen(true)
  }

  // Function to regenerate HTML
  const regenerateHtml = async () => {
    if (!effectiveSlideData?.html_css_description_of_image) {
      alert('No description available for HTML generation')
      return
    }

    if (!confirm('Are you sure you want to regenerate the HTML? This will replace the current content.')) {
      return
    }

    try {
      console.log('🔄 Regenerating HTML for slide', slideNumber)

      const response = await fetch('/api/generate-html', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: effectiveSlideData.html_css_description_of_image,
          slideNumber,
          unitNumber,
          lessonNumber,
          gradeLevel,
          curriculum,
          lang
        }),
      })

      const data = await response.json()

      if (data.success) {
        console.log('✅ HTML regenerated successfully')

        // Save the new HTML
        await saveHtmlContent(data.html)

        alert('✅ HTML regenerated successfully!')
      } else {
        console.error('❌ HTML regeneration failed:', data.error)
        alert(`❌ HTML regeneration failed: ${data.error}`)
      }
    } catch (error) {
      console.error('❌ Error regenerating HTML:', error)
      alert('❌ Failed to regenerate HTML. Please try again.')
    }
  }
  const { unitNumber, lessonNumber, gradeLevel, lang, curriculum } = useLessonContext();

  // Function to save HTML content
  const saveHtmlContent = async (html: string) => {
    try {
      console.log('Saving HTML content for slide:', {
        unitNumber,
        lessonNumber,
        gradeLevel,
        slideNumber,
        curriculum,
        lang,
        htmlLength: html?.length
      });

      const result = await saveHtmlAndRefresh(
        slideNumber,
        unitNumber,
        lessonNumber,
        gradeLevel,
        lang,
        html,
        curriculum
      );

      if (!result.success) {
        throw new Error(result.message);
      }

      console.log('HTML content updated successfully');
    } catch (error) {
      console.error('Error updating HTML content:', error);
      alert('Failed to update HTML content. Please try again.');
    }
  };

  // Check if we're in the presenter view
  const pathname = usePathname()
  const isPresenterView = pathname?.includes('/presenter')

  // Get the slide title from props or slideData
  const slideTitle = title || (effectiveSlideData ? getSlideTitle(effectiveSlideData) : "Concept");

  // Get the image URL from slideData if available
  const imageUrl = effectiveSlideData ? getSlideImageUrl(effectiveSlideData) : undefined;

  // Check if this slide should have swapped layout
  const swapLayout = [5, 7, 9, 11, 15, 17, 19, 23].includes(slideNumber)

  return (
    <div className="text-white">
      <h2 className="slide-title">{slideTitle}</h2>

      {/* HTML Editor Modal */}
      <HtmlEditorModal
        isOpen={isHtmlEditorOpen}
        onClose={() => setIsHtmlEditorOpen(false)}
        initialHtml={currentHtml}
        onSave={saveHtmlContent}
        onSaveComplete={refreshSlideData}
        slideNumber={slideNumber}
        documentId={getDocumentIdFromStorage()}
        unitNumber={unitNumber}
        lessonNumber={lessonNumber}
        gradeLevel={gradeLevel}
        curriculum={curriculum}
        lang={lang}
        session={session}
      />

      <div className="concept-slide h-full">
        {swapLayout ? (
          <>
            <div className="illustration-box" style={{ minHeight: "400px", ...illustrationStyle }}>
              {[4, 5, 6].includes(slideNumber) && imageUrl ? (
                <div className="slide-image-container">
                  <div className="w-full h-full flex justify-center items-center overflow-hidden">
                    <img
                      src={imageUrl}
                      alt={`Illustration for ${slideTitle}`}
                      style={{
                        maxWidth: "100%",
                        maxHeight: "100%",
                        objectFit: "contain",
                        backgroundColor: "white",
                        border: "none",
                        borderRadius: "0.5rem",
                      }}
                      onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                        console.error("Image failed to load:", e.currentTarget.src)
                        e.currentTarget.src = "/place-value-comparison.png"
                      }}
                    />
                  </div>
                </div>

              ) : effectiveSlideData?.html_css_description_of_image ? (
                <div className="p-4 bg-white/10 h-full rounded-lg w-full">
                  {effectiveSlideData.html_css_description_of_image.includes('<!DOCTYPE html>') ||
                    effectiveSlideData.html_css_description_of_image.includes('<html') ? (
                    <div className="w-full h-full flex justify-center items-center">
                      <div
                        className="generated-html-container w-full overflow-hidden"
                        style={{ height: "100%", maxHeight: "100%" }}
                      >
                        <HtmlContentWrapper
                          key={`html-${slideNumber}-${effectiveSlideData?.html_css_description_of_image?.length || 0}`}
                          html={effectiveSlideData.html_css_description_of_image}
                          className="w-full"
                          onEditClick={openHtmlEditor}
                          onRegenerateClick={regenerateHtml}
                          slideNumber={slideNumber}
                          session={session}
                        />
                      </div>
                    </div>
                  ) : (
                    <div className="text-center">
                      <div className="text-lg font-medium text-white mb-2">Image Not Available</div>
                      <div className="text-sm text-white/70 mb-4">
                        The image for this slide has not been added yet.
                      </div>
                      <div className="mt-2 p-3 bg-white/5 rounded-md">
                        <div className="text-sm text-white/90 font-medium mb-1">Image Description:</div>
                        <div className="text-sm text-white/80">
                          {effectiveSlideData.html_css_description_of_image}
                        </div>
                      </div>
                      <div className="mt-4">
                        {isSuperAdmin(session) && (
                          <button
                            onClick={openHtmlEditor}
                            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors mx-auto"
                          >
                            <Code size={16} />
                            Create HTML Content
                          </button>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center text-center p-4 bg-white/10 rounded-lg w-full h-full">
                  <div className="text-lg font-medium text-white mb-2">Image Not Available</div>
                  <div className="text-sm text-white/70 mb-4">
                    The image for this slide has not been added yet.
                  </div>
                  {isSuperAdmin(session) && (
                    <button
                      onClick={openHtmlEditor}
                      className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                    >
                      <Code size={16} />
                      Add HTML Content
                    </button>
                  )}
                </div>
              )}
            </div>
            <div className="space-y-6 rounded-xl p-6 relative bg-white/10" style={{ minHeight: "400px" }}>
              {/* Add show all button */}
              <div className="flex justify-end mb-4">
                <button onClick={() => setShowAll(!showAll)} className="flex items-center gap-2 text-sm cursor-pointer">
                  <span>{showAll ? "Hide All" : "Show All"}</span>
                  <Eye size={18} className={showAll ? "text-[#fadb9a]" : ""} />
                </button>
              </div>

              {slidePoints.map((point, index) => (
                <RevealPoint
                  key={index}
                  number={
                    point.isQuestion
                      ? "Q"
                      : point.isAnswer
                        ? "A"
                        : index + 1
                  }
                  title={point.title}
                  content={point.content}
                  forceReveal={revealedItems.includes(index) || showAll}
                  isQuestion={point.isQuestion}
                  isAnswer={false} //need that to open all if only answer change to point.isAnswer
                  hasAnswer={point.hasAnswer}
                  questionIndex={point.questionIndex}
                  onQuestionClick={handleQuestionClick}
                  revealedAnswers={revealedAnswers}
                />
              ))}
            </div>
          </>
        ) : (
          <>
            <div className="space-y-6 rounded-xl p-6 relative bg-white/10" style={{ minHeight: "400px" }}>
              {/* Add show all button */}
              <div className="flex justify-end mb-4">
                <button onClick={() => setShowAll(!showAll)} className="flex items-center gap-2 text-sm cursor-pointer">
                  <span>{showAll ? "Hide All" : "Show All"}</span>
                  <Eye size={18} className={showAll ? "text-[#fadb9a]" : ""} />
                </button>
              </div>

              {slidePoints.map((point, index) => (
                <RevealPoint
                  key={index}
                  number={
                    point.isQuestion
                      ? "Q"
                      : point.isAnswer
                        ? "A"
                        : index + 1
                  }
                  title={point.title}
                  content={point.content}
                  forceReveal={revealedItems.includes(index) || showAll}
                  isQuestion={point.isQuestion}
                  isAnswer={false} //need that to open all if only answer change to point.isAnswer
                  hasAnswer={point.hasAnswer}
                  questionIndex={point.questionIndex}
                  onQuestionClick={handleQuestionClick}
                  revealedAnswers={revealedAnswers}
                />
              ))}
            </div>
            <div className="illustration-box" style={{ minHeight: "400px", ...illustrationStyle }}>
              <div className="w-full h-full flex justify-center items-center">
                {[4, 5, 6].includes(slideNumber) && imageUrl ? (
                  <div className="slide-image-container">
                    <div className="w-full h-full flex justify-center items-center overflow-hidden">
                      <img
                        src={imageUrl}
                        alt={`Illustration for ${slideTitle}`}
                        style={{
                          maxWidth: "100%",
                          maxHeight: "100%",
                          objectFit: "contain",
                          backgroundColor: "white",
                          border: "none",
                          borderRadius: "0.5rem",
                        }}
                        onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                          console.error("Image failed to load:", e.currentTarget.src)
                          e.currentTarget.src = "/place-value-comparison.png"
                        }}
                      />
                    </div>
                  </div>
                ) : imageUrl ? (
                  <div className="slide-image-container">
                    <div className="w-full h-full flex justify-center items-center overflow-hidden">
                      <img
                        src={imageUrl}
                        alt={`Illustration for ${slideTitle}`}
                        style={{
                          maxWidth: "100%",
                          maxHeight: "100%",
                          objectFit: "contain",
                          backgroundColor: "transparent",
                          border: "none",
                        }}
                        onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                          console.error("Image failed to load:", e.currentTarget.src)
                          e.currentTarget.src = "/place-value-comparison.png"
                        }}
                      />
                    </div>
                  </div>
                ) : effectiveSlideData?.html_css_description_of_image ? (
                  <div className="p-4 bg-white/10 rounded-lg w-full h-full">
                    {effectiveSlideData.html_css_description_of_image.includes('<!DOCTYPE html>') ||
                      effectiveSlideData.html_css_description_of_image.includes('<html') ? (
                      <div className="w-full h-full flex justify-center items-center">
                        <div
                          className="generated-html-container w-full overflow-hidden"
                          style={{ height: "100%", maxHeight: "100%" }}
                        >
                          <HtmlContentWrapper
                            key={`html-${slideNumber}-${effectiveSlideData?.html_css_description_of_image?.length || 0}`}
                            html={effectiveSlideData.html_css_description_of_image}
                            className="w-full"
                            onEditClick={openHtmlEditor}
                            onRegenerateClick={regenerateHtml}
                            slideNumber={slideNumber}
                            session={session}
                          />
                        </div>
                      </div>
                    ) : (
                      <div className="text-center">
                        <div className="text-lg font-medium text-white mb-2">Image Not Available</div>
                        <div className="text-sm text-white/70 mb-4">
                          The image for this slide has not been added yet.
                        </div>
                        <div className="mt-2 p-3 bg-white/5 rounded-md">
                          <div className="text-sm text-white/90 font-medium mb-1">Image Description:</div>
                          <div className="text-sm text-white/80">
                            {effectiveSlideData.html_css_description_of_image}
                          </div>
                        </div>
                        <div className="mt-4">
                          {isSuperAdmin(session) && (
                            <button
                              onClick={openHtmlEditor}
                              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors mx-auto"
                            >
                              <Code size={16} />
                              Create HTML Content
                            </button>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center text-center p-4 bg-white/10 rounded-lg w-full h-full">
                    <div className="text-lg font-medium text-white mb-2">Image Not Available</div>
                    <div className="text-sm text-white/70 mb-4">
                      The image for this slide has not been added yet.
                    </div>
                    <button
                      onClick={openHtmlEditor}
                      className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                    >
                      <Code size={16} />
                      Add HTML Content
                    </button>
                  </div>
                )}
                {/* No default text for slides without images */}
              </div>
            </div>
          </>
        )}
      </div>

      {/* Teacher Tips - Only shown in presenter view */}
      {/* {isPresenterView && (
        <div className="mt-8 p-4 bg-white/10 rounded-lg w-full h-full">
          <h3 className="text-xl font-semibold mb-2">Teacher Tips</h3>
          <div className="mb-4">
            <h4 className="text-lg font-medium text-[#fadb9a]">General Tip:</h4>
            <div className="mt-1 text-sm">
              <HtmlContent
                html={effectiveSlideData?.teacher_tips?.general_tip ||
                "Use visual aids like place value charts or base-10 blocks to help students understand the concept."}
              />
            </div>
          </div>
          <div>
            <h4 className="text-lg font-medium text-[#fadb9a]">Common Misconceptions:</h4>
            <div className="mt-1 text-sm">
              <HtmlContent
                html={effectiveSlideData?.teacher_tips?.misconception_tip ||
                "Students might confuse the value of a digit with the digit itself. Emphasize that the value depends on the position."}
              />
            </div>
          </div>
        </div>
      )} */}
    </div>
  )
}
