"use client"

import React from "react"
import { ChevronDown, ChevronUp } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"

interface ActivityCardProps {
  number: number
  question: string
  answer: string
  isVisible: boolean
  showAnswer: boolean
  onToggleVisibility: () => void
  onToggleAnswer: () => void
}

export function ActivityCard({
  number,
  question,
  answer,
  isVisible,
  showAnswer,
  onToggleVisibility,
  onToggleAnswer,
}: ActivityCardProps) {
  return (
    <div className="rounded-lg bg-white/10 overflow-hidden h-full flex flex-col">
      <div className="p-4 flex items-center justify-between gap-4 cursor-pointer" onClick={onToggleVisibility}>
        <div className="concept-number bg-[#2B6DFE] text-white">{number}</div>
        {isVisible ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
      </div>

      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ height: 0, opacity: 0, y: -10 }}
            animate={{ height: "auto", opacity: 1, y: 0 }}
            exit={{ height: 0, opacity: 0, y: -10 }}
            transition={{
              duration: 0.3,
              ease: "easeInOut",
              opacity: { duration: 0.2 },
            }}
            className="overflow-hidden flex flex-col flex-grow"
          >
            <div className="p-4 pt-0 flex flex-col flex-grow">
              <div className="text-3xl mb-4">{question}</div>

              <div className="mt-auto">
                <div
                  className="flex items-center justify-between p-3 bg-white/10 rounded-md cursor-pointer"
                  onClick={(e) => {
                    e.stopPropagation()
                    onToggleAnswer()
                  }}
                >
                  <span className="font-medium">Answer</span>
                  {showAnswer ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
                </div>

                <AnimatePresence>
                  {showAnswer && (
                    <motion.div
                      initial={{ height: 0, opacity: 0, y: -5 }}
                      animate={{ height: "auto", opacity: 1, y: 0 }}
                      exit={{ height: 0, opacity: 0, y: -5 }}
                      transition={{ duration: 0.25 }}
                      className="overflow-hidden"
                    >
                      <div className="mt-2 p-3 bg-[#fadb9a]/10 rounded-md text-3xl">{answer}</div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
