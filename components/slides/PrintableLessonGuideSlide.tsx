"use client"

import React, { useEffect, useState } from "react"
import { Download } from "lucide-react"
import { useSession } from "next-auth/react"
import { PdfUploader } from "../PdfUploader"

interface PrintableLessonGuideSlideProps {
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
  documentId?: string
  unitNumber?: string
  lessonNumber?: string
  gradeLevel?: string
}

export function PrintableLessonGuideSlide({
  revealedItems = [],
  registerRevealableItems = () => {},
  documentId,
  unitNumber,
  lessonNumber,
  gradeLevel,
}: PrintableLessonGuideSlideProps) {
  const { data: session } = useSession()
  const [currentPdfUrl, setCurrentPdfUrl] = useState<string>('')

  // Check if user is admin
  const isAdmin = (session?.user as any)?.role === 'admin'

  // Generate document ID if not provided
  const effectiveDocumentId = documentId || (unitNumber && lessonNumber && gradeLevel ?
    `${unitNumber}-${lessonNumber}-${gradeLevel}` : '')

  // Handle PDF upload success
  const handlePdfUploadSuccess = (url: string) => {
    setCurrentPdfUrl(url)
  }

  // Register revealable items (just one for the download button)
  useEffect(() => {
    registerRevealableItems(1)
  }, [registerRevealableItems])

  return (
    <div className="text-white">
      <h2 className="slide-title">Printable Lesson Guide</h2>
      <div className="flex flex-col items-center justify-center h-[500px] bg-white/10 rounded-xl p-8">
        <div className="text-center mb-8">
          <p className="text-3xl mb-4">Download the printable lesson guide for this lesson.</p>
          <p className="text-xl">
            This guide includes all the key concepts, vocabulary, and practice problems from this lesson.
          </p>
        </div>

        <a
          href={currentPdfUrl || "/printable-lesson-guide.pdf"}
          download
          target="_blank"
          rel="noopener noreferrer"
          className={`flex items-center gap-2 px-6 py-3 rounded-full ${
            revealedItems.includes(0) ? "bg-[#fadb9a] text-[#4169E1]" : "bg-white/20 text-white"
          } transition-colors duration-300`}
        >
          <Download size={24} />
          <span className="text-xl font-medium">Download Lesson Guide</span>
        </a>

        {/* Admin PDF Upload Section */}
        {isAdmin && effectiveDocumentId && (
          <div className="mt-6 max-w-md mx-auto">
            <PdfUploader
              documentId={effectiveDocumentId}
              slideType="lesson_guide"
              currentPdfUrl={currentPdfUrl}
              onUploadSuccess={handlePdfUploadSuccess}
            />
          </div>
        )}
      </div>
    </div>
  )
}
