"use client"

import React from 'react'
import { Check, AlertCircle, RefreshCw } from 'lucide-react'

interface UploadResult {
  status: 'created' | 'updated' | 'error'
  message: string
  id?: string
  slide?: any
}

interface UploadResultsProps {
  results: UploadResult[]
  isLoading: boolean
}

export default function UploadResults({ results, isLoading }: UploadResultsProps) {
  if (isLoading) {
    return (
      <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <div className="flex items-center justify-center space-x-3">
          <RefreshCw className="w-5 h-5 text-blue-500 animate-spin" />
          <span className="text-gray-700 dark:text-gray-300">Processing upload...</span>
        </div>
      </div>
    )
  }

  if (!results || results.length === 0) {
    return null
  }

  const successCount = results.filter(r => r.status === 'created' || r.status === 'updated').length
  const errorCount = results.filter(r => r.status === 'error').length
  const createdCount = results.filter(r => r.status === 'created').length
  const updatedCount = results.filter(r => r.status === 'updated').length

  return (
    <div className="mt-6">
      <div className="mb-4 flex flex-wrap gap-3">
        <div className="px-3 py-1 bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 rounded-full text-sm">
          {successCount} slides processed successfully
        </div>
        {createdCount > 0 && (
          <div className="px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 rounded-full text-sm">
            {createdCount} slides created
          </div>
        )}
        {updatedCount > 0 && (
          <div className="px-3 py-1 bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300 rounded-full text-sm">
            {updatedCount} slides updated
          </div>
        )}
        {errorCount > 0 && (
          <div className="px-3 py-1 bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300 rounded-full text-sm">
            {errorCount} errors
          </div>
        )}
      </div>

      <div className="space-y-3 max-h-96 overflow-y-auto">
        {results.map((result, index) => (
          <div
            key={index}
            className={`p-4 rounded-lg border ${
              result.status === 'created'
                ? 'border-green-200 bg-green-50 dark:border-green-900 dark:bg-green-900/10'
                : result.status === 'updated'
                ? 'border-yellow-200 bg-yellow-50 dark:border-yellow-900 dark:bg-yellow-900/10'
                : 'border-red-200 bg-red-50 dark:border-red-900 dark:bg-red-900/10'
            }`}
          >
            <div className="flex items-start">
              <div className="flex-shrink-0 mt-0.5">
                {result.status === 'created' || result.status === 'updated' ? (
                  <Check className={`w-5 h-5 ${
                    result.status === 'created' ? 'text-green-500' : 'text-yellow-500'
                  }`} />
                ) : (
                  <AlertCircle className="w-5 h-5 text-red-500" />
                )}
              </div>
              <div className="ml-3">
                <h3 className={`text-sm font-medium ${
                  result.status === 'created'
                    ? 'text-green-800 dark:text-green-300'
                    : result.status === 'updated'
                    ? 'text-yellow-800 dark:text-yellow-300'
                    : 'text-red-800 dark:text-red-300'
                }`}>
                  {result.message}
                </h3>
                {result.id && (
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    ID: {result.id}
                  </p>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
