"use client"

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import UserMenu from './user-menu'
import { Home, Pencil, Presentation, Upload, BarChart3 } from 'lucide-react'

export default function Navbar() {
  const pathname = usePathname()

  const navItems = [
    {
      name: 'Home',
      href: '/',
      icon: <Home className="h-5 w-5" />,
    },
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: <BarChart3 className="h-5 w-5" />,
    },
    // {
    //   name: 'Upload JSON',
    //   href: '/upload',
    //   icon: <Upload className="h-5 w-5" />,
    // }
  ]

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center">
        <div className="mr-4 flex">
          <Link href="/" className="flex items-center space-x-2">
            <img src="/images/embrs-logo.png" alt="EMBRS Logo" className="h-8 w-auto" />
            <span className="hidden font-bold sm:inline-block">
              Math Education
            </span>
          </Link>
        </div>
        <nav className="flex flex-1 items-center justify-between space-x-2 md:justify-end">
          <div className="flex-1 md:flex-initial">
            <div className="hidden items-center justify-between md:flex">
              {navItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    "flex h-9 items-center px-4 text-sm font-medium transition-colors hover:text-primary",
                    pathname === item.href
                      ? "text-foreground"
                      : "text-foreground/60"
                  )}
                >
                  {item.icon}
                  <span className="ml-2">{item.name}</span>
                </Link>
              ))}
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <UserMenu />
          </div>
        </nav>
      </div>
    </header>
  )
}
