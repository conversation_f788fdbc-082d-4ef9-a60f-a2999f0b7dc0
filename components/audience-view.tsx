"use client"

import { useEffect, useState } from "react"
import { usePresenter } from "./presenter-context"
import { useLessonContext } from "./lesson-context"
import { SlideContent } from "./slide-content"
import { ChevronLeft, ChevronRight } from "lucide-react"
import DrawingButton from "./drawing/drawing-button"
import HtmlFullscreenModal from "./HtmlFullscreenModal"

export function AudienceView() {
  const { currentSlide, revealedItems, setRevealedItems, setCurrentSlide, setIsAudienceView, isShown, setIsShown } = usePresenter()
  const { unitNumber, lessonNumber, gradeLevel, lang, totalSlides } = useLessonContext()
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [showFullscreenModal, setShowFullscreenModal] = useState(false) // Don't open by default
  const [slideData, setSlideData] = useState<any>(null)
  const [maxRevealableItems, setMaxRevealableItems] = useState(0)

  // Keyboard navigation handlers for audience
  const handleAudienceToggleShow = () => {
    setIsShown(!isShown)
  }

  const handleAudienceShowFirst = () => {
    if (maxRevealableItems > 0) {
      setRevealedItems([0])
      setIsShown(true)
    }
  }

  // Set audience view mode on mount
  useEffect(() => {
    setIsAudienceView(true)
    return () => setIsAudienceView(false)
  }, [setIsAudienceView])

  // Add keyboard event listeners for audience view
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Skip if user is typing in an input field
      const target = event.target as HTMLElement
      if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable) {
        return
      }

      switch (event.key) {
        case 'ArrowLeft':
          event.preventDefault()
          if (currentSlide > 1) {
            setCurrentSlide(currentSlide - 1)
          }
          break
        case 'ArrowRight':
          event.preventDefault()
          if (currentSlide < totalSlides) {
            setCurrentSlide(currentSlide + 1)
          }
          break
        case 'ArrowUp':
          event.preventDefault()
          if (revealedItems.length < maxRevealableItems) {
            setRevealedItems([...revealedItems, revealedItems.length])
          }
          break
        case 'ArrowDown':
          event.preventDefault()
          if (revealedItems.length > 0) {
            setRevealedItems(revealedItems.slice(0, -1))
          }
          break
        case ' ':
        case 'Spacebar':
          event.preventDefault()
          if (revealedItems.length < maxRevealableItems) {
            setRevealedItems([...revealedItems, revealedItems.length])
          }
          break
        case 'h':
        case 'H':
          event.preventDefault()
          handleAudienceToggleShow()
          break
        case 's':
        case 'S':
          event.preventDefault()
          handleAudienceShowFirst()
          break
        case '?':
          event.preventDefault()
          // Could show help modal here if needed
          break
        case 'Escape':
          event.preventDefault()
          // Could close modals here if needed
          break
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => {
      window.removeEventListener('keydown', handleKeyDown)
    }
  }, [currentSlide, totalSlides, revealedItems, maxRevealableItems, isShown, setCurrentSlide, setRevealedItems, setIsShown])

  // Auto-open modal when there are revealed items (regardless of popup visibility)
  // useEffect(() => {
  //   if (revealedItems.length > 0) {
  //     setShowFullscreenModal(true)
  //   } else {
  //     setShowFullscreenModal(false)
  //   }
  // }, [revealedItems.length])

  // Load slide data for fullscreen modal
  useEffect(() => {
    const loadSlideData = async () => {
      console.log('🔄 Loading slide data for:', { unitNumber, lessonNumber, gradeLevel, currentSlide, lang })

      if (!unitNumber || !lessonNumber || !gradeLevel) {
        console.log('❌ Missing required params:', { unitNumber, lessonNumber, gradeLevel })
        return
      }

      try {
        const url = `/api/json-documents?unit_number=${unitNumber}&lesson_number=${lessonNumber}&grade_level=${encodeURIComponent(gradeLevel)}${lang ? `&lang=${lang}` : ''}`
        console.log('📡 Fetching from URL:', url)

        const response = await fetch(url)
        console.log('📡 Response status:', response.status, response.statusText)

        if (response.ok) {
          const data = await response.json()
          console.log('✅ Loaded lesson data:', data)

          // The API returns slide data as properties like "Slide 1: Quick_Review", not as an array
          const slideKey = `Slide ${currentSlide}: ${data[`Slide ${currentSlide}: Quick_Review`] ? 'Quick_Review' :
                           data[`Slide ${currentSlide}: Learning_Goals`] ? 'Learning_Goals' :
                           data[`Slide ${currentSlide}: Vocabulary`] ? 'Vocabulary' :
                           Object.keys(data).find(key => key.startsWith(`Slide ${currentSlide}:`))?.split(': ')[1] || 'Unknown'}`

          console.log('🎯 Looking for slide key:', slideKey)
          console.log('🎯 Available slide keys:', Object.keys(data).filter(key => key.startsWith('Slide')))

          // Find the slide data by looking for the slide key
          let currentSlideData = null
          const slideKeyPattern = `Slide ${currentSlide}:`
          const foundKey = Object.keys(data).find(key => key.startsWith(slideKeyPattern))

          if (foundKey) {
            currentSlideData = data[foundKey]
            console.log('🎯 Found slide with key:', foundKey)
            console.log('🎯 Current slide data:', currentSlideData)
            console.log('🎯 HTML content exists:', !!currentSlideData?.html_css_description_of_image)
            console.log('🎯 HTML content length:', currentSlideData?.html_css_description_of_image?.length || 0)

            setSlideData(currentSlideData)

            // Open modal only if HTML content exists
            if (currentSlideData?.html_css_description_of_image &&
                currentSlideData.html_css_description_of_image.trim() !== '') {
              console.log('✅ HTML content found, opening modal')
              setShowFullscreenModal(true)
            } else {
              console.log('❌ No HTML content, keeping modal closed')
              setShowFullscreenModal(false)
            }
          } else {
            console.log('❌ No slide data found for slide', currentSlide)
            console.log('❌ Available keys:', Object.keys(data).filter(key => key.startsWith('Slide')))
            setSlideData(null)
            setShowFullscreenModal(false)
          }
        } else {
          console.error('❌ Response not OK:', response.status, response.statusText)
        }
      } catch (error) {
        console.error('❌ Error loading slide data:', error)
      }
    }

    loadSlideData()
  }, [currentSlide, unitNumber, lessonNumber, gradeLevel, lang])

  // Handle fullscreen on load and listen for close messages
  useEffect(() => {
    const enterFullscreen = async () => {
      try {
        // await document.documentElement.requestFullscreen()
        setIsFullscreen(true)
        console.log("Entered fullscreen mode")
      } catch (err) {
        console.error("Error attempting to enable full-screen mode:", err)
      }
    }

    // Slight delay to ensure the DOM is ready
    const timer = setTimeout(() => {
      enterFullscreen()
    }, 1000)

    // Debug logging
    console.log("Audience view mounted, current slide:", currentSlide)
    console.log("Revealed items:", revealedItems)

    // Listen for localStorage changes (first approach)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'closeAudienceWindow') {
        console.log("Received close command via localStorage")
        window.close()
      }
    }

    // Listen for broadcast messages (second approach)
    const bc = new BroadcastChannel('presenter-audience-channel')
    bc.onmessage = (event) => {
      if (event.data && event.data.action === 'close-audience') {
        console.log("Received close command via BroadcastChannel")
        window.close()
      }
    }

    // Register this window with the opener if possible
    if (window.opener) {
      try {
        // Try to add this window to the opener's list of opened windows
        if (!(window.opener as any)._openedWindows) {
          (window.opener as any)._openedWindows = []
        }
        (window.opener as any)._openedWindows.push(window)
      } catch (e) {
        console.error("Error registering window with opener:", e)
      }
    }

    window.addEventListener('storage', handleStorageChange)

    return () => {
      clearTimeout(timer)
      window.removeEventListener('storage', handleStorageChange)
      bc.close()

      if (document.fullscreenElement) {
        document.exitFullscreen().catch((err) => {
          console.error("Error attempting to exit full-screen mode:", err)
        })
      }
    }
  }, [currentSlide, revealedItems])

  // Define registerRevealableItems function locally
  const registerRevealableItems = (count: number) => {
    console.log(`Audience: registered ${count} revealable items for slide ${currentSlide}`)
    setMaxRevealableItems(count)
  }

  // Navigation functions (using presenter context for proper sync)
  const handlePrevSlide = () => {
    if (currentSlide > 1) {
      const newSlide = currentSlide - 1
      setCurrentSlide(newSlide) // This uses handleSetCurrentSlide from context
      console.log("Audience: navigated to slide", newSlide)
    }
  }

  const handleNextSlide = () => {
    if (currentSlide < totalSlides) {
      const newSlide = currentSlide + 1
      setCurrentSlide(newSlide) // This uses handleSetCurrentSlide from context
      console.log("Audience: navigated to slide", newSlide)
    }
  }

  return (
    <div className="h-screen w-screen min-h-max bg-[linear-gradient(150deg,#2B6DFE_0%,#2B6DFE_70%,#00F2FF_100%)] flex items-center justify-center relative">
      <div className="w-full h-full h-max overflow-y-auto">
        <SlideContent
          slideNumber={currentSlide}
          highContrast={false}
          revealedItems={revealedItems}
          registerRevealableItems={registerRevealableItems}
          // @ts-ignore - We know this works in practice even though TypeScript complains
          setRevealedItems={setRevealedItems}
          unitNumber={unitNumber}
          lessonNumber={lessonNumber}
          gradeLevel={gradeLevel}
          lang={lang}
        />
      </div>

      {/* Navigation Arrows */}
      <div className="fixed -bottom-4 left-4 transform !z-[500000] -translate-y-1/2 z-40">
        <button
          onClick={handlePrevSlide}
          disabled={currentSlide === 1}
          className={`w-10 h-10 rounded-full bg-white/90  shadow-lg border-opacity-50 border-[1px] border-black flex items-center justify-center transition-all ${
            currentSlide === 1
              ? "opacity-50 cursor-not-allowed"
              : "hover:bg-white hover:scale-110 active:scale-95"
          }`}
          aria-label="Previous slide"
          title="Previous slide"
        >
          <ChevronLeft className="h-4 w-4 text-gray-700" />
        </button>
      </div>

      <div className="fixed -bottom-4 right-4 transform !z-[500000] -translate-y-1/2 z-40">
        <button
          onClick={handleNextSlide}
          disabled={currentSlide === totalSlides}
          className={`w-10 h-10 rounded-full bg-white/90  border-[1px] border-black border-opacity-50  shadow-lg flex items-center justify-center transition-all ${
            currentSlide === totalSlides
              ? "opacity-50 cursor-not-allowed"
              : "hover:bg-white hover:scale-110 active:scale-95"
          }`}
          aria-label="Next slide"
          title="Next slide"
        >
          <ChevronRight className="h-4 w-4 text-gray-700" />
        </button>
      </div>

      {/* Drawing Button */}
      <DrawingButton />

      {/* Debug info - only visible during development */}
      {/* {process.env.NODE_ENV === "development" && (
        <div className="fixed bottom-0 left-0 bg-black/70 text-white p-2 text-xs">
          Slide: {currentSlide} | Items: {revealedItems.join(",")} | Fullscreen: {isFullscreen ? "Yes" : "No"}
        </div>
      )} */}


      {/* Fullscreen Modal - default open for audience view */}
      <HtmlFullscreenModal
        isOpen={showFullscreenModal}
        onClose={() => setShowFullscreenModal(false)}
        html={slideData?.html_css_description_of_image || ''}
        title={`Slide ${currentSlide}`}
        slideData={slideData}
        showSidebar={true}
        revealedItems={revealedItems}
        setRevealedItems={setRevealedItems}
      />
    </div>
  )
}
