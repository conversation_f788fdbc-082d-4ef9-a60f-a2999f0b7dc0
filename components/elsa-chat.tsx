"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Send, User } from "lucide-react"
import Image from "next/image"

type Message = {
  id: number
  text: string
  sender: "user" | "bot"
  timestamp: Date
}

interface ElsaChatProps {
  compact?: boolean
  textSize?: number
}

export default function ElsaChat({ compact = false, textSize = 1 }: ElsaChatProps) {
  const [input, setInput] = useState("")
  const [messages, setMessages] = useState<Message[]>([
    {
      id: 1,
      text: "Hello! I'm <PERSON><PERSON><PERSON>, your AI teaching assistant. How can I help with today's math lesson?",
      sender: "bot",
      timestamp: new Date(),
    },
    {
      id: 2,
      text: "I can provide teaching tips, suggest activities, or help with explaining difficult concepts to students.",
      sender: "bot",
      timestamp: new Date(Date.now() + 100),
    },
  ])

  // Calculate font sizes based on textSize prop
  const headerFontSize = compact ? `${0.75 * Math.min(textSize, 2)}rem` : `${0.875 * Math.min(textSize, 2)}rem`
  const messageFontSize = `${textSize}rem`
  const inputFontSize = compact ? `${0.75 * Math.min(textSize, 2)}rem` : `${0.875 * Math.min(textSize, 2)}rem`

  const handleSendMessage = () => {
    if (!input.trim()) return

    // Add user message
    const userMessage: Message = {
      id: messages.length + 1,
      text: input,
      sender: "user",
      timestamp: new Date(),
    }

    setMessages([...messages, userMessage])
    setInput("")

    // Simulate ELSA's response
    setTimeout(() => {
      const botResponses = [
        "Based on this slide, you might want to emphasize the relationship between the variables. Students often struggle with understanding how they interact.",
        "For this concept, try using a real-world example like measuring the height of a building using shadows and similar triangles.",
        "When explaining this formula, remind students about the prerequisite knowledge from last week's lesson on algebraic expressions.",
        "This is a good opportunity to address the common misconception that all equations have exactly one solution.",
      ]

      const randomResponse = botResponses[Math.floor(Math.random() * botResponses.length)]

      const botMessage: Message = {
        id: messages.length + 2,
        text: randomResponse,
        sender: "bot",
        timestamp: new Date(),
      }

      setMessages((prev) => [...prev, botMessage])
    }, 1000)
  }

  return (
    <div className="h-full flex flex-col !z-[500] relative">
      {/* Coming Soon Overlay */}
      <div className="absolute text-white inset-0 !z-[5000] bg-gray-500/50 backdrop-blur-sm rounded-md z-10 flex items-center justify-center">
        <div className="rounded-lg p-6 shadow-lg text-center">
          <div className="bg-amber-100 rounded-full p-3 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
            <span className="text-amber-600 font-bold text-2xl">E</span>
          </div>
          <h3 className="text-xl font-bold text-white mb-2" style={{ fontFamily: 'Inter, sans-serif' }}>
            ELSA AI Assistant
          </h3>
          <p className="text-gray-600 mb-4 text-white"  style={{ fontFamily: 'Inter, sans-serif' }}>
            Coming Soon
          </p>
          <p className="text-sm text-white" style={{ fontFamily: 'Inter, sans-serif' }}>
            AI-powered teaching assistant to help with lesson planning and student support
          </p>
        </div>
      </div>

      <div className="flex-1 overflow-auto rounded-md mb-2 bg-white border border-gray-200 shadow-inner p-2 space-y-3">
        {messages.map((message) => (
          <div key={message.id} className={`flex ${message.sender === "user" ? "justify-end" : "justify-start"}`}>
            <div
              className={`max-w-[85%] px-3 py-2 rounded-lg ${
                message.sender === "user"
                  ? "bg-gradient-to-r from-blue-600 to-blue-500 text-white shadow-md"
                  : "bg-white border border-gray-200 shadow-sm text-gray-800"
              }`}
            >
              <div className="flex items-center gap-1.5 mb-1">
                {message.sender === "user" ? (
                  <div className="bg-blue-600/20 rounded-full p-0.5">
                    <User className={`${compact ? "h-3 w-3" : "h-4 w-4"} text-blue-600`} />
                  </div>
                ) : (
                  <div
                    className="bg-amber-100 rounded-full p-0.5 relative"
                    style={{ width: compact ? "16px" : "20px", height: compact ? "16px" : "20px" }}
                  >
                    <div className="w-full h-full flex items-center justify-center">
                      <span className="text-amber-600 font-bold text-xs">E</span>
                    </div>
                  </div>
                )}
                <span style={{ fontSize: headerFontSize }} className="font-medium">
                  {message.sender === "user" ? "You" : "ELSA"} •{" "}
                  <span style={{ fontSize: `${Number.parseFloat(headerFontSize) * 0.85}rem` }} className="opacity-75">
                    {message.timestamp.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
                  </span>
                </span>
              </div>
              <p style={{ fontSize: messageFontSize }} className="leading-relaxed">
                {message.text}
              </p>
            </div>
          </div>
        ))}
      </div>

      <div className="flex gap-1.5">
        <input
          value={input}
          onChange={(e) => setInput(e.target.value)}
          placeholder="Ask ELSA about this slide..."
          onKeyDown={(e) => e.key === "Enter" && handleSendMessage()}
          style={{ fontSize: inputFontSize }}
          className={`${
            compact ? "h-7" : "h-9"
          } border border-gray-300 focus:border-blue-400 rounded-md px-3 py-2 w-full`}
        />
        <Button
          onClick={handleSendMessage}
          size={compact ? "sm" : "default"}
          className={`${
            compact ? "h-7 w-7 p-0" : "h-9 w-9 p-0"
          } bg-gradient-to-r from-blue-500 to-blue-400 hover:from-blue-600 hover:to-blue-500 rounded-md flex items-center justify-center`}
        >
          <Send className={`${compact ? "h-3 w-3" : "h-4 w-4"}`} />
        </Button>
      </div>
    </div>
  )
}
