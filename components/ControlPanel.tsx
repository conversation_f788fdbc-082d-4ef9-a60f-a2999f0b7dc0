'use client'

import { useState, useEffect } from 'react'
import { ChevronLeft, ChevronRight, Eye, EyeOff, Keyboard, Loader, X } from 'lucide-react'

interface ControlPanelProps {
  revealedItems: number[]
  setRevealedItems: (items: number[]) => void
  maxRevealableItems: number
  isShown: boolean
  setIsShown: (shown: boolean) => void
  onPrevSlide: () => void
  onNextSlide: () => void
  currentSlide: number
  totalSlides: number
}

export function ControlPanel({
  revealedItems,
  setRevealedItems,
  maxRevealableItems,
  isShown,
  setIsShown,
  onPrevSlide,
  onNextSlide,
  currentSlide,
  totalSlides
}: ControlPanelProps) {
  const [showKeyboardHelp, setShowKeyboardHelp] = useState(false)

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Don't trigger shortcuts if user is typing in an input
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return
      }

      switch (event.key) {
        case 'ArrowLeft':
          event.preventDefault()
          onPrevSlide()
          break
        case 'ArrowRight':
          event.preventDefault()
          onNextSlide()
          break
        case 'ArrowUp':
          event.preventDefault()
          if (revealedItems.length < maxRevealableItems) {
            setRevealedItems([...revealedItems, revealedItems.length])
          }
          break
        case 'ArrowDown':
          event.preventDefault()
          if (revealedItems.length > 0) {
            setRevealedItems(revealedItems.slice(0, -1))
          }
          break
        case ' ':
        case 'Spacebar':
          event.preventDefault()
          if (revealedItems.length < maxRevealableItems) {
            setRevealedItems([...revealedItems, revealedItems.length])
          }
          break
        case 'h':
        case 'H':
          event.preventDefault()
          if (revealedItems.length > 0) {
            setIsShown(!isShown)
          }
          break
        case 's':
        case 'S':
          event.preventDefault()
          if (revealedItems.length === 0 && maxRevealableItems > 0) {
            setRevealedItems([0])
            setIsShown(true)
          }
          break
        case '?':
          event.preventDefault()
          setShowKeyboardHelp(true)
          break
        case 'Escape':
          event.preventDefault()
          setShowKeyboardHelp(false)
          break
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [revealedItems, maxRevealableItems, isShown, setRevealedItems, setIsShown, onPrevSlide, onNextSlide])

  const handlePrevReveal = () => {
    if (revealedItems.length > 0) {
      setRevealedItems(revealedItems.slice(0, -1))
    }
  }

  const handleNextReveal = () => {
    if (revealedItems.length < maxRevealableItems) {
      setRevealedItems([...revealedItems, revealedItems.length])
    }
  }

  const handleToggleShow = () => {
    setIsShown(!isShown)
  }

  const handleShowFirst = () => {
    if (revealedItems.length === 0) {
      setRevealedItems([0])
      setIsShown(true)
    }
  }

  return (
    <>
      {/* Modern Control Panel */}
      <div className="h-full bg-gradient-to-br from-[#2B6DFE] to-[#00F2FF] rounded-xl p-3 sm:p-4 border border-blue-300/30 shadow-lg flex flex-col">
        {/* Header with Status Indicator */}
        <div className="flex items-center justify-between mb-3 sm:mb-4">
          <div className="flex items-center gap-2 sm:gap-3">
            <div className="w-2 h-2 sm:w-3 sm:h-3 bg-white rounded-full"></div>
            <h3 className="text-white font-bold text-sm sm:text-base tracking-wide">CONTROL PANEL</h3>
          </div>
          <button
            onClick={() => setShowKeyboardHelp(true)}
            className="p-1.5 sm:p-2 bg-white/20 hover:bg-white/30 rounded-lg text-white hover:text-white transition-colors border border-white/20"
            title="Keyboard shortcuts (?)"
          >
            <Keyboard size={14} className="sm:w-4 sm:h-4" />
          </button>
        </div>

        {/* Main Control Grid */}
        <div className="flex-1 flex flex-col space-y-3 sm:space-y-4">

          {/* Slide Navigation Section */}
          <div className="space-y-2 sm:space-y-3">
            <div className="flex items-center gap-1.5 sm:gap-2 mb-1 sm:mb-2">
              <div className="w-0.5 sm:w-1 h-3 sm:h-4 bg-white rounded-full"></div>
              <span className="text-white/90 text-xs sm:text-sm font-medium uppercase tracking-wider">Slide Navigation</span>
            </div>

            <div className="grid grid-cols-5 gap-2 sm:gap-3 items-center">
              {/* Previous Slide */}
              <button
                onClick={onPrevSlide}
                disabled={currentSlide === 1}
                className={`col-span-2 flex items-center justify-center gap-1 sm:gap-2 py-2 sm:py-3 px-2 sm:px-4 rounded-lg text-xs sm:text-sm font-semibold transition-colors ${
                  currentSlide === 1
                    ? "bg-white/20 text-white/50 cursor-not-allowed border border-white/20"
                    : "bg-white/30 hover:bg-white/40 text-white border border-white/30"
                }`}
                title="Previous slide (←)"
              >
                <ChevronLeft size={14} className="sm:w-4 sm:h-4" />
                <span className="hidden sm:inline">PREV</span>
                <span className="sm:hidden">←</span>
              </button>

              {/* Slide Counter */}
              <div className="col-span-1 flex flex-col items-center justify-center py-2 sm:py-3 px-1 sm:px-2 bg-white/30 rounded-lg border border-white/30">
                <div className="text-white text-sm sm:text-lg font-bold leading-none">{currentSlide}</div>
                <div className="text-white/80 text-xs font-medium">of {totalSlides}</div>
              </div>

              {/* Next Slide */}
              <button
                onClick={onNextSlide}
                disabled={currentSlide === totalSlides}
                className={`col-span-2 flex items-center justify-center gap-1 sm:gap-2 py-2 sm:py-3 px-2 sm:px-4 rounded-lg text-xs sm:text-sm font-semibold transition-colors ${
                  currentSlide === totalSlides
                    ? "bg-white/20 text-white/50 cursor-not-allowed border border-white/20"
                    : "bg-white/30 hover:bg-white/40 text-white border border-white/30"
                }`}
                title="Next slide (→)"
              >
                <span className="hidden sm:inline">NEXT</span>
                <span className="sm:hidden">→</span>
                <ChevronRight size={14} className="sm:w-4 sm:h-4" />
              </button>
            </div>
          </div>

          {/* Item Controls Section */}
          <div className="space-y-2 sm:space-y-3">
            <div className="flex items-center gap-1.5 sm:gap-2 mb-1 sm:mb-2">
              <div className="w-0.5 sm:w-1 h-3 sm:h-4 bg-white rounded-full"></div>
              <span className="text-white/90 text-xs sm:text-sm font-medium uppercase tracking-wider">Item Controls</span>
            </div>

            {/* Item Counter - Top */}
            {(
              <div className="flex justify-center mb-2 sm:mb-3">
                <div className="bg-white/30 px-3 sm:px-4 py-1.5 sm:py-2 rounded-lg text-white text-sm sm:text-base font-medium border border-white/30">
                  <span className="text-white font-bold">{revealedItems?.length}</span>
                  <span className="text-white/80 mx-1">/</span>
                  <span className="text-white">{maxRevealableItems ? maxRevealableItems : <><Loader size={14} className="inline" /></>}</span>
                  <span className="text-white/80 ml-1 text-xs sm:text-sm">ITEMS</span>
                </div>
              </div>
            )}

            {/* Control Buttons Row */}
            <div className="flex items-center justify-center gap-2 sm:gap-3">
              {/* Up Button (Add Item) */}
              <button
                onClick={handleNextReveal}
                disabled={revealedItems.length >= maxRevealableItems}
                className={`w-10 h-10 sm:w-12 sm:h-12 rounded-lg flex items-center justify-center transition-colors ${
                  revealedItems.length >= maxRevealableItems
                    ? "bg-white/20 text-white/50 cursor-not-allowed border border-white/20"
                    : "bg-white/30 hover:bg-white/40 text-white border border-white/30"
                }`}
                title="Add item (↑)"
              >
                <ChevronLeft className="rotate-90 w-5 h-5 sm:w-6 sm:h-6" />
              </button>

              {/* Show/Hide Toggle */}
              {revealedItems.length > 0 ? (
                <button
                  onClick={handleToggleShow}
                  className={`px-3 sm:px-4 py-2 sm:py-2.5 rounded-lg text-sm sm:text-base font-semibold transition-colors border ${
                    isShown
                      ? "bg-white/40 hover:bg-white/50 text-white border-white/40"
                      : "bg-white/30 hover:bg-white/40 text-white border-white/30"
                  }`}
                  title={`${isShown ? "Hide" : "Show"} questions (H)`}
                >
                  {isShown ? (
                    <>
                      <EyeOff size={16} className="inline mr-1 sm:mr-2" />
                      <span>HIDE</span>
                    </>
                  ) : (
                    <>
                      <Eye size={16} className="inline mr-1 sm:mr-2" />
                      <span>SHOW</span>
                    </>
                  )}
                </button>
              ) : maxRevealableItems > 0 && (
                <button
                  onClick={handleShowFirst}
                  className="bg-white/30 hover:bg-white/40 px-3 sm:px-4 py-2 sm:py-2.5 rounded-lg text-white text-sm sm:text-base font-semibold transition-colors border border-white/30"
                  title="Show first question (S)"
                >
                  <Eye size={16} className="inline mr-1 sm:mr-2" />
                  <span>SHOW</span>
                </button>
              )}

              {/* Down Button (Remove Item) */}
              <button
                onClick={handlePrevReveal}
                disabled={revealedItems.length === 0}
                className={`w-10 h-10 sm:w-12 sm:h-12 rounded-lg flex items-center justify-center transition-colors ${
                  revealedItems.length === 0
                    ? "bg-white/20 text-white/50 cursor-not-allowed border border-white/20"
                    : "bg-white/30 hover:bg-white/40 text-white border border-white/30"
                }`}
                title="Remove item (↓)"
              >
                <ChevronLeft className="-rotate-90 w-5 h-5 sm:w-6 sm:h-6" />
              </button>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="pt-2 sm:pt-3 border-t border-white/20 mt-auto">
            <div className="flex items-center justify-center text-xs sm:text-sm text-white/80">
              <span className="flex items-center gap-1">
                <div className="w-1 h-1 bg-white rounded-full"></div>
                <span className="text-xs sm:text-sm">READY</span>
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Modern Keyboard Help Modal */}
      {showKeyboardHelp && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-[9999]">
          <div className="bg-gradient-to-br from-slate-900 to-slate-800 rounded-2xl p-8 max-w-lg w-full mx-4 border border-slate-700/50 shadow-2xl">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-500/20 rounded-xl">
                  <Keyboard size={24} className="text-blue-400" />
                </div>
                <h3 className="text-xl font-bold text-white">Keyboard Shortcuts</h3>
              </div>
              <button
                onClick={() => setShowKeyboardHelp(false)}
                className="p-2 hover:bg-slate-700/50 rounded-xl text-slate-400 hover:text-white transition-all duration-200"
              >
                <X size={20} />
              </button>
            </div>

            <div className="space-y-4">
              {/* Navigation Section */}
              <div className="space-y-3">
                <h4 className="text-sm font-semibold text-slate-300 uppercase tracking-wider flex items-center gap-2">
                  <div className="w-1 h-4 bg-indigo-400 rounded-full"></div>
                  Navigation
                </h4>
                <div className="grid grid-cols-1 gap-2">
                  <div className="flex items-center justify-between py-2 px-3 bg-slate-800/50 rounded-lg border border-slate-700/30">
                    <kbd className="px-3 py-1 bg-slate-700 text-slate-200 rounded-md font-mono text-sm font-semibold">←</kbd>
                    <span className="text-slate-300 text-sm">Previous slide</span>
                  </div>
                  <div className="flex items-center justify-between py-2 px-3 bg-slate-800/50 rounded-lg border border-slate-700/30">
                    <kbd className="px-3 py-1 bg-slate-700 text-slate-200 rounded-md font-mono text-sm font-semibold">→</kbd>
                    <span className="text-slate-300 text-sm">Next slide</span>
                  </div>
                </div>
              </div>

              {/* Item Controls Section */}
              <div className="space-y-3">
                <h4 className="text-sm font-semibold text-slate-300 uppercase tracking-wider flex items-center gap-2">
                  <div className="w-1 h-4 bg-cyan-400 rounded-full"></div>
                  Item Controls
                </h4>
                <div className="grid grid-cols-1 gap-2">
                  <div className="flex items-center justify-between py-2 px-3 bg-slate-800/50 rounded-lg border border-slate-700/30">
                    <kbd className="px-3 py-1 bg-slate-700 text-slate-200 rounded-md font-mono text-sm font-semibold">↑</kbd>
                    <span className="text-slate-300 text-sm">Add item (reveal next)</span>
                  </div>
                  <div className="flex items-center justify-between py-2 px-3 bg-slate-800/50 rounded-lg border border-slate-700/30">
                    <kbd className="px-3 py-1 bg-slate-700 text-slate-200 rounded-md font-mono text-sm font-semibold">↓</kbd>
                    <span className="text-slate-300 text-sm">Remove item (hide last)</span>
                  </div>
                  <div className="flex items-center justify-between py-2 px-3 bg-slate-800/50 rounded-lg border border-slate-700/30">
                    <kbd className="px-3 py-1 bg-slate-700 text-slate-200 rounded-md font-mono text-sm font-semibold">Space</kbd>
                    <span className="text-slate-300 text-sm">Add item (same as ↑)</span>
                  </div>
                </div>
              </div>

              {/* Quick Actions Section */}
              <div className="space-y-3">
                <h4 className="text-sm font-semibold text-slate-300 uppercase tracking-wider flex items-center gap-2">
                  <div className="w-1 h-4 bg-emerald-400 rounded-full"></div>
                  Quick Actions
                </h4>
                <div className="grid grid-cols-1 gap-2">
                  <div className="flex items-center justify-between py-2 px-3 bg-slate-800/50 rounded-lg border border-slate-700/30">
                    <kbd className="px-3 py-1 bg-slate-700 text-slate-200 rounded-md font-mono text-sm font-semibold">H</kbd>
                    <span className="text-slate-300 text-sm">Hide/Show questions</span>
                  </div>
                  <div className="flex items-center justify-between py-2 px-3 bg-slate-800/50 rounded-lg border border-slate-700/30">
                    <kbd className="px-3 py-1 bg-slate-700 text-slate-200 rounded-md font-mono text-sm font-semibold">S</kbd>
                    <span className="text-slate-300 text-sm">Show first question</span>
                  </div>
                </div>
              </div>

              {/* Help Section */}
              <div className="space-y-3">
                <h4 className="text-sm font-semibold text-slate-300 uppercase tracking-wider flex items-center gap-2">
                  <div className="w-1 h-4 bg-amber-400 rounded-full"></div>
                  Help
                </h4>
                <div className="grid grid-cols-1 gap-2">
                  <div className="flex items-center justify-between py-2 px-3 bg-slate-800/50 rounded-lg border border-slate-700/30">
                    <kbd className="px-3 py-1 bg-slate-700 text-slate-200 rounded-md font-mono text-sm font-semibold">?</kbd>
                    <span className="text-slate-300 text-sm">Show this help</span>
                  </div>
                  <div className="flex items-center justify-between py-2 px-3 bg-slate-800/50 rounded-lg border border-slate-700/30">
                    <kbd className="px-3 py-1 bg-slate-700 text-slate-200 rounded-md font-mono text-sm font-semibold">Esc</kbd>
                    <span className="text-slate-300 text-sm">Close help</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="mt-6 pt-4 border-t border-slate-700/50">
              <div className="flex items-center justify-center text-xs text-slate-400">
                <span className="flex items-center gap-2">
                  <div className="w-1 h-1 bg-emerald-400 rounded-full"></div>
                  Compatible with Windows, Mac & all browsers
                </span>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
