"use client";

import React, { isValidElement, cloneElement, useEffect, useRef } from 'react';

interface HtmlContentProps {
  html: React.ReactNode;
  className?: string;
}

/**
 * Simple hash function to generate a hash from a string
 * Used to create stable keys for iframes to prevent unnecessary re-renders
 */
function hashString(str: string): string {
  let hash = 0;
  if (str.length === 0) return hash.toString();

  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }

  return Math.abs(hash).toString(16);
}

// Use React.memo to prevent unnecessary re-renders
export const HtmlContent = React.memo(function HtmlContent({ html, className = '' }: HtmlContentProps) {
  const iframeRef = useRef<HTMLIFrameElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // Auto-scale iframe content to fit container
  useEffect(() => {
    const calculateScale = () => {
      if (!iframeRef.current || !containerRef.current) return

      const iframe = iframeRef.current
      const container = containerRef.current

      const containerRect = container.getBoundingClientRect()
      const containerWidth = containerRect.width > 540 ? containerRect.width : 540
      const containerHeight = containerRect.height > 200 ? containerRect.height : 200

      // Skip if container has no dimensions yet
      if (containerWidth === 0 || containerHeight === 0) return

      // Get the actual content size from iframe
      try {
        const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document
        if (iframeDoc && iframeDoc.body) {
          // First, inject CSS to prevent scrolling
          let styleElement = iframeDoc.getElementById('auto-scale-styles')
          if (!styleElement) {
            styleElement = iframeDoc.createElement('style')
            styleElement.id = 'auto-scale-styles'
            styleElement.textContent = `
              html, body {
                overflow: hidden !important;
                margin: 0 !important;
                padding: 0 !important;
                box-sizing: border-box !important;
              }
              body {
                width: 100% !important;
                height: 100% !important;
              }
            `
            iframeDoc.head.appendChild(styleElement)
          }
          const bodyWidth = iframeDoc.body.scrollWidth
          const bodyHeight = iframeDoc.body.scrollHeight

          // Calculate scale to fit both width and height with some padding
          const padding = 20
          const availableWidth = containerWidth - padding
          const availableHeight = containerHeight - padding

          const scaleX = availableWidth / bodyWidth
          const scaleY = availableHeight / bodyHeight
          const scale = Math.min(scaleX, scaleY, 1) // Don't scale up, only down

          // Apply the scale to iframe body and hide overflow
          if (scale < 1) {
            // Calculate centering offsets
            const scaledWidth = bodyWidth * scale
            const scaledHeight = bodyHeight * scale
            const offsetX = (containerWidth - scaledWidth) / 2
            const offsetY = (containerHeight - scaledHeight) / 2

            iframeDoc.body.style.transform = `translate(${offsetX}px, ${offsetY}px) scale(${scale})`
            iframeDoc.body.style.transformOrigin = 'top left'
            iframeDoc.body.style.width = `${100 / scale}%`
            iframeDoc.body.style.height = `${100 / scale}%`
            iframeDoc.body.style.overflow = 'hidden'

            // Also set html element to prevent scrolling
            if (iframeDoc.documentElement) {
              iframeDoc.documentElement.style.overflow = 'hidden'
              iframeDoc.documentElement.style.width = '100%'
              iframeDoc.documentElement.style.height = '100%'
            }
          } else {
            // Reset styles if no scaling needed
            iframeDoc.body.style.transform = ''
            iframeDoc.body.style.transformOrigin = ''
            iframeDoc.body.style.width = ''
            iframeDoc.body.style.height = ''
            iframeDoc.body.style.overflow = 'hidden'

            if (iframeDoc.documentElement) {
              iframeDoc.documentElement.style.overflow = 'hidden'
            }
          }

          console.log('Auto-scaling iframe content:', {
            containerWidth,
            containerHeight,
            bodyWidth,
            bodyHeight,
            scale
          })
        }
      } catch (error) {
        console.warn('Could not access iframe content for scaling:', error)
      }
    }

    // Wait for iframe to load before calculating scale
    const timer = setTimeout(() => {
      calculateScale()
    }, 200)

    const resizeObserver = new ResizeObserver(() => {
      setTimeout(calculateScale, 100)
    })

    if (containerRef.current) {
      resizeObserver.observe(containerRef.current)
    }

    return () => {
      clearTimeout(timer)
      resizeObserver.disconnect()
    }
  }, [html])

  if (typeof html === 'string') {
    // Check if the HTML content is an error message (like a JSON string)
    if (html.includes('{"message":') || html.includes('Document not found')) {
      console.warn('Invalid HTML content detected:', html);
      return <span className={`${className} text-red-500`}>Error in HTML content. Please edit or remove this content.</span>;
    }

    // If the HTML is empty, show a placeholder
    if (!html.trim()) {
      return <span className={`${className} text-gray-400`}>No content available</span>;
    }

    // Log the HTML content for debugging
    console.log('Rendering HTML content:', html?.substring(0, 100) + '...');

    // Always try to render as HTML first, regardless of content
    try {
      // Check if this is a full HTML document
      const isFullHtmlDocument = html.includes('<!DOCTYPE html>') ||
                                (html.includes('<html') && html.includes('<body'));

      // For full HTML documents, use an iframe to render them properly
      if (isFullHtmlDocument) {
        // Use a key based on the content hash to prevent unnecessary re-renders
        const contentHash = hashString(html);

        console.log('Rendering as full HTML document in iframe');

        // Create a unique ID for this iframe to ensure it's properly refreshed
        const iframeId = `iframe-${contentHash}-${Date.now()}`;

        return (
          <div
            ref={containerRef}
            className="w-full h-full"
            style={{
              width: "100%",
              height: "100%",
              maxWidth: "100%",
              maxHeight: "100%",
              overflow: "hidden",
              display: "flex",
              alignItems: "center",
              justifyContent: "center"
            }}
          >
            <iframe
              ref={iframeRef}
              id={iframeId}
              key={iframeId}
              srcDoc={html}
              className={`${className} w-full h-full border-0`}
              sandbox="allow-scripts allow-same-origin"
              loading="eager"
              title="HTML Content"
              style={{
                width: "100%",
                height: "100%",
                maxWidth: "100%",
                maxHeight: "100%",
                background: "white",
                border: "1px solid #ddd",
                borderRadius: "4px",
                objectFit: "contain",
                display: "block"
              }}
              onLoad={() => {
                console.log(`Iframe ${iframeId} loaded successfully`)
                // Trigger scale calculation after iframe loads
                setTimeout(() => {
                  if (iframeRef.current && containerRef.current) {
                    const event = new Event('resize')
                    window.dispatchEvent(event)
                  }
                }, 100)
              }}
            />
          </div>
        );
      }

      // For HTML fragments, check if they contain HTML tags
      const containsHtmlTags = /<[a-z][\s\S]*>/i.test(html);

      if (containsHtmlTags) {
        console.log('Rendering as HTML fragment');

        // Force the content to be treated as HTML by wrapping it in a div
        // Add a unique key to force re-render when content changes
        const uniqueKey = `html-${hashString(html)}-${Date.now()}`;

        return (
          <div
            key={uniqueKey}
            className={`${className} html-content-wrapper`}
            dangerouslySetInnerHTML={{ __html: html }}
            style={{ padding: "10px", borderRadius: "4px" }}
          />
        );
      } else {
        // For plain text, just render it as text
        console.log('Rendering as plain text');
        return <div className={className}>{html}</div>;
      }
    } catch (error) {
      console.error('Error rendering HTML content:', error);

      // Fallback to plain text if HTML rendering fails
      return <div className={className}>{html}</div>;
    }
  }

  // Якщо це валідний React елемент і в ньому є children -> string з <p>...</p>
  if (
    isValidElement(html) &&
    html.props &&
    typeof html.props === 'object' &&
    html.props !== null &&
    'children' in html.props &&
    isValidElement(html.props.children) &&
    html.props.children.props &&
    html.props.children.props &&
    typeof html.props.children.props === 'object' &&
    html.props.children.props !== null &&
    'children' in html.props.children.props &&
    typeof html.props.children.props.children === 'string'
  ) {
    const originalString = html.props.children.props.children;
    const cleanedString = originalString.replace(/<\/?p>/g, '');

    const clonedInner = cloneElement(
      html.props.children as React.ReactElement<{ children: string }>,
      { children: cleanedString }
    );

    const clonedOuter = cloneElement(
      html as React.ReactElement<{ children: React.ReactNode }>,
      { children: clonedInner }
    );

    return <div className={className}>{clonedOuter}</div>;
  }

  // fallback — повертаємо як є
  return <div className={className}>{html}</div>;
});

export default HtmlContent;
