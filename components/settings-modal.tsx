"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Loader2, Save, X, RefreshCw } from "lucide-react"
import ReferenceFilesManager from "./reference-files-manager"

interface Setting {
  _id: string
  key: string
  value: string
  description: string
  updatedAt: string
  updatedBy: string
}

interface SettingsModalProps {
  isOpen: boolean
  onClose: () => void
}

export default function SettingsModal({ isOpen, onClose }: SettingsModalProps) {
  const [settings, setSettings] = useState<Setting[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState<string | null>(null)
  const [error, setError] = useState("")
  const [editingSettings, setEditingSettings] = useState<Record<string, string>>({})

  // Load settings when modal opens
  useEffect(() => {
    if (isOpen) {
      loadSettings()
    }
  }, [isOpen])

  const loadSettings = async () => {
    setIsLoading(true)
    setError("")

    try {
      const response = await fetch('/api/settings')
      const data = await response.json()

      if (data.success) {
        setSettings(data.settings)
        // Initialize editing state
        const editState: Record<string, string> = {}
        data.settings.forEach((setting: Setting) => {
          editState[setting.key] = setting.value
        })
        setEditingSettings(editState)
        console.log('Loaded settings:', data.settings.length)
      } else {
        setError(`Failed to load settings: ${data.error || 'Unknown error'}`)
      }
    } catch (err) {
      setError('Failed to load settings')
      console.error('Error loading settings:', err)
    } finally {
      setIsLoading(false)
    }
  }

  const saveSetting = async (key: string) => {
    if (!editingSettings[key]) return

    setIsSaving(key)
    setError("")

    try {
      console.log(`🔄 Saving setting: ${key}`)

      const response = await fetch('/api/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          key,
          value: editingSettings[key],
          updatedBy: 'admin'
        }),
      })

      const data = await response.json()

      if (data.success) {
        console.log(`✅ Setting ${key} saved successfully`)
        alert(`✅ Setting ${key} saved successfully!`)
        await loadSettings() // Reload to get updated data
      } else {
        console.error(`❌ Failed to save setting ${key}:`, data.error)
        alert(`❌ Failed to save setting ${key}: ${data.error}`)
      }
    } catch (err) {
      console.error(`❌ Error saving setting ${key}:`, err)
      alert(`❌ Failed to save setting ${key}. Please try again.`)
    } finally {
      setIsSaving(null)
    }
  }

  const handleTextareaChange = (key: string, value: string) => {
    setEditingSettings(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const getSettingTitle = (key: string) => {
    switch (key) {
      case 'STYLE_GUIDE':
        return 'Style Guide'
      case 'INSTRUCTIONS':
        return 'Generation Instructions'
      default:
        return key
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center -top-6 justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-6xl h-[90vh] mx-4 flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <div>
            <h2 className="text-2xl font-bold" style={{ fontFamily: 'Inter, sans-serif' }}>
              HTML Generation Settings
            </h2>
            <p className="text-gray-600 mt-1" style={{ fontFamily: 'Inter, sans-serif' }}>
              Configure prompts, style guides, and reference files for HTML generation
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              onClick={loadSettings}
              disabled={isLoading}
              variant="outline"
              style={{ fontFamily: 'Inter, sans-serif' }}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button
              onClick={onClose}
              variant="outline"
              style={{ fontFamily: 'Inter, sans-serif' }}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-6">
          <Tabs defaultValue="settings" className="w-full h-full">
            <TabsList className="grid w-full grid-cols-2 mb-6">
              <TabsTrigger value="settings" style={{ fontFamily: 'Inter, sans-serif' }}>
                Global Settings
              </TabsTrigger>
              <TabsTrigger value="references" style={{ fontFamily: 'Inter, sans-serif' }}>
                Reference Files
              </TabsTrigger>
            </TabsList>

            <TabsContent value="settings" className="space-y-6">
              {/* Error Display */}
              {error && (
                <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-red-700 text-sm" style={{ fontFamily: 'Inter, sans-serif' }}>{error}</p>
                </div>
              )}

              {/* Loading State */}
              {isLoading ? (
                <div className="flex justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
                </div>
              ) : (
                <div className="space-y-6">
                  {settings.map((setting) => (
                    <Card key={setting.key}>
                      <CardHeader>
                        <div className="flex justify-between items-start">
                          <div>
                            <CardTitle style={{ fontFamily: 'Inter, sans-serif' }}>
                              {getSettingTitle(setting.key)}
                            </CardTitle>
                            <CardDescription style={{ fontFamily: 'Inter, sans-serif' }}>
                              {setting.description}
                            </CardDescription>
                          </div>
                          <Button
                            onClick={() => saveSetting(setting.key)}
                            disabled={isSaving === setting.key || !editingSettings[setting.key] || editingSettings[setting.key] === setting.value}
                            style={{ fontFamily: 'Inter, sans-serif' }}
                          >
                            {isSaving === setting.key ? (
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            ) : (
                              <Save className="h-4 w-4 mr-2" />
                            )}
                            Save Changes
                          </Button>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          <textarea
                            value={editingSettings[setting.key] || ''}
                            onChange={(e) => handleTextareaChange(setting.key, e.target.value)}
                            className="w-full h-64 p-3 border border-gray-300 rounded-md resize-none font-mono text-sm"
                            style={{ fontFamily: 'Monaco, Consolas, monospace' }}
                            placeholder={`Enter ${getSettingTitle(setting.key).toLowerCase()}...`}
                          />

                          <div className="flex justify-between text-xs text-gray-500" style={{ fontFamily: 'Inter, sans-serif' }}>
                            <span>Last updated: {new Date(setting.updatedAt).toLocaleString()}</span>
                            <span>Updated by: {setting.updatedBy}</span>
                            <span>Characters: {editingSettings[setting.key]?.length || 0}</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="references" className="space-y-6">
              <ReferenceFilesManager isOpen={isOpen} />
            </TabsContent>
          </Tabs>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 bg-gray-50">
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-600" style={{ fontFamily: 'Inter, sans-serif' }}>
              <strong>Note:</strong> Changes to settings and reference files will affect all future HTML generation.
              Existing jobs will not be affected until regenerated.
            </div>
            <Button
              onClick={onClose}
              variant="outline"
              style={{ fontFamily: 'Inter, sans-serif' }}
            >
              Close
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
