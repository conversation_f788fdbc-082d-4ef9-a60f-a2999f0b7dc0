"use client"

import { useState, useEffect, Suspense } from "react"
import { getSlide } from "@/services/slideService"
import { useSearchParams } from "next/navigation"

interface NotesPanelProps {
  title: string
  type: "script" | "misconceptions"
  currentSlide: number
  textSize?: number
  isNotesFocus?: boolean
}

// Separate component that uses useSearchParams
function NotesPanelContent({ title, type, currentSlide, textSize = 1, isNotesFocus = false }: NotesPanelProps) {
  const [notes, setNotes] = useState("")
  const searchParams = useSearchParams()

  // Load notes for the current slide
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Get parameters from URL
        const unitParam = searchParams.get('unit')
        const lessonParam = searchParams.get('lesson')
        const gradeParam = searchParams.get('grade')

        // Extract unit, lesson, and grade from URL parameters
        let unitNumber = "3"
        let lessonNumber = "2"
        let gradeLevel = "Grade 1"

        if (unitParam) {
          unitNumber = unitParam
        }

        if (lessonParam) {
          lessonNumber = lessonParam
        }

        if (gradeParam) {
          // Check if gradeParam already includes "Grade" prefix
          gradeLevel = gradeParam.startsWith('Grade ') ? gradeParam : `Grade ${gradeParam}`
        }

        // If we're in the presenter view, try to get parameters from the URL path
        const pathname = window.location.pathname
        const unitMatch = pathname.match(/\/unit\/(\d+)/)
        const lessonMatch = pathname.match(/\/lesson\/(\d+)/)
        const gradeMatch = pathname.match(/\/grade\/(\d+)/)

        if (unitMatch && unitMatch[1]) {
          unitNumber = unitMatch[1]
        }

        if (lessonMatch && lessonMatch[1]) {
          lessonNumber = lessonMatch[1]
        }

        if (gradeMatch && gradeMatch[1]) {
          gradeLevel = `Grade ${gradeMatch[1]}`
        }

        // Try to get parameters from localStorage as a fallback
        if (!unitParam || !lessonParam || !gradeParam) {
          try {
            const storedUnit = localStorage.getItem('lesson_unit_number');
            const storedLesson = localStorage.getItem('lesson_lesson_number');
            const storedGrade = localStorage.getItem('lesson_grade_level');

            console.log('Checking localStorage for missing parameters:', {
              storedUnit, storedLesson, storedGrade
            });

            if (storedUnit && !unitParam) {
              unitNumber = storedUnit;
            }

            if (storedLesson && !lessonParam) {
              lessonNumber = storedLesson;
            }

            if (storedGrade && !gradeParam) {
              gradeLevel = storedGrade.startsWith('Grade ') ? storedGrade : `Grade ${storedGrade}`;
            }
          } catch (error) {
            console.error('Error loading from localStorage:', error);
          }
        }

        console.log(`Loading slide ${currentSlide} data with parameters:`, {
          unitNumber,
          lessonNumber,
          gradeLevel
        });

        console.log(`Trying to fetch slide ${currentSlide} with document ID: ${unitNumber}-${lessonNumber}-${gradeLevel}`);

        // Try with hardcoded values for testing
        let slideData;

        // Fix gradeLevel format if needed
        if (gradeLevel && !gradeLevel.startsWith('Grade ')) {
          gradeLevel = `Grade ${gradeLevel}`;
        }

        // Use the parameters from URL to get slide data
        console.log(`Fetching slide with exact parameters: slide=${currentSlide}, unit=${unitNumber}, lesson=${lessonNumber}, grade=${gradeLevel}`);
        slideData = await getSlide(currentSlide, unitNumber, lessonNumber, gradeLevel);

        // If no data found, try with different grade format
        if (!slideData || !slideData.script) {
          // Try with different grade format (with or without "Grade " prefix)
          const alternativeGradeLevel = gradeLevel.startsWith('Grade ')
            ? gradeLevel.substring(6) // Remove "Grade " prefix
            : `Grade ${gradeLevel}`; // Add "Grade " prefix

          console.log(`No script found. Trying with alternative grade format: ${alternativeGradeLevel}`);
          slideData = await getSlide(currentSlide, unitNumber, lessonNumber, alternativeGradeLevel);

          if (!slideData || !slideData.script) {
            console.log(`No script data found for slide ${currentSlide} with any parameters.`);
            console.log(`Tried: ${unitNumber}-${lessonNumber}-${gradeLevel} and ${unitNumber}-${lessonNumber}-${alternativeGradeLevel}`);
          }
        }

        console.log(`Slide data found:`, slideData ? 'Yes' : 'No');
        if (slideData) {
          console.log(`Slide data has script:`, slideData.script ? 'Yes' : 'No');
          if (slideData.script) {
            console.log(`Script preview:`, slideData.script.substring(0, 50) + '...');
          }
        }

        if (type === "script") {
          // Always use script from database
          if (slideData?.script) {
            console.log(`Using script from database for slide ${currentSlide}:`, slideData.script.substring(0, 50) + '...')
            setNotes(slideData.script)
          } else {
            console.log(`No script found in database for slide ${currentSlide}`)
            setNotes(`No script available for slide ${currentSlide}. Please check database connection.`)
          }
        } else if (type === "misconceptions") {
          if (slideData?.teacher_tips) {
            setNotes(
              `${slideData.teacher_tips.general_tip || 'No general tip available.'}\n\n ${slideData.teacher_tips.misconception_tip || 'No misconception tip available.'}`
            )
          } else {
            setNotes(
              `Common misconceptions for slide ${currentSlide}:\n\n1. Students may confuse the order of operations when solving these equations.\n\n2. Some students might apply the formula incorrectly by substituting values in the wrong positions.\n\n3. Watch for students who try to memorize without understanding the underlying concept.`
            )
          }
        }
      } catch (error) {
        console.error("Error loading slide data:", error);
        if (type === "script") {
          setNotes(`Error loading script for slide ${currentSlide}. Please try again.`)
        } else {
          setNotes(`No teacher tips available for slide ${currentSlide}`)
        }
      }
    };

    fetchData();
  }, [currentSlide, type, searchParams])

  const padding =
    textSize === 1
      ? "pl-4"
      : textSize === 1.25
        ? "pl-6"
        : textSize === 1.5
          ? "pl-12"
        : textSize === 1.75
          ? "pl-12"
        : "pl-12"

  // Calculate font size based on textSize prop
  const fontSize =
    textSize === 1
      ? "1rem"
      : textSize === 1.25
        ? "1.25rem"
        : textSize === 1.5
          ? "1.5rem"
          : textSize === 1.75
            ? "1.75rem"
            : textSize === 2
              ? "2rem"
              : textSize === 2.25
                ? "2.25rem"
                : textSize === 2.5
                  ? "2.5rem"
                  : textSize === 2.75
                    ? "2.75rem"
                    : "3rem"

  // For script and misconceptions (tips) types, we want to render HTML content
  if (type === "script" || type === "misconceptions") {
    const contentClass = type === "script" ? "script-content" : "tips-content";

    return (
      <div className="h-full flex flex-col min-h-0 min-h-[300px]">
        <div
          className={`flex-1 !overflow-scroll bg-white font-normal mt-0 p-4 ${padding} w-full ${contentClass} min-h-0 ${!isNotesFocus ? 'max-h-[300px]' : ''}`}
          style={{
            fontFamily: "'Public Sans', sans-serif",
            lineHeight: "1.6",
            fontWeight: 300,
            color: "#111827",
            fontSize: fontSize,
            wordWrap: "break-word",
            overflowWrap: "break-word",
            hyphens: "auto",
            maxWidth: "100%",
            overflowY: "auto",
            overflowX: "hidden",
          }}
          dangerouslySetInnerHTML={{ __html: notes }}
        />
        <style jsx global>{`
          .script-content, .tips-content {
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
            hyphens: auto !important;
            max-width: 100% !important;
            overflow-y: auto !important;
            overflow-x: hidden !important;
            height: 100% !important;
          }
          .script-content *, .tips-content * {
            max-width: 100% !important;
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
            box-sizing: border-box !important;
          }
          .script-content ol, .tips-content ol {
            list-style-type: decimal;
            padding-left: 1.5rem;
            margin-bottom: 1rem;
            max-width: 100% !important;
          }
          .script-content ul, .tips-content ul {
            list-style-type: disc;
            padding-left: 1.5rem;
            margin-bottom: 1rem;
            max-width: 100% !important;
          }
          .script-content li, .tips-content li {
            margin-bottom: 0.5rem;
            max-width: 100% !important;
            word-wrap: break-word !important;
          }
          .script-content p, .tips-content p {
            margin-bottom: 1rem;
            max-width: 100% !important;
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
          }
          .script-content h1, .script-content h2, .script-content h3, .script-content h4,
          .tips-content h1, .tips-content h2, .tips-content h3, .tips-content h4 {
            font-weight: bold;
            margin-top: 1.5rem;
            margin-bottom: 0.75rem;
            max-width: 100% !important;
            word-wrap: break-word !important;
          }
          .script-content h1, .tips-content h1 {
            font-size: 1.5em;
          }
          .script-content h2, .tips-content h2 {
            font-size: 1.3em;
          }
          .script-content h3, .tips-content h3 {
            font-size: 1.1em;
          }
          .script-content a, .tips-content a {
            color: #2563eb;
            text-decoration: underline;
            word-wrap: break-word !important;
          }
          .script-content blockquote, .tips-content blockquote {
            border-left: 4px solid #e5e7eb;
            padding-left: 1rem;
            font-style: italic;
            margin: 1rem 0;
            max-width: 100% !important;
            word-wrap: break-word !important;
          }
          .script-content code, .tips-content code {
            background-color: #f3f4f6;
            padding: 0.2rem 0.4rem;
            border-radius: 0.25rem;
            font-family: monospace;
            word-wrap: break-word !important;
            word-break: break-all !important;
          }
          .script-content img, .tips-content img {
            max-width: 100% !important;
            height: auto !important;
          }
          .script-content div, .tips-content div {
            max-width: 100% !important;
          }
          .script-content table, .tips-content table {
            width: 100% !important;
            max-width: 100% !important;
            table-layout: fixed !important;
          }
          .script-content td, .script-content th, .tips-content td, .tips-content th {
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
          }
        `}</style>
      </div>
    );
  }

  // For other types, use textarea as before
  return (
    <div className="h-full flex flex-col min-h-0 min-h-[300px]">
      <textarea
        value={notes}
        readOnly
        className="flex-1 !overflow-scroll resize-none bg-white font-normal mt-0 senior-friendly-textarea p-4 w-full min-h-0"
        style={{
          fontFamily: "'Public Sans', sans-serif",
          lineHeight: "1.6",
          fontWeight: 400,
          color: "#111827",
          fontSize: fontSize,
          height: "100%",
          minHeight: "0",
        }}
        placeholder={`${type} notes for this slide...`}
      />
    </div>
  )
}

// Main component with Suspense boundary
export default function NotesPanel(props: NotesPanelProps) {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <NotesPanelContent {...props} />
    </Suspense>
  )
}
