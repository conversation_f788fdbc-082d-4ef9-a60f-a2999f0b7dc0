"use client"

import { Button } from "@/components/ui/button"
import { Minus, Plus, Type } from "lucide-react"

interface TextSizeControlProps {
  textSize: number
  onIncrease: () => void
  onDecrease: () => void
  onReset: () => void
}

export default function TextSizeControl({ textSize, onIncrease, onDecrease, onReset }: TextSizeControlProps) {
  return (
    <div className="flex items-center justify-between gap-1 mb-3 p-2 bg-blue-50 rounded-md border border-blue-100 shadow-sm">
      <div className="flex items-center">
        <Type className="h-5 w-5 mr-1 text-blue-600" />
        <span className="text-sm text-blue-700 font-bold">
          Text Size:{" "}
          {textSize === 1
            ? "100%"
            : textSize === 1.25
              ? "125%"
              : textSize === 1.5
                ? "150%"
                : textSize === 1.75
                  ? "175%"
                  : textSize === 2
                    ? "200%"
                    : textSize === 2.25
                      ? "225%"
                      : textSize === 2.5
                        ? "250%"
                        : textSize === 2.75
                          ? "275%"
                          : "300%"}
        </span>
      </div>
      <div className="flex items-center gap-1">
        <Button
          variant="outline"
          size="sm"
          onClick={onDecrease}
          disabled={textSize <= 1}
          className="h-8 w-8 p-0 flex items-center justify-center bg-white"
          title="Decrease text size"
        >
          <Minus className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={onReset}
          className="h-8 px-2 text-sm bg-white font-bold min-w-[70px]"
          title="Reset to default text size"
        >
          Reset ({Math.round(textSize * 100)}%)
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={onIncrease}
          disabled={textSize >= 3}
          className="h-8 w-8 p-0 flex items-center justify-center bg-white"
          title="Increase text size"
        >
          <Plus className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )
}
