"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Loader2, Trash2, FileText, AlertCircle } from "lucide-react"
import { useSession } from "next-auth/react"
import HtmlFileUploader from "./HtmlFileUploader"

interface ReferenceFile {
  _id: string
  filename: string
  content: string
  description: string
  uploadedAt: string
  uploadedBy: string
}

interface ReferenceFilesManagerProps {
  isOpen: boolean
}

export default function ReferenceFilesManager({ isOpen }: ReferenceFilesManagerProps) {
  const { data: session } = useSession()
  const [goodFiles, setGoodFiles] = useState<ReferenceFile[]>([])
  const [badFiles, setBadFiles] = useState<ReferenceFile[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [error, setError] = useState("")


  // Check if user is admin
  const isAdmin = session?.user && (session.user as any)?.role === 'admin'

  useEffect(() => {
    if (isOpen && isAdmin) {
      loadFiles()
    }
  }, [isOpen, isAdmin])

  const loadFiles = async () => {
    setIsLoading(true)
    setError("")

    try {
      const [goodResponse, badResponse] = await Promise.all([
        fetch('/api/reference-files?type=good'),
        fetch('/api/reference-files?type=bad')
      ])

      const goodData = await goodResponse.json()
      const badData = await badResponse.json()

      if (goodData.files) setGoodFiles(goodData.files)
      if (badData.files) setBadFiles(badData.files)
    } catch (err) {
      setError('Failed to load reference files')
      console.error('Error loading reference files:', err)
    } finally {
      setIsLoading(false)
    }
  }



  const deleteFile = async (id: string, type: 'good' | 'bad', filename: string) => {
    if (!confirm(`Are you sure you want to delete "${filename}"?`)) return

    try {
      const response = await fetch(`/api/reference-files?id=${id}&type=${type}`, {
        method: 'DELETE'
      })

      const data = await response.json()

      if (data.success) {
        alert(`✅ File "${filename}" deleted successfully!`)
        await loadFiles() // Reload files
      } else {
        setError(`Failed to delete file: ${data.error}`)
      }
    } catch (err) {
      setError('Failed to delete file')
      console.error('Error deleting file:', err)
    }
  }



  if (!isAdmin) {
    return (
      <div className="p-6 text-center">
        <AlertCircle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
        <p className="text-gray-600" style={{ fontFamily: 'Inter, sans-serif' }}>
          Only administrators can manage reference files.
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Error Display */}
      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-700 text-sm" style={{ fontFamily: 'Inter, sans-serif' }}>{error}</p>
        </div>
      )}

      <Tabs defaultValue="good" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="good" style={{ fontFamily: 'Inter, sans-serif' }}>
            Good Examples ({goodFiles.length})
          </TabsTrigger>
          <TabsTrigger value="bad" style={{ fontFamily: 'Inter, sans-serif' }}>
            Bad Examples ({badFiles.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="good" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle style={{ fontFamily: 'Inter, sans-serif' }}>Upload Good Example</CardTitle>
              <CardDescription style={{ fontFamily: 'Inter, sans-serif' }}>
                Upload HTML files that demonstrate good practices and should be followed
              </CardDescription>
            </CardHeader>
            <CardContent>
              <HtmlFileUploader
                type="good"
                onUploadSuccess={loadFiles}
                isUploading={isUploading}
                onUploadStart={() => setIsUploading(true)}
                onUploadEnd={() => setIsUploading(false)}
                onError={setError}
              />
            </CardContent>
          </Card>

          {/* Good Files List */}
          <div className="space-y-3">
            {isLoading ? (
              <div className="flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
              </div>
            ) : goodFiles.length === 0 ? (
              <Card>
                <CardContent className="p-6 text-center text-gray-500">
                  <FileText className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                  <p style={{ fontFamily: 'Inter, sans-serif' }}>No good examples uploaded yet</p>
                </CardContent>
              </Card>
            ) : (
              goodFiles.map((file) => (
                <Card key={file._id}>
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-lg" style={{ fontFamily: 'Inter, sans-serif' }}>
                          {file.filename}
                        </CardTitle>
                        {file.description && (
                          <CardDescription style={{ fontFamily: 'Inter, sans-serif' }}>
                            {file.description}
                          </CardDescription>
                        )}
                      </div>
                      <Button
                        onClick={() => deleteFile(file._id, 'good', file.filename)}
                        variant="outline"
                        size="sm"
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="text-xs text-gray-500 space-y-1" style={{ fontFamily: 'Inter, sans-serif' }}>
                      <div>Uploaded: {new Date(file.uploadedAt).toLocaleString()}</div>
                      <div>By: {file.uploadedBy}</div>
                      <div>Size: {file.content.length} characters</div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="bad" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle style={{ fontFamily: 'Inter, sans-serif' }}>Upload Bad Example</CardTitle>
              <CardDescription style={{ fontFamily: 'Inter, sans-serif' }}>
                Upload HTML files that demonstrate bad practices and should be avoided
              </CardDescription>
            </CardHeader>
            <CardContent>
              <HtmlFileUploader
                type="bad"
                onUploadSuccess={loadFiles}
                isUploading={isUploading}
                onUploadStart={() => setIsUploading(true)}
                onUploadEnd={() => setIsUploading(false)}
                onError={setError}
              />
            </CardContent>
          </Card>

          {/* Bad Files List */}
          <div className="space-y-3">
            {isLoading ? (
              <div className="flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
              </div>
            ) : badFiles.length === 0 ? (
              <Card>
                <CardContent className="p-6 text-center text-gray-500">
                  <FileText className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                  <p style={{ fontFamily: 'Inter, sans-serif' }}>No bad examples uploaded yet</p>
                </CardContent>
              </Card>
            ) : (
              badFiles.map((file) => (
                <Card key={file._id}>
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-lg" style={{ fontFamily: 'Inter, sans-serif' }}>
                          {file.filename}
                        </CardTitle>
                        {file.description && (
                          <CardDescription style={{ fontFamily: 'Inter, sans-serif' }}>
                            {file.description}
                          </CardDescription>
                        )}
                      </div>
                      <Button
                        onClick={() => deleteFile(file._id, 'bad', file.filename)}
                        variant="outline"
                        size="sm"
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="text-xs text-gray-500 space-y-1" style={{ fontFamily: 'Inter, sans-serif' }}>
                      <div>Uploaded: {new Date(file.uploadedAt).toLocaleString()}</div>
                      <div>By: {file.uploadedBy}</div>
                      <div>Size: {file.content.length} characters</div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
