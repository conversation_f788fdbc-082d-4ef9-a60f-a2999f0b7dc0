"use client"

import React, { useState, useCallback, useRef } from 'react'
import { Upload, X, FileText, Check, AlertCircle } from 'lucide-react'

interface FileUploaderProps {
  onFilesSelected: (files: File[]) => void
  accept?: string
  multiple?: boolean
}

export default function FileUploader({
  onFilesSelected,
  accept = ".json",
  multiple = true
}: FileUploaderProps) {
  const [isDragging, setIsDragging] = useState(false)
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(false)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(false)

    const files = Array.from(e.dataTransfer.files).filter(file => 
      file.type === 'application/json' || file.name.endsWith('.json')
    )

    if (files.length > 0) {
      setSelectedFiles(prev => multiple ? [...prev, ...files] : [files[0]])
      onFilesSelected(multiple ? files : [files[0]])
    }
  }, [multiple, onFilesSelected])

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const files = Array.from(e.target.files)
      setSelectedFiles(prev => multiple ? [...prev, ...files] : [files[0]])
      onFilesSelected(multiple ? files : [files[0]])
    }
  }, [multiple, onFilesSelected])

  const handleButtonClick = useCallback(() => {
    fileInputRef.current?.click()
  }, [])

  const removeFile = useCallback((index: number) => {
    setSelectedFiles(prev => {
      const newFiles = [...prev]
      newFiles.splice(index, 1)
      return newFiles
    })
  }, [])

  const clearFiles = useCallback(() => {
    setSelectedFiles([])
  }, [])

  return (
    <div className="w-full">
      <div
        className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
          isDragging
            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
            : 'border-gray-300 hover:border-blue-400 dark:border-gray-700 dark:hover:border-blue-500'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={handleButtonClick}
      >
        <input
          type="file"
          ref={fileInputRef}
          className="hidden"
          accept={accept}
          multiple={multiple}
          onChange={handleFileInputChange}
        />
        <div className="flex flex-col items-center justify-center space-y-3">
          <div className="p-3 bg-blue-100 rounded-full dark:bg-blue-900/30">
            <Upload className="w-8 h-8 text-blue-500" />
          </div>
          <div className="text-lg font-medium">
            {isDragging ? 'Drop files here' : 'Drag and drop JSON files here'}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            or <span className="text-blue-500 hover:underline">browse files</span>
          </div>
          <div className="text-xs text-gray-400 dark:text-gray-500">
            Only JSON files are accepted
          </div>
        </div>
      </div>

      {selectedFiles.length > 0 && (
        <div className="mt-4">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-lg font-medium">Selected Files ({selectedFiles.length})</h3>
            <button
              onClick={clearFiles}
              className="text-sm text-red-500 hover:text-red-700 dark:hover:text-red-400"
            >
              Clear All
            </button>
          </div>
          <div className="space-y-2 max-h-60 overflow-y-auto">
            {selectedFiles.map((file, index) => (
              <div
                key={`${file.name}-${index}`}
                className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
              >
                <div className="flex items-center space-x-3">
                  <FileText className="w-5 h-5 text-blue-500" />
                  <div>
                    <div className="font-medium truncate max-w-xs">{file.name}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {(file.size / 1024).toFixed(2)} KB
                    </div>
                  </div>
                </div>
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    removeFile(index)
                  }}
                  className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-full"
                >
                  <X className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
