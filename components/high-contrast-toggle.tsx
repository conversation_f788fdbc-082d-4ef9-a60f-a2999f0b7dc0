"use client"

import { useHighContrast } from "./high-contrast-context"

export default function HighContrastToggle() {
  const { highContrast, setHighContrast } = useHighContrast()

  const handleToggle = async () => {
    await setHighContrast(!highContrast)
  }

  return (
    <div className="space-y-1">
      <label className="block font-medium text-gray-600">Display Mode</label>
      <div className="flex items-center justify-between">
        <span className="text-gray-700">High Contrast Mode</span>
        <button
          onClick={handleToggle}
          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-[#2B6DFE] focus:ring-offset-2 ${
            highContrast ? 'bg-[#2B6DFE]' : 'bg-gray-200'
          }`}
        >
          <span
            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
              highContrast ? 'translate-x-6' : 'translate-x-1'
            }`}
          />
        </button>
      </div>
    </div>
  )
}
