"use client"

import React, { useState, useCallback, useRef } from 'react'
import { Upload, X, FileText, Loader2 } from 'lucide-react'
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { But<PERSON> } from "@/components/ui/button"

interface HtmlFileUploaderProps {
  type: 'good' | 'bad'
  onUploadSuccess: () => void
  isUploading: boolean
  onUploadStart: () => void
  onUploadEnd: () => void
  onError: (error: string) => void
}

export default function HtmlFileUploader({
  type,
  onUploadSuccess,
  isUploading,
  onUploadStart,
  onUploadEnd,
  onError
}: HtmlFileUploaderProps) {
  const [isDragging, setIsDragging] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [description, setDescription] = useState("")
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(false)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(false)

    const files = Array.from(e.dataTransfer.files).filter(file => 
      file.type === 'text/html' || file.name.endsWith('.html') || file.name.endsWith('.htm')
    )

    if (files.length > 0) {
      setSelectedFile(files[0])
    }
  }, [])

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      setSelectedFile(files[0])
    }
  }, [])

  const handleButtonClick = useCallback(() => {
    fileInputRef.current?.click()
  }, [])

  const removeFile = useCallback(() => {
    setSelectedFile(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }, [])

  const uploadFile = async () => {
    if (!selectedFile) return

    onUploadStart()

    try {
      const formData = new FormData()
      formData.append('file', selectedFile)
      formData.append('type', type)
      formData.append('description', description)

      const response = await fetch('/api/reference-files', {
        method: 'POST',
        body: formData
      })

      const data = await response.json()

      if (data.success) {
        alert(`✅ ${type === 'good' ? 'Good' : 'Bad'} reference file uploaded successfully!`)
        setDescription("")
        setSelectedFile(null)
        if (fileInputRef.current) {
          fileInputRef.current.value = ''
        }
        onUploadSuccess()
      } else {
        onError(`Failed to upload file: ${data.error}`)
      }
    } catch (err) {
      onError('Failed to upload file')
      console.error('Error uploading file:', err)
    } finally {
      onUploadEnd()
    }
  }

  const typeLabel = type === 'good' ? 'Good' : 'Bad'
  const typeColors = type === 'good' ? {
    border: 'border-green-500',
    borderHover: 'hover:border-green-400',
    bg: 'bg-green-50',
    iconBg: 'bg-green-100',
    iconText: 'text-green-500',
    text: 'text-green-500',
    button: 'bg-green-500 hover:bg-green-600'
  } : {
    border: 'border-red-500',
    borderHover: 'hover:border-red-400',
    bg: 'bg-red-50',
    iconBg: 'bg-red-100',
    iconText: 'text-red-500',
    text: 'text-red-500',
    button: 'bg-red-500 hover:bg-red-600'
  }

  return (
    <div className="space-y-4">
      {/* Description Input */}
      <div>
        <Label htmlFor={`${type}-description`} style={{ fontFamily: 'Inter, sans-serif' }}>
          Description (optional)
        </Label>
        <Textarea
          id={`${type}-description`}
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder={`Describe what makes this a ${type} example...`}
          className="mt-1"
          style={{ fontFamily: 'Inter, sans-serif' }}
          disabled={isUploading}
        />
      </div>

      {/* File Drop Zone */}
      <div className="w-full">
        <div
          className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
            isDragging
              ? `${typeColors.border} ${typeColors.bg}`
              : `border-gray-300 ${typeColors.borderHover}`
          } ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={!isUploading ? handleButtonClick : undefined}
        >
          <input
            type="file"
            ref={fileInputRef}
            className="hidden"
            accept=".html,.htm"
            onChange={handleFileInputChange}
            disabled={isUploading}
          />
          <div className="flex flex-col items-center justify-center space-y-3">
            <div className={`p-3 ${typeColors.iconBg} rounded-full`}>
              <Upload className={`w-6 h-6 ${typeColors.iconText}`} />
            </div>
            <div className="text-lg font-medium" style={{ fontFamily: 'Inter, sans-serif' }}>
              {isDragging ? 'Drop HTML file here' : `Drag and drop ${typeLabel.toLowerCase()} HTML example here`}
            </div>
            <div className="text-sm text-gray-500" style={{ fontFamily: 'Inter, sans-serif' }}>
              or <span className={`${typeColors.text} hover:underline`}>browse files</span>
            </div>
            <div className="text-xs text-gray-400" style={{ fontFamily: 'Inter, sans-serif' }}>
              Only HTML files (.html, .htm) are accepted
            </div>
          </div>
        </div>

        {/* Selected File Display */}
        {selectedFile && (
          <div className="mt-4">
            <div className="bg-gray-50 rounded-lg p-4 border">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 ${typeColors.iconBg} rounded`}>
                    <FileText className={`w-4 h-4 ${typeColors.iconText}`} />
                  </div>
                  <div>
                    <div className="font-medium text-sm" style={{ fontFamily: 'Inter, sans-serif' }}>
                      {selectedFile.name}
                    </div>
                    <div className="text-xs text-gray-500" style={{ fontFamily: 'Inter, sans-serif' }}>
                      {(selectedFile.size / 1024).toFixed(2)} KB
                    </div>
                  </div>
                </div>
                <button
                  onClick={removeFile}
                  disabled={isUploading}
                  className="p-1 hover:bg-gray-200 rounded-full disabled:opacity-50"
                >
                  <X className="w-4 h-4 text-gray-500" />
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Upload Button */}
      {selectedFile && (
        <div className="flex justify-end">
          <Button
            onClick={uploadFile}
            disabled={isUploading}
            className={`${typeColors.button} text-white`}
            style={{ fontFamily: 'Inter, sans-serif' }}
          >
            {isUploading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Uploading...
              </>
            ) : (
              <>
                <Upload className="h-4 w-4 mr-2" />
                Upload {typeLabel} Example
              </>
            )}
          </Button>
        </div>
      )}
    </div>
  )
}
