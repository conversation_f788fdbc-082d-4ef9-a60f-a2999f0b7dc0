"use client"

import { useState, useEffect } from "react"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Check,
  UserRound,
  X,
  ChevronRight,
  ChevronDown,
  CheckSquare,
  Square,
  MinusCircle,
  LayoutGrid,
  SortAsc,
  SortDesc,
} from "lucide-react"
import { Button } from "@/components/ui/button"

type Student = {
  id: number
  name: string
  status: "present" | "absent" | "late"
  hasQuestion: boolean
  participation: number // 0-5 scale
  notes?: string
  feedback: Array<"positive" | "neutral" | "negative">
  comment: string
}

interface ClassListProps {
  compact?: boolean
  expanded?: boolean
}

type FeedbackType = "positive" | "neutral" | "negative" | null
type SortOption = "firstName" | "lastName" | "understanding"
type SortDirection = "asc" | "desc"

// Define a consistent font style to use across components
const fontStyle = { fontFamily: "'Public Sans', sans-serif" }

export default function ClassList({ compact = false, expanded = false }: ClassListProps) {
  // Track which student cards are expanded
  const [expandedStudents, setExpandedStudents] = useState<number[]>([])

  // Track which students are selected for bulk actions
  const [selectedStudents, setSelectedStudents] = useState<number[]>([])

  // Track which feedback type is currently selected for bulk application
  const [bulkFeedbackType, setBulkFeedbackType] = useState<FeedbackType>(null)

  // Track confirmation message visibility
  const [showConfirmation, setShowConfirmation] = useState(false)
  const [confirmationCount, setConfirmationCount] = useState(0)

  // Display customization options
  const [columnCount, setColumnCount] = useState(2)
  const [sortOption, setSortOption] = useState<SortOption>("firstName")
  const [sortDirection, setSortDirection] = useState<SortDirection>("asc")

  const [students, setStudents] = useState<Student[]>([
    {
      id: 1,
      name: "Alex Johnson",
      status: "present",
      hasQuestion: false,
      participation: 3,
      notes: "Strong in algebra",
      feedback: [],
      comment: "",
    },
    {
      id: 2,
      name: "Jamie Smith",
      status: "present",
      hasQuestion: true,
      participation: 1,
      notes: "Visual learner",
      feedback: [],
      comment: "",
    },
    {
      id: 3,
      name: "Avery Thompson",
      status: "present",
      hasQuestion: false,
      participation: 4,
      notes: "Strong critical thinker",
      feedback: [],
      comment: "",
    },
    {
      id: 4,
      name: "Blake Turner",
      status: "absent",
      hasQuestion: false,
      participation: 0,
      notes: "Family emergency",
      feedback: [],
      comment: "",
    },
    {
      id: 5,
      name: "Cameron Wright",
      status: "present",
      hasQuestion: true,
      participation: 2,
      notes: "Needs more confidence",
      feedback: [],
      comment: "",
    },
    {
      id: 6,
      name: "Casey Wilson",
      status: "absent",
      hasQuestion: false,
      participation: 0,
      notes: "Missed classes",
      feedback: [],
      comment: "",
    },
    {
      id: 7,
      name: "Dakota Hill",
      status: "present",
      hasQuestion: false,
      participation: 2,
      notes: "Needs extra practice",
      feedback: [],
      comment: "",
    },
    {
      id: 8,
      name: "Drew Martinez",
      status: "present",
      hasQuestion: false,
      participation: 4,
      notes: "Helps other students",
      feedback: [],
      comment: "",
    },
    {
      id: 9,
      name: "Emerson King",
      status: "present",
      hasQuestion: false,
      participation: 3,
      notes: "Good with geometry",
      feedback: [],
      comment: "",
    },
    {
      id: 10,
      name: "Finley Lopez",
      status: "late",
      hasQuestion: false,
      participation: 2,
      notes: "Needs help with word problems",
      feedback: [],
      comment: "",
    },
    {
      id: 11,
      name: "Hayden Scott",
      status: "absent",
      hasQuestion: false,
      participation: 0,
      notes: "Medical absence",
      feedback: [],
      comment: "",
    },
    {
      id: 12,
      name: "Jordan Lee",
      status: "present",
      hasQuestion: false,
      participation: 5,
      notes: "Excels at problem-solving",
      feedback: [],
      comment: "",
    },
    {
      id: 13,
      name: "Jordan Evans",
      status: "late",
      hasQuestion: false,
      participation: 2,
      notes: "Improving steadily",
      feedback: [],
      comment: "",
    },
    {
      id: 14,
      name: "Morgan Davis",
      status: "present",
      hasQuestion: true,
      participation: 4,
      notes: "Good at group work",
      feedback: [],
      comment: "",
    },
    {
      id: 15,
      name: "Olivia Carter",
      status: "present",
      hasQuestion: false,
      participation: 3,
      notes: "Creative problem solver",
      feedback: [],
      comment: "",
    },
    {
      id: 16,
      name: "Parker Robinson",
      status: "late",
      hasQuestion: true,
      participation: 1,
      notes: "Catching up after absence",
      feedback: [],
      comment: "",
    },
    {
      id: 17,
      name: "Quinn Miller",
      status: "present",
      hasQuestion: false,
      participation: 3,
      notes: "Good with word problems",
      feedback: [],
      comment: "",
    },
    {
      id: 18,
      name: "Riley Garcia",
      status: "present",
      hasQuestion: false,
      participation: 3,
      notes: "Needs help with fractions",
      feedback: [],
      comment: "",
    },
    {
      id: 19,
      name: "Skyler Adams",
      status: "present",
      hasQuestion: true,
      participation: 2,
      notes: "Visual spatial learner",
      feedback: [],
      comment: "",
    },
    {
      id: 20,
      name: "Taylor Brown",
      status: "late",
      hasQuestion: false,
      participation: 2,
      notes: "Working on focus",
      feedback: [],
      comment: "",
    },
  ])

  // Track filtered students for search/filter functionality
  const [filteredStudents, setFilteredStudents] = useState<Student[]>([])

  // Initialize filteredStudents with all students when component mounts
  useEffect(() => {
    setFilteredStudents(students)
  }, [students])

  // Make sure filteredStudents is always updated when sortOption changes
  useEffect(() => {
    // Start with all students
    const currentStudents = [...students]

    // Then apply sorting
    currentStudents.sort((a, b) => {
      let comparison = 0

      switch (sortOption) {
        case "firstName":
          comparison = a.name.split(" ")[0].localeCompare(b.name.split(" ")[0])
          break
        case "lastName":
          const aLastName = a.name.split(" ").slice(-1)[0]
          const bLastName = b.name.split(" ").slice(-1)[0]
          comparison = aLastName.localeCompare(bLastName)
          break
        case "understanding":
          // Get the last feedback entry for each student
          const aLastFeedback = a.feedback.length > 0 ? a.feedback[a.feedback.length - 1] : null
          const bLastFeedback = b.feedback.length > 0 ? b.feedback[b.feedback.length - 1] : null

          // If neither has feedback, they're equal
          if (!aLastFeedback && !bLastFeedback) return 0

          // If only one has feedback, prioritize the one with feedback
          if (!aLastFeedback) return 1
          if (!bLastFeedback) return -1

          // Define priority: positive (green) > neutral (yellow) > negative (red)
          const getPriority = (feedback: "positive" | "neutral" | "negative") => {
            if (feedback === "positive") return 0
            if (feedback === "neutral") return 1
            return 2 // negative
          }

          // Sort by priority (lower number = higher priority)
          comparison = getPriority(aLastFeedback) - getPriority(bLastFeedback)
          break
        default:
          comparison = 0
      }

      // Apply sort direction
      return sortDirection === "asc" ? comparison : -comparison
    })

    setFilteredStudents(currentStudents)
  }, [students, sortOption, sortDirection])

  // Toggle expanded state for a student
  const toggleExpanded = (id: number) => {
    if (bulkFeedbackType !== null) {
      toggleSelected(id)
      return
    }

    if (expandedStudents.includes(id)) {
      setExpandedStudents(expandedStudents.filter((studentId) => studentId !== id))
    } else {
      setExpandedStudents([...expandedStudents, id])
    }
  }

  // Toggle selected state for a student
  const toggleSelected = (id: number) => {
    if (selectedStudents.includes(id)) {
      setSelectedStudents(selectedStudents.filter((studentId) => studentId !== id))
    } else {
      setSelectedStudents([...selectedStudents, id])
    }
  }

  // Select or deselect all students
  const toggleSelectAll = () => {
    if (selectedStudents.length === students.length) {
      setSelectedStudents([])
    } else {
      setSelectedStudents(students.map((student) => student.id))
    }
  }

  // Apply bulk feedback
  const applyBulkFeedback = () => {
    if (bulkFeedbackType === null || selectedStudents.length === 0) return

    setStudents(
      students.map((student) =>
        selectedStudents.includes(student.id)
          ? { ...student, feedback: [...student.feedback, bulkFeedbackType] }
          : student,
      ),
    )

    // Show confirmation message
    const count = selectedStudents.length
    setConfirmationCount(count)
    setShowConfirmation(true)

    // Hide confirmation after 4 seconds
    setTimeout(() => {
      setShowConfirmation(false)
    }, 4000)

    // Reset selection and exit bulk mode after applying
    setSelectedStudents([])
    setBulkFeedbackType(null)
  }

  // Toggle bulk feedback mode
  const toggleBulkFeedback = (type: FeedbackType) => {
    if (bulkFeedbackType === type) {
      setBulkFeedbackType(null)
      setSelectedStudents([])
    } else {
      setBulkFeedbackType(type)
      setSelectedStudents([])
    }
  }

  const addFeedback = (id: number, type: "positive" | "neutral" | "negative") => {
    setStudents(
      students.map((student) => (student.id === id ? { ...student, feedback: [...student.feedback, type] } : student)),
    )

    // Show confirmation message
    setConfirmationCount(1)
    setShowConfirmation(true)

    // Hide confirmation after 4 seconds
    setTimeout(() => {
      setShowConfirmation(false)
    }, 4000)
  }

  const removeFeedback = (studentId: number, feedbackIndex: number) => {
    setStudents(
      students.map((student) => {
        if (student.id === studentId) {
          const newFeedback = [...student.feedback]
          newFeedback.splice(feedbackIndex, 1)
          return { ...student, feedback: newFeedback }
        }
        return student
      }),
    )
  }

  const updateComment = (id: number, comment: string) => {
    setStudents(students.map((student) => (student.id === id ? { ...student, comment } : student)))
  }

  // Toggle between expanding all and collapsing all students
  const toggleAllExpanded = () => {
    if (expandedStudents.length === filteredStudents.length) {
      // If all are expanded, collapse all
      setExpandedStudents([])
    } else {
      // Otherwise, expand all
      setExpandedStudents(filteredStudents.map((student) => student.id))
    }
  }

  // Function to organize students into columns (vertical sorting)
  const organizeStudentsIntoColumns = (students: Student[], columnCount: number) => {
    const totalStudents = students.length
    const studentsPerColumn = Math.ceil(totalStudents / columnCount)
    const columns: Student[][] = []

    // Create empty columns
    for (let i = 0; i < columnCount; i++) {
      columns.push([])
    }

    // Fill columns one by one
    for (let i = 0; i < totalStudents; i++) {
      const columnIndex = Math.floor(i / studentsPerColumn)
      if (columnIndex < columnCount) {
        columns[columnIndex].push(students[i])
      }
    }

    return columns
  }

  // Function to get the display text for the current sort option
  const getSortOptionDisplayText = () => {
    switch (sortOption) {
      case "firstName":
        return "First Name"
      case "lastName":
        return "Last Name"
      case "understanding":
        return "Last Feedback"
      default:
        return "First Name"
    }
  }

  // Function to cycle through sort options
  const cycleSortOption = () => {
    if (sortOption === "firstName") setSortOption("lastName")
    else if (sortOption === "lastName") setSortOption("understanding")
    else setSortOption("firstName")
  }

  // Function to toggle sort direction
  const toggleSortDirection = () => {
    setSortDirection(sortDirection === "asc" ? "desc" : "asc")
  }

  return (
    <div className="h-full flex flex-col relative rounded-lg border text-card-foreground p-4 bg-white shadow-lg flex-1 senior-friendly-card" style={fontStyle}>
      {/* Search and Filter Bar */}
      <div className="mb-2 flex items-center gap-2">
        <div className="relative w-full h-full">
          <input
            type="text"
            placeholder="Search students..."
            className="w-full h-8 pl-8 pr-2 text-sm border border-gray-200 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-400"
            style={fontStyle}
            onChange={(e) => {
              const searchTerm = e.target.value.toLowerCase()
              if (searchTerm === "") {
                // Reset to show all students
                setFilteredStudents([...students])
              } else {
                // Filter students by name
                setFilteredStudents(students.filter((student) => student.name.toLowerCase().includes(searchTerm)))
              }
            }}
          />
          <UserRound className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        </div>
      </div>

      {/* Quick Add Section - Redesigned */}
      <div className="mb-2 p-2 border border-gray-200 rounded-md bg-gray-50">
        <div className="flex flex-col">
          <div className="flex items-center justify-between mb-2">
            <div className="flex flex-wrap items-center gap-3">
              <div className="text-sm font-bold text-blue-400 mr-1" style={fontStyle}>
                Quick Feedback
              </div>

              {/* Display customization options */}
              <div className="flex flex-wrap items-center gap-2 border-l border-gray-300 pl-3">
                {/* Column selector - button style */}
                <div className="flex items-center gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setColumnCount(columnCount === 4 ? 1 : columnCount + 1)}
                    className="h-7 text-xs whitespace-nowrap flex items-center gap-1"
                    style={fontStyle}
                  >
                    <LayoutGrid className="h-3.5 w-3.5" />
                    {columnCount} {columnCount === 1 ? "Column" : "Columns"}
                  </Button>
                </div>

                {/* Sort selector - split into two buttons */}
                <div className="flex items-center gap-0.5">
                  {/* Sort direction button */}
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={toggleSortDirection}
                    className="h-7 w-7 p-0 flex items-center justify-center"
                    title={sortDirection === "asc" ? "Ascending (A-Z)" : "Descending (Z-A)"}
                  >
                    {sortDirection === "asc" ? (
                      <SortAsc className="h-3.5 w-3.5" />
                    ) : (
                      <SortDesc className="h-3.5 w-3.5" />
                    )}
                  </Button>

                  {/* Sort option button */}
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={cycleSortOption}
                    className="h-7 text-xs whitespace-nowrap flex items-center gap-1"
                    style={fontStyle}
                  >
                    {getSortOptionDisplayText()}
                  </Button>
                </div>
              </div>

              {/* Open All/Collapse All button */}
              <Button
                size="sm"
                variant="outline"
                onClick={toggleAllExpanded}
                className="h-7 text-xs whitespace-nowrap ml-auto"
                style={fontStyle}
              >
                {expandedStudents.length === filteredStudents.length ? "Collapse All" : "Open All"}
              </Button>
            </div>

            {bulkFeedbackType !== null && (
              <div className="flex items-center gap-2">
                <span className="text-xs text-gray-500" style={fontStyle}>
                  {selectedStudents.length} selected
                </span>
                <Button
                  size="sm"
                  className="h-7 px-2 bg-blue-400 text-white hover:bg-blue-500 font-bold"
                  onClick={toggleSelectAll}
                  style={fontStyle}
                >
                  {selectedStudents.length === students.length ? "Deselect All" : "Select All"}
                </Button>
                <Button
                  size="sm"
                  className="h-7 px-2 bg-blue-400 text-white hover:bg-blue-500 font-bold"
                  onClick={applyBulkFeedback}
                  style={fontStyle}
                >
                  Apply to Selected
                </Button>
              </div>
            )}
          </div>

          {/* Replace the buttons section with simplified versions without text labels */}
          <div className="flex gap-3">
            <button
              onClick={() => toggleBulkFeedback("positive")}
              className={`flex-1 p-2 rounded-md flex items-center justify-center transition-colors ${
                bulkFeedbackType === "positive"
                  ? "bg-green-100 border-2 border-green-400"
                  : "bg-white border border-gray-200 hover:bg-green-50"
              }`}
              title="Positive feedback"
            >
              <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center">
                <Check className="h-5 w-5 text-green-600" />
              </div>
            </button>

            <button
              onClick={() => toggleBulkFeedback("neutral")}
              className={`flex-1 p-2 rounded-md flex items-center justify-center transition-colors ${
                bulkFeedbackType === "neutral"
                  ? "bg-yellow-100 border-2 border-yellow-400"
                  : "bg-white border border-gray-200 hover:bg-yellow-50"
              }`}
              title="Neutral feedback"
            >
              <div className="w-8 h-8 rounded-full bg-yellow-100 flex items-center justify-center">
                <MinusCircle className="h-5 w-5 text-yellow-600" />
              </div>
            </button>

            <button
              onClick={() => toggleBulkFeedback("negative")}
              className={`flex-1 p-2 rounded-md flex items-center justify-center transition-colors ${
                bulkFeedbackType === "negative"
                  ? "bg-red-100 border-2 border-red-400"
                  : "bg-white border border-gray-200 hover:bg-red-50"
              }`}
              title="Negative feedback"
            >
              <div className="w-8 h-8 rounded-full bg-red-100 flex items-center justify-center">
                <X className="h-5 w-5 text-red-600" />
              </div>
            </button>
          </div>
        </div>
      </div>

      {/* Simplified Confirmation message - always green */}
      {showConfirmation && (
        <div
          className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2
          rounded-lg shadow-lg z-50 animate-fade-in-out
          flex items-center gap-4 px-8 py-6
          bg-green-600 bg-opacity-90 text-white"
          style={fontStyle}
        >
          <div className="w-10 h-10 rounded-full bg-white flex items-center justify-center">
            <Check className="h-6 w-6 text-green-600" />
          </div>
          <div>
            <div className="text-lg font-medium">
              Applied feedback to {confirmationCount} student{confirmationCount !== 1 ? "s" : ""}
            </div>
            <div className="text-sm opacity-80">Changes applied successfully</div>
          </div>
        </div>
      )}

      <ScrollArea className="flex-1">
        {filteredStudents.length === 0 ? (
          <div className="flex items-center justify-center h-32 text-gray-500" style={fontStyle}>
            No students match your search
          </div>
        ) : (
          <div className="flex gap-1">
            {/* Organize students into columns and render each column */}
            {organizeStudentsIntoColumns(filteredStudents, columnCount).map((column, columnIndex) => (
              <div key={columnIndex} className="flex-1">
                <div className="flex flex-col gap-1">
                  {column.map((student) => {
                    const isExpanded = expandedStudents.includes(student.id)
                    const isSelected = selectedStudents.includes(student.id)

                    return (
                      <div key={student.id} className="rounded-md border border-gray-200 bg-white">
                        {/* Collapsed header - always visible */}
                        <div
                          className={`flex items-center justify-between p-2 cursor-pointer ${
                            bulkFeedbackType !== null && isSelected ? "bg-blue-50" : ""
                          }`}
                          onClick={() => toggleExpanded(student.id)}
                        >
                          <div className="flex items-center gap-1">
                            {bulkFeedbackType !== null ? (
                              isSelected ? (
                                <CheckSquare className="h-4 w-4 text-blue-600" />
                              ) : (
                                <Square className="h-4 w-4 text-gray-400" />
                              )
                            ) : (
                              <UserRound className="h-3.5 w-3.5 text-gray-400" />
                            )}
                            <span
                              className="text-gray-900 font-medium text-sm"
                              style={{
                                fontFamily: "'Public Sans', sans-serif",
                                fontSize: "0.875rem",
                                fontWeight: 400,
                                lineHeight: "1.5",
                              }}
                            >
                              {student.name}
                            </span>
                            {student.feedback.length > 0 && (
                              <div className="flex gap-0.5 ml-1">
                                {student.feedback.slice(-3).map((type, index) => (
                                  <span
                                    key={index}
                                    className={`inline-flex items-center justify-center w-3 h-3 rounded-full ${
                                      type === "positive"
                                        ? "bg-green-100 text-green-600"
                                        : type === "neutral"
                                          ? "bg-yellow-100 text-yellow-600"
                                          : "bg-red-100 text-red-600"
                                    }`}
                                  >
                                    {type === "positive" ? (
                                      <Check className="h-2 w-2" />
                                    ) : type === "neutral" ? (
                                      <MinusCircle className="h-2 w-2" />
                                    ) : (
                                      <X className="h-2 w-2" />
                                    )}
                                  </span>
                                ))}
                                {student.feedback.length > 3 && (
                                  <span className="text-xs text-gray-500 ml-0.5" style={fontStyle}>
                                    +{student.feedback.length - 3}
                                  </span>
                                )}
                              </div>
                            )}
                          </div>

                          {bulkFeedbackType === null &&
                            (isExpanded ? (
                              <ChevronDown className="h-4 w-4 text-gray-400" />
                            ) : (
                              <ChevronRight className="h-4 w-4 text-gray-400" />
                            ))}
                        </div>

                        {/* Expanded content - only visible when expanded and not in bulk mode */}
                        {isExpanded && bulkFeedbackType === null && (
                          <div className="p-2 pt-0 border-t border-gray-100">
                            <div className="flex flex-col gap-2 mt-2">
                              {/* Add feedback controls */}
                              <div className="bg-gray-50 p-2 rounded-md">
                                <div className="text-xs font-bold text-blue-400 mb-1" style={fontStyle}>
                                  Add Feedback
                                </div>
                                <div className="flex gap-2">
                                  <button
                                    onClick={() => addFeedback(student.id, "positive")}
                                    className="p-2 rounded-md bg-white border border-gray-200 hover:bg-green-50"
                                    title="Positive feedback"
                                  >
                                    <div className="w-6 h-6 rounded-full bg-green-100 flex items-center justify-center">
                                      <Check className="h-4 w-4 text-green-600 font-bold" />
                                    </div>
                                  </button>
                                  <button
                                    onClick={() => addFeedback(student.id, "neutral")}
                                    className="p-2 rounded-md bg-white border border-gray-200 hover:bg-yellow-50"
                                    title="Neutral feedback"
                                  >
                                    <div className="w-6 h-6 rounded-full bg-yellow-100 flex items-center justify-center">
                                      <MinusCircle className="h-4 w-4 text-yellow-600" />
                                    </div>
                                  </button>
                                  <button
                                    onClick={() => addFeedback(student.id, "negative")}
                                    className="p-2 rounded-md bg-white border border-gray-200 hover:bg-red-50"
                                    title="Negative feedback"
                                  >
                                    <div className="w-6 h-6 rounded-full bg-red-100 flex items-center justify-center">
                                      <X className="h-4 w-4 text-red-600 font-bold" />
                                    </div>
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    )
                  })}
                </div>
              </div>
            ))}
          </div>
        )}
      </ScrollArea>

      {/* Coming Soon overlay - more flexible for resizing */}
      <div className="absolute inset-0 bg-gray-900/70 backdrop-blur-sm flex flex-col items-center justify-center rounded-lg overflow-hidden">
        <div className="text-4xl md:text-5xl font-bold text-white mb-4 text-center px-4 responsive-text">Coming Soon</div>
        <div className="text-base sm:text-lg md:text-xl text-white/80 max-w-md text-center px-6 responsive-text">
          Student tracking and feedback features will be available in the next update
        </div>
      </div>
    </div>
  )
}
