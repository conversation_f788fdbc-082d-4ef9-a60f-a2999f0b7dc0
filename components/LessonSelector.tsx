"use client"

import React, { useState, useEffect } from "react"
import { ChevronDown } from "lucide-react"

interface LessonSelectorProps {
  onLessonSelect: (unitNumber: string) => void
  currentUnitNumber: string
}

export const LessonSelector: React.FC<LessonSelectorProps> = ({
  onLessonSelect,
  currentUnitNumber
}) => {
  const [standards, setStandards] = useState("Common Core (CCSS)")
  const [grade, setGrade] = useState("Grade 5")
  const [unit, setUnit] = useState(`Unit ${currentUnitNumber}: Numbers`)
  const [lesson, setLesson] = useState("Lesson 1: Counting On")
  
  // Update unit title based on unit number
  useEffect(() => {
    if (currentUnitNumber === "2") {
      setUnit("Unit 2: Numbers")
    } else if (currentUnitNumber === "5") {
      setUnit("Unit 5: Fractions")
    }
  }, [currentUnitNumber])

  const handleUnitChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedUnit = e.target.value
    setUnit(selectedUnit)
    
    // Extract unit number from the selected unit
    const unitNumber = selectedUnit.split(":")[0].replace("Unit ", "").trim()
    
    // Update lesson title based on unit
    if (unitNumber === "2") {
      setLesson("Lesson 1: Counting On")
    } else if (unitNumber === "5") {
      setLesson("Lesson 1: Adding Fractions")
    }
    
    // Notify parent component about the unit change
    onLessonSelect(unitNumber)
  }

  return (
    <div className="flex items-center space-x-2 p-2 bg-blue-600 text-white">
      <div className="relative">
        <select
          value={standards}
          onChange={(e) => setStandards(e.target.value)}
          className="appearance-none bg-blue-500 text-white px-4 py-2 pr-8 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-300 cursor-pointer"
        >
          <option>Common Core (CCSS)</option>
          <option>TEKS (Texas)</option>
          <option>Florida B.E.S.T.</option>
        </select>
        <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 pointer-events-none" />
      </div>
      
      <div className="relative">
        <select
          value={grade}
          onChange={(e) => setGrade(e.target.value)}
          className="appearance-none bg-blue-500 text-white px-4 py-2 pr-8 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-300 cursor-pointer"
        >
          <option>Grade 5</option>
          <option>Grade 4</option>
          <option>Grade 3</option>
        </select>
        <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 pointer-events-none" />
      </div>
      
      <div className="relative">
        <select
          value={unit}
          onChange={handleUnitChange}
          className="appearance-none bg-blue-500 text-white px-4 py-2 pr-8 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-300 cursor-pointer"
        >
          <option>Unit 2: Numbers</option>
          <option>Unit 5: Fractions</option>
        </select>
        <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 pointer-events-none" />
      </div>
      
      <div className="relative">
        <select
          value={lesson}
          onChange={(e) => setLesson(e.target.value)}
          className="appearance-none bg-blue-500 text-white px-4 py-2 pr-8 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-300 cursor-pointer"
        >
          <option>{unit === "Unit 2: Numbers" ? "Lesson 1: Counting On" : "Lesson 1: Adding Fractions"}</option>
        </select>
        <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 pointer-events-none" />
      </div>
      
      <button
        className="bg-yellow-400 hover:bg-yellow-500 text-blue-900 font-bold py-2 px-4 rounded"
      >
        Go
      </button>
    </div>
  )
}

export default LessonSelector
