"use client"

import React, { useEffect, useState } from 'react'
import { X, Maximize2, Minimize2, ChevronUp, ChevronDown } from 'lucide-react'
import { usePresenter } from '@/components/presenter-context'

interface HtmlFullscreenModalProps {
  isOpen: boolean
  onClose: () => void
  html: string
  title?: string
  slideData?: any
  showSidebar?: boolean
  revealedItems?: number[]
  setRevealedItems?: (items: number[]) => void
}

export default function HtmlFullscreenModal({
  isOpen,
  onClose,
  html,
  title = 'HTML Content',
  slideData,
  showSidebar = false,
  revealedItems = [],
  setRevealedItems = () => {}
}: HtmlFullscreenModalProps) {
  const { isShown, setIsShown } = usePresenter()

  // Handler to show first item when no items are revealed
  const handleShowFirst = () => {
    if (revealedItems.length === 0) {
      setRevealedItems([0]) // Reveal first item
      setIsShown(true) // Show sidebar
    }
  }

  // Use showSidebar prop, but default to true for audience view
  const shouldShowSidebar = showSidebar || true
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [showAll, setShowAll] = useState(false)

  // Function to get display name for slide
  const getSlideDisplayName = (slideNumber: number, slideData?: any) => {
    if (slideData?.slide_pedagogical_name) {
      return slideData.slide_pedagogical_name
    }

    // Fallback based on slide number
    switch (slideNumber) {
      case 1: return "Quick Review"
      case 2: return "Learning Goals"
      case 3: return "Vocabulary"
      case 4: return "Hook 1"
      case 5: return "Hook 2"
      case 6: return "Hook 3"
      case 7: return "Teach 1.1"
      case 8: return "Teach 1.2"
      case 9: return "Teach 1.3"
      case 10: return "Talk 1.1"
      case 11: return "Talk 1.2"
      case 12: return "Talk 1.3"
      case 13: return "Try 1"
      case 14: return "Teach 2.1"
      case 15: return "Teach 2.2"
      case 16: return "Teach 2.3"
      case 17: return "Talk 2.1"
      case 18: return "Talk 2.2"
      case 19: return "Talk 2.3"
      case 20: return "Try 2"
      case 21: return "Practice"
      case 22: return "On-Ramp Teach"
      case 23: return "On-Ramp Talk"
      case 24: return "On-Ramp Try"
      case 25: return "Lesson Guide"
      case 26: return "Practice PDF"
      case 27: return "Accelerator PDF"
      default: return `Slide ${slideNumber}`
    }
  }

  // Extract slide number from title (e.g., "Slide 8" -> 8)
  const slideNumber = parseInt(title.replace('Slide ', '')) || 1
  const displayName = getSlideDisplayName(slideNumber, slideData)

  // Extract slide content from slideData as structured objects
  const getSlideContent = () => {
    if (!slideData) return []

    const content: Array<{type: string, content: string, isAnswer?: boolean, questionIndex: number, globalIndex: number}> = []
    let globalIndex = 0

    // Debug: log all slideData keys to see what's available
    if (process.env.NODE_ENV === "development") {
      console.log('slideData keys:', Object.keys(slideData))
      console.log('slideData values:', slideData)
    }

    // For slides with questions and answers (q1/a1, q2/a2, etc.)
    for (let i = 1; i <= 12; i++) {
      const question = slideData[`q${i}`]
      const answer = slideData[`a${i}`]

      if (question) {
        // Add question
        content.push({
          type: 'qa',
          content: question,
          isAnswer: false,
          questionIndex: i,
          globalIndex: globalIndex++
        })

        // Add answer if exists
        if (answer) {
          content.push({
            type: 'qa',
            content: answer,
            isAnswer: true,
            questionIndex: i,
            globalIndex: globalIndex++
          })
        }
      }
    }

    // For talk slides
    if (slideData.slide_q) {
      // Add question
      content.push({
        type: 'talk',
        content: slideData.slide_q,
        isAnswer: false,
        questionIndex: 1,
        globalIndex: globalIndex++
      })

      // Add answer if exists
      if (slideData.slide_a) {
        content.push({
          type: 'talk',
          content: slideData.slide_a,
          isAnswer: true,
          questionIndex: 1,
          globalIndex: globalIndex++
        })
      }
    }

    // For vocabulary slides
    for (let i = 1; i <= 4; i++) {
      const term = slideData[`term_${i}`]
      const definition = slideData[`definition_${i}_with_emoji`]

      if (term) {
        // Add term
        content.push({
          type: 'vocabulary',
          content: term,
          isAnswer: false,
          questionIndex: i,
          globalIndex: globalIndex++
        })

        // Add definition if exists
        if (definition) {
          content.push({
            type: 'vocabulary',
            content: definition,
            isAnswer: true,
            questionIndex: i,
            globalIndex: globalIndex++
          })
        }
      }
    }

    // For slides with text content (slide_text_1, slide_text_2, etc.)
    for (let i = 1; i <= 4; i++) {
      const text = slideData[`slide_text_${i}`]
      if (text) {
        content.push({
          type: 'text',
          content: text,
          isAnswer: false,
          questionIndex: i,
          globalIndex: globalIndex++
        })
      }
    }

    // For learning goals (lg_1, lg_2, etc.)
    for (let i = 1; i <= 4; i++) {
      const goal = slideData[`lg_${i}`]
      if (goal) {
        content.push({
          type: 'goal',
          content: goal,
          isAnswer: false,
          questionIndex: i,
          globalIndex: globalIndex++
        })
      }
    }

    return content
  }

  const slideContent = getSlideContent()

  // Calculate if any items are revealed to determine Show/Hide state
  const hasRevealedItems = revealedItems.length > 0

  // Update showAll based on revealed items
  useEffect(() => {
    setShowAll(hasRevealedItems)
  }, [hasRevealedItems])

  // Control sidebar: only show when both conditions are met
  useEffect(() => {
    setSidebarOpen(hasRevealedItems && isShown)
  }, [hasRevealedItems, isShown])

  // Debug logging
  useEffect(() => {
    if (process.env.NODE_ENV === "development") {
      console.log('🔍 HtmlFullscreenModal props:', {
        isOpen,
        showSidebar,
        shouldShowSidebar,
        slideData: slideData ? 'exists' : 'null',
        slideDataType: typeof slideData,
        slideDataKeys: slideData ? Object.keys(slideData) : 'none',
        slideContent: slideContent.length,
        htmlProp: html ? `exists (${html.length} chars)` : 'empty',
        htmlPreview: html ? html.substring(0, 100) + '...' : 'none',
        revealedItems: revealedItems,
        revealedItemsLength: revealedItems.length,
        hasRevealedItems: hasRevealedItems,
        showAll: showAll
      })

      if (slideData) {
        console.log('🔍 Full slideData object:', slideData)
        console.log('🔍 HTML from slideData:', slideData.html_css_description_of_image ?
          slideData.html_css_description_of_image.substring(0, 100) + '...' : 'none')
      }
    }
  }, [isOpen, showSidebar, shouldShowSidebar, slideData, slideContent, html, revealedItems, hasRevealedItems, showAll])

  // Handle escape key to close modal
  useEffect(() => {
    const handleEsc = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose()
      }
    }
    window.addEventListener('keydown', handleEsc)
    return () => {
      window.removeEventListener('keydown', handleEsc)
    }
  }, [onClose])

  // Toggle fullscreen mode
  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      const modalElement = document.getElementById('html-fullscreen-modal')
      if (modalElement) {
        modalElement.requestFullscreen().catch(err => {
          console.error(`Error attempting to enable fullscreen: ${err.message}`)
        })
        setIsFullscreen(true)
      }
    } else {
      document.exitFullscreen()
      setIsFullscreen(false)
    }
  }

  // Listen for fullscreen change events
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange)
    }
  }, [])

  if (!isOpen) return null

  // Check if the HTML content is a full HTML document
  const isFullHtmlDocument = html.includes('<!DOCTYPE html>') ||
                           (html.includes('<html') && html.includes('<body'))

  const hasHtmlContent = html && html.trim() && html.trim() !== ''

  return (
    <div className="fixed inset-0 z-[1000] flex items-center justify-center bg-black/80">
      <div
        id="html-fullscreen-modal"
        className="w-[98vw] h-[98vh] bg-white dark:bg-gray-800 rounded-lg shadow-xl flex flex-col"
      >
        {/* Header */}
        <div className="flex items-center  bg-[linear-gradient(150deg,#2B6DFE_0%,#2B6DFE_70%,#00F2FF_100%)]   justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-white">
            {displayName}
          </h2>
          <div className="flex items-center space-x-2">
            <button
              onClick={toggleFullscreen}
              className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              aria-label={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
            >
              {isFullscreen ? <Minimize2 size={20} /> : <Maximize2 size={20} />}
            </button>
            <button
              onClick={onClose}
              className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              aria-label="Close"
            >
              <X size={20} />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1  overflow-hidden p-4 bg-white dark:bg-gray-900 relative">
          {hasHtmlContent ? (
            isFullHtmlDocument ? (
              <iframe
                srcDoc={html}
                className="w-full h-full border-0"
                sandbox="allow-scripts allow-same-origin"
                loading="eager"
                title="HTML Content"
                style={{
                  width: "100%",
                  height: "calc(100vh - 120px)", // Account for header and padding
                  minHeight: "400px",
                  background: "white",
                  border: "1px solid #ddd",
                  borderRadius: "4px"
                }}
              />
            ) : (
              <div
                className="w-full h-full border border-gray-200 dark:border-gray-700 rounded-md bg-white dark:bg-gray-800 overflow-auto"
                style={{
                  height: "calc(100vh - 120px)", // Account for header and padding
                  minHeight: "400px",
                  padding: "1rem"
                }}
                dangerouslySetInnerHTML={{ __html: html }}
              />
            )
          ) : (
            <div className="w-full h-full flex items-center justify-center text-gray-500 text-lg">
              No HTML content available
            </div>
          )}

        </div>

        {/* Sidebar toggle button */}
        {shouldShowSidebar && (
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-50">
            {hasRevealedItems ? (
              <button
                onClick={() => setIsShown(!isShown)}
                className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-full shadow-lg hover:bg-blue-700 transition-colors"
              >
                <span className="text-sm font-medium">
                  {isShown ? 'Hide' : 'Show'}
                </span>
                {isShown ? <ChevronDown size={16} /> : <ChevronUp size={16} />}
              </button>
            ) : slideContent.length > 0 && (
              <button
                onClick={handleShowFirst}
                className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-full shadow-lg transition-colors"
              >
                <span className="text-sm font-medium">
                  Show
                </span>
                <ChevronUp size={16} />
              </button>
            )}
          </div>
        )}


        {/* Sidebar with questions/content */}
        {shouldShowSidebar && (
          <div className={`absolute pt-10 bottom-0 left-20 right-20 bg-[linear-gradient(150deg,#2B6DFE_0%,#2B6DFE_70%,#00F2FF_100%)] rounded-t-lg transition-transform duration-300 ease-in-out ${
            sidebarOpen ? 'transform translate-y-0' : 'transform translate-y-full'
          }`} >
            <div className="px-6 pb-6 h-full overflow-y-auto">
              <div className="space-y-6 rounded-xl p-6 relative bg-white/10" >
                {/* <h2 className="text-2xl font-bold text-white mb-4 pt-4">
                  {displayName}
                </h2> */}

                {/* <div className="flex justify-end mb-4">
                  <button
                    onClick={() => {
                      if (hasRevealedItems) {
                        // If any items are revealed, hide all
                        setRevealedItems([])
                      } else {
                        // If no items are revealed, show all
                        const allItems = slideContent.length > 0
                          ? slideContent.map(item => item.globalIndex)
                          : [0, 1, 2, 3] // test data indices
                        setRevealedItems(allItems)
                      }
                    }}
                    className="flex items-center gap-2 text-sm cursor-pointer text-white hover:text-[#fadb9a]"
                  >
                    <span>{hasRevealedItems ? 'Hide All1' : 'Show All'}</span>
                    <Eye size={18} className="text-[#fadb9a]" />
                  </button>
                </div> */}

                <div className="space-y-8">
                  {slideContent.length > 0 ? (() => {
                    // Get items to display based on slide type
                    const getItemsToDisplay = () => {
                      const revealedContent = slideContent.filter(item => revealedItems.includes(item.globalIndex))

                      if (revealedContent.length === 0) return []

                      // Check if this is a Q/A type slide (Quick Review, Talk, Vocabulary)
                      const isQASlide = revealedContent.some(item =>
                        item.type === 'qa' || item.type === 'talk' || item.type === 'vocabulary'
                      )

                      if (isQASlide) {
                        // For Q/A slides: show current question-answer pair
                        // Find the highest revealed index
                        const maxIndex = Math.max(...revealedItems)
                        const maxItem = slideContent.find(item => item.globalIndex === maxIndex)

                        if (!maxItem) return []

                        // If it's an answer, show both question and answer
                        if (maxItem.isAnswer) {
                          const questionItem = slideContent.find(item =>
                            item.questionIndex === maxItem.questionIndex && !item.isAnswer
                          )
                          return questionItem ? [questionItem, maxItem] : [maxItem]
                        } else {
                          // If it's a question, show only the question
                          return [maxItem]
                        }
                      } else {
                        // For regular slides: show only the latest item
                        const maxIndex = Math.max(...revealedItems)
                        const maxItem = slideContent.find(item => item.globalIndex === maxIndex)
                        return maxItem ? [maxItem] : []
                      }
                    }

                    const itemsToDisplay = getItemsToDisplay()

                    return itemsToDisplay.map((item) => {
                      // Determine what to show in the circle
                      const getCircleContent = () => {
                        if (item.type === 'qa' || item.type === 'talk' || item.type === 'vocabulary') {
                          return item.isAnswer ? 'A' : 'Q'
                        } else {
                          // For text and goal slides, use the question index
                          return item.questionIndex
                        }
                      }

                      return (
                        <div key={item.globalIndex} className="concept-point">
                          <div
                            className="concept-number bg-[#fadb9a]/20 text-[#2B6DFE]"
                            onClick={() => {
                              // disable Hide this specific item
                              // setRevealedItems(revealedItems.filter(i => i !== item.globalIndex))
                            }}
                            style={{ fontSize: "1.25rem" }}
                          >
                            {getCircleContent()}
                          </div>
                          <div className="flex-1">
                            <div className="overflow-hidden">
                              <div className="concept-content ml-4 text-white text-3xl" style={{ lineHeight: "2.25rem" }}>
                                <div dangerouslySetInnerHTML={{ __html: item.content }} />
                              </div>
                            </div>
                          </div>
                        </div>
                      )
                    })
                  })() : (
                    <div className="text-center text-white py-8">
                      No content available for this slide
                    </div>
                  )}

                  {/* Show message when no items are revealed */}
                  {slideContent.length > 0 && revealedItems.length === 0 && (
                    <div className="text-center text-white/70 py-8">
                      No questions revealed yet
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
