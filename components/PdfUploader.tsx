"use client"

import React, { useState } from 'react'
import { Upload, X, CheckCircle, AlertCircle } from 'lucide-react'
import { useSession } from 'next-auth/react'

interface PdfUploaderProps {
  documentId: string
  slideType: 'practice' | 'lesson_guide' | 'accelerator'
  currentPdfUrl?: string
  onUploadSuccess?: (url: string) => void
}

export const PdfUploader: React.FC<PdfUploaderProps> = ({
  documentId,
  slideType,
  currentPdfUrl,
  onUploadSuccess
}) => {
  const { data: session } = useSession()
  const [isUploading, setIsUploading] = useState(false)
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'success' | 'error'>('idle')
  const [errorMessage, setErrorMessage] = useState('')
  const [selectedFile, setSelectedFile] = useState<File | null>(null)

  // Check if user is admin
  const isAdmin = session?.user?.role === 'admin'

  if (!isAdmin) {
    return null // Don't render anything for non-admin users
  }

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Validate file type
      if (file.type !== 'application/pdf') {
        setErrorMessage('Only PDF files are allowed')
        setUploadStatus('error')
        return
      }

      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        setErrorMessage('File size must be less than 10MB')
        setUploadStatus('error')
        return
      }

      setSelectedFile(file)
      setUploadStatus('idle')
      setErrorMessage('')
    }
  }

  const handleUpload = async () => {
    if (!selectedFile) return

    setIsUploading(true)
    setUploadStatus('idle')
    setErrorMessage('')

    try {
      // Upload the file
      const formData = new FormData()
      formData.append('file', selectedFile)
      formData.append('slideType', slideType)
      formData.append('documentId', documentId)

      const uploadResponse = await fetch('/api/upload-pdf', {
        method: 'POST',
        body: formData
      })

      if (!uploadResponse.ok) {
        const errorData = await uploadResponse.json()
        throw new Error(errorData.error || 'Failed to upload PDF')
      }

      const uploadData = await uploadResponse.json()

      // Update the PDF link in the document
      const updateResponse = await fetch('/api/update-pdf-link', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          documentId,
          slideType,
          pdfUrl: uploadData.url
        })
      })

      if (!updateResponse.ok) {
        const errorData = await updateResponse.json()
        throw new Error(errorData.error || 'Failed to update PDF link')
      }

      setUploadStatus('success')
      setSelectedFile(null)
      
      // Call success callback
      if (onUploadSuccess) {
        onUploadSuccess(uploadData.url)
      }

      // Reset file input
      const fileInput = document.getElementById(`pdf-upload-${slideType}`) as HTMLInputElement
      if (fileInput) {
        fileInput.value = ''
      }

    } catch (error) {
      console.error('Error uploading PDF:', error)
      setErrorMessage(error instanceof Error ? error.message : 'Failed to upload PDF')
      setUploadStatus('error')
    } finally {
      setIsUploading(false)
    }
  }

  const getSlideTypeName = () => {
    switch (slideType) {
      case 'practice': return 'Practice PDF'
      case 'lesson_guide': return 'Lesson Guide PDF'
      case 'accelerator': return 'Accelerator PDF'
      default: return 'PDF'
    }
  }

  return (
    <div className="bg-white/10 rounded-lg p-4 border border-white/20">
      <div className="flex items-center justify-between mb-3">
        <h4 className="text-sm font-medium text-white">
          Admin: Upload {getSlideTypeName()}
        </h4>
        {uploadStatus === 'success' && (
          <CheckCircle className="w-4 h-4 text-green-400" />
        )}
        {uploadStatus === 'error' && (
          <AlertCircle className="w-4 h-4 text-red-400" />
        )}
      </div>

      {currentPdfUrl && (
        <div className="mb-3 text-xs text-white/70 overflow-hidden text-ellipsis">
          Current: <a href={currentPdfUrl} target="_blank" rel="noopener noreferrer" className="text-cyan-400 hover:underline">
            {currentPdfUrl.split('/').pop()}
          </a>
        </div>
      )}

      <div className="space-y-3">
        <div>
          <input
            id={`pdf-upload-${slideType}`}
            type="file"
            accept=".pdf"
            onChange={handleFileSelect}
            className="hidden"
          />
          <label
            htmlFor={`pdf-upload-${slideType}`}
            className="flex items-center gap-2 px-3 py-2 bg-white/10 hover:bg-white/20 text-white text-sm rounded cursor-pointer transition-colors"
          >
            <Upload className="w-4 h-4" />
            Choose PDF File
          </label>
        </div>

        {selectedFile && (
          <div className="flex items-center justify-between bg-white/5 rounded p-2">
            <div className="text-xs text-white/80">
              <div>{selectedFile.name}</div>
              <div>{(selectedFile.size / 1024 / 1024).toFixed(2)} MB</div>
            </div>
            <button
              onClick={() => setSelectedFile(null)}
              className="text-white/60 hover:text-white"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        )}

        {selectedFile && (
          <button
            onClick={handleUpload}
            disabled={isUploading}
            className="w-full px-3 py-2 bg-green-600 hover:bg-green-700 disabled:bg-green-800 text-white text-sm rounded transition-colors"
          >
            {isUploading ? 'Uploading...' : 'Upload PDF'}
          </button>
        )}

        {uploadStatus === 'success' && (
          <div className="text-xs text-green-400">
            PDF uploaded successfully!
          </div>
        )}

        {uploadStatus === 'error' && errorMessage && (
          <div className="text-xs text-red-400">
            {errorMessage}
          </div>
        )}
      </div>
    </div>
  )
}
