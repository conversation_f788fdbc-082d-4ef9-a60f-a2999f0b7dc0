"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Loader2, Play, RefreshCw, CheckCircle, XCircle, Clock, Wand2, Edit, RotateCcw, Save, X, Eye, Settings, Timer, Activity, Square, Archive } from "lucide-react"
import SettingsModal from "./settings-modal"
import { useRouter } from "next/navigation"

interface Job {
  _id: string
  jobId: string
  documentId: string
  unitNumber: string
  lessonNumber: string
  gradeLevel: string
  slideNumber: number
  slideKey: string
  slideType: string
  prompt: string
  originalPrompt?: string
  status: 'pending' | 'processing' | 'success' | 'failed'
  generatedHtml?: string
  errorMessage?: string
  createdAt: string
  processedAt?: string
  processingStartedAt?: string
  modelVersion?: string
  retryCount: number
}

interface JobsManagerProps {
  documentId?: string
  grade?: string
  unit?: string
  lesson?: string
  slide?: string
}

export default function JobsManager({ documentId, grade, unit, lesson, slide }: JobsManagerProps) {
  const router = useRouter()
  const [jobs, setJobs] = useState<Job[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isProcessing, setIsProcessing] = useState<string | null>(null)
  const [error, setError] = useState("")
  const [filter, setFilter] = useState<string>("all")
  const [editingJob, setEditingJob] = useState<Job | null>(null)
  const [editPrompt, setEditPrompt] = useState<string>("")
  const [previewJob, setPreviewJob] = useState<Job | null>(null)
  const [showSettings, setShowSettings] = useState(false)
  const [cronStatus, setCronStatus] = useState<any>(null)
  const [showCronStatus, setShowCronStatus] = useState(false)
  const [isCronTesting, setIsCronTesting] = useState(false)
  const [jobStats, setJobStats] = useState<any>(null)

  // Filter states
  const [selectedGrade, setSelectedGrade] = useState(grade || "")
  const [selectedUnit, setSelectedUnit] = useState(unit || "")
  const [selectedLesson, setSelectedLesson] = useState(lesson || "")
  const [selectedSlide, setSelectedSlide] = useState(slide || "")

  // Available options for filters
  const [availableGrades, setAvailableGrades] = useState<string[]>([])
  const [availableUnits, setAvailableUnits] = useState<string[]>([])
  const [availableLessons, setAvailableLessons] = useState<string[]>([])
  const [availableSlides, setAvailableSlides] = useState<number[]>([])

  // Load jobs on component mount
  useEffect(() => {
    loadJobs()
    loadJobStats()
    loadCronStatus()
    loadFilterOptions()
  }, [documentId, selectedGrade, selectedUnit, selectedLesson, selectedSlide, filter])

  const loadFilterOptions = async () => {
    try {
      // Load all jobs to extract filter options
      const response = await fetch('/api/jobs')
      const data = await response.json()

      if (data.success) {
        const allJobs = data.jobs

        // Extract unique values for filters
        const grades = [...new Set(allJobs.map((job: Job) => job.gradeLevel))].sort()
        const units = [...new Set(allJobs.map((job: Job) => job.unitNumber))].sort()
        const lessons = [...new Set(allJobs.map((job: Job) => job.lessonNumber))].sort()
        const slides = [...new Set(allJobs.map((job: Job) => job.slideNumber))].sort((a: any, b: any) => a - b)

        setAvailableGrades(grades as any)
        setAvailableUnits(units as any)
        setAvailableLessons(lessons as any)
        setAvailableSlides(slides as any)
      }
    } catch (err) {
      console.error('Error loading filter options:', err)
    }
  }

  const loadJobs = async () => {
    setIsLoading(true)
    setError("")

    try {
      const params = new URLSearchParams()
      if (documentId) params.append('documentId', documentId)
      if (selectedGrade) params.append('gradeLevel', selectedGrade)
      if (selectedUnit) params.append('unitNumber', selectedUnit)
      if (selectedLesson) params.append('lessonNumber', selectedLesson)
      if (selectedSlide) params.append('slideNumber', selectedSlide)

      // Add status filter if not 'all'
      if (filter !== 'all') {
        params.append('status', filter)
      }

      const url = `/api/jobs?${params.toString()}`
      console.log('Loading jobs from:', url)

      const response = await fetch(url)
      console.log('Response status:', response.status)

      const data = await response.json()
      console.log('Response data:', data)

      if (data.success) {
        setJobs(data.jobs)
        console.log('Loaded jobs:', data.jobs.length)
      } else {
        setError(`Failed to load jobs: ${data.error || 'Unknown error'}`)
      }
    } catch (err) {
      setError('Failed to load jobs')
      console.error('Error loading jobs:', err)
    } finally {
      setIsLoading(false)
    }
  }

  const loadJobStats = async () => {
    try {
      const params = new URLSearchParams()
      if (documentId) params.append('documentId', documentId)
      if (selectedGrade) params.append('gradeLevel', selectedGrade)
      if (selectedUnit) params.append('unitNumber', selectedUnit)
      if (selectedLesson) params.append('lessonNumber', selectedLesson)
      if (selectedSlide) params.append('slideNumber', selectedSlide)

      const url = `/api/jobs/stats?${params.toString()}`
      console.log('Loading job stats from:', url)

      const response = await fetch(url)
      const data = await response.json()

      if (data.success) {
        setJobStats(data.statistics)
        console.log('Loaded job stats:', data.statistics)
      }
    } catch (err) {
      console.error('Error loading job stats:', err)
    }
  }

  const loadCronStatus = async () => {
    try {
      const response = await fetch('/api/cron/status')
      const data = await response.json()

      if (data.success) {
        setCronStatus(data)
        console.log('Loaded cron status:', data)
      }
    } catch (err) {
      console.error('Error loading cron status:', err)
    }
  }

  // Function to update URL with current filters
  const updateUrlWithFilters = (newGrade?: string, newUnit?: string, newLesson?: string, newSlide?: string) => {
    try {
      const url = new URL(window.location.href)

      // Clear existing params
      url.searchParams.delete('grade')
      url.searchParams.delete('unit')
      url.searchParams.delete('lesson')
      url.searchParams.delete('slide')

      // Add new params if they exist
      if (newGrade) url.searchParams.set('grade', newGrade)
      if (newUnit) url.searchParams.set('unit', newUnit)
      if (newLesson) url.searchParams.set('lesson', newLesson)
      if (newSlide) url.searchParams.set('slide', newSlide)

      // Keep documentId if it exists
      if (documentId) url.searchParams.set('documentId', documentId)

      window.history.replaceState({}, '', url.toString())
    } catch (error) {
      console.error("Error updating URL:", error)
    }
  }

  const testCronJob = async () => {
    setIsCronTesting(true)
    try {
      console.log('🧪 Testing cron job manually...')

      const response = await fetch('/api/cron/test')
      const data = await response.json()

      if (data.success) {
        alert(`✅ Cron test completed!\n\n${data.message}\n\nResponse: ${JSON.stringify(data.cronResponse, null, 2)}`)
        // Reload jobs and cron status after test
        await loadJobs()
        await loadJobStats()
        await loadCronStatus()
      } else {
        alert(`❌ Cron test failed: ${data.error}`)
      }
    } catch (err) {
      console.error('Error testing cron job:', err)
      alert('❌ Failed to test cron job. Please try again.')
    } finally {
      setIsCronTesting(false)
    }
  }

  const stopAllJobs = async () => {
    const confirmed = confirm('🛑 Are you sure you want to stop all currently processing jobs?\n\nThis will reset all processing jobs back to pending status.')
    if (!confirmed) return

    setIsCronTesting(true) // Reuse loading state
    try {
      console.log('🛑 Stopping all processing jobs...')

      const response = await fetch('/api/jobs/stop-all', {
        method: 'POST'
      })
      const data = await response.json()

      if (data.success) {
        alert(`✅ Stop All completed!\n\n${data.message}\n\nReset ${data.modifiedCount} jobs in ${data.duration}ms`)
        // Reload jobs and cron status after stopping
        await loadJobs()
        await loadJobStats()
        await loadCronStatus()
      } else {
        alert(`❌ Stop All failed: ${data.error}`)
      }
    } catch (err) {
      console.error('Error stopping all jobs:', err)
      alert('❌ Failed to stop all jobs. Please try again.')
    } finally {
      setIsCronTesting(false)
    }
  }

  const processJob = async (jobId: string) => {
    setIsProcessing(jobId)
    setError("")

    try {
      console.log(`🚀 Processing job: ${jobId}`)

      const response = await fetch('/api/jobs/process-job', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ jobId }),
      })

      console.log(`📡 Process response status: ${response.status}`)

      const data = await response.json()
      console.log(`📄 Process response data:`, data)

      if (data.success) {
        console.log(`✅ Job processed successfully: ${jobId}`)
        console.log(`📄 Generated HTML length: ${data.htmlLength || 'unknown'}`)

        // Show success message with details
        const message = `✅ Job processed successfully!\n` +
                       `Job ID: ${jobId}\n` +
                       `HTML generated: ${data.htmlLength || 'unknown'} characters\n` +
                       `Status: ${data.job?.status || 'success'}`

        alert(message)

        // Reload jobs to get updated status
        await loadJobs()
        await loadJobStats()
      } else {
        console.error(`❌ Job processing failed:`, data.error)
        setError(`Failed to process job: ${data.error}`)

        // Show error alert
        alert(`❌ Job processing failed: ${data.error}`)
      }
    } catch (err) {
      console.error('❌ Error processing job:', err)
      setError('Failed to process job')
    } finally {
      setIsProcessing(null)
    }
  }

  const processAllJobs = async () => {
    try {
      setIsProcessing('batch')
      console.log(`🔄 Starting batch job processing`)

      // First, do a dry run to see what would be processed
      const dryRunResponse = await fetch('/api/jobs/process-all', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ limit: 30, dryRun: true }),
      })

      const dryRunData = await dryRunResponse.json()
      console.log(`🧪 Dry run result:`, dryRunData)

      if (!dryRunData.success || !dryRunData.jobs || dryRunData.jobs.length === 0) {
        alert('No pending jobs found to process.')
        return
      }

      // Confirm with user
      const confirmMessage = `Found ${dryRunData.jobs.length} pending jobs to process.\n\n` +
                            `This will generate HTML for all pending jobs.\n\n` +
                            `Continue?`

      if (!confirm(confirmMessage)) {
        console.log('User cancelled batch processing')
        return
      }

      // Process all jobs
      const response = await fetch('/api/jobs/process-all', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ limit: 1, dryRun: false }),
      })

      const data = await response.json()
      console.log(`📄 Batch processing result:`, data)

      if (data.success) {
        const message = `✅ Batch processing completed!\n\n` +
                       `Processed: ${data.processed} jobs\n` +
                       `Success: ${data.successCount}\n` +
                       `Failures: ${data.failureCount}`

        alert(message)

        // Reload jobs to show updated statuses
        await loadJobs()
        await loadJobStats()
      } else {
        console.error(`❌ Batch processing failed:`, data.error)
        alert(`❌ Batch processing failed: ${data.error}`)
      }
    } catch (err) {
      console.error('❌ Error in batch processing:', err)
      alert('❌ Failed to process jobs. Please try again.')
    } finally {
      setIsProcessing(null)
    }
  }

  const openEditPrompt = (job: Job) => {
    setEditingJob(job)
    setEditPrompt(job.prompt)
  }

  const closeEditPrompt = () => {
    setEditingJob(null)
    setEditPrompt("")
  }

  const updateJobPrompt = async () => {
    if (!editingJob || !editPrompt.trim()) return

    try {
      setIsLoading(true)
      console.log(`🔄 Updating prompt for job: ${editingJob.jobId}`)

      const response = await fetch('/api/jobs/update-prompt', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          jobId: editingJob.jobId,
          newPrompt: editPrompt.trim()
        }),
      })

      const data = await response.json()

      if (data.success) {
        console.log(`✅ Prompt updated successfully`)
        alert('✅ Prompt updated successfully!')
        closeEditPrompt()
        await loadJobs()
        await loadJobStats()
      } else {
        console.error(`❌ Failed to update prompt:`, data.error)
        alert(`❌ Failed to update prompt: ${data.error}`)
      }
    } catch (err) {
      console.error('❌ Error updating prompt:', err)
      alert('❌ Failed to update prompt. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const regenerateJob = async (jobId: string) => {
    const job = jobs.find(j => j.jobId === jobId)
    const isRetry = job?.status === 'failed'

    const confirmMessage = isRetry
      ? 'Are you sure you want to retry this failed job? This will attempt to generate HTML again.'
      : 'Are you sure you want to regenerate this job? This will create a new HTML version.'

    if (!confirm(confirmMessage)) {
      return
    }

    try {
      setIsProcessing(jobId)
      console.log(`🔄 ${isRetry ? 'Retrying' : 'Regenerating'} job: ${jobId}`)

      const response = await fetch('/api/jobs/process-job', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ jobId }),
      })

      const data = await response.json()

      if (data.success) {
        const actionType = data.isRegeneration ? 'regenerated' : (isRetry ? 'retried' : 'processed')
        console.log(`✅ Job ${actionType} successfully: ${jobId}`)
        const message = `✅ Job ${actionType} successfully!\n` +
                       `Job ID: ${jobId}\n` +
                       `HTML generated: ${data.htmlLength || 'unknown'} characters`

        alert(message)
        await loadJobs()
        await loadJobStats()
      } else {
        console.error(`❌ Job ${isRetry ? 'retry' : 'regeneration'} failed:`, data.error)
        alert(`❌ Job ${isRetry ? 'retry' : 'regeneration'} failed: ${data.error}`)
      }
    } catch (err) {
      console.error(`❌ Error ${isRetry ? 'retrying' : 'regenerating'} job:`, err)
      alert(`❌ Failed to ${isRetry ? 'retry' : 'regenerate'} job. Please try again.`)
    } finally {
      setIsProcessing(null)
    }
  }

  const validateSuccessJobs = async () => {
    try {
      setIsProcessing('validate-success')
      console.log(`🔍 Starting success jobs validation`)

      // Use async mode for large number of jobs
      const useAsync = statusCounts.success > 100

      const response = await fetch('/api/jobs/validate-success', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          limit: 50,
          async: useAsync
        }),
      })

      const data = await response.json()
      console.log(`📄 Success validation result:`, data)

      if (data.success) {
        if (data.async) {
          const message = `🚀 Async Success Jobs Validation Started!\n\n` +
                         `Total Jobs: ${data.totalJobs}\n` +
                         `Batch Size: ${data.batchSize}\n` +
                         `Estimated Batches: ${data.estimatedBatches}\n\n` +
                         `Validation is running in background.\n` +
                         `Check console logs for progress.`

          alert(message)
        } else {
          const message = `🔍 Success Jobs Validation Complete!\n\n` +
                         `Total Checked: ${data.totalChecked}\n` +
                         `Valid HTML: ${data.validCount}\n` +
                         `Empty/Invalid HTML: ${data.invalidCount}\n` +
                         `Fixed Jobs: ${data.fixedCount}`

          alert(message)
        }

        // Reload jobs to show updated statuses
        await loadJobs()
        await loadJobStats()
      } else {
        console.error(`❌ Success validation failed:`, data.error)
        alert(`❌ Success validation failed: ${data.error}`)
      }
    } catch (err) {
      console.error('❌ Error in success validation:', err)
      alert('❌ Failed to validate success jobs. Please try again.')
    } finally {
      setIsProcessing(null)
    }
  }

  const moveFailedToPending = async () => {
    try {
      setIsProcessing('move-failed')
      console.log(`🔄 Moving all failed jobs to pending`)

      const response = await fetch('/api/jobs/move-failed-to-pending', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const data = await response.json()
      console.log(`📄 Move failed result:`, data)

      if (data.success) {
        const message = `🔄 Move Failed to Pending Complete!\n\n` +
                       `Total Found: ${data.totalFound}\n` +
                       `Moved to Pending: ${data.movedCount}`

        alert(message)

        // Reload jobs to show updated statuses
        await loadJobs()
        await loadJobStats()
      } else {
        console.error(`❌ Move failed to pending failed:`, data.error)
        alert(`❌ Move failed to pending failed: ${data.error}`)
      }
    } catch (err) {
      console.error('❌ Error in move failed to pending:', err)
      alert('❌ Failed to move failed jobs to pending. Please try again.')
    } finally {
      setIsProcessing(null)
    }
  }

  const deleteArchivedJobs = async () => {
    if (!confirm('⚠️ Are you sure you want to delete all archived jobs? This action cannot be undone.\n\nArchived jobs are for lessons that were deleted from the database.')) {
      return
    }

    try {
      setIsProcessing('delete-archived')
      console.log(`🗑️ Deleting all archived jobs`)

      const response = await fetch('/api/jobs/delete-archived', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const data = await response.json()
      console.log(`📄 Delete archived result:`, data)

      if (data.success) {
        const message = `🗑️ Delete Archived Jobs Complete!\n\n` +
                       `Total Found: ${data.totalFound}\n` +
                       `Deleted: ${data.deletedCount}`

        alert(message)

        // Reload jobs to show updated statuses
        await loadJobs()
        await loadJobStats()
      } else {
        console.error(`❌ Delete archived jobs failed:`, data.error)
        alert(`❌ Delete archived jobs failed: ${data.error}`)
      }
    } catch (err) {
      console.error('❌ Error in delete archived jobs:', err)
      alert('❌ Failed to delete archived jobs. Please try again.')
    } finally {
      setIsProcessing(null)
    }
  }

  const processAllFailedJobs = async () => {
    try {
      
      setIsProcessing('batch-failed')
      console.log(`🔄 Starting failed jobs retry`)

      // First, do a dry run to see what would be processed
      const dryRunResponse = await fetch('/api/jobs/retry-failed', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ limit: 1, dryRun: true }),
      })

      const dryRunData = await dryRunResponse.json()
      console.log(`🧪 Dry run result:`, dryRunData)

      if (!dryRunData.success || !dryRunData.jobs || dryRunData.jobs.length === 0) {
        alert('No failed jobs found to retry.')
        return
      }

      // Confirm with user
      const confirmMessage = `Found ${dryRunData.jobs.length} failed jobs to retry.\n\n` +
                            `This will attempt to regenerate HTML for all failed jobs.\n\n` +
                            `Continue?`

      if (!confirm(confirmMessage)) {
        console.log('User cancelled failed jobs retry')
        return
      }

      // Process all failed jobs
      const response = await fetch('/api/jobs/retry-failed', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ limit: 10, dryRun: false }),
      })

      const data = await response.json()
      console.log(`📄 Failed jobs retry result:`, data)

      if (data.success) {
        const message = `✅ Failed jobs retry completed!\n\n` +
                       `Processed: ${data.processed} failed jobs\n` +
                       `Success: ${data.successCount}\n` +
                       `Still Failed: ${data.failureCount}`

        alert(message)

        // Reload jobs to show updated statuses
        await loadJobs()
        await loadJobStats()
      } else {
        console.error(`❌ Failed jobs retry failed:`, data.error)
        alert(`❌ Failed jobs retry failed: ${data.error}`)
      }
    } catch (err) {
      console.error('❌ Error in failed jobs retry:', err)
      alert('❌ Failed to retry jobs. Please try again.')
    } finally {
      setIsProcessing(null)
    }
  }



  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'processing':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'archived':
        return <Archive className="h-4 w-4 text-gray-400" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusBadge = (status: string) => {
    const variants: Record<string, "default" | "secondary" | "destructive" | "outline"> = {
      pending: "outline",
      processing: "default",
      success: "secondary",
      failed: "destructive",
      archived: "outline"
    }

    return (
      <Badge variant={variants[status] || "outline"} className="flex items-center gap-1">
        {getStatusIcon(status)}
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  // Jobs are already filtered on the server side based on the filter state
  const filteredJobs = jobs

  // Use real statistics from API instead of counting loaded jobs
  const statusCounts = jobStats || jobs.reduce((acc, job) => {
    acc[job.status] = (acc[job.status] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold" style={{ fontFamily: 'Inter, sans-serif' }}>
            HTML Generation Jobs
          </h1>
          <p className="text-gray-600" style={{ fontFamily: 'Inter, sans-serif' }}>
            Manage and process HTML generation jobs for lesson slides
          </p>
          <p className="text-sm text-gray-500" style={{ fontFamily: 'Inter, sans-serif' }}>
            ⏱️ Each job takes 60-90 seconds to generate HTML using Gemini AI
          </p>
          <p className="text-xs text-gray-400" style={{ fontFamily: 'Inter, sans-serif' }}>
            🚦 Ultra conservative: 1 job at a time to prevent rate limiting
          </p>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
          <Button onClick={loadJobs} disabled={isLoading} style={{ fontFamily: 'Inter, sans-serif' }}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button
            onClick={() => setShowSettings(true)}
            variant="outline"
            style={{ fontFamily: 'Inter, sans-serif' }}
            title="Global Settings"
          >
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
          <Button
            onClick={() => setShowCronStatus(true)}
            variant="outline"
            style={{ fontFamily: 'Inter, sans-serif' }}
            title="Cron Job Status"
          >
            <Timer className="h-4 w-4 mr-2" />
            Cron Status
          </Button>
          <Button
            onClick={processAllJobs}
            disabled={isLoading || isProcessing !== null}
            variant="outline"
            style={{ fontFamily: 'Inter, sans-serif' }}
          >
            {isProcessing === 'batch' ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Wand2 className="h-4 w-4 mr-2" />
            )}
            {isProcessing === 'batch' ? 'Processing...' : 'Process All Pending'}
          </Button>
          {statusCounts.failed > 0 && (
            <Button
              onClick={processAllFailedJobs}
              disabled={isLoading || isProcessing !== null}
              variant="destructive"
              style={{ fontFamily: 'Inter, sans-serif' }}
            >
              {isProcessing === 'batch-failed' ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <RotateCcw className="h-4 w-4 mr-2" />
              )}
              {isProcessing === 'batch-failed' ? 'Retrying...' : `Retry All Failed (${statusCounts.failed})`}
            </Button>
          )}
          {statusCounts.failed > 0 && (
            <Button
              onClick={moveFailedToPending}
              disabled={isLoading || isProcessing !== null}
              variant="outline"
              style={{ fontFamily: 'Inter, sans-serif' }}
              title="Move all failed jobs to pending status for retry"
            >
              {isProcessing === 'move-failed' ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4 mr-2" />
              )}
              {isProcessing === 'move-failed' ? 'Moving...' : `Move Failed to Pending (${statusCounts.failed})`}
            </Button>
          )}
          {statusCounts.archived > 0 && (
            <Button
              onClick={deleteArchivedJobs}
              disabled={isLoading || isProcessing !== null}
              variant="outline"
              style={{ fontFamily: 'Inter, sans-serif' }}
              title="Delete all archived jobs (for deleted lessons)"
            >
              {isProcessing === 'delete-archived' ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Archive className="h-4 w-4 mr-2" />
              )}
              {isProcessing === 'delete-archived' ? 'Deleting...' : `Delete Archived (${statusCounts.archived})`}
            </Button>
          )}
          {statusCounts.success > 0 && (
            <Button
              onClick={validateSuccessJobs}
              disabled={isLoading || isProcessing !== null}
              variant="outline"
              style={{ fontFamily: 'Inter, sans-serif' }}
              title="Validate success jobs to check if HTML is actually saved"
            >
              {isProcessing === 'validate-success' ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <CheckCircle className="h-4 w-4 mr-2" />
              )}
              {isProcessing === 'validate-success' ? 'Validating...' : `Validate Success (${statusCounts.success})`}
            </Button>
          )}
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Grade Filter */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Grade</label>
            <select
              value={selectedGrade}
              onChange={(e) => {
                const newGrade = e.target.value
                setSelectedGrade(newGrade)
                setSelectedUnit("") // Reset dependent filters
                setSelectedLesson("")
                setSelectedSlide("")
                updateUrlWithFilters(newGrade, "", "", "")
              }}
              className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-md text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/30"
              style={{ fontFamily: 'Inter, sans-serif' }}
            >
              <option value="" className="text-gray-900">All Grades</option>
              {availableGrades.map((grade) => (
                <option key={grade} value={grade} className="text-gray-900">
                  {grade}
                </option>
              ))}
            </select>
          </div>

          {/* Unit Filter */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Unit</label>
            <select
              value={selectedUnit}
              onChange={(e) => {
                const newUnit = e.target.value
                setSelectedUnit(newUnit)
                setSelectedLesson("") // Reset dependent filters
                setSelectedSlide("")
                updateUrlWithFilters(selectedGrade, newUnit, "", "")
              }}
              className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-md text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/30"
              style={{ fontFamily: 'Inter, sans-serif' }}
            >
              <option value="" className="text-gray-900">All Units</option>
              {availableUnits.map((unit) => (
                <option key={unit} value={unit} className="text-gray-900">
                  Unit {unit}
                </option>
              ))}
            </select>
          </div>

          {/* Lesson Filter */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Lesson</label>
            <select
              value={selectedLesson}
              onChange={(e) => {
                const newLesson = e.target.value
                setSelectedLesson(newLesson)
                setSelectedSlide("") // Reset dependent filter
                updateUrlWithFilters(selectedGrade, selectedUnit, newLesson, "")
              }}
              className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-md text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/30"
              style={{ fontFamily: 'Inter, sans-serif' }}
            >
              <option value="" className="text-gray-900">All Lessons</option>
              {availableLessons.map((lesson) => (
                <option key={lesson} value={lesson} className="text-gray-900">
                  Lesson {lesson}
                </option>
              ))}
            </select>
          </div>

          {/* Slide Filter */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Slide</label>
            <select
              value={selectedSlide}
              onChange={(e) => {
                const newSlide = e.target.value
                setSelectedSlide(newSlide)
                updateUrlWithFilters(selectedGrade, selectedUnit, selectedLesson, newSlide)
              }}
              className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-md text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/30"
              style={{ fontFamily: 'Inter, sans-serif' }}
            >
              <option value="" className="text-gray-900">All Slides</option>
              {availableSlides.map((slide) => (
                <option key={slide} value={slide.toString()} className="text-gray-900">
                  Slide {slide}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Clear Filters Button */}
        {(selectedGrade || selectedUnit || selectedLesson || selectedSlide) && (
          <div className="mt-4 flex justify-end">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setSelectedGrade("")
                setSelectedUnit("")
                setSelectedLesson("")
                setSelectedSlide("")
                updateUrlWithFilters("", "", "", "")
                router.push(`/jobs`)
              }}
              className="text-black border-white/20 hover:bg-white/10"
              style={{ fontFamily: 'Inter, sans-serif' }}
            >
              Clear Filters
            </Button>
          </div>
        )}
      </div>

      {/* Status Summary */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-yellow-600">{statusCounts.pending || 0}</div>
            <div className="text-sm text-gray-600">Pending</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-blue-600">{statusCounts.processing || 0}</div>
            <div className="text-sm text-gray-600">Processing</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">{statusCounts.success || 0}</div>
            <div className="text-sm text-gray-600">Success</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-red-600">{statusCounts.failed || 0}</div>
            <div className="text-sm text-gray-600">Failed</div>
          </CardContent>
        </Card>
      </div>

      {/* Filter Buttons */}
      <div className="flex gap-2 flex-wrap">
        {['all', 'pending', 'processing', 'success', 'failed'].map((status) => (
          <Button
            key={status}
            variant={filter === status ? "default" : "outline"}
            size="sm"
            onClick={() => setFilter(status)}
            style={{ fontFamily: 'Inter, sans-serif' }}
          >
            {status.charAt(0).toUpperCase() + status.slice(1)}
            {status !== 'all' && statusCounts[status] && (
              <span className="ml-1 bg-white/20 px-1 rounded text-xs">
                {statusCounts[status]}
              </span>
            )}
          </Button>
        ))}
      </div>

      {/* Error Display */}
      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-700 text-sm" style={{ fontFamily: 'Inter, sans-serif' }}>{error}</p>
        </div>
      )}

      {/* Jobs List */}
      {isLoading ? (
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
        </div>
      ) : (
        <div className="space-y-4">
          {filteredJobs.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <Wand2 className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-500" style={{ fontFamily: 'Inter, sans-serif' }}>
                  {filter === 'all' ? 'No jobs found' : `No ${filter} jobs found`}
                </p>
              </CardContent>
            </Card>
          ) : (
            filteredJobs.map((job) => (
              <Card key={job.jobId}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg" style={{ fontFamily: 'Inter, sans-serif' }}>
                        {job.gradeLevel} - Unit {job.unitNumber}, Lesson {job.lessonNumber}
                      </CardTitle>
                      <CardDescription style={{ fontFamily: 'Inter, sans-serif' }}>
                        Slide {job.slideNumber}: {job.slideKey} ({job.slideType})
                      </CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusBadge(job.status)}

                      {/* Edit Prompt Button - available for all jobs */}
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => openEditPrompt(job)}
                        disabled={isLoading}
                        style={{ fontFamily: 'Inter, sans-serif' }}
                        title="Edit prompt"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>

                      {/* Process Button - for pending jobs */}
                      {job.status === 'pending' && (
                        <Button
                          size="sm"
                          onClick={() => processJob(job.jobId)}
                          disabled={isProcessing === job.jobId}
                          style={{ fontFamily: 'Inter, sans-serif' }}
                          title="Process job"
                        >
                          {isProcessing === job.jobId ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <Play className="h-4 w-4" />
                          )}
                        </Button>
                      )}

                      {/* Preview Button - for successful jobs */}
                      {job.status === 'success' && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => setPreviewJob(job)}
                          style={{ fontFamily: 'Inter, sans-serif' }}
                          title="Preview generated HTML"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      )}

                      {/* Regenerate Button - for successful and failed jobs */}
                      {(job.status === 'success' || job.status === 'failed') && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => regenerateJob(job.jobId)}
                          disabled={isProcessing === job.jobId}
                          style={{ fontFamily: 'Inter, sans-serif' }}
                          title={job.status === 'success' ? 'Regenerate HTML' : 'Retry failed job'}
                        >
                          {isProcessing === job.jobId ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <RotateCcw className="h-4 w-4" />
                          )}
                        </Button>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div>
                      <h4 className="font-medium text-sm text-blue-700 mb-1" style={{ fontFamily: 'Inter, sans-serif' }}>
                        Description (html_css_description_of_image):
                      </h4>
                      <p className="text-sm text-blue-600 bg-blue-50 p-2 rounded border-l-4 border-blue-500" style={{ fontFamily: 'Inter, sans-serif' }}>
                        {job.prompt}
                      </p>
                    </div>

                    {/* {job.errorMessage && (
                      <div>
                        <h4 className="font-medium text-sm text-red-700 mb-1" style={{ fontFamily: 'Inter, sans-serif' }}>
                          Error:
                        </h4>
                        <p className="text-sm text-red-600 bg-red-50 p-2 rounded" style={{ fontFamily: 'Inter, sans-serif' }}>
                          {job.errorMessage}
                        </p>
                      </div>
                    )} */}

                    <div className="flex justify-between text-xs text-gray-500" style={{ fontFamily: 'Inter, sans-serif' }}>
                      <span>Created: {new Date(job.createdAt).toLocaleString()}</span>
                      {job.processedAt && (
                        <span>Processed: {new Date(job.processedAt).toLocaleString()}</span>
                      )}
                      {job.modelVersion && (
                        <span>Model: {job.modelVersion}</span>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      )}

      {/* Edit Prompt Modal */}
      {editingJob && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl mx-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold" style={{ fontFamily: 'Inter, sans-serif' }}>
                Edit Prompt for Job: {editingJob.jobId}
              </h3>
              <Button
                size="sm"
                variant="outline"
                onClick={closeEditPrompt}
                style={{ fontFamily: 'Inter, sans-serif' }}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2" style={{ fontFamily: 'Inter, sans-serif' }}>
                  Job Details:
                </label>
                <p className="text-sm text-gray-600" style={{ fontFamily: 'Inter, sans-serif' }}>
                  {editingJob.gradeLevel} - Unit {editingJob.unitNumber}, Lesson {editingJob.lessonNumber} - Slide {editingJob.slideNumber}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2" style={{ fontFamily: 'Inter, sans-serif' }}>
                  Prompt (html_css_description_of_image):
                </label>
                <textarea
                  value={editPrompt}
                  onChange={(e) => setEditPrompt(e.target.value)}
                  className="w-full h-32 p-3 border border-gray-300 rounded-md resize-none"
                  style={{ fontFamily: 'Inter, sans-serif' }}
                  placeholder="Enter the description for HTML generation..."
                />
              </div>

              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={closeEditPrompt}
                  disabled={isLoading}
                  style={{ fontFamily: 'Inter, sans-serif' }}
                >
                  Cancel
                </Button>
                <Button
                  onClick={updateJobPrompt}
                  disabled={isLoading || !editPrompt.trim()}
                  style={{ fontFamily: 'Inter, sans-serif' }}
                >
                  {isLoading ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  Save Changes
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Preview HTML Modal */}
      {previewJob && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-full max-w-6xl h-[90vh] mx-4 flex flex-col">
            <div className="flex justify-between items-center p-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold" style={{ fontFamily: 'Inter, sans-serif' }}>
                Preview HTML - Job: {previewJob.jobId}
              </h3>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setPreviewJob(null)}
                style={{ fontFamily: 'Inter, sans-serif' }}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="flex-1 p-4">
              <div className="h-full border border-gray-300 rounded-md overflow-hidden">
                {previewJob.generatedHtml ? (
                  <iframe
                    srcDoc={previewJob.generatedHtml}
                    className="w-full h-full border-none"
                    title="HTML Preview"
                    sandbox="allow-scripts"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-500">
                    <div className="text-center">
                      <p>No HTML content available for preview</p>
                      <p className="text-sm mt-2">Job ID: {previewJob.jobId}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="p-4 border-t border-gray-200 bg-gray-50">
              <div className="text-sm text-gray-600" style={{ fontFamily: 'Inter, sans-serif' }}>
                <strong>Job Details:</strong> {previewJob.gradeLevel} - Unit {previewJob.unitNumber}, Lesson {previewJob.lessonNumber} - Slide {previewJob.slideNumber}
              </div>
              <div className="text-sm text-gray-600 mt-1" style={{ fontFamily: 'Inter, sans-serif' }}>
                <strong>Prompt:</strong> {previewJob.prompt.substring(0, 200)}...
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Settings Modal */}
      <SettingsModal
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
      />

      {/* Cron Status Modal */}
      {showCronStatus && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-full max-w-4xl h-[80vh] mx-4 flex flex-col">
            <div className="flex justify-between items-center p-6 border-b border-gray-200">
              <div>
                <h2 className="text-2xl font-bold" style={{ fontFamily: 'Inter, sans-serif' }}>
                  Cron Job Status
                </h2>
                <p className="text-gray-600 mt-1" style={{ fontFamily: 'Inter, sans-serif' }}>
                  Automated job processing every minute
                </p>
              </div>
              <div className="flex gap-2">
                <Button
                  onClick={stopAllJobs}
                  disabled={isCronTesting}
                  variant="destructive"
                  style={{ fontFamily: 'Inter, sans-serif' }}
                  title="Stop all currently processing jobs"
                >
                  {isCronTesting ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Square className="h-4 w-4 mr-2" />
                  )}
                  Stop All
                </Button>
                <Button
                  onClick={testCronJob}
                  disabled={isCronTesting}
                  variant="outline"
                  style={{ fontFamily: 'Inter, sans-serif' }}
                >
                  {isCronTesting ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Play className="h-4 w-4 mr-2" />
                  )}
                  Test Cron
                </Button>
                <Button
                  onClick={loadCronStatus}
                  variant="outline"
                  style={{ fontFamily: 'Inter, sans-serif' }}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
                <Button
                  onClick={() => setShowCronStatus(false)}
                  variant="outline"
                  style={{ fontFamily: 'Inter, sans-serif' }}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="flex-1 overflow-auto p-6">
              {cronStatus ? (
                <div className="space-y-6">
                  {/* Cron Info */}
                  <Card>
                    <CardHeader>
                      <CardTitle style={{ fontFamily: 'Inter, sans-serif' }}>
                        <Activity className="h-5 w-5 inline mr-2" />
                        Cron Configuration
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 gap-4 text-sm" style={{ fontFamily: 'Inter, sans-serif' }}>
                        <div>
                          <strong>Schedule:</strong> {cronStatus.cronInfo?.schedule}
                        </div>
                        <div>
                          <strong>Endpoint:</strong> {cronStatus.cronInfo?.endpoint}
                        </div>
                        <div>
                          <strong>Priority:</strong> {cronStatus.cronInfo?.priority}
                        </div>
                        <div>
                          <strong>Max Retries:</strong> {cronStatus.cronInfo?.maxRetries}
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Statistics */}
                  <Card>
                    <CardHeader>
                      <CardTitle style={{ fontFamily: 'Inter, sans-serif' }}>
                        Job Statistics
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                        <div className="text-center p-4 bg-yellow-50 rounded-lg">
                          <div className="text-2xl font-bold text-yellow-600">{cronStatus.statistics?.pending || 0}</div>
                          <div className="text-sm text-gray-600">Pending</div>
                        </div>
                        <div className="text-center p-4 bg-blue-50 rounded-lg">
                          <div className="text-2xl font-bold text-blue-600">{cronStatus.statistics?.processing || 0}</div>
                          <div className="text-sm text-gray-600">Processing</div>
                        </div>
                        <div className="text-center p-4 bg-green-50 rounded-lg">
                          <div className="text-2xl font-bold text-green-600">{cronStatus.statistics?.success || 0}</div>
                          <div className="text-sm text-gray-600">Success</div>
                        </div>
                        <div className="text-center p-4 bg-red-50 rounded-lg">
                          <div className="text-2xl font-bold text-red-600">{cronStatus.statistics?.failed || 0}</div>
                          <div className="text-sm text-gray-600">Failed</div>
                        </div>
                        <div className="text-center p-4 bg-orange-50 rounded-lg">
                          <div className="text-2xl font-bold text-orange-600">{cronStatus.statistics?.retryableFailedJobs || 0}</div>
                          <div className="text-sm text-gray-600">Retryable</div>
                        </div>
                        <div className="text-center p-4 bg-gray-50 rounded-lg">
                          <div className="text-2xl font-bold text-gray-600">{cronStatus.statistics?.totalJobs || 0}</div>
                          <div className="text-sm text-gray-600">Total</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Next Job */}
                  {cronStatus.nextJobToProcess && (
                    <Card>
                      <CardHeader>
                        <CardTitle style={{ fontFamily: 'Inter, sans-serif' }}>
                          Next Job to Process
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-sm" style={{ fontFamily: 'Inter, sans-serif' }}>
                          <p><strong>Job ID:</strong> {cronStatus.nextJobToProcess.jobId}</p>
                          <p><strong>Document:</strong> {cronStatus.nextJobToProcess.documentId}</p>
                          <p><strong>Slide:</strong> {cronStatus.nextJobToProcess.slideNumber}</p>
                          <p><strong>Created:</strong> {new Date(cronStatus.nextJobToProcess.createdAt).toLocaleString()}</p>
                          {cronStatus.nextJobToProcess.retryCount > 0 && (
                            <p><strong>Retry Count:</strong> {cronStatus.nextJobToProcess.retryCount}</p>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              ) : (
                <div className="flex justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
