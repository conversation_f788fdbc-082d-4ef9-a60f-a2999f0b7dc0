"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import HighContrastToggle from "./high-contrast-toggle"
import { CurriculumType, getCurriculumDisplayName } from "@/types/curriculumTypes"

interface UserSettings {
  name: string
  school: string
  schoolDistrict: string
  defaultCurriculum: string
  defaultGrade: string
  highContrastMode: boolean
}

interface UserSettingsModalProps {
  isOpen: boolean
  onClose: () => void
}

export default function UserSettingsModal({ isOpen, onClose }: UserSettingsModalProps) {
  const { data: session, update } = useSession()
  const [settings, setSettings] = useState<UserSettings>({
    name: '',
    school: '',
    schoolDistrict: '',
    defaultCurriculum: 'CCSS',
    defaultGrade: '',
    highContrastMode: false,
  })
  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState('')
  const [isEditingName, setIsEditingName] = useState(false)
  const [initialized, setInitialized] = useState(false)

  // Available options
  const [availableCurriculums, setAvailableCurriculums] = useState<CurriculumType[]>([])
  const [availableGrades, setAvailableGrades] = useState<string[]>([])
  const [isLoadingOptions, setIsLoadingOptions] = useState(false)

  // Load fresh user data when modal opens
  useEffect(() => {
    if (isOpen && !initialized) {
      loadFreshUserData()
      loadAvailableCurriculums()
    }
  }, [isOpen, initialized])

  // Load available grades when curriculum changes
  useEffect(() => {
    if (settings.defaultCurriculum) {
      loadAvailableGrades(settings.defaultCurriculum)
    } else {
      setAvailableGrades([])
    }
  }, [settings.defaultCurriculum])

  const loadAvailableCurriculums = async () => {
    try {
      setIsLoadingOptions(true)
      const response = await fetch('/api/available-curriculums')
      const data = await response.json()

      if (data.curriculums) {
        setAvailableCurriculums(data.curriculums)
      }
    } catch (error) {
      console.error('Error fetching available curriculums:', error)
    } finally {
      setIsLoadingOptions(false)
    }
  }

  const loadAvailableGrades = async (curriculum: string) => {
    try {
      setIsLoadingOptions(true)
      const response = await fetch(`/api/available-options?option_type=grade_level&curriculum=${curriculum}`)
      const data = await response.json()

      if (data.grade_levels) {
        setAvailableGrades(data.grade_levels)
      }
    } catch (error) {
      console.error('Error fetching available grades:', error)
    } finally {
      setIsLoadingOptions(false)
    }
  }

  const loadFreshUserData = async () => {
    setIsLoading(true)
    setError('')

    try {
      const response = await fetch('/api/refresh-session', {
        method: 'POST',
      })
      const data = await response.json()

      if (data.success && data.user) {
        setSettings({
          name: data.user.name || '',
          school: data.user.school || '',
          schoolDistrict: data.user.schoolDistrict || '',
          defaultCurriculum: data.user.defaultCurriculum || CurriculumType.CCSS,
          defaultGrade: data.user.defaultGrade || '',
          highContrastMode: data.user.highContrastMode || false,
        })
        setInitialized(true)
        console.log('User settings loaded from fresh data:', data.user)
      } else {
        setError(data.error || 'Failed to load user data')
      }
    } catch (err) {
      setError('Failed to load user data')
      console.error('Error loading fresh user data:', err)
    } finally {
      setIsLoading(false)
    }
  }



  const saveSettings = async () => {
    setIsSaving(true)
    setError('')

    console.log('Saving settings:', settings)

    try {
      const response = await fetch('/api/user-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      })

      const data = await response.json()
      console.log('Save response:', data)

      if (data.success) {
        console.log('Updating session with:', {
          name: settings.name,
          school: settings.school,
          schoolDistrict: settings.schoolDistrict,
          defaultCurriculum: settings.defaultCurriculum,
          defaultGrade: settings.defaultGrade,
          highContrastMode: settings.highContrastMode,
        })

        // Update the session with all changed settings
        await update({
          ...session,
          user: {
            ...session?.user,
            name: settings.name,
            school: settings.school,
            schoolDistrict: settings.schoolDistrict,
            defaultCurriculum: settings.defaultCurriculum,
            defaultGrade: settings.defaultGrade,
            highContrastMode: settings.highContrastMode,
          },
        })

        console.log('Session updated successfully')
        setIsEditingName(false)
        onClose()
      } else {
        setError(data.error || 'Failed to save settings')
      }
    } catch (err) {
      setError('Failed to save settings')
      console.error('Error saving user settings:', err)
    } finally {
      setIsSaving(false)
    }
  }

  const handleInputChange = (field: keyof UserSettings, value: string | boolean) => {
    setSettings(prev => {
      const newSettings = {
        ...prev,
        [field]: value
      }

      // If curriculum changes, reset grade
      if (field === 'defaultCurriculum') {
        newSettings.defaultGrade = ''
      }

      return newSettings
    })
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-[linear-gradient(150deg,#2B6DFE_0%,#2B6DFE_70%,#00F2FF_100%)] rounded-lg w-full max-w-md shadow-xl text-white overflow-hidden">
        <div className="flex justify-between items-center p-6 border-b border-white/10">
          <h2 className="text-xl font-semibold text-white">Settings</h2>
          <button
            onClick={onClose}
            className="text-white/70 hover:text-white transition-colors"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="bg-gray-50 p-6 space-y-5 text-gray-700 text-sm">
          {error && (
            <div className="p-3 bg-red-100 border border-red-300 rounded-md text-red-700 text-sm">
              {error}
            </div>
          )}

          {isLoading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#2B6DFE]"></div>
            </div>
          ) : (
            <>
              {/* Name */}
              <div className="space-y-1">
                <label className="block font-medium text-gray-600">Name</label>
                <div className="flex items-center">
                  {isEditingName ? (
                    <input
                      type="text"
                      value={settings.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      className="flex-1 py-2 px-3 bg-white border border-gray-200 rounded-md text-gray-700 focus:outline-none focus:ring-2 focus:ring-[#2B6DFE]"
                      autoFocus
                    />
                  ) : (
                    <span className="flex-1 py-2 text-gray-700">{settings.name}</span>
                  )}
                  <button
                    onClick={() => setIsEditingName(!isEditingName)}
                    className="ml-2 text-gray-400 hover:text-[#2B6DFE] transition-colors"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                    </svg>
                  </button>
                </div>
              </div>

              {/* School */}
              <div className="space-y-1">
                <label className="block font-medium text-gray-600">School</label>
                <input
                  type="text"
                  value={settings.school}
                  onChange={(e) => handleInputChange('school', e.target.value)}
                  className="w-full py-2 px-3 bg-white border border-gray-200 rounded-md text-gray-700 focus:outline-none focus:ring-2 focus:ring-[#2B6DFE]"
                  placeholder="Enter your school name"
                />
              </div>

              {/* School District */}
              <div className="space-y-1">
                <label className="block font-medium text-gray-600">School District</label>
                <input
                  type="text"
                  value={settings.schoolDistrict}
                  onChange={(e) => handleInputChange('schoolDistrict', e.target.value)}
                  className="w-full py-2 px-3 bg-white border border-gray-200 rounded-md text-gray-700 focus:outline-none focus:ring-2 focus:ring-[#2B6DFE]"
                  placeholder="Enter your school district"
                />
              </div>

              {/* Display Mode - Separate High Contrast Toggle */}
              <HighContrastToggle />

              {/* Default Curriculum */}
              <div className="space-y-1">
                <label className="block font-medium text-gray-600">Default Curriculum</label>
                <div className="relative">
                  <select
                    value={settings.defaultCurriculum}
                    onChange={(e) => handleInputChange('defaultCurriculum', e.target.value)}
                    className="w-full bg-white border border-gray-200 rounded-md px-3 py-2 text-gray-700 appearance-none focus:outline-none focus:ring-2 focus:ring-[#2B6DFE]"
                    disabled={isLoadingOptions}
                  >
                    <option value="">Select Curriculum</option>
                    {availableCurriculums.map((curriculum) => (
                      <option key={curriculum} value={curriculum}>
                        {getCurriculumDisplayName(curriculum)}
                      </option>
                    ))}
                  </select>
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-chevron-down absolute right-2 top-1/2 h-4 w-4 -translate-y-1/2 pointer-events-none text-gray-400">
                    <path d="m6 9 6 6 6-6"></path>
                  </svg>
                </div>
              </div>

              {/* Default Grade */}
              <div className="space-y-1">
                <label className="block font-medium text-gray-600">Default Grade</label>
                <div className="relative">
                  <select
                    value={settings.defaultGrade}
                    onChange={(e) => handleInputChange('defaultGrade', e.target.value)}
                    className={`w-full border border-gray-200 rounded-md px-3 py-2 appearance-none focus:outline-none focus:ring-2 focus:ring-[#2B6DFE] ${
                      !settings.defaultCurriculum || isLoadingOptions
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'bg-white text-gray-700'
                    }`}
                    disabled={!settings.defaultCurriculum || isLoadingOptions}
                  >
                    <option value="">
                      {!settings.defaultCurriculum ? 'Select curriculum first' : 'Select Grade'}
                    </option>
                    {availableGrades.map((grade) => {
                      // Convert full grade name to short format for value
                      const gradeValue = grade === 'Kindergarten' ? 'K' : grade.replace('Grade ', '')
                      return (
                        <option key={grade} value={gradeValue}>
                          {grade}
                        </option>
                      )
                    })}
                  </select>
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-chevron-down absolute right-2 top-1/2 h-4 w-4 -translate-y-1/2 pointer-events-none text-gray-400">
                    <path d="m6 9 6 6 6-6"></path>
                  </svg>
                </div>
              </div>
            </>
          )}
        </div>

        <div className="p-6 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-3 py-1.5 border border-gray-200 rounded-md text-gray-700 hover:bg-gray-100 transition-colors text-sm"
            disabled={isSaving}
          >
            Cancel
          </button>
          <button
            onClick={saveSettings}
            disabled={isSaving || isLoading}
            className="px-3 py-1.5 bg-gray-700 text-white font-medium rounded-md hover:bg-gray-600 transition-colors text-sm disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSaving ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </div>
    </div>
  )
}
