"use client"

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { useSession } from 'next-auth/react'

interface HighContrastContextType {
  highContrast: boolean
  setHighContrast: (value: boolean) => void
  toggleHighContrast: () => void
}

const HighContrastContext = createContext<HighContrastContextType | undefined>(undefined)

export function HighContrastProvider({ children }: { children: ReactNode }) {
  const { data: session, update } = useSession()

  // Initialize from localStorage immediately to avoid flash
  const [highContrast, setHighContrastState] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('highContrastMode')
      return saved === 'true'
    }
    return false
  })
  const [initialized, setInitialized] = useState(false)

  // Initialize from session when available, or mark as initialized
  useEffect(() => {
    if (!initialized) {
      if (session?.user?.highContrastMode !== undefined && session?.user?.highContrastMode !== null) {
        // Session is available, use session value
        const sessionMode = Boolean(session.user.highContrastMode)
        setHighContrastState(sessionMode)
        setInitialized(true)
        console.log('High contrast initialized from session:', sessionMode)
      } else if (session?.user) {
        // Session is available but no highContrastMode field, mark as initialized
        setInitialized(true)
        console.log('High contrast initialized from localStorage (session has no highContrastMode):', highContrast)
      }
      // If session is not available yet, we'll wait for it
    }
    // If session becomes available later and has different value
    else if (initialized && session?.user?.highContrastMode !== undefined && session?.user?.highContrastMode !== null) {
      const sessionMode = Boolean(session.user.highContrastMode)
      if (sessionMode !== highContrast) {
        setHighContrastState(sessionMode)
        console.log('High contrast updated from session after initialization:', sessionMode)
      }
    }
  }, [session?.user?.highContrastMode, session?.user, initialized, highContrast])

  // Update localStorage when high contrast changes
  useEffect(() => {
    if (initialized && typeof window !== 'undefined') {
      localStorage.setItem('highContrastMode', highContrast.toString())
    }
  }, [highContrast, initialized])

  // Apply high contrast class to body
  useEffect(() => {
    const body = document.getElementById('app-body')
    if (body) {
      if (highContrast) {
        body.classList.add('high-contrast-content')
      } else {
        body.classList.remove('high-contrast-content')
      }
    }
  }, [highContrast])

  // Function to update high contrast mode and save to user settings
  const setHighContrast = async (value: boolean) => {
    setHighContrastState(value)

    // Update user settings in database - use session data to avoid extra GET request
    try {
      if (session?.user) {
        // Use session data instead of fetching current settings
        const response = await fetch('/api/user-settings', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: session.user.name || '',
            school: session.user.school || '',
            schoolDistrict: session.user.schoolDistrict || '',
            defaultCurriculum: session.user.defaultCurriculum || 'CCSS',
            defaultGrade: session.user.defaultGrade || '',
            highContrastMode: value,
          }),
        })

        if (response.ok) {
          // Update session
          await update({
            ...session,
            user: {
              ...session?.user,
              highContrastMode: value,
            },
          })
        }
      }
    } catch (error) {
      console.error('Failed to update high contrast mode:', error)
    }
  }

  // Remove the polling - we'll rely on session updates instead
  // The session will be updated when settings are saved from the modal

  const toggleHighContrast = () => {
    setHighContrast(!highContrast)
  }

  return (
    <HighContrastContext.Provider value={{ 
      highContrast, 
      setHighContrast, 
      toggleHighContrast 
    }}>
      {children}
    </HighContrastContext.Provider>
  )
}

export function useHighContrast() {
  const context = useContext(HighContrastContext)
  if (context === undefined) {
    throw new Error('useHighContrast must be used within a HighContrastProvider')
  }
  return context
}
