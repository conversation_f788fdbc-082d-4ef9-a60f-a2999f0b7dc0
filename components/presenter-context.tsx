"use client"

import type React from "react"
import { createContext, useContext, useState, useEffect } from "react"

type PresenterContextType = {
  isPresenterMode: boolean
  setIsPresenterMode: (value: boolean) => void
  isAudienceView: boolean
  setIsAudienceView: (value: boolean) => void
  presenterNotes: Record<number, string>
  currentSlide: number
  setCurrentSlide: (slide: number) => void
  revealedItems: number[]
  setRevealedItems: (items: number[]) => void
  isPopupVisible: boolean
  setIsPopupVisible: (visible: boolean) => void
  isShown: boolean
  setIsShown: (shown: boolean) => void
  elapsedTime: number
  startTime: number | null
  startTimer: () => void
  pauseTimer: () => void
  resetTimer: () => void
}

const defaultPresenterContext: PresenterContextType = {
  isPresenterMode: false,
  setIsPresenterMode: () => {},
  isAudienceView: false,
  setIsAudienceView: () => {},
  presenterNotes: {},
  currentSlide: 1,
  setCurrentSlide: () => {},
  revealedItems: [],
  setRevealedItems: () => {},
  isPopupVisible: true,
  setIsPopupVisible: () => {},
  isShown: true,
  setIsShown: () => {},
  elapsedTime: 0,
  startTime: null,
  startTimer: () => {},
  pauseTimer: () => {},
  resetTimer: () => {},
}

// Empty presenter notes - will be loaded from database
const samplePresenterNotes: Record<number, string> = {}

export const PresenterContext = createContext<PresenterContextType>(defaultPresenterContext)

export const usePresenter = () => useContext(PresenterContext)

// Check if Broadcast Channel API is supported
const isBroadcastChannelSupported = typeof BroadcastChannel !== "undefined"

// Unique ID for this session to avoid conflicts with other tabs
const SESSION_ID = Date.now().toString()

export const PresenterProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Use useEffect for state initialization to avoid hydration issues
  const [isPresenterMode, setIsPresenterMode] = useState(false)
  const [isAudienceView, setIsAudienceView] = useState(false)
  const [currentSlide, setCurrentSlide] = useState(1)
  const [revealedItems, setRevealedItems] = useState<number[]>([])
  const [isPopupVisible, setIsPopupVisible] = useState(true)
  const [isShown, setIsShown] = useState(true)
  const [elapsedTime, setElapsedTime] = useState(0)
  const [startTime, setStartTime] = useState<number | null>(null)
  const [isRunning, setIsRunning] = useState(false)
  const [channel, setChannel] = useState<BroadcastChannel | null>(null)
  const [isClient, setIsClient] = useState(false)

  // Set isClient to true once component mounts
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Initialize communication channel - only run on client side
  useEffect(() => {
    if (!isClient) return; // Skip during server-side rendering

    let broadcastChannel: BroadcastChannel | null = null

    // Set up Broadcast Channel if supported
    if (isBroadcastChannelSupported) {
      try {
        broadcastChannel = new BroadcastChannel("math-lesson-presenter")
        setChannel(broadcastChannel)

        // Debug log
        console.log("Broadcast Channel initialized")

        // Listen for messages
        broadcastChannel.onmessage = (event) => {
          const { type, data, sessionId } = event.data

          // Ignore messages from this same window
          if (sessionId === SESSION_ID) return

          console.log("Received message:", event.data)

          switch (type) {
            case "CHANGE_SLIDE":
              if (data.slide !== currentSlide) {
                setCurrentSlide(data.slide)
              }
              break
            case "SET_REVEALED_ITEMS":
              // Check if arrays are different before updating
              const isDifferent =
                data.items.length !== revealedItems.length ||
                data.items.some((item: number, index: number) => item !== revealedItems[index])

              if (isDifferent) {
                setRevealedItems(data.items)
              }
              break
            case "SET_POPUP_VISIBLE":
              setIsPopupVisible(data.visible)
              break
            case "SET_IS_SHOWN":
              setIsShown(data.shown)
              break
            case "TIMER_UPDATE":
              setElapsedTime(data.time)
              setIsRunning(data.isRunning)
              setStartTime(data.isRunning ? Date.now() - data.time : null)
              break
          }
        }
      } catch (error) {
        console.error("Error initializing Broadcast Channel:", error)
        broadcastChannel = null
      }
    }

    // Set up localStorage fallback listener
    const handleStorageChange = (e: StorageEvent) => {
      if (!e.key || !e.newValue) return

      if (e.key === "presenter_slide") {
        const newSlide = Number.parseInt(e.newValue, 10)
        if (!isNaN(newSlide) && newSlide !== currentSlide) {
          console.log("Storage event: changing slide to", newSlide)
          setCurrentSlide(newSlide)
        }
      } else if (e.key === "presenter_revealed_items") {
        try {
          const newItems = JSON.parse(e.newValue)
          if (Array.isArray(newItems)) {
            // Check if arrays are different before updating
            const isDifferent =
              newItems.length !== revealedItems.length ||
              newItems.some((item: number, index: number) => item !== revealedItems[index])

            if (isDifferent) {
              console.log("Storage event: updating revealed items to", newItems)
              setRevealedItems(newItems)
            }
          }
        } catch (error) {
          console.error("Error parsing revealed items from localStorage", error)
        }
      } else if (e.key === "presenter_popup_visible") {
        const isVisible = e.newValue === "true"
        console.log("Storage event: updating popup visibility to", isVisible)
        setIsPopupVisible(isVisible)
      } else if (e.key === "presenter_is_shown") {
        const shown = e.newValue === "true"
        console.log("Storage event: updating is shown to", shown)
        setIsShown(shown)
      }
    }

    window.addEventListener("storage", handleStorageChange)

    // Clean up
    return () => {
      if (broadcastChannel) {
        broadcastChannel.close()
      }
      window.removeEventListener("storage", handleStorageChange)
    }
  }, [isClient, currentSlide, revealedItems]) // Add isClient dependency

  // Function to send updates to other windows - only run on client side
  const sendUpdate = (type: string, data: any) => {
    if (!isClient) return; // Skip during server-side rendering

    // Use Broadcast Channel if available
    if (channel) {
      try {
        const message = { type, data, sessionId: SESSION_ID }
        console.log("Sending message via Broadcast Channel:", message)
        channel.postMessage(message)
      } catch (error) {
        console.error("Error sending message via Broadcast Channel:", error)
      }
    }

    // Always use localStorage as a fallback
    try {
      if (type === "CHANGE_SLIDE") {
        localStorage.setItem("presenter_slide", data.slide.toString())
      } else if (type === "SET_REVEALED_ITEMS") {
        localStorage.setItem("presenter_revealed_items", JSON.stringify(data.items))
      } else if (type === "SET_POPUP_VISIBLE") {
        localStorage.setItem("presenter_popup_visible", data.visible.toString())
      } else if (type === "SET_IS_SHOWN") {
        localStorage.setItem("presenter_is_shown", data.shown.toString())
      }
    } catch (error) {
      console.error("Error updating localStorage:", error)
    }
  }

  // Custom slide setter that also sends updates - only run on client side
  const handleSetCurrentSlide = (slide: number) => {
    // Only update if the slide has actually changed and we're on the client
    if (slide !== currentSlide) {
      setCurrentSlide(slide)

      // Update URL parameter for slide
      if (isClient) {
        try {
          const url = new URL(window.location.href);
          url.searchParams.set('slide', slide.toString());
          window.history.replaceState({}, '', url.toString());
          console.log('Updated slide in URL:', slide);
        } catch (error) {
          console.error("Error updating slide in URL:", error);
        }
      }

      if (isClient && (isPresenterMode || isAudienceView)) {
        sendUpdate("CHANGE_SLIDE", { slide })
      }
    }
  }

  // Custom revealed items setter that also sends updates - only run on client side
  const handleSetRevealedItems = (items: number[]) => {
    // Check if the arrays are different before updating
    const isDifferent =
      items.length !== revealedItems.length || items.some((item, index) => item !== revealedItems[index])

    if (isDifferent) {
      setRevealedItems(items)
      if (isClient && (isPresenterMode || isAudienceView)) {
        sendUpdate("SET_REVEALED_ITEMS", { items })
      }
    }
  }

  // Custom popup visibility setter that also sends updates
  const handleSetIsPopupVisible = (visible: boolean) => {
    if (visible !== isPopupVisible) {
      setIsPopupVisible(visible)
      if (isClient && (isPresenterMode || isAudienceView)) {
        sendUpdate("SET_POPUP_VISIBLE", { visible })
      }
    }
  }

  // Custom isShown setter that also sends updates
  const handleSetIsShown = (shown: boolean) => {
    if (shown !== isShown) {
      setIsShown(shown)
      if (isClient && (isPresenterMode || isAudienceView)) {
        sendUpdate("SET_IS_SHOWN", { shown })
      }
    }
  }

  // Timer functionality - only run on client side
  useEffect(() => {
    if (!isClient) return; // Skip during server-side rendering

    let interval: NodeJS.Timeout | null = null

    if (isRunning && startTime) {
      interval = setInterval(() => {
        const newElapsedTime = Date.now() - startTime
        setElapsedTime(newElapsedTime)

        if (isPresenterMode || isAudienceView) {
          sendUpdate("TIMER_UPDATE", {
            time: newElapsedTime,
            isRunning: true,
            startTime,
          })
        }
      }, 1000)
    } else if (interval) {
      clearInterval(interval)
    }

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [isClient, isRunning, startTime, isPresenterMode])

  const startTimer = () => {
    if (!isClient) return; // Skip during server-side rendering

    const now = Date.now()
    setStartTime(now - elapsedTime)
    setIsRunning(true)

    if (isPresenterMode || isAudienceView) {
      sendUpdate("TIMER_UPDATE", {
        time: elapsedTime,
        isRunning: true,
        startTime: now - elapsedTime,
      })
    }
  }

  const pauseTimer = () => {
    if (!isClient) return; // Skip during server-side rendering

    setIsRunning(false)

    if (isPresenterMode || isAudienceView) {
      sendUpdate("TIMER_UPDATE", {
        time: elapsedTime,
        isRunning: false,
      })
    }
  }

  const resetTimer = () => {
    if (!isClient) return; // Skip during server-side rendering

    setElapsedTime(0)
    setStartTime(null)
    setIsRunning(false)

    if (isPresenterMode || isAudienceView) {
      sendUpdate("TIMER_UPDATE", {
        time: 0,
        isRunning: false,
        startTime: null,
      })
    }
  }

  // Initialize from localStorage on mount - only run on client side
  useEffect(() => {
    if (!isClient) return; // Skip during server-side rendering

    try {
      // Only initialize from localStorage if we're in audience view
      if (window.location.pathname.includes("/audience")) {
        const storedSlide = localStorage.getItem("presenter_slide")
        if (storedSlide) {
          const slideNum = Number.parseInt(storedSlide, 10)
          if (!isNaN(slideNum)) {
            console.log("Initializing from localStorage: slide", slideNum)
            setCurrentSlide(slideNum)
          }
        }

        const storedItems = localStorage.getItem("presenter_revealed_items")
        if (storedItems) {
          try {
            const items = JSON.parse(storedItems)
            if (Array.isArray(items)) {
              console.log("Initializing from localStorage: revealed items", items)
              setRevealedItems(items)
            }
          } catch (e) {
            console.error("Error parsing revealed items from localStorage", e)
          }
        }
      }
    } catch (error) {
      console.error("Error initializing from localStorage:", error)
    }
  }, [isClient])

  return (
    <PresenterContext.Provider
      value={{
        isPresenterMode,
        setIsPresenterMode,
        isAudienceView,
        setIsAudienceView,
        presenterNotes: samplePresenterNotes,
        currentSlide,
        setCurrentSlide: handleSetCurrentSlide,
        revealedItems,
        setRevealedItems: handleSetRevealedItems,
        isPopupVisible,
        setIsPopupVisible: handleSetIsPopupVisible,
        isShown,
        setIsShown: handleSetIsShown,
        elapsedTime,
        startTime,
        startTimer,
        pauseTimer,
        resetTimer,
      }}
    >
      {children}
    </PresenterContext.Provider>
  )
}
