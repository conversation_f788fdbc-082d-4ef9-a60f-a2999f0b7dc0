"use client"

import React, { useState, useEffect } from 'react'
import { X, Save, Code, Eye, Wand2, Loader2, Settings } from 'lucide-react'
import { isSuperAdmin } from '@/utils/adminUtils'
import SettingsModal from './settings-modal'
import { Session } from 'next-auth'

interface HtmlEditorModalProps {
  isOpen: boolean
  onClose: () => void
  initialHtml: string
  onSave: (html: string) => void
  onSaveComplete?: () => void // Callback after successful save
  slideId?: string
  slideNumber?: number
  documentId?: string
  unitNumber?: string
  lessonNumber?: string
  gradeLevel?: string
  curriculum?: string
  lang?: string
  session?: Session | null
}

export default function HtmlEditorModal({
  isOpen,
  onClose,
  initialHtml,
  onSave,
  onSaveComplete,
  slideNumber,
  documentId,
  unitNumber,
  lessonNumber,
  gradeLevel,
  curriculum,
  lang,
  session
}: HtmlEditorModalProps) {
  const [html, setHtml] = useState(initialHtml || '')
  const [previewMode, setPreviewMode] = useState(false)
  const [isCheckingDocument, setIsCheckingDocument] = useState(false)
  const [documentExists, setDocumentExists] = useState(true)
  const [prompt, setPrompt] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [existingJob, setExistingJob] = useState<any>(null)
  const [showSettings, setShowSettings] = useState(false)

  useEffect(() => {
    setHtml(initialHtml || '')
  }, [initialHtml])

  // Check if the document exists when the modal opens
  useEffect(() => {
    if (isOpen && unitNumber && lessonNumber && gradeLevel) {
      checkDocumentExists()
      findExistingJob()
    }
  }, [isOpen, documentId, unitNumber, lessonNumber, gradeLevel, slideNumber])

  // Initialize prompt from initialHtml if it's not HTML
  useEffect(() => {
    if (isOpen && initialHtml && !initialHtml.includes('<')) {
      setPrompt(initialHtml)
    }
  }, [isOpen, initialHtml])

  // Function to check if the document exists
  const checkDocumentExists = async () => {
    if (!unitNumber || !lessonNumber || !gradeLevel) return

    try {
      setIsCheckingDocument(true)

      // Create the correct documentId format: unitNumber-lessonNumber-gradeLevel
      const correctDocumentId = `${unitNumber}-${lessonNumber}-${gradeLevel}`

      // Call the API to check if the document exists
      const response = await fetch(`/api/available-documents?unit_number=${unitNumber}&lesson_number=${lessonNumber}&grade_level=${encodeURIComponent(gradeLevel || '')}`)

      if (!response.ok) {
        throw new Error('Failed to check document existence')
      }

      const data = await response.json()

      // Check if the document exists in the results
      const exists = data.documents && data.documents.some((doc: any) => doc.id === correctDocumentId)
      setDocumentExists(exists)

      console.log(`Document ${correctDocumentId} exists: ${exists}`)

      if (!exists) {
        console.warn(`Document ${correctDocumentId} not found. Available documents:`, data.documents)
      }
    } catch (error) {
      console.error('Error checking document existence:', error)
    } finally {
      setIsCheckingDocument(false)
    }
  }

  // Function to find existing job for this slide
  const findExistingJob = async () => {
    if (!unitNumber || !lessonNumber || !gradeLevel || !slideNumber) {
      console.log('Missing parameters for job search:', { unitNumber, lessonNumber, gradeLevel, slideNumber })
      return
    }

    try {
      // Create the correct documentId format: unitNumber-lessonNumber-gradeLevel
      const correctDocumentId = `${unitNumber}-${lessonNumber}-${gradeLevel}`
      console.log('Looking for jobs with documentId:', correctDocumentId, 'slideNumber:', slideNumber)
      console.log('Parameters received:', { unitNumber, lessonNumber, gradeLevel, slideNumber })

      const response = await fetch(`/api/jobs?documentId=${encodeURIComponent(correctDocumentId)}`)
      const data = await response.json()

      if (data.success && data.jobs) {
        console.log('Found jobs:', data.jobs.length)
        // Find job for this specific slide
        const job = data.jobs.find((j: any) => j.slideNumber === slideNumber)
        if (job) {
          setExistingJob(job)
          // If we have a job with a prompt, use it
          if (job.prompt && !prompt) {
            setPrompt(job.prompt)
          }
          console.log('Found existing job for slide:', job)
        } else {
          console.log('No job found for slideNumber:', slideNumber, 'Available slides:', data.jobs.map((j: any) => j.slideNumber))
        }
      } else {
        console.log('No jobs found for documentId:', correctDocumentId)
      }
    } catch (error) {
      console.error('Error finding existing job:', error)
    }
  }

  // Function to generate HTML from prompt
  const generateHtml = async () => {
    if (!prompt.trim()) {
      alert('Please enter a prompt for HTML generation')
      return
    }

    try {
      setIsGenerating(true)
      console.log('Generating HTML from prompt:', prompt)

      const response = await fetch('/api/generate-html', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: prompt.trim(),
          slideNumber,
          unitNumber,
          lessonNumber,
          gradeLevel,
          curriculum,
          lang
        }),
      })

      const data = await response.json()

      if (data.success) {
        setHtml(data.html)
        console.log('HTML generated successfully')
      } else {
        alert(`Failed to generate HTML: ${data.error}`)
        console.error('HTML generation failed:', data.error)
      }
    } catch (error) {
      console.error('Error generating HTML:', error)
      alert('Failed to generate HTML. Please try again.')
    } finally {
      setIsGenerating(false)
    }
  }

  if (!isOpen) return null

  const handleSave = async () => {
    try {
      console.log('HtmlEditorModal: *** STARTING SAVE PROCESS ***')
      console.log('HtmlEditorModal: onSaveComplete callback exists?', !!onSaveComplete)

      // Call the onSave function passed from parent component
      console.log('HtmlEditorModal: Calling onSave...')
      await onSave(html)
      console.log('HtmlEditorModal: onSave completed')

      // Close the modal
      console.log('HtmlEditorModal: Closing modal...')
      onClose()

      // Call the completion callback if provided
      if (onSaveComplete) {
        console.log('HtmlEditorModal: *** CALLING onSaveComplete CALLBACK ***')
        setTimeout(() => {
          console.log('HtmlEditorModal: *** EXECUTING onSaveComplete CALLBACK ***')
          onSaveComplete()
          console.log('HtmlEditorModal: *** onSaveComplete CALLBACK EXECUTED ***')
        }, 500) // Small delay to ensure save is complete
      } else {
        console.log('HtmlEditorModal: *** NO onSaveComplete CALLBACK PROVIDED ***')
      }

      console.log('HtmlEditorModal: HTML saved, modal closed')
    } catch (error) {
      console.error('Error in handleSave:', error)
    }
  }

  return (
    <div className="fixed !z-[500] inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="w-full max-w-4xl h-[80vh] bg-white dark:bg-gray-800 rounded-lg shadow-xl flex flex-col">
        {/* Header */}
        <div className="flex relative items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
              Edit HTML Content {slideNumber ? `for Slide ${slideNumber}` : ''}
            </h2>
            {unitNumber && lessonNumber && gradeLevel && (
              <div className="mt-1 text-sm">
                Document ID: <span className="font-mono">{unitNumber}-{lessonNumber}-{gradeLevel}</span>
                {isCheckingDocument && (
                  <span className="ml-2 text-blue-500">Checking...</span>
                )}
                {!isCheckingDocument && !documentExists && (
                  <span className="ml-2 text-amber-600 dark:text-amber-400">
                    (Warning: Document not found in database)
                  </span>
                )}
                {!isCheckingDocument && documentExists && (
                  <span className="ml-2 text-green-600 dark:text-green-400">
                    (Document exists)
                  </span>
                )}
              </div>
            )}
          </div>
          <div className="flex items-center space-x-2">
            {/* Preview/Edit toggle button - only visible for super admins */}
            {isSuperAdmin(session) && (
              <button
                onClick={() => setPreviewMode(!previewMode)}
                className={`p-2 rounded-md ${previewMode
                    ? 'bg-blue-100 text-blue-600 text-black dark:bg-blue-900/30 dark:text-blue-400'
                    : 'hover:bg-gray-100 dark:hover:bg-gray-700'
                  }`}
                title={previewMode ? "Edit mode" : "Preview mode"}
              >
                {previewMode ? <Code size={20} /> : <Eye size={20} />}
              </button>
            )}
            <button
              onClick={onClose}
              className="p-2 rounded-md text-black hover:bg-gray-100 dark:hover:bg-gray-700"
              title="Close"
            >
              <X size={20} />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          {previewMode ? (
            <div className="h-full overflow-auto p-4 bg-white dark:bg-gray-900">
              <div
                className="p-4 border border-gray-200 dark:border-gray-700 rounded-md min-h-[200px] bg-white dark:bg-gray-800"
                dangerouslySetInnerHTML={{ __html: html }}
              />
            </div>
          ) : (
            <textarea
              value={html}
              onChange={(e) => setHtml(e.target.value)}
              className="w-full h-full p-4 font-mono text-sm resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-900 text-gray-900 dark:text-gray-100 border-none"
              placeholder="Enter HTML content here..."
            />
          )}
        </div>

        {/* Regenerate Prompt Section - only show when we have existing job */}
        {existingJob && existingJob.prompt && (
          <div className="p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
            <div className="mb-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Original Prompt (from job {existingJob.jobId}):
              </label>
              <textarea
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                className="w-full h-20 p-2 border border-gray-300 dark:border-gray-600 rounded-md resize-none text-sm dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                placeholder="Edit the prompt for regeneration..."
              />
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {existingJob ? (
              <span>Found job: {existingJob.jobId} | Status: {existingJob.status}</span>
            ) : (
              <span>No existing job found for this slide</span>
            )}
          </div>
          <div className="flex gap-2">
            {/* Settings button - show for super admins */}
            {isSuperAdmin(session) && (
              <button
                onClick={() => setShowSettings(true)}
                className="flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
                title="Global HTML Generation Settings"
              >
                <Settings size={16} />
                Settings
              </button>
            )}

            {/* Generate HTML button - show when no existing job */}
            {isSuperAdmin(session) && !existingJob && (
              <button
                onClick={() => {
                  const promptText = initialHtml && !initialHtml.includes('<') ? initialHtml :
                    window.prompt('Enter a prompt to generate HTML:')
                  if (promptText) {
                    setPrompt(promptText)
                    generateHtml()
                  }
                }}
                disabled={isGenerating}
                className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isGenerating ? (
                  <Loader2 size={16} className="animate-spin" />
                ) : (
                  <Wand2 size={16} />
                )}
                {isGenerating ? 'Generating...' : 'Generate HTML'}
              </button>
            )}

            {/* Regenerate HTML button - show when we have existing job */}
            {isSuperAdmin(session) && existingJob && existingJob.prompt && (
              <button
                onClick={generateHtml}
                disabled={isGenerating || !prompt.trim()}
                className="flex items-center gap-2 px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isGenerating ? (
                  <Loader2 size={16} className="animate-spin" />
                ) : (
                  <Wand2 size={16} />
                )}
                {isGenerating ? 'Regenerating...' : 'Regenerate HTML'}
              </button>
            )}

            <button
              onClick={handleSave}
              disabled={!html.trim()}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Save size={16} />
              Save HTML
            </button>
          </div>
        </div>

        {/* Settings Modal */}
        <SettingsModal
          isOpen={showSettings}
          onClose={() => setShowSettings(false)}
        />
      </div>
    </div>
  )
}
