"use client"

import React, { createContext, useContext, useState, useEffect } from "react"

type LessonContextType = {
  unitNumber: string
  setUnitNumber: (value: string) => void
  lessonNumber: string
  setLessonNumber: (value: string) => void
  gradeLevel: string
  setGradeLevel: (value: string) => void
  lang: string | undefined
  setLang: (value: string | undefined) => void
  totalSlides: number
  setTotalSlides: (value: number) => void
  updateUrlParams: () => void
  hasTranslation: boolean
}

const defaultLessonContext: LessonContextType = {
  unitNumber: "",
  setUnitNumber: () => {},
  lessonNumber: "",
  setLessonNumber: () => {},
  gradeLevel: "",
  setGradeLevel: () => {},
  lang: undefined,
  setLang: () => {},
  totalSlides: 26,
  setTotalSlides: () => {},
  updateUrlParams: () => {},
  hasTranslation: false
}

export const LessonContext = createContext<LessonContextType>(defaultLessonContext)

export const useLessonContext = () => useContext(LessonContext)

export const LessonProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Initialize state with empty values
  const [unitNumber, setUnitNumberState] = useState<string>("")
  const [lessonNumber, setLessonNumberState] = useState<string>("")
  const [gradeLevel, setGradeLevelState] = useState<string>("")
  const [lang, setLangState] = useState<string | undefined>(undefined)
  const [totalSlides, setTotalSlidesState] = useState<number>(26)
  const [isClient, setIsClient] = useState(false)
  const [hasTranslation, setHasTranslation] = useState<boolean>(false)

  // Set isClient to true once component mounts
  useEffect(() => {
    setIsClient(true)
  }, [])

  // We're not loading state from localStorage on initial load
  // This ensures dropdowns start empty as required
  useEffect(() => {
    if (!isClient) return

    try {
      // Only load total slides from localStorage
      const storedTotalSlides = localStorage.getItem("lesson_total_slides")
      if (storedTotalSlides) {
        setTotalSlidesState(parseInt(storedTotalSlides, 10))
      }

      // Clear any previously stored values to ensure fresh start
      localStorage.removeItem("lesson_unit_number")
      localStorage.removeItem("lesson_lesson_number")
      localStorage.removeItem("lesson_grade_level")
    } catch (error) {
      console.error("Error handling localStorage:", error)
    }
  }, [isClient])

  // Function to update URL parameters
  const updateUrlParams = () => {
    if (!isClient) return

    try {
      const url = new URL(window.location.href)

      // Only add parameters that have values
      if (unitNumber) {
        url.searchParams.set('unit', unitNumber)
      } else {
        url.searchParams.delete('unit')
      }

      if (lessonNumber) {
        url.searchParams.set('lesson', lessonNumber)
      } else {
        url.searchParams.delete('lesson')
      }

      if (gradeLevel) {
        url.searchParams.set('grade', gradeLevel)
      } else {
        url.searchParams.delete('grade')
      }

      if (lang) {
        url.searchParams.set('lang', lang)
      } else {
        url.searchParams.delete('lang')
      }

      // Update the URL without reloading the page
      window.history.pushState({}, '', url.toString())

      console.log('Updated URL parameters:', {
        unit: unitNumber,
        lesson: lessonNumber,
        grade: gradeLevel,
        lang
      })
    } catch (error) {
      console.error("Error updating URL parameters:", error)
    }
  }

  // Custom setters that update localStorage and URL parameters
  const setUnitNumber = (value: string) => {
    // Update state
    setUnitNumberState(value)

    if (isClient) {
      try {
        // Update localStorage
        localStorage.setItem("lesson_unit_number", value)

        // Update URL parameters immediately
        const url = new URL(window.location.href)
        if (value) {
          url.searchParams.set('unit', value)
        } else {
          url.searchParams.delete('unit')
        }
        window.history.pushState({}, '', url.toString())

        console.log('Updated unit in URL:', value)
      } catch (error) {
        console.error("Error saving unit number to localStorage:", error)
      }
    }
  }

  const setLessonNumber = (value: string) => {
    // Update state
    setLessonNumberState(value)

    if (isClient) {
      try {
        // Update localStorage
        localStorage.setItem("lesson_lesson_number", value)

        // Update URL parameters immediately
        const url = new URL(window.location.href)
        if (value) {
          url.searchParams.set('lesson', value)
        } else {
          url.searchParams.delete('lesson')
        }
        window.history.pushState({}, '', url.toString())

        console.log('Updated lesson in URL:', value)
      } catch (error) {
        console.error("Error saving lesson number to localStorage:", error)
      }
    }
  }

  const setGradeLevel = (value: string) => {
    // Update state
    setGradeLevelState(value)

    if (isClient) {
      try {
        // Update localStorage
        localStorage.setItem("lesson_grade_level", value)

        // Update URL parameters immediately
        const url = new URL(window.location.href)
        if (value) {
          url.searchParams.set('grade', value)
        } else {
          url.searchParams.delete('grade')
        }
        window.history.pushState({}, '', url.toString())

        console.log('Updated grade in URL:', value)
      } catch (error) {
        console.error("Error saving grade level to localStorage:", error)
      }
    }
  }

  const setLang = (value: string | undefined) => {
    setLangState(value)

    if (isClient) {
      try {
        // Save language preference
        localStorage.setItem("lesson_lang", value || "en")

        // Update URL with lang parameter
        const url = new URL(window.location.href)
        if (value && value !== 'en') {
          url.searchParams.set('lang', value)
        } else {
          url.searchParams.delete('lang')
        }
        window.history.pushState({}, '', url.toString())

        console.log('Language updated to:', value || 'en')
      } catch (error) {
        console.error("Error updating language:", error)
      }
    }
  }

  const setTotalSlides = (value: number) => {
    setTotalSlidesState(value)
    if (isClient) {
      try {
        localStorage.setItem("lesson_total_slides", value.toString())
      } catch (error) {
        console.error("Error saving total slides to localStorage:", error)
      }
    }
  }

  // Function to check if translation is available
  const checkTranslationAvailability = async () => {
    if (!unitNumber || !lessonNumber || !gradeLevel) {
      setHasTranslation(false)
      return
    }

    try {
      const response = await fetch(`/api/check-translation?unit_number=${unitNumber}&lesson_number=${lessonNumber}&grade_level=${encodeURIComponent(gradeLevel)}&lang=esp`)
      const data = await response.json()
      setHasTranslation(data.exists)
      console.log('Translation availability:', data.exists)
    } catch (error) {
      console.error('Error checking translation availability:', error)
      setHasTranslation(false)
    }
  }

  // Check translation availability when lesson parameters change
  useEffect(() => {
    if (isClient && unitNumber && lessonNumber && gradeLevel) {
      checkTranslationAvailability()
    }
  }, [unitNumber, lessonNumber, gradeLevel, isClient])

  return (
    <LessonContext.Provider
      value={{
        unitNumber,
        setUnitNumber,
        lessonNumber,
        setLessonNumber,
        gradeLevel,
        setGradeLevel,
        lang,
        setLang,
        totalSlides,
        setTotalSlides,
        updateUrlParams,
        hasTranslation,
      }}
    >
      {children}
    </LessonContext.Provider>
  )
}
