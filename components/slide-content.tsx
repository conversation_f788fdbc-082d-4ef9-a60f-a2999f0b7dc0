"use client"

import { useState, useEffect, useRef, use<PERSON><PERSON>back, useMemo } from "react"
import React from "react"
import { getSlide } from "@/services/slideService"
import { SlideImage } from "./slides/SlideImage"
import { ConceptSlide } from "./slides/ConceptSlide"
import { IntroductionSlide } from "./slides/IntroductionSlide"
import { PrintablesSlide } from "./slides/PrintablesSlide"
import { PracticeSlide } from "./slides/PracticeSlide"
import { TrySlide } from "./slides/TrySlide"

import { SlideType } from "@/types/slideTypes"

interface SlideContentProps {
  slideNumber: number
  highContrast?: boolean
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
  setRevealedItems?: React.Dispatch<React.SetStateAction<number[]>>
  scale?: number // Scale percentage (e.g., 70 for 70%)
  unitNumber?: string // Unit number to load specific unit data
  lessonNumber?: string // Lesson number to load specific lesson data
  gradeLevel?: string // Grade level to load specific grade data
  lang?: string // Language code (en, esp) to load specific language data
}

// Use React.memo to prevent unnecessary re-renders
export const SlideContent = React.memo(function SlideContent({
  slideNumber,
  highContrast = false,
  revealedItems = [],
  registerRevealableItems = () => {},
  setRevealedItems = () => {},
  scale = 100, // Default to 100% scale
  unitNumber = "2", // Default to unit 2
  lessonNumber = "1", // Default to lesson 1
  gradeLevel = "Grade 1", // Default to Grade 1
  lang // Optional language code
}: SlideContentProps) {
  const [slideData, setSlideData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Use refs to track previous values and avoid unnecessary re-renders
  const prevSlideNumberRef = useRef(slideNumber);
  const prevUnitNumberRef = useRef(unitNumber);
  const prevLessonNumberRef = useRef(lessonNumber);
  const prevGradeLevelRef = useRef(gradeLevel);
  const prevLangRef = useRef(lang);

  // Create a ref for tracking request IDs to avoid race conditions
  const requestIdRef = useRef(0);

  // Load slide data when component mounts or slideNumber or unitNumber changes
  useEffect(() => {
    // Skip if any required parameter is missing
    if (!unitNumber || !lessonNumber || !gradeLevel) {
      if (process.env.NODE_ENV === 'development') {
        console.log(`Skipping slide load - missing parameters: unitNumber=${unitNumber}, lessonNumber=${lessonNumber}, gradeLevel=${gradeLevel}`);
      }
      // Set loading to false when parameters are missing so we show EMBRS logo instead
      setLoading(false);
      setSlideData(null);
      setError(null);
      return;
    }

    console.log(`🎯 SlideContent: Loading slide ${slideNumber} with params:`, {
      unitNumber,
      lessonNumber,
      gradeLevel,
      lang
    });

    // Check if any of the key parameters have changed
    const hasChanged =
      prevSlideNumberRef.current !== slideNumber ||
      prevUnitNumberRef.current !== unitNumber ||
      prevLessonNumberRef.current !== lessonNumber ||
      prevGradeLevelRef.current !== gradeLevel ||
      prevLangRef.current !== lang;

    // Update refs with current values
    prevSlideNumberRef.current = slideNumber;
    prevUnitNumberRef.current = unitNumber;
    prevLessonNumberRef.current = lessonNumber;
    prevGradeLevelRef.current = gradeLevel;
    prevLangRef.current = lang;

    // If nothing has changed and we already have data, skip the fetch
    if (!hasChanged && slideData) {
      return;
    }

    // Create a cache key for this specific slide request
    const cacheKey = `slide-${slideNumber}-${unitNumber}-${lessonNumber}-${gradeLevel}${lang ? `-${lang}` : ''}`;

    // Check if we already have this data in memory
    const cachedData = (window as any).__slideCache?.[cacheKey];
    if (cachedData) {
      if (process.env.NODE_ENV === 'development') {
        console.log(`Using in-memory cached data for ${cacheKey}`);
      }
      setSlideData(cachedData);
      setLoading(false);
      return;
    }

    // Create a unique request ID to avoid race conditions
    const requestId = Date.now();
    requestIdRef.current = requestId;

    let isMounted = true;
    const fetchData = async () => {
      try {
        setLoading(true);

        if (process.env.NODE_ENV === 'development') {
          console.log(`Fetching slide data: slide=${slideNumber}, unit=${unitNumber}, lesson=${lessonNumber}, grade=${gradeLevel}, lang=${lang || 'default'}`);
        }

        const data = await getSlide(slideNumber, unitNumber, lessonNumber, gradeLevel, lang);

        console.log(`📊 SlideContent: getSlide returned:`, {
          slideNumber,
          hasData: !!data,
          dataKeys: data ? Object.keys(data) : [],
          title: data?.slide_pedagogical_name,
          type: data?.type
        });

        // Only update state if component is still mounted and this is the latest request
        if (isMounted && requestIdRef.current === requestId) {
          setSlideData(data);
          setLoading(false);

          // Cache the data in memory for future use
          if (data) {
            if (!(window as any).__slideCache) {
              (window as any).__slideCache = {};
            }
            (window as any).__slideCache[cacheKey] = data;
            console.log(`💾 SlideContent: Cached slide data with key: ${cacheKey}`);
          } else {
            console.log(`❌ SlideContent: No data to cache for slide ${slideNumber}`);
          }
        }
      } catch (err) {
        console.error("Error loading slide data:", err);
        if (isMounted && requestIdRef.current === requestId) {
          setError("Failed to load slide data");
          setLoading(false);
        }
      }
    };

    fetchData();

    // Cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false;
    };
  }, [slideNumber, unitNumber, lessonNumber, gradeLevel, lang, slideData]);

  // Apply scale as a style if not 100% - memoize to avoid recalculation
  const scaleStyle = useMemo(() => {
    return scale !== 100 ? {
      transform: `scale(${scale / 100})`,
      transformOrigin: 'center center',
    } : {};
  }, [scale]);

  // Force button repositioning when slide content loads (especially for slides without HTML)
  useEffect(() => {
    if (!loading && slideData) {
      // Trigger a resize event to ensure proper positioning of fixed elements
      const triggerReposition = () => {
        // Dispatch a custom event that the main page can listen to
        window.dispatchEvent(new CustomEvent('slideContentLoaded', {
          detail: { slideNumber, hasHtml: !!slideData.html_css_description_of_image }
        }))

        // Also trigger a resize event as a fallback
        setTimeout(() => {
          window.dispatchEvent(new Event('resize'))
        }, 50)
      }

      // Trigger immediately and after a short delay to ensure DOM is ready
      triggerReposition()
      const timer = setTimeout(triggerReposition, 200)

      return () => clearTimeout(timer)
    }
  }, [loading, slideData, slideNumber]);

  // Memoize the slide content rendering function to avoid unnecessary recalculations
  const renderSlideContent = useCallback(() => {
    // If slide data is not found, check if it's because lesson is not selected
    if (!slideData) {
      // If lesson parameters are missing, show EMBRS logo instead of error
      if (!unitNumber || !lessonNumber || !gradeLevel) {
        // Determine what needs to be selected
        const getMissingSelections = () => {
          const missing = [];
          if (!gradeLevel) missing.push("Grade");
          if (!unitNumber) missing.push("Unit");
          if (!lessonNumber) missing.push("Lesson");

          if (missing.length === 3) return "Select Grade, Unit and Lesson";
          if (missing.length === 2) return `Select ${missing.join(" and ")}`;
          return `Select ${missing[0]}`;
        };

        return (
          <div className="flex h-full w-full items-center justify-center">
            <div className="flex flex-col items-center gap-4">
              <div className="flex items-center gap-1 sm:gap-2">
                <div className="h-6 w-6 sm:h-8 sm:w-8 relative">
                  <img
                    alt="EMBRS Logo"
                    loading="lazy"
                    decoding="async"
                    data-nimg="fill"
                    src="/images/embrs-logo.png"
                    style={{
                      position: "absolute",
                      height: "100%",
                      width: "100%",
                      inset: "0",
                      objectFit: "contain",
                      color: "transparent"
                    }}
                  />
                </div>
                <h1
                  className="text-white logo-text text-lg sm:text-xl md:text-2xl"
                  style={{
                    fontFamily: "var(--font-montserrat), sans-serif",
                    fontWeight: "900"
                  }}
                >
                  EMBRS
                </h1>
              </div>

              {/* Blinking message */}
              <div className="slow-blink">
                <p
                  className="text-white/80 text-sm sm:text-2xl text-center"
                  style={{
                    fontFamily: "var(--font-montserrat), sans-serif",
                    fontWeight: "500"
                  }}
                >
                  {getMissingSelections()}
                </p>
              </div>
            </div>
          </div>
        );
      }

      // If lesson is selected but slide not found, show error
      return (
        <div className="flex h-full w-full items-center justify-center">
          <div className="text-center text-white bg-yellow-500/20 p-8 rounded-lg backdrop-blur-sm">
            <p className="text-lg mb-2">Slide {slideNumber} not found</p>
            <p className="text-sm">Please check the slide number and try again.</p>
          </div>
        </div>
      );
    }

    // Determine which component to render based on the slide type
    const slideType = slideData.type || "hook"; // Default to hook if type is not specified

    // Use a stable key based on slide number and type, not timestamp
    const stableKey = `slide-${slideNumber}-${slideType}`;

    if (process.env.NODE_ENV === 'development') {
      console.log(`Rendering slide ${slideNumber} (${slideType}) with key: ${stableKey}`);
    }

    switch (slideType) {
      case SlideType.INTRODUCTION:
        return (
          <IntroductionSlide
            key={`intro-${stableKey}`}
            slideNumber={slideNumber}
            slideData={slideData}
            revealedItems={revealedItems}
            registerRevealableItems={registerRevealableItems}
            setRevealedItems={setRevealedItems}
          />
        );
      case SlideType.PRINTABLES:
        return (
          <PrintablesSlide
            key={`printables-${stableKey}`}
            slideNumber={slideNumber}
            slideData={slideData}
            revealedItems={revealedItems}
            registerRevealableItems={registerRevealableItems}
            unitNumber={unitNumber}
            lessonNumber={lessonNumber}
            gradeLevel={gradeLevel}
          />
        );
      case SlideType.PRACTICE:
        return (
          <PracticeSlide
            key={`practice-${stableKey}`}
            revealedItems={revealedItems}
            registerRevealableItems={registerRevealableItems}
            setRevealedItems={setRevealedItems}
            slideNumber={slideNumber}
            slideData={slideData}
          />
        );
      case SlideType.TRY_1:
      case SlideType.TRY_2:
        return (
          <TrySlide
            key={`try-${stableKey}`}
            revealedItems={revealedItems}
            registerRevealableItems={registerRevealableItems}
            setRevealedItems={setRevealedItems}
            slideNumber={slideNumber}
            slideData={slideData}
          />
        );
      case SlideType.ON_RAMP:
        // Determine the on-ramp slide subtype based on slide number (language-independent)
        if (slideNumber === 24) {
          // Slide 24 is On-Ramp Try
          return (
            <TrySlide
              key={`on-ramp-try-${stableKey}`}
              revealedItems={revealedItems}
              registerRevealableItems={registerRevealableItems}
              setRevealedItems={setRevealedItems}
              slideNumber={slideNumber}
              slideData={slideData}
            />
          );
        } else if (slideNumber === 23) {
          // Slide 23 is On-Ramp Talk
          return (
            <ConceptSlide
              key={`on-ramp-talk-${stableKey}`}
              revealedItems={revealedItems}
              registerRevealableItems={registerRevealableItems}
              slideNumber={slideNumber}
              slideData={slideData}
            />
          );
        } else {
          // Slide 22 is On-Ramp Teach and others use ConceptSlide component
          return (
            <ConceptSlide
              key={`on-ramp-teach-${stableKey}`}
              revealedItems={revealedItems}
              registerRevealableItems={registerRevealableItems}
              slideNumber={slideNumber}
              slideData={slideData}
            />
          );
        }
      case SlideType.HOOK:
      case SlideType.TEACH_1:
      case SlideType.TALK_1:
      case SlideType.TEACH_2:
      case SlideType.TALK_2:
      default:
        // Default case for other slides
        return (
          <ConceptSlide
            key={`concept-${stableKey}`}
            revealedItems={revealedItems}
            registerRevealableItems={registerRevealableItems}
            slideNumber={slideNumber}
            slideData={slideData}
          />
        );
    }
  }, [slideData, slideNumber, revealedItems, registerRevealableItems, setRevealedItems, unitNumber, lessonNumber, gradeLevel]);

  // Memoize the loading and error states to avoid unnecessary re-renders
  const contentToRender = useMemo(() => {
    if (loading) {
      // If lesson parameters are missing, show EMBRS logo instead of loading
      if (!unitNumber || !lessonNumber || !gradeLevel) {
        // Determine what needs to be selected
        const getMissingSelections = () => {
          const missing = [];
          if (!gradeLevel) missing.push("Grade");
          if (!unitNumber) missing.push("Unit");
          if (!lessonNumber) missing.push("Lesson");

          if (missing.length === 3) return "Select Grade, Unit and Lesson";
          if (missing.length === 2) return `Select ${missing.join(" and ")}`;
          return `Select ${missing[0]}`;
        };

        return (
          <div className="flex h-full w-full items-center justify-center">
            <div className="flex flex-col items-center gap-4">
              <div className="flex items-center gap-1 sm:gap-2">
                <div className="h-6 w-6 sm:h-8 sm:w-8 relative">
                  <img
                    alt="EMBRS Logo"
                    loading="lazy"
                    decoding="async"
                    data-nimg="fill"
                    src="/images/embrs-logo.png"
                    style={{
                      position: "absolute",
                      height: "100%",
                      width: "100%",
                      inset: "0",
                      objectFit: "contain",
                      color: "transparent"
                    }}
                  />
                </div>
                <h1
                  className="text-white logo-text text-lg sm:text-xl md:text-2xl"
                  style={{
                    fontFamily: "var(--font-montserrat), sans-serif",
                    fontWeight: "900"
                  }}
                >
                  EMBRS
                </h1>
              </div>

              {/* Blinking message */}
              <div className="slow-blink">
                <p
                  className="text-white/80 text-sm sm:text-base text-center"
                  style={{
                    fontFamily: "var(--font-montserrat), sans-serif",
                    fontWeight: "500"
                  }}
                >
                  {getMissingSelections()}
                </p>
              </div>
            </div>
          </div>
        );
      }

      // Show loading only when lesson is selected
      return (
        <div className="flex h-full w-full items-center justify-center">
          <div className="text-center text-white">
            <p>Loading slide {slideNumber}...</p>
          </div>
        </div>
      );
    }

    if (error) {
      return (
        <div className="flex h-full w-full items-center justify-center">
          <div className="text-center text-white bg-red-500/20 p-8 rounded-lg backdrop-blur-sm">
            <p className="text-lg mb-2">Error loading slide</p>
            <p className="text-sm">{error}</p>
          </div>
        </div>
      );
    }

    return renderSlideContent();
  }, [loading, error, slideNumber, renderSlideContent, unitNumber, lessonNumber, gradeLevel]);

  // Create a wrapper div with the scale style
  return (
    <div style={scaleStyle} className="p-8 slide-content-wrapper">
      {contentToRender}
    </div>
  );
});
