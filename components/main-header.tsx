"use client"

import { useState, useEffect } from "react"
import { useR<PERSON><PERSON>, useSearch<PERSON>ara<PERSON>, usePathname } from "next/navigation"
import { useSession } from "next-auth/react"
import Link from "next/link"
import {
  ChevronDown,
  Maximize2,
  Settings,
  LogOut,
  Home,
  Briefcase,
  Wand2,
  Upload,
  FileEdit
} from "lucide-react"
import ComingSoonModal from "./ComingSoonModal"
import UserSettingsModal from "./user-settings-modal"
import { clearSlidesCache } from "@/services/slideService"
import { CurriculumType, getCurriculumDisplayName } from "@/types/curriculumTypes"
import { useLessonContext } from "./lesson-context"

// Loading spinner component
const LoadingSpinner = () => (
  <div className="flex items-center justify-center px-4 py-2">
    <div className="animate-spin rounded-full h-4 w-4 border-2 border-gray-300 border-t-gray-600 mr-2"></div>
    <span className="text-sm text-gray-500">Loading...</span>
  </div>
)

interface MainHeaderProps {
  // Props for dashboard-specific functionality
  isDashboard?: boolean
  // Callback functions to update parent state
  onGradeChange?: (grade: string) => void
  onUnitChange?: (unit: string) => void
  onLessonChange?: (lesson: string) => void
  onSlideChange?: (slide: number) => void
  // Initial values from parent
  initialGrade?: string
  initialUnit?: string
  initialLesson?: string
  initialSlide?: number
}

export default function MainHeader({
  isDashboard = false,
  onGradeChange,
  onUnitChange,
  onLessonChange,
  onSlideChange,
  initialGrade,
  initialUnit,
  initialLesson,
  initialSlide
}: MainHeaderProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const pathname = usePathname()
  const { data: session } = useSession()
  const { totalSlides } = useLessonContext()

  // Check if user is admin
  const isAdmin = (session?.user as any)?.role === 'admin'

  // Filter states - use initial values if provided, otherwise use URL params
  // Don't use session?.user?.defaultGrade here - let auto-select effect handle it
  const [gradeLevel, setGradeLevel] = useState(
    initialGrade ||
    searchParams.get('grade') ||
    ""
  )

  // Track if we're initializing from URL to prevent reset chain
  const [isInitializingFromUrl, setIsInitializingFromUrl] = useState(false)
  const [urlParamsProcessed, setUrlParamsProcessed] = useState(false)
  const [unitNumber, setUnitNumber] = useState(initialUnit || searchParams.get('unit') || "")
  const [lessonNumber, setLessonNumber] = useState(initialLesson || searchParams.get('lesson') || "")
  const [currentSlide, setCurrentSlide] = useState(initialSlide || parseInt(searchParams.get('slide') || "1"))

  // Effect to handle URL parameters initialization (prevent reset chain during initial load)
  useEffect(() => {
    if (!urlParamsProcessed) {
      const gradeFromUrl = searchParams.get('grade')
      const unitFromUrl = searchParams.get('unit')
      const lessonFromUrl = searchParams.get('lesson')
      const slideFromUrl = searchParams.get('slide')
      const curriculumFromUrl = searchParams.get('curriculum')

      console.log('🔄 Processing URL parameters on initial load:', {
        gradeFromUrl,
        unitFromUrl,
        lessonFromUrl,
        slideFromUrl,
        curriculumFromUrl,
        currentState: {
          gradeLevel,
          unitNumber,
          lessonNumber,
          selectedCurriculum
        }
      })

      // If we have curriculum and complete lesson parameters, set them all at once to prevent reset chain
      if (curriculumFromUrl && gradeFromUrl && unitFromUrl && lessonFromUrl) {
        console.log('✅ Complete URL parameters found - setting all at once to prevent reset chain')
        setIsInitializingFromUrl(true)

        // Set curriculum first if provided
        if (curriculumFromUrl !== selectedCurriculum) {
          console.log('🔄 Setting curriculum from URL:', curriculumFromUrl)
          setSelectedCurriculum(curriculumFromUrl as CurriculumType)
        }

        // Set all parameters without triggering reset chain
        console.log('🔄 Setting grade from URL:', gradeFromUrl)
        setGradeLevel(gradeFromUrl)
        console.log('🔄 Setting unit from URL:', unitFromUrl)
        setUnitNumber(unitFromUrl)
        console.log('🔄 Setting lesson from URL:', lessonFromUrl)
        setLessonNumber(lessonFromUrl)

        if (slideFromUrl) {
          const slideNumber = parseInt(slideFromUrl)
          if (!isNaN(slideNumber)) {
            console.log('🔄 Setting slide from URL:', slideNumber)
            setCurrentSlide(slideNumber)
          }
        }

        // Mark URL params as processed
        setUrlParamsProcessed(true)

        // Reset initialization flag after a longer delay to ensure all effects complete
        setTimeout(() => {
          console.log('🔄 Resetting isInitializingFromUrl flag')
          setIsInitializingFromUrl(false)
        }, 1000) // Increased delay to ensure all effects complete
      } else if (curriculumFromUrl) {
        // If we only have curriculum, set it without the initialization flag
        console.log('🔄 Setting curriculum from URL (partial parameters):', curriculumFromUrl)
        if (curriculumFromUrl !== selectedCurriculum) {
          setSelectedCurriculum(curriculumFromUrl as CurriculumType)
        }
        setUrlParamsProcessed(true)
      } else {
        console.log('⚠️ No URL parameters - allowing normal flow')
        // Mark as processed even if incomplete to allow normal flow
        setUrlParamsProcessed(true)
      }
    }
  }, [searchParams, urlParamsProcessed])

  // Effect to update currentSlide when URL slide parameter changes
  useEffect(() => {
    const slideFromUrl = searchParams.get('slide')
    console.log('🔄 URL slide parameter changed:', {
      slideFromUrl,
      currentSlide,
      searchParamsString: searchParams.toString(),
      pathname
    })

    if (slideFromUrl) {
      const slideNumber = parseInt(slideFromUrl)
      if (slideNumber !== currentSlide) {
        console.log('✅ Updating currentSlide from URL:', slideNumber, 'previous:', currentSlide)
        setCurrentSlide(slideNumber)
        onSlideChange?.(slideNumber)
      } else {
        console.log('⏭️ Slide number unchanged:', slideNumber)
      }
    } else {
      console.log('❌ No slide parameter in URL')
    }
  }, [searchParams, pathname]) // Remove currentSlide and onSlideChange from dependencies to avoid infinite loops

  // Listen for URL changes (including manual changes)
  useEffect(() => {
    let lastUrl = window.location.href

    const handleUrlChange = () => {
      const currentUrl = window.location.href
      if (currentUrl !== lastUrl) {
        lastUrl = currentUrl
        const url = new URL(currentUrl)
        const slideFromUrl = url.searchParams.get('slide')
        console.log('🌐 URL changed manually:', {
          slideFromUrl,
          currentSlide,
          fullUrl: currentUrl
        })

        if (slideFromUrl) {
          const slideNumber = parseInt(slideFromUrl)
          if (slideNumber !== currentSlide) {
            console.log('🔄 Manual URL change - updating slide:', slideNumber)
            setCurrentSlide(slideNumber)
            onSlideChange?.(slideNumber)
          }
        }
      }
    }

    // Listen for popstate events (back/forward button)
    window.addEventListener('popstate', handleUrlChange)

    // Also listen for hashchange in case URL changes manually
    window.addEventListener('hashchange', handleUrlChange)

    // Poll for URL changes every 100ms as fallback
    const urlCheckInterval = setInterval(() => {
      handleUrlChange()
    }, 100)

    return () => {
      window.removeEventListener('popstate', handleUrlChange)
      window.removeEventListener('hashchange', handleUrlChange)
      clearInterval(urlCheckInterval)
    }
  }, [currentSlide, onSlideChange])

  // Separate effect to handle initial slide setup
  useEffect(() => {
    const slideFromUrl = searchParams.get('slide')
    if (slideFromUrl) {
      const slideNumber = parseInt(slideFromUrl)
      console.log('🎯 Initial slide setup:', slideNumber)
      setCurrentSlide(slideNumber)
    }
  }, []) // Run only once on mount
  const [lang, setLang] = useState(searchParams.get('lang') || "")

  // Menu states
  const [userMenuOpen, setUserMenuOpen] = useState(false)
  const [curriculumMenuOpen, setCurriculumMenuOpen] = useState(false)
  const [gradeMenuOpen, setGradeMenuOpen] = useState(false)
  const [unitMenuOpen, setUnitMenuOpen] = useState(false)
  const [lessonMenuOpen, setLessonMenuOpen] = useState(false)
  const [showSettingsModal, setShowSettingsModal] = useState(false)
  const [showComingSoonModal, setShowComingSoonModal] = useState(false)

  // Curriculum state - initialize from URL only, no default
  const [selectedCurriculum, setSelectedCurriculum] = useState<CurriculumType>(() => {
    const curriculumFromUrl = searchParams.get('curriculum')
    console.log('Initializing curriculum from URL (no default):', curriculumFromUrl)
    return curriculumFromUrl as CurriculumType || '' as CurriculumType
  })
  const [availableCurriculums, setAvailableCurriculums] = useState<CurriculumType[]>([])
  const [isLoadingCurriculums, setIsLoadingCurriculums] = useState(true)
  const [curriculumInitialized, setCurriculumInitialized] = useState(false)
  const [isCurriculumDisabled, setIsCurriculumDisabled] = useState(false)

  // Available options
  const [availableGrades, setAvailableGrades] = useState<string[]>([])
  const [availableUnits, setAvailableUnits] = useState<string[]>([])
  const [availableUnitsWithTitles, setAvailableUnitsWithTitles] = useState<any[]>([])
  const [availableLessons, setAvailableLessons] = useState<string[]>([])
  const [availableLessonsWithTitles, setAvailableLessonsWithTitles] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(false)

  // Separate loading states for each selector
  const [isLoadingGrades, setIsLoadingGrades] = useState(false)
  const [isLoadingUnits, setIsLoadingUnits] = useState(false)
  const [isLoadingLessons, setIsLoadingLessons] = useState(false)

  // User settings state - initialize from session
  const [userSettings, setUserSettings] = useState<any>(null)
  const [userSettingsInitialized, setUserSettingsInitialized] = useState(false)

  // Initialize user settings from session
  useEffect(() => {
    console.log('User settings initialization effect triggered:', {
      hasSession: !!session?.user,
      userSettingsInitialized,
      sessionDefaultGrade: session?.user?.defaultGrade,
      sessionDefaultCurriculum: session?.user?.defaultCurriculum
    })

    if (session?.user && !userSettingsInitialized) {
      const sessionSettings = {
        name: session.user.name || '',
        school: session.user.school || '',
        schoolDistrict: session.user.schoolDistrict || '',
        defaultCurriculum: session.user.defaultCurriculum || '',
        defaultGrade: session.user.defaultGrade || '',
        highContrastMode: session.user.highContrastMode || false,
      }
      setUserSettings(sessionSettings)
      setUserSettingsInitialized(true)

      // Set default curriculum from user settings
      if (sessionSettings.defaultCurriculum) {
        setSelectedCurriculum(sessionSettings.defaultCurriculum as CurriculumType)
      }

      console.log('User settings initialized from session:', sessionSettings)
    }
  }, [session?.user, userSettingsInitialized])

  // API functions - only used as fallback
  const fetchUserSettings = async () => {
    if (isLoading) return null // Prevent duplicate calls

    try {
      setIsLoading(true)
      const response = await fetch('/api/user-settings')
      const data = await response.json()

      if (data.success) {
        setUserSettings(data.settings)
        console.log('User settings updated from API:', data.settings)
        return data.settings
      }
    } catch (error) {
      console.error('Error fetching user settings:', error)
    } finally {
      setIsLoading(false)
    }
    return null
  }

  const fetchAvailableCurriculums = async () => {
    try {
      setIsLoadingCurriculums(true)
      const response = await fetch('/api/available-curriculums')
      const data = await response.json()

      if (data.curriculums) {
        setAvailableCurriculums(data.curriculums)
        // Only set curriculum if there's one in URL and it's available
        const curriculumFromUrl = searchParams.get('curriculum')
        if (curriculumFromUrl && data.curriculums.includes(curriculumFromUrl as CurriculumType)) {
          setSelectedCurriculum(curriculumFromUrl as CurriculumType)
          setCurriculumInitialized(true)
        } else if (!curriculumFromUrl) {
          // No curriculum in URL, mark as initialized but empty
          setCurriculumInitialized(true)
        }
        // Don't auto-select any curriculum - user must choose
      }
    } catch (error) {
      console.error('Error fetching available curriculums:', error)
      setCurriculumInitialized(true) // Mark as initialized even on error
    } finally {
      setIsLoadingCurriculums(false)
    }
  }

  const fetchAvailableGrades = async (curriculum?: string) => {
    const curriculumToUse = curriculum || selectedCurriculum
    try {
      setIsLoadingGrades(true)
      setAvailableGrades([]) // Clear grades immediately to show loading state
      console.log('Fetching grades for curriculum:', curriculumToUse)
      const response = await fetch(`/api/available-options?option_type=grade_level&curriculum=${curriculumToUse}`)
      const data = await response.json()

      if (data.grade_levels) {
        console.log('Received grades for curriculum', curriculumToUse, ':', data.grade_levels)
        setAvailableGrades(data.grade_levels)
      } else {
        setAvailableGrades([]) // Clear grades if no data
      }
    } catch (error) {
      console.error('Error fetching available grades:', error)
      setAvailableGrades([]) // Clear grades on error
    } finally {
      setIsLoadingGrades(false)
    }
  }

  const fetchAvailableUnits = async (selectedGrade: string, curriculum?: string) => {
    const curriculumToUse = curriculum || selectedCurriculum
    try {
      setIsLoadingUnits(true)
      setAvailableUnits([]) // Clear units immediately to show loading state
      setAvailableUnitsWithTitles([]) // Clear units with titles immediately

      console.log('Fetching units for grade:', selectedGrade, 'curriculum:', curriculumToUse)

      // Fetch units with titles
      const responseWithTitles = await fetch(`/api/available-options?option_type=units_with_titles&grade_level=${encodeURIComponent(selectedGrade)}&curriculum=${curriculumToUse}`)
      const dataWithTitles = await responseWithTitles.json()

      if (dataWithTitles.units_with_titles) {
        console.log('Received units with titles for grade', selectedGrade, 'curriculum', curriculumToUse, ':', dataWithTitles.units_with_titles)
        setAvailableUnitsWithTitles(dataWithTitles.units_with_titles)
      }

      // Fetch basic units as fallback
      const response = await fetch(`/api/available-options?option_type=unit_number&grade_level=${encodeURIComponent(selectedGrade)}&curriculum=${curriculumToUse}`)
      const data = await response.json()

      if (data.unit_numbers) {
        console.log('Received units for grade', selectedGrade, 'curriculum', curriculumToUse, ':', data.unit_numbers)
        setAvailableUnits(data.unit_numbers)
      }
    } catch (error) {
      console.error('Error fetching available units:', error)
      setAvailableUnits([]) // Clear units on error
      setAvailableUnitsWithTitles([]) // Clear units with titles on error
    } finally {
      setIsLoadingUnits(false)
    }
  }

  const fetchAvailableLessons = async (selectedGrade: string, selectedUnit: string, curriculum?: string) => {
    const curriculumToUse = curriculum || selectedCurriculum
    try {
      setIsLoadingLessons(true)
      setAvailableLessons([]) // Clear lessons immediately to show loading state
      setAvailableLessonsWithTitles([]) // Clear lessons with titles immediately

      console.log('Fetching lessons for grade:', selectedGrade, 'unit:', selectedUnit, 'curriculum:', curriculumToUse)

      // Fetch lessons with titles
      const responseWithTitles = await fetch(`/api/available-options?option_type=lessons_with_titles&grade_level=${encodeURIComponent(selectedGrade)}&unit_number=${encodeURIComponent(selectedUnit)}&curriculum=${curriculumToUse}`)
      const dataWithTitles = await responseWithTitles.json()

      if (dataWithTitles.lessons_with_titles) {
        console.log('Received lessons with titles for grade', selectedGrade, 'unit', selectedUnit, 'curriculum', curriculumToUse, ':', dataWithTitles.lessons_with_titles)
        setAvailableLessonsWithTitles(dataWithTitles.lessons_with_titles)
      }

      // Fetch basic lessons as fallback
      const response = await fetch(`/api/available-options?option_type=lesson_number&grade_level=${encodeURIComponent(selectedGrade)}&unit_number=${encodeURIComponent(selectedUnit)}&curriculum=${curriculumToUse}`)
      const data = await response.json()

      if (data.lesson_numbers) {
        console.log('Received lessons for grade', selectedGrade, 'unit', selectedUnit, 'curriculum', curriculumToUse, ':', data.lesson_numbers)
        setAvailableLessons(data.lesson_numbers)
      }
    } catch (error) {
      console.error('Error fetching available lessons:', error)
      setAvailableLessons([]) // Clear lessons on error
      setAvailableLessonsWithTitles([]) // Clear lessons with titles on error
    } finally {
      setIsLoadingLessons(false)
    }
  }

  // Helper function to get lesson title
  const getLessonTitle = (unit: string, lesson: string) => {
    const lessonTitles: { [key: string]: { [key: string]: string } } = {
      "1": {
        "1": "Counting On",
        "2": "Place Value",
        "3": "Number Bonds",
        "4": "Number Comparison",
        "5": "Number Patterns"
      },
      "2": {
        "1": "Addition Facts",
        "2": "Addition Strategies",
        "3": "Subtraction Facts",
        "4": "Subtraction Strategies",
        "5": "Word Problems"
      },
      "3": {
        "1": "Multiplication Facts",
        "2": "Multiplication Strategies",
        "3": "Division Facts",
        "4": "Division Strategies",
        "5": "Word Problems"
      }
    }

    return lessonTitles[unit]?.[lesson] || "Unknown Lesson"
  }

  // Helper function to get curriculum display text with loading state
  const getCurriculumDisplayText = () => {
    if (isLoadingCurriculums) {
      return "Loading..."
    }

    if (!curriculumInitialized) {
      return "Loading..."
    }

    if (!selectedCurriculum) {
      return "Select Curriculum"
    }

    return getCurriculumDisplayName(selectedCurriculum)
  }

  // Function to update URL with current filters
  const updateUrlWithFilters = (newGrade?: string, newUnit?: string, newLesson?: string, newSlide?: string, newCurriculum?: string) => {
    try {
      const url = new URL(window.location.href)

      // Clear existing params
      url.searchParams.delete('grade')
      url.searchParams.delete('unit')
      url.searchParams.delete('lesson')
      url.searchParams.delete('slide')
      url.searchParams.delete('curriculum')

      // Add new params if they exist
      if (newGrade) url.searchParams.set('grade', newGrade)
      if (newUnit) url.searchParams.set('unit', newUnit)
      if (newLesson) url.searchParams.set('lesson', newLesson)
      if (newSlide) url.searchParams.set('slide', newSlide)
      const curriculumToUse = newCurriculum || selectedCurriculum
      url.searchParams.set('curriculum', curriculumToUse)

      // Update URL without reloading the page
      window.history.replaceState({}, '', url.toString())

      console.log('Updated URL parameters:', {
        grade: newGrade,
        unit: newUnit,
        lesson: newLesson,
        slide: newSlide,
        curriculum: curriculumToUse
      })
    } catch (error) {
      console.error('Error updating URL:', error)
    }
  }

  // Dashboard Go button handler
  const handleGoToDashboard = () => {
    const params = new URLSearchParams()
    if (gradeLevel) params.set('grade', gradeLevel)
    if (unitNumber) params.set('unit', unitNumber)
    if (lessonNumber) params.set('lesson', lessonNumber)
    if (currentSlide > 1) params.set('slide', currentSlide.toString())
    if (lang) params.set('lang', lang)
    if (selectedCurriculum) params.set('curriculum', selectedCurriculum)

    const queryString = params.toString()
    router.push(`/${queryString ? `?${queryString}` : ''}`)
  }

  // Effects for data loading
  useEffect(() => {
    console.log('Initial effect triggered, searchParams:', searchParams.toString())
    fetchAvailableCurriculums()
  }, [])



  // Effect to initialize curriculum from URL parameters
  useEffect(() => {
    const curriculumFromUrl = searchParams.get('curriculum')
    console.log('Initializing curriculum from URL:', {
      curriculumFromUrl,
      currentSelectedCurriculum: selectedCurriculum,
      availableCurriculums: availableCurriculums.length,
      isInitializingFromUrl,
      curriculumInitialized
    })

    if (curriculumFromUrl && availableCurriculums.length > 0) {
      // Check if the curriculum from URL is available
      if (availableCurriculums.includes(curriculumFromUrl as CurriculumType)) {
        console.log('Setting curriculum from URL:', curriculumFromUrl)
        // Set initialization flag when setting curriculum from URL
        if (curriculumFromUrl !== selectedCurriculum) {
          setIsInitializingFromUrl(true)
        }
        setSelectedCurriculum(curriculumFromUrl as CurriculumType)
        setCurriculumInitialized(true)
      } else {
        console.log('Curriculum from URL not available:', curriculumFromUrl, 'Available:', availableCurriculums)
        setCurriculumInitialized(true)
      }
    } else if (availableCurriculums.length > 0 && !curriculumFromUrl) {
      // No curriculum in URL but curriculums are loaded
      setCurriculumInitialized(true)
    }
  }, [searchParams, availableCurriculums, curriculumInitialized])

  // Removed automatic grade fetching - now only on click

  // Removed grade validation - now only on click

  // Effect to load user settings from API only if session data is incomplete
  useEffect(() => {
    if (userSettingsInitialized && userSettings &&
        (!userSettings.name || !userSettings.school) &&
        session?.user) {
      console.log('Session data incomplete, fetching additional user settings from API...')
      fetchUserSettings()
    }
  }, [userSettingsInitialized, userSettings?.name, userSettings?.school, session?.user?.id])

  // Effect to apply default curriculum and grade from user settings
  useEffect(() => {
    console.log('Auto-select defaults effect triggered:', {
      userSettingsDefaultCurriculum: userSettings?.defaultCurriculum,
      userSettingsDefaultGrade: userSettings?.defaultGrade,
      searchParamsCurriculum: searchParams.get('curriculum'),
      searchParamsGrade: searchParams.get('grade'),
      initialGrade,
      selectedCurriculum,
      gradeLevel,
      availableGrades: availableGrades.length
    })

    // Auto-select curriculum only if not set in URL and user has default
    if (userSettings?.defaultCurriculum &&
        !searchParams.get('curriculum') &&
        !selectedCurriculum) {
      console.log('Auto-selecting default curriculum from user settings:', userSettings.defaultCurriculum)
      setSelectedCurriculum(userSettings.defaultCurriculum as CurriculumType)
    }

    // Auto-select grade if curriculum is set and user has default grade
    if (userSettings?.defaultGrade &&
        selectedCurriculum &&
        availableGrades.length > 0 &&
        !searchParams.get('grade') &&
        !initialGrade &&
        !gradeLevel) {
      const rawDefaultGrade = userSettings.defaultGrade

      // Convert grade format: "4" -> "Grade 4", "K" -> "Kindergarten"
      const formattedGrade = rawDefaultGrade === "K" ? "Kindergarten" : `Grade ${rawDefaultGrade}`

      // Check if this grade is available for the selected curriculum
      if (availableGrades.includes(formattedGrade)) {
        console.log('Auto-selecting default grade from user settings:', rawDefaultGrade, '-> formatted:', formattedGrade)
        setGradeLevel(formattedGrade)
        updateUrlWithFilters(formattedGrade, "", "", "1", selectedCurriculum)
        onGradeChange?.(formattedGrade)
        onUnitChange?.("")
        onLessonChange?.("")
        onSlideChange?.(1)
      } else {
        console.log('Default grade not available for selected curriculum:', formattedGrade, 'Available:', availableGrades)
      }
    }
  }, [userSettings?.defaultCurriculum, userSettings?.defaultGrade, searchParams, initialGrade, gradeLevel, selectedCurriculum, availableGrades, onGradeChange, onUnitChange, onLessonChange, onSlideChange])

  // Removed automatic unit fetching - now only on click

  // Removed unit validation - now only on click

  // Removed automatic lesson fetching - now only on click

  // Removed lesson validation - now only on click

  // Close menus when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      if (userMenuOpen && !target.closest(".user-menu-container")) {
        setUserMenuOpen(false)
      }
      if (curriculumMenuOpen && !target.closest(".curriculum-menu-container")) {
        setCurriculumMenuOpen(false)
      }
      if (gradeMenuOpen && !target.closest(".grade-menu-container")) {
        setGradeMenuOpen(false)
      }
      if (unitMenuOpen && !target.closest(".unit-menu-container")) {
        setUnitMenuOpen(false)
      }
      if (lessonMenuOpen && !target.closest(".lesson-menu-container")) {
        setLessonMenuOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [userMenuOpen, curriculumMenuOpen, gradeMenuOpen, unitMenuOpen, lessonMenuOpen])

  return (
    <header className="bg-[linear-gradient(150deg,#2B6DFE_0%,#2B6DFE_70%,#00F2FF_100%)] !z-[500] text-white p-4">
      <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 shadow-lg">
        {/* Top row with logo and user controls */}
        <div className="flex items-center justify-between mb-4">
          {/* Logo */}
          <div className="flex items-center gap-4">
            <button
              onClick={() => {
                // Clear all slides cache using the service function
                clearSlidesCache();

                // Clear localStorage
                try {
                  localStorage.removeItem('lesson_unit_number');
                  localStorage.removeItem('lesson_lesson_number');
                  localStorage.removeItem('lesson_grade_level');
                  localStorage.removeItem('lesson_current_slide');
                  localStorage.removeItem('lesson_lang');
                } catch (error) {
                  console.error('Error clearing localStorage:', error);
                }
                // Navigate to home with refresh
                router.push('/?refresh=true');
              }}
              className="flex items-center gap-4 "
            >
              <div className="flex h-8 w-8 items-center justify-center rounded-md bg-white/10">
                <img src="/images/embrs-logo.png" alt="EMBRS Logo" className="h-5 w-5" />
              </div>
              <span className="logo-text text-xl tracking-wide text-white" style={{ fontFamily: 'var(--font-montserrat), sans-serif', fontWeight: 900 }}>EMBRS</span>
            </button>

            {!isDashboard && (
              isAdmin ? (
                <Link
                  href="/dashboard"
                  className="flex items-center gap-2 rounded-md bg-white/10 px-3 py-2 text-sm font-medium text-white hover:bg-white/20 transition-colors"
                >
                  <Home className="h-4 w-4" />
                  <span>Dashboard</span>
                </Link>
              ) : (
                <button
                  onClick={() => setShowComingSoonModal(true)}
                  className="flex items-center gap-2 rounded-md bg-white/10 px-3 py-2 text-sm font-medium text-white hover:bg-white/20 transition-colors"
                >
                  <Home className="h-4 w-4" />
                  <span>Dashboard</span>
                </button>
              )
            )}

            {isAdmin && (
              <Link
                href={`/upload`}
                className="flex items-center gap-2 rounded-md bg-white/10 px-3 py-2 text-sm font-medium text-white hover:bg-white/20 transition-colors"
              >
                <Upload className="mr-2 h-4 w-4" />
                <span>Upload JSON</span>
              </Link>
            )}
            {isAdmin && (
              <Link
                href={`/jobs?${new URLSearchParams({
                  ...(unitNumber && lessonNumber && gradeLevel && { documentId: `${unitNumber}-${lessonNumber}-${gradeLevel}` }),
                  ...(gradeLevel && { grade: gradeLevel }),
                  ...(unitNumber && { unit: unitNumber }),
                  ...(lessonNumber && { lesson: lessonNumber }),
                  ...(currentSlide && { slide: currentSlide.toString() }),
                  ...(selectedCurriculum && { curriculum: selectedCurriculum })
                }).toString()}`}
                className="flex items-center gap-2 rounded-md bg-white/10 px-3 py-2 text-sm font-medium text-white hover:bg-white/20 transition-colors"
              >
                <Briefcase className="h-4 w-4" />
                <span>Jobs Manager</span>
              </Link>
            )}

            {isAdmin && gradeLevel && unitNumber && lessonNumber && (
              <Link
                href={`/edit-json?${new URLSearchParams({
                  grade: gradeLevel,
                  unit: unitNumber,
                  lesson: lessonNumber,
                  ...(currentSlide && { slide: currentSlide.toString() }),
                  ...(selectedCurriculum && { curriculum: selectedCurriculum })
                }).toString()}`}
                className="flex items-center gap-2 rounded-md bg-white/10 px-3 py-2 text-sm font-medium text-white hover:bg-white/20 transition-colors"
              >
                <FileEdit className="h-4 w-4" />
                <span>Edit JSON</span>
              </Link>
            )}
          </div>

          {/* Right side controls */}
          <div className="flex items-center gap-4">
            {/* Progress indicator - only show on main page */}
            {!isDashboard && (() => {
              const progressPercent = Math.min(100, Math.max(0, ((currentSlide - 1) / (totalSlides - 1)) * 100))
              console.log('📊 Progress bar render:', {
                currentSlide,
                totalSlides,
                calculation: `((${currentSlide} - 1) / (${totalSlides} - 1)) * 100`,
                progressPercent,
                timestamp: new Date().toISOString()
              })

              return (
                <div className="flex items-center gap-2">
                  <div className="h-2 w-32 rounded-full bg-white/20">
                    <div
                      className="h-2 rounded-full bg-[#fadb9a]"
                      style={{
                        width: `${progressPercent}%`,
                      }}
                    />
                  </div>
                  <span className="text-sm">
                    {Math.round(progressPercent)}%
                  </span>
                </div>
              )
            })()}

            {/* Action buttons */}

            <button
              onClick={() => {
                if (!document.fullscreenElement) {
                  document.documentElement.requestFullscreen().catch((err) => {
                    console.error(`Error attempting to enable full-screen mode: ${err.message}`)
                  })
                } else {
                  if (document.exitFullscreen) {
                    document.exitFullscreen()
                  }
                }
              }}
              className="flex items-center gap-2 rounded-md bg-white/10 px-3 py-2 text-sm font-medium text-white "
            >
              <Maximize2 className="h-4 w-4" />
              <span>Fullscreen</span>
            </button>

            {!isDashboard && (
              <button
                onClick={() => {
                  // Validate that all required parameters are present
                  if (!unitNumber || !lessonNumber || !gradeLevel) {
                    alert("Please select Grade, Unit, and Lesson before presenting");
                    return;
                  }

                  // Save current state to localStorage for persistence
                  try {
                    localStorage.setItem('lesson_unit_number', unitNumber);
                    localStorage.setItem('lesson_lesson_number', lessonNumber);
                    localStorage.setItem('lesson_grade_level', gradeLevel);
                    localStorage.setItem('lesson_current_slide', currentSlide.toString());
                    // Always save English for demo
                    localStorage.setItem('lesson_lang', 'en');
                  } catch (error) {
                    console.error('Error saving to localStorage:', error);
                  }

                  // Construct query parameters for both windows (English only)
                  const queryParams = new URLSearchParams({
                    unit: unitNumber,
                    lesson: lessonNumber,
                    grade: gradeLevel,
                    slide: currentSlide.toString(),
                    ...(selectedCurriculum && { curriculum: selectedCurriculum })
                  });

                  // Remove lang parameter - English only for demo
                  // if (lang) {
                  //   queryParams.append('lang', lang);
                  // }

                  const queryString = queryParams.toString();

                  // Open audience view in a new window with query parameters
                  window.open(
                    `/audience?${queryString}`,
                    "audience",
                    "width=1200,height=800,menubar=no,toolbar=no,location=no",
                  )

                  // Navigate to presenter view in the current window with query parameters
                  window.location.href = `/presenter?${queryString}`;
                }}
                className="flex items-center gap-2 rounded-md bg-white/10 px-4 py-2 text-sm font-medium text-white "
                disabled={!gradeLevel || !unitNumber || !lessonNumber}
              >
                Present Lesson
              </button>
            )}

            {/* User Menu */}
            <div className="relative user-menu-container">
              <button
                onClick={() => setUserMenuOpen(!userMenuOpen)}
                className="flex h-9 w-9 items-center justify-center rounded-full bg-[#fadb9a] text-[#2B6DFE] font-medium text-sm hover:bg-[#fadb9a]/90 transition-colors"
              >
                {session?.user?.name ? session.user.name.charAt(0).toUpperCase() : "U"}
              </button>
              {userMenuOpen && (
                <div className="absolute right-0 mt-2 w-48 rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 z-50">
                  <button
                    className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    onClick={() => {
                      setUserMenuOpen(false)
                      setShowSettingsModal(true)
                    }}
                  >
                    <Settings className="mr-2 h-4 w-4" />
                    Settings
                  </button>
                  <button
                    className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    onClick={() => {
                      setUserMenuOpen(false)
                      router.push("/api/auth/signout")
                    }}
                  >
                    <LogOut className="mr-2 h-4 w-4" />
                    Sign out
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Bottom row with curriculum selectors */}
        <div className="flex items-center gap-4">
          {/* Curriculum Dropdown */}
          <div className="relative curriculum-menu-container">
            <button
              onClick={() => {
                if (!isCurriculumDisabled) {
                  setCurriculumMenuOpen(!curriculumMenuOpen)
                }
              }}
              className={`appearance-none rounded-md ${isCurriculumDisabled ? 'bg-white/5 text-white/70 cursor-not-allowed' : 'bg-white/10 text-white cursor-pointer'} px-3 py-1.5 text-sm font-medium pr-8 w-40 text-left flex items-center`}
              disabled={isCurriculumDisabled}
            >
              <span className="truncate">
                {getCurriculumDisplayText()}
              </span>
              <ChevronDown className={`absolute right-2 top-1/2 h-4 w-4 -translate-y-1/2 pointer-events-none ${isCurriculumDisabled ? 'opacity-50' : ''}`} />
            </button>
            {curriculumMenuOpen && !isCurriculumDisabled && (
              <div className="absolute left-0 mt-2 w-40 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 z-50">
                <div className="py-1">
                  {availableCurriculums.map((curriculum) => (
                    <button
                      key={curriculum}
                      className={`flex w-full items-center px-4 py-2 text-sm text-left ${selectedCurriculum === curriculum ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                      onClick={() => {
                        setSelectedCurriculum(curriculum)
                        setCurriculumMenuOpen(false)
                        // Reset other filters when curriculum changes
                        setGradeLevel("")
                        setUnitNumber("")
                        setLessonNumber("")
                        // Clear cached options when curriculum changes
                        setAvailableGrades([])
                        setAvailableUnits([])
                        setAvailableUnitsWithTitles([])
                        setAvailableLessons([])
                        setAvailableLessonsWithTitles([])
                        updateUrlWithFilters("", "", "", "1", curriculum)
                        onGradeChange?.("")
                        onUnitChange?.("")
                        onLessonChange?.("")
                        onSlideChange?.(1)
                      }}
                    >
                      {getCurriculumDisplayName(curriculum)}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Grade Dropdown */}
          <div className="relative grade-menu-container">
            <button
              onClick={() => {
                if (selectedCurriculum) {
                  // Load grades only when clicking on dropdown
                  if (!gradeMenuOpen && availableGrades.length === 0) {
                    console.log('🔄 Loading grades on click for curriculum:', selectedCurriculum)
                    fetchAvailableGrades(selectedCurriculum)
                  }
                  setGradeMenuOpen(!gradeMenuOpen)
                }
              }}
              className={`appearance-none rounded-md ${
                !selectedCurriculum ? 'bg-white/5 text-white/50 cursor-not-allowed' :
                'bg-white/10 text-white cursor-pointer'
              } px-3 py-1.5 text-sm font-medium pr-8 w-32 text-left flex items-center`}
              disabled={!selectedCurriculum}
            >
              <span className="truncate">
                {!selectedCurriculum ? "Select curriculum first" :
                 isLoadingGrades ? "Loading..." :
                 (gradeLevel || "Select Grade")}
              </span>
              <ChevronDown className={`absolute right-2 top-1/2 h-4 w-4 -translate-y-1/2 pointer-events-none ${!selectedCurriculum ? 'opacity-50' : ''}`} />
            </button>
            {gradeMenuOpen && selectedCurriculum && (
              <div className="absolute left-0 mt-2 w-32 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 z-50">
                <div className="py-1 max-h-60 overflow-y-auto">
                  {isLoadingGrades ? (
                    <LoadingSpinner />
                  ) : availableGrades.length > 0 ? (
                    availableGrades.map((grade) => (
                      <button
                        key={grade}
                        className={`flex w-full items-center px-4 py-2 text-sm text-left ${gradeLevel === grade ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                        onClick={() => {
                          setGradeLevel(grade)
                          setGradeMenuOpen(false)
                          setUnitNumber("")
                          setLessonNumber("")
                          // Clear cached units and lessons when grade changes
                          setAvailableUnits([])
                          setAvailableUnitsWithTitles([])
                          setAvailableLessons([])
                          setAvailableLessonsWithTitles([])
                          // Update URL with new grade and curriculum
                          updateUrlWithFilters(grade, "", "", "1", selectedCurriculum)
                          // Call parent callback
                          onGradeChange?.(grade)
                          onUnitChange?.("")
                          onLessonChange?.("")
                          onSlideChange?.(1)
                        }}
                      >
                        {grade}
                      </button>
                    ))
                  ) : (
                    <>
                      <button
                        className={`flex w-full items-center px-4 py-2 text-sm text-left ${gradeLevel === "Grade 1" ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                        onClick={() => {
                          setGradeLevel("Grade 1")
                          setGradeMenuOpen(false)
                          setUnitNumber("")
                          setLessonNumber("")
                          // Clear cached units and lessons when grade changes
                          setAvailableUnits([])
                          setAvailableUnitsWithTitles([])
                          setAvailableLessons([])
                          setAvailableLessonsWithTitles([])
                          // Update URL with new grade
                          updateUrlWithFilters("Grade 1", "", "", "1", selectedCurriculum)
                          // Call parent callback
                          onGradeChange?.("Grade 1")
                          onUnitChange?.("")
                          onLessonChange?.("")
                          onSlideChange?.(1)
                        }}
                      >
                        Grade 1
                      </button>
                      {[...Array(12)].map((_, i) => (
                        <button
                          key={i + 2}
                          className={`flex w-full items-center px-4 py-2 text-sm text-left ${gradeLevel === `Grade ${i + 2}` ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                          onClick={() => {
                            const grade = `Grade ${i + 2}`
                            setGradeLevel(grade)
                            setGradeMenuOpen(false)
                            setUnitNumber("")
                            setLessonNumber("")
                            // Clear cached units and lessons when grade changes
                            setAvailableUnits([])
                            setAvailableUnitsWithTitles([])
                            setAvailableLessons([])
                            setAvailableLessonsWithTitles([])
                            // Update URL with new grade
                            updateUrlWithFilters(grade, "", "", "1", selectedCurriculum)
                            // Call parent callback
                            onGradeChange?.(grade)
                            onUnitChange?.("")
                            onLessonChange?.("")
                            onSlideChange?.(1)
                          }}
                        >
                          Grade {i + 2}
                        </button>
                      ))}
                    </>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Unit Dropdown */}
          <div className="relative unit-menu-container">
            <button
              onClick={() => {
                if (gradeLevel) {
                  // Load units only when clicking on dropdown
                  if (!unitMenuOpen && availableUnits.length === 0 && availableUnitsWithTitles.length === 0) {
                    console.log('🔄 Loading units on click for grade:', gradeLevel, 'curriculum:', selectedCurriculum)
                    fetchAvailableUnits(gradeLevel, selectedCurriculum)
                  }
                  setUnitMenuOpen(!unitMenuOpen)
                }
              }}
              className={`appearance-none rounded-md ${
                !gradeLevel ? 'bg-white/5 text-white/50 cursor-not-allowed' :
                'bg-white/10 text-white cursor-pointer'
              } px-3 py-1.5 text-sm font-medium pr-8 w-48 text-left flex items-center`}
              disabled={!gradeLevel}
            >
              <span className="truncate">
                {isLoadingUnits ? "Loading..." :
                 unitNumber ? (() => {
                  // Try to find unit title from database data
                  const unitWithTitle = availableUnitsWithTitles.find(unit => unit.unit_number === unitNumber);
                  if (unitWithTitle) {
                    // Check if unit_title already contains the unit number
                    const title = unitWithTitle.unit_title;
                    if (title.toLowerCase().startsWith(`unit ${unitNumber}`)) {
                      return title; // Use title as is if it already contains unit number
                    } else {
                      return `Unit ${unitNumber}: ${title}`;
                    }
                  }
                  // Fallback to hardcoded titles
                  const fallbackTitle = unitNumber === "1" ? "Counting" :
                                       unitNumber === "2" ? "Numbers" :
                                       unitNumber === "3" ? "Addition" :
                                       unitNumber === "4" ? "Subtraction" :
                                       "Fractions";
                  return `Unit ${unitNumber}: ${fallbackTitle}`;
                })() : "Select Unit"}
              </span>
              <ChevronDown className={`absolute right-2 top-1/2 h-4 w-4 -translate-y-1/2 pointer-events-none ${!gradeLevel ? 'opacity-50' : ''}`} />
            </button>
            {unitMenuOpen && (
              <div className="absolute left-0 mt-2 w-48 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 z-50">
                <div className="py-1">
                  {isLoadingUnits ? (
                    <LoadingSpinner />
                  ) : availableUnitsWithTitles.length > 0 ? (
                    availableUnitsWithTitles.map((unitData) => {
                      // Check if unit_title already contains the unit number
                      const title = unitData.unit_title;
                      const displayTitle = title.toLowerCase().startsWith(`unit ${unitData.unit_number}`)
                        ? title
                        : `Unit ${unitData.unit_number}: ${title}`;

                      return (
                        <button
                          key={unitData.unit_number}
                          className={`flex w-full items-center px-4 py-2 text-sm text-left ${unitNumber === unitData.unit_number ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                          onClick={() => {
                            setUnitNumber(unitData.unit_number)
                            setLessonNumber("")
                            setUnitMenuOpen(false)
                            // Clear cached lessons when unit changes
                            setAvailableLessons([])
                            setAvailableLessonsWithTitles([])
                            // Update URL with new unit
                            updateUrlWithFilters(gradeLevel, unitData.unit_number, "", "1", selectedCurriculum || undefined)
                            // Call parent callback
                            onUnitChange?.(unitData.unit_number)
                            onLessonChange?.("")
                            onSlideChange?.(1)
                          }}
                        >
                          {displayTitle}
                        </button>
                      );
                    })
                  ) : availableUnits.length > 0 ? (
                    availableUnits.map((unit) => {
                      // Get unit title based on unit number
                      const unitTitle = unit === "1" ? "Counting" :
                                       unit === "2" ? "Numbers" :
                                       unit === "3" ? "Addition" :
                                       unit === "4" ? "Subtraction" :
                                       unit === "5" ? "Fractions" :
                                       "Unknown";

                      return (
                        <button
                          key={unit}
                          className={`flex w-full items-center px-4 py-2 text-sm text-left ${unitNumber === unit ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                          onClick={() => {
                            setUnitNumber(unit)
                            setLessonNumber("")
                            setUnitMenuOpen(false)
                            // Clear cached lessons when unit changes
                            setAvailableLessons([])
                            setAvailableLessonsWithTitles([])
                            // Update URL with new unit
                            updateUrlWithFilters(gradeLevel, unit, "", "1", selectedCurriculum || undefined)
                            // Call parent callback
                            onUnitChange?.(unit)
                            onLessonChange?.("")
                            onSlideChange?.(1)
                          }}
                        >
                          Unit {unit}: {unitTitle}
                        </button>
                      );
                    })
                  ) : (
                    <>
                      <button
                        className={`flex w-full items-center px-4 py-2 text-sm text-left ${unitNumber === "1" ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                        onClick={() => {
                          setUnitNumber("1")
                          setLessonNumber("")
                          setUnitMenuOpen(false)
                          // Clear cached lessons when unit changes
                          setAvailableLessons([])
                          setAvailableLessonsWithTitles([])
                          // Update URL with new unit
                          updateUrlWithFilters(gradeLevel, "1", "", "1", selectedCurriculum || undefined)
                          // Call parent callback
                          onUnitChange?.("1")
                          onLessonChange?.("")
                          onSlideChange?.(1)
                        }}
                      >
                        Unit 1: Counting
                      </button>
                      <button
                        className={`flex w-full items-center px-4 py-2 text-sm text-left ${unitNumber === "2" ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                        onClick={() => {
                          setUnitNumber("2")
                          setLessonNumber("")
                          setUnitMenuOpen(false)
                          // Clear cached lessons when unit changes
                          setAvailableLessons([])
                          setAvailableLessonsWithTitles([])
                          // Update URL with new unit
                          updateUrlWithFilters(gradeLevel, "2", "", "1", selectedCurriculum || undefined)
                          // Call parent callback
                          onUnitChange?.("2")
                          onLessonChange?.("")
                          onSlideChange?.(1)
                        }}
                      >
                        Unit 2: Numbers
                      </button>
                      <button
                        className={`flex w-full items-center px-4 py-2 text-sm text-left ${unitNumber === "3" ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                        onClick={() => {
                          setUnitNumber("3")
                          setLessonNumber("")
                          setUnitMenuOpen(false)
                          // Clear cached lessons when unit changes
                          setAvailableLessons([])
                          setAvailableLessonsWithTitles([])
                          // Update URL with new unit
                          updateUrlWithFilters(gradeLevel, "3", "", "1", selectedCurriculum || undefined)
                          // Call parent callback
                          onUnitChange?.("3")
                          onLessonChange?.("")
                          onSlideChange?.(1)
                        }}
                      >
                        Unit 3: Addition
                      </button>
                    </>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Lesson Dropdown */}
          <div className="relative lesson-menu-container">
            <button
              onClick={() => {
                if (unitNumber && gradeLevel) {
                  // Load lessons only when clicking on dropdown
                  if (!lessonMenuOpen && availableLessons.length === 0 && availableLessonsWithTitles.length === 0) {
                    console.log('🔄 Loading lessons on click for grade:', gradeLevel, 'unit:', unitNumber, 'curriculum:', selectedCurriculum)
                    fetchAvailableLessons(gradeLevel, unitNumber, selectedCurriculum)
                  }
                  setLessonMenuOpen(!lessonMenuOpen)
                }
              }}
              className={`appearance-none rounded-md ${
                !unitNumber || !gradeLevel ? 'bg-white/5 text-white/50 cursor-not-allowed' :
                'bg-white/10 text-white cursor-pointer'
              } px-3 py-1.5 text-sm font-medium pr-8 w-48 text-left flex items-center`}
              disabled={!unitNumber || !gradeLevel}
            >
              <span className="truncate">
                {isLoadingLessons ? "Loading..." :
                 lessonNumber && unitNumber ? (() => {
                  // Try to find lesson title from database data
                  const lessonWithTitle = availableLessonsWithTitles.find(lesson => lesson.lesson_number === lessonNumber);
                  if (lessonWithTitle) {
                    // Check if lesson_title already contains the lesson number
                    const title = lessonWithTitle.lesson_title;
                    if (title.toLowerCase().startsWith(`lesson ${lessonNumber}`)) {
                      return title; // Use title as is if it already contains lesson number
                    } else {
                      return `Lesson ${lessonNumber}: ${title}`;
                    }
                  }
                  // Fallback to hardcoded titles
                  return `Lesson ${lessonNumber}: ${getLessonTitle(unitNumber, lessonNumber)}`;
                })() : "Select Lesson"}
              </span>
              <ChevronDown className={`absolute right-2 top-1/2 h-4 w-4 -translate-y-1/2 pointer-events-none ${!unitNumber || !gradeLevel ? 'opacity-50' : ''}`} />
            </button>
            {lessonMenuOpen && (
              <div className="absolute left-0 mt-2 w-48 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 z-50 max-h-60 overflow-y-auto">
                <div className="py-1">
                  {isLoadingLessons ? (
                    <LoadingSpinner />
                  ) : availableLessonsWithTitles.length > 0 ? (
                    availableLessonsWithTitles.map((lessonData) => {
                      // Check if lesson_title already contains the lesson number
                      const title = lessonData.lesson_title;
                      const displayTitle = title.toLowerCase().startsWith(`lesson ${lessonData.lesson_number}`)
                        ? title
                        : `Lesson ${lessonData.lesson_number}: ${title}`;

                      return (
                        <button
                          key={lessonData.lesson_number}
                          className={`flex w-full items-center px-4 py-2 text-sm text-left ${
                            lessonNumber === lessonData.lesson_number ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"
                          }`}
                          onClick={() => {
                            setLessonNumber(lessonData.lesson_number)
                            setLessonMenuOpen(false)
                            // Update URL with new lesson
                            updateUrlWithFilters(gradeLevel, unitNumber, lessonData.lesson_number, "1", selectedCurriculum || undefined)
                            // Call parent callback
                            onLessonChange?.(lessonData.lesson_number)
                            onSlideChange?.(1)
                          }}
                        >
                          {displayTitle}
                        </button>
                      );
                    })
                  ) : availableLessons.length > 0 ? (
                    availableLessons.map((lesson) => (
                      <button
                        key={lesson}
                        className={`flex w-full items-center px-4 py-2 text-sm text-left ${
                          lessonNumber === lesson ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"
                        }`}
                        onClick={() => {
                          setLessonNumber(lesson)
                          setLessonMenuOpen(false)
                          // Update URL with new lesson
                          updateUrlWithFilters(gradeLevel, unitNumber, lesson, "1", selectedCurriculum || undefined)
                          // Call parent callback
                          onLessonChange?.(lesson)
                          onSlideChange?.(1)
                        }}
                      >
                        Lesson {lesson}: {getLessonTitle(unitNumber, lesson)}
                      </button>
                    ))
                  ) : (
                    // Fallback to generated options if no data from database
                    Array.from({ length: 10 }, (_, i) => i + 1).map((num) => (
                      <button
                        key={num}
                        className={`flex w-full items-center px-4 py-2 text-sm text-left ${
                          lessonNumber === num.toString() ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"
                        }`}
                        onClick={() => {
                          setLessonNumber(num.toString())
                          setLessonMenuOpen(false)
                          // Update URL with new lesson
                          updateUrlWithFilters(gradeLevel, unitNumber, num.toString(), "1", selectedCurriculum || undefined)
                          // Call parent callback
                          onLessonChange?.(num.toString())
                          onSlideChange?.(1)
                        }}
                      >
                        Lesson {num}: {getLessonTitle(unitNumber, num.toString())}
                      </button>
                    ))
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Go Button - only show on dashboard */}
          {isDashboard && (
            <button
              onClick={handleGoToDashboard}
              className="rounded-md bg-[#fadb9a] px-4 py-1.5 text-sm font-medium text-[#2B6DFE] hover:bg-[#fadb9a]/90 transition-colors"
            >
              Go
            </button>
          )}
        </div>
      </div>

      {/* Coming Soon Modal */}
      <ComingSoonModal
        isOpen={showComingSoonModal}
        onClose={() => setShowComingSoonModal(false)}
      />

      {/* User Settings Modal */}
      <UserSettingsModal
        isOpen={showSettingsModal}
        onClose={() => setShowSettingsModal(false)}
      />
    </header>
  )
}
