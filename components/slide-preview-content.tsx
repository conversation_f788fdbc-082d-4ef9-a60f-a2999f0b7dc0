"use client"

import React, { useState, useEffect, useRef, use<PERSON><PERSON>back, useMemo } from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { SlideContent } from "./slide-content"
import { usePresenter } from "./presenter-context"
import { useLessonContext } from "./lesson-context"

interface SlidePreviewContentProps {
  currentSlide: number
  totalSlides: number
  onPrevSlide?: () => void // Kept for API compatibility but not used
  onNextSlide?: () => void // Kept for API compatibility but not used
  scale?: number // Scale percentage (e.g., 70 for 70%)
}

// Use React.memo to prevent unnecessary re-renders
const SlidePreviewContent = React.memo(function SlidePreviewContent({
  currentSlide,
  totalSlides,
  // We're not using these props anymore, but keeping them for API compatibility
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  onPrevSlide,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  onNextSlide,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  scale = 70 // Default to 70% scale for the preview
}: SlidePreviewContentProps) {
  const { revealedItems, setRevealedItems: contextSetRevealedItems } = usePresenter()
  const { unitNumber, lessonNumber, gradeLevel, lang } = useLessonContext()

  // Use useRef to store the current slide number to avoid unnecessary re-renders
  const currentSlideRef = useRef(currentSlide)


  // Create a stable wrapper function for setRevealedItems that doesn't change on re-renders
  const setRevealedItems = useCallback((value: React.SetStateAction<number[]>) => {
    if (typeof value === 'function') {
      // If it's a function, we need to call it with the current state
      contextSetRevealedItems(value(revealedItems))
    } else {
      // If it's a direct value
      contextSetRevealedItems(value)
    }
  }, [contextSetRevealedItems, revealedItems])

    // Update the ref when the prop changes
  useEffect(() => {
    currentSlideRef.current = currentSlide;
    setRevealedItems([])
  }, [currentSlide]);

  const [maxRevealableItems, setMaxRevealableItems] = useState(0)

  // Track the maximum number of revealable items for the current slide
  // Use useCallback to create a stable function reference
  const trackRevealableItems = useCallback((count: number) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`Slide ${currentSlideRef.current}: Registering ${count} revealable items`)
    }
    if (count > maxRevealableItems) {
      setMaxRevealableItems(count)
    }
  }, [maxRevealableItems])

  // Reset max items when slide changes - use a ref to track previous slide
  const prevSlideRef = useRef(currentSlide);

  useEffect(() => {
    // Only reset if the slide actually changed
    if (prevSlideRef.current !== currentSlide) {
      prevSlideRef.current = currentSlide;

      if (maxRevealableItems > 0) {
        if (process.env.NODE_ENV === 'development') {
          console.log(`Slide changed to ${currentSlide}, resetting max revealable items`);
        }
        setMaxRevealableItems(0);
      }
    }
  }, [currentSlide, maxRevealableItems]);

  // Only log in development mode to reduce console noise
  // Use a ref to avoid unnecessary effect triggers
  const maxItemsRef = useRef(maxRevealableItems);
  useEffect(() => {
    if (maxItemsRef.current !== maxRevealableItems) {
      maxItemsRef.current = maxRevealableItems;

      if (process.env.NODE_ENV === 'development') {
        console.log(`Max revealable items for slide ${currentSlide}: ${maxRevealableItems}`);
      }
    }
  }, [maxRevealableItems, currentSlide]);

  // Only log in development mode to reduce console noise
  // Use a ref to avoid unnecessary effect triggers
  const revealedItemsRef = useRef(revealedItems);
  useEffect(() => {
    if (JSON.stringify(revealedItemsRef.current) !== JSON.stringify(revealedItems)) {
      revealedItemsRef.current = revealedItems;

      if (process.env.NODE_ENV === 'development' && revealedItems.length > 0) {
        console.log(`Slide ${currentSlide}: Revealed items: ${revealedItems.join(', ')}`);
      }
    }
  }, [revealedItems, currentSlide]);

  // Function to handle revealing previous item - use useCallback for stability
  const handlePrevReveal = useCallback(() => {
    if (revealedItems.length > 0) {
      const newRevealedItems = [...revealedItems];
      newRevealedItems.pop(); // Remove the last item

      if (process.env.NODE_ENV === 'development') {
        console.log(`Hiding last item, new revealed items:`, newRevealedItems);
      }

      setRevealedItems(newRevealedItems);
    }
  }, [revealedItems, setRevealedItems]);

  // Function to handle revealing next item - use useCallback for stability
  const handleNextReveal = useCallback(() => {
    if (revealedItems.length < maxRevealableItems) {
      // In most slides, items are 0-indexed, so we start from 0
      const nextItemNumber = revealedItems.length === 0 ? 0 : revealedItems.length;

      // Check if this item is already revealed
      if (!revealedItems.includes(nextItemNumber)) {
        const newRevealedItems = [...revealedItems, nextItemNumber];

        if (process.env.NODE_ENV === 'development') {
          console.log(`Revealing item ${nextItemNumber}`, newRevealedItems);
        }

        setRevealedItems(newRevealedItems);
      } else if (process.env.NODE_ENV === 'development') {
        console.log(`Item ${nextItemNumber} already revealed`);
      }
    }
  }, [revealedItems, maxRevealableItems, setRevealedItems]);

  // We're not using the slide navigation handlers anymore since we're focusing on item reveal
  // The slide navigation is handled by the parent component

  // Memoize the slide content to prevent unnecessary re-renders
  const MemoizedSlideContent = useMemo(() => (
    <SlideContent
      slideNumber={currentSlide}
      highContrast={false}
      revealedItems={revealedItems}
      registerRevealableItems={trackRevealableItems}
      setRevealedItems={setRevealedItems}
      scale={70} // Pass the scale prop to SlideContent
      unitNumber={unitNumber}
      lessonNumber={lessonNumber}
      gradeLevel={gradeLevel}
      lang={lang}
    />
  ), [
    currentSlide,
    revealedItems,
    trackRevealableItems,
    setRevealedItems,
    unitNumber,
    lessonNumber,
    gradeLevel,
    lang
  ]);

  // Memoize the buttons to prevent unnecessary re-renders
  const RevealButtons = useMemo(() => (
    <div className="absolute bottom-2 left-0 right-0 flex justify-between items-center px-4">
      <button
        onClick={handlePrevReveal}
        disabled={revealedItems.length === 0}
        className={`w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center ${
          revealedItems.length === 0 ? "opacity-50 cursor-not-allowed" : "hover:from-blue-600 hover:to-blue-700"
        }`}
        aria-label="Previous item"
        title="Reveal previous item"
      >
        <ChevronLeft className="h-4 w-4 text-white" />
      </button>

      {/* Reveal state indicator */}
      {maxRevealableItems > 0 && (
        <div className="bg-white/20 px-2 py-1 rounded-full text-white text-xs">
          {revealedItems.length} / {maxRevealableItems} Items
        </div>
      )}

      <button
        onClick={handleNextReveal}
        disabled={revealedItems.length >= maxRevealableItems}
        className={`w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center ${
          revealedItems.length >= maxRevealableItems ? "opacity-50 cursor-not-allowed" : "hover:from-blue-600 hover:to-blue-700"
        }`}
        aria-label="Next item"
        title="Reveal next item"
      >
        <ChevronRight className="h-4 w-4 text-white" />
      </button>
    </div>
  ), [handlePrevReveal, handleNextReveal, revealedItems.length, maxRevealableItems]);

  return (
    <div className="absolute inset-0 bg-white border border-gray-200 rounded-lg overflow-hidden shadow-md">
      {/* Main slide content - scaled down version of the actual slide */}
      <div className="absolute inset-0 bg-[linear-gradient(150deg,#2B6DFE_0%,#2B6DFE_70%,#00F2FF_100%)] overflow-hidden">
        {/* Current slide number in top right */}
        <div className="absolute top-2 right-2 text-white text-sm font-medium">
          {currentSlide} of {totalSlides}
        </div>

        {/* Scale container with proper centering */}
        <div className="absolute inset-0 flex items-center justify-center overflow-hidden">
          {/* Wrapper with fixed scaling and centering */}
          <div
            className="transform origin-center"
            style={{
              transform: `scale(0.3)`, // Fixed scale for consistency
              minWidth: '1280px',
              height: '768px',
              margin: '0 auto'
            }}
          >
            {/* Render the memoized slide content */}
            {MemoizedSlideContent}
          </div>
        </div>

        {/* Render the memoized buttons */}
        {RevealButtons}
      </div>
    </div>
  );
});

// Export with displayName for better debugging
SlidePreviewContent.displayName = 'SlidePreviewContent';

export default SlidePreviewContent;
