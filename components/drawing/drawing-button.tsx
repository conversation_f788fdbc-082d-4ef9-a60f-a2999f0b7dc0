"use client"

import { useState } from "react"
import { Pencil } from "lucide-react"
import { cn } from "@/lib/utils"
import DrawingModal from "./drawing-modal"

export default function DrawingButton() {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <>
      {/* Floating action button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          "fixed bottom-[54px] right-4 w-14 h-14 rounded-full shadow-lg flex items-center justify-center z-[1300] transition-colors",
          isOpen ? "bg-blue-600 text-white" : "bg-white text-gray-700 hover:bg-gray-100"
        )}
        aria-label="Toggle drawing tool"
      >
        <Pencil className="h-6 w-6" />
      </button>

      {/* Drawing modal */}
      {isOpen && <DrawingModal onClose={() => setIsOpen(false)} />}
    </>
  )
}
