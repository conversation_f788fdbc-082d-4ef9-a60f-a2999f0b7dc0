"use client"

import { useState, useRef, useEffect } from "react"
import { Trash2, SquareIcon, Monitor, MousePointer, Grid, ChevronDown, ChevronRight } from "lucide-react"
import { cn } from "@/lib/utils"
import DrawingToolbar from "./drawing-toolbar"
import DrawingCanvas from "./drawing-canvas"

interface DrawingModalProps {
  onClose: () => void
}

export default function DrawingModal({ onClose }: DrawingModalProps) {
  // State for drawing settings
  const [isDrawing, setIsDrawing] = useState(false)
  const [fullCanvas, setFullCanvas] = useState(false)
  const [color, setColor] = useState("#282A36")
  const [brushSize, setBrushSize] = useState(10)
  const [drawingMode, setDrawingMode] = useState<"freehand" | "select" | "line" | "text">("freehand")
  const [showGrid, setShowGrid] = useState(false)

  // Handle escape key to exit full canvas mode
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        if (fullCanvas) {
          setFullCanvas(false)
        } else {
          onClose()
        }
      }
    }

    window.addEventListener('keydown', handleEscape)
    return () => window.removeEventListener('keydown', handleEscape)
  }, [fullCanvas, onClose])



  // Refs for canvases
  const screenCanvasRef = useRef<HTMLCanvasElement>(null)
  const fullCanvasRef = useRef<HTMLCanvasElement>(null)

  // Drawing state
  const currentPathRef = useRef<{ x: number; y: number }[]>([])
  const animationFrameRef = useRef<number | null>(null)
  const [shapes, setShapes] = useState<any[]>([])

  // Initialize canvas on component mount
  useEffect(() => {
    const initCanvas = () => {
      const screenCanvas = screenCanvasRef.current
      const fullCanvas = fullCanvasRef.current

      if (screenCanvas) {
        screenCanvas.width = window.innerWidth
        screenCanvas.height = window.innerHeight
      }

      if (fullCanvas) {
        fullCanvas.width = window.innerWidth
        fullCanvas.height = window.innerHeight
      }
    }

    initCanvas()
    window.addEventListener('resize', initCanvas)

    return () => {
      window.removeEventListener('resize', initCanvas)
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [])

  // Toggle full canvas mode
  const toggleFullCanvas = () => {
    setFullCanvas(!fullCanvas)
  }

  // We don't need to copy canvas content since we're using the shapes array to redraw
  // Instead, let's make sure both canvases are properly initialized
  useEffect(() => {
    const initCanvas = () => {
      const screenCanvas = screenCanvasRef.current
      const fullCanvas = fullCanvasRef.current

      if (screenCanvas) {
        screenCanvas.width = window.innerWidth
        screenCanvas.height = window.innerHeight
      }

      if (fullCanvas) {
        fullCanvas.width = window.innerWidth
        fullCanvas.height = window.innerHeight
      }
    }

    // Initialize canvases when fullCanvas changes
    initCanvas()
  }, [fullCanvas])

  // Clear both canvases and the shapes array
  const clearCanvas = () => {
    // Clear screen canvas
    const screenCanvas = screenCanvasRef.current
    if (screenCanvas) {
      const screenCtx = screenCanvas.getContext("2d")
      if (screenCtx) {
        screenCtx.clearRect(0, 0, screenCanvas.width, screenCanvas.height)
      }
    }

    // Clear full canvas
    const fullCanvas = fullCanvasRef.current
    if (fullCanvas) {
      const fullCtx = fullCanvas.getContext("2d")
      if (fullCtx) {
        fullCtx.clearRect(0, 0, fullCanvas.width, fullCanvas.height)
      }
    }

    // Clear shapes array
    setShapes([])
  }



  return (
    <>
      {/* Screen drawing canvas overlay - only shown when not in full canvas mode */}
      {!fullCanvas && (
        <DrawingCanvas
          isActive={true}
          canvasRef={screenCanvasRef}
          isDrawing={isDrawing}
          setIsDrawing={setIsDrawing}
          color={color}
          brushSize={brushSize}
          drawingMode={drawingMode}
          shapes={shapes}
          setShapes={setShapes}
          currentPathRef={currentPathRef}
          animationFrameRef={animationFrameRef}
          showGrid={showGrid}
        />
      )}

      {/* Full canvas mode - only shown when in full canvas mode */}
      {fullCanvas && (
        <div className="fixed inset-0 z-[1200] bg-white">
          <DrawingCanvas
            isActive={true}
            canvasRef={fullCanvasRef}
            isDrawing={isDrawing}
            setIsDrawing={setIsDrawing}
            color={color}
            brushSize={brushSize}
            drawingMode={drawingMode}
            shapes={shapes}
            setShapes={setShapes}
            currentPathRef={currentPathRef}
            animationFrameRef={animationFrameRef}
            showGrid={showGrid}
          />
        </div>
      )}

      {/* Drawing toolbar */}
      <DrawingToolbar
        color={color}
        setColor={setColor}
        brushSize={brushSize}
        setBrushSize={setBrushSize}
        drawingMode={drawingMode}
        setDrawingMode={setDrawingMode}
        clearCanvas={clearCanvas}
        fullCanvas={fullCanvas}
        toggleFullCanvas={toggleFullCanvas}
        showGrid={showGrid}
        setShowGrid={setShowGrid}
      />
    </>
  )
}
