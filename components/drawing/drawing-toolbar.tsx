"use client"

import { Trash2, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "lucide-react"
import { cn } from "@/lib/utils"

interface DrawingToolbarProps {
  color: string
  setColor: (color: string) => void
  brushSize: number
  setBrushSize: (size: number) => void
  drawingMode: "freehand" | "select" | "line" | "text"
  setDrawingMode: (mode: "freehand" | "select" | "line" | "text") => void
  clearCanvas: () => void
  fullCanvas: boolean
  toggleFullCanvas: () => void
  showGrid: boolean
  setShowGrid: (show: boolean) => void
}

export default function DrawingToolbar({
  color,
  setColor,
  brushSize,
  setBrushSize,
  drawingMode,
  setDrawingMode,
  clearCanvas,
  fullCanvas,
  toggleFullCanvas,
  showGrid,
  setShowGrid,
}: DrawingToolbarProps) {
  // Color palette
  const colors = [
    "#282A36", // Dark gray
    "#FF5555", // Red
    "#50FA7B", // Green
    "#F1FA8C", // Yellow
    "#BD93F9", // Purple
    "#FF79C6", // Pink
    "#8BE9FD", // Cyan
    "#F8F8F2", // Light gray
  ]

  return (
    <div className={`fixed bottom-[130px] right-4 bg-white rounded-lg shadow-lg p-4 w-64 ${fullCanvas ? 'z-[1300]' : 'z-[1100]'}`}>
      <div className="flex flex-col gap-4">
        {/* Drawing modes */}
        <div className="flex gap-2">
          <button
            onClick={() => setDrawingMode("select")}
            className={cn(
              "flex items-center justify-center p-2 rounded-md",
              drawingMode === "select" ? "bg-blue-100 text-blue-600" : "bg-gray-100 hover:bg-gray-200"
            )}
            title="Select"
          >
            <MousePointer className="h-4 w-4" />
          </button>

          <button
            onClick={() => setDrawingMode("freehand")}
            className={cn(
              "flex items-center justify-center p-2 rounded-md",
              drawingMode === "freehand" ? "bg-blue-100 text-blue-600" : "bg-gray-100 hover:bg-gray-200"
            )}
            title="Freehand"
          >
            <Pencil className="h-4 w-4" />
          </button>

          <button
            onClick={() => setDrawingMode("line")}
            className={cn(
              "flex items-center justify-center p-2 rounded-md",
              drawingMode === "line" ? "bg-blue-100 text-blue-600" : "bg-gray-100 hover:bg-gray-200"
            )}
            title="Line"
          >
            <Minus className="h-4 w-4" />
          </button>

          <button
            onClick={() => setShowGrid(!showGrid)}
            className={cn(
              "flex items-center justify-center p-2 rounded-md",
              showGrid ? "bg-blue-100 text-blue-600" : "bg-gray-100 hover:bg-gray-200"
            )}
            title="Toggle grid"
          >
            <Grid className="h-4 w-4" />
          </button>
        </div>

        {/* Color picker */}
        <div>
          <div className="text-xs font-medium text-gray-500 mb-2">Color</div>
          <div className="flex flex-wrap gap-2">
            {colors.map((c) => (
              <button
                key={c}
                onClick={() => setColor(c)}
                className={cn(
                  "w-6 h-6 rounded-full border",
                  color === c ? "ring-2 ring-offset-2 ring-blue-500" : ""
                )}
                style={{ backgroundColor: c }}
              />
            ))}
          </div>
        </div>

        {/* Brush size slider */}
        <div>
          <div className="text-xs font-medium text-gray-500 mb-2">Brush Size: {brushSize}px</div>
          <input
            type="range"
            min="1"
            max="50"
            value={brushSize}
            onChange={(e) => setBrushSize(parseInt(e.target.value))}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
          />
        </div>



        {/* Action buttons */}
        <div className="flex gap-2">
          <button
            onClick={clearCanvas}
            className="flex items-center justify-center p-2 bg-gray-100 rounded-md hover:bg-gray-200 flex-1"
            title="Clear canvas"
          >
            <Trash2 className="h-4 w-4 mr-1" />
            <span className="text-sm">Clear</span>
          </button>

          <button
            onClick={toggleFullCanvas}
            className={cn(
              "flex items-center justify-center p-2 rounded-md flex-1",
              fullCanvas ? "bg-blue-500 text-white" : "bg-gray-100 hover:bg-gray-200"
            )}
            title={fullCanvas ? "Exit full canvas" : "Full canvas"}
          >
            {fullCanvas ? (
              <>
                <Monitor className="h-4 w-4 mr-1" />
                <span className="text-sm">Exit</span>
              </>
            ) : (
              <>
                <SquareIcon className="h-4 w-4 mr-1" />
                <span className="text-sm">Full</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  )
}
