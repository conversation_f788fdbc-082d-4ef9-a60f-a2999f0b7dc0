"use client"

import { useRef, useEffect, useState, useCallback, MutableRefObject } from "react"

interface Point {
  x: number
  y: number
}

interface DrawingCanvasProps {
  isActive: boolean
  canvasRef: React.RefObject<HTMLCanvasElement>
  isDrawing: boolean
  setIsDrawing: (isDrawing: boolean) => void
  color: string
  brushSize: number
  drawingMode: "freehand" | "select" | "line" | "text"
  shapes: any[]
  setShapes: (shapes: any[]) => void
  currentPathRef: MutableRefObject<Point[]>
  animationFrameRef: MutableRefObject<number | null>
  showGrid: boolean
}

export default function DrawingCanvas({
  isActive,
  canvasRef,
  isDrawing,
  setIsDrawing,
  color,
  brushSize,
  drawingMode,
  shapes,
  setShapes,
  currentPathRef,
  animationFrameRef,
  showGrid,
}: DrawingCanvasProps) {
  // Line drawing state
  const [lineStartPoint, setLineStartPoint] = useState<Point | null>(null)
  const [lineEndPoint, setLineEndPoint] = useState<Point | null>(null)
  const [isDrawingLine, setIsDrawingLine] = useState(false)

  // Function to draw the entire path at once
  const drawPath = () => {
    const canvas = canvasRef.current
    if (!canvas || currentPathRef.current.length < 2) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Set up the marker style
    ctx.lineJoin = "round"
    ctx.lineCap = "round"
    ctx.lineWidth = brushSize
    ctx.strokeStyle = color
    ctx.globalAlpha = 0.6 // This creates the faded effect

    // Draw the entire path as one continuous stroke
    ctx.beginPath()
    const firstPoint = currentPathRef.current[0]
    ctx.moveTo(firstPoint.x, firstPoint.y)

    // Add all points to the path
    for (let i = 1; i < currentPathRef.current.length; i++) {
      const point = currentPathRef.current[i]
      ctx.lineTo(point.x, point.y)
    }

    // Stroke once for the entire path
    ctx.stroke()
  }

  // Function to draw a line
  const drawLine = useCallback(() => {
    if (!lineStartPoint || !lineEndPoint) return

    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Set up the line style
    ctx.lineJoin = "round"
    ctx.lineCap = "round"
    ctx.lineWidth = brushSize
    ctx.strokeStyle = color
    ctx.globalAlpha = 0.6

    // Draw the line
    ctx.beginPath()
    ctx.moveTo(lineStartPoint.x, lineStartPoint.y)
    ctx.lineTo(lineEndPoint.x, lineEndPoint.y)
    ctx.stroke()

    // Draw points at the ends
    ctx.fillStyle = color
    ctx.beginPath()
    ctx.arc(lineStartPoint.x, lineStartPoint.y, 3, 0, Math.PI * 2)
    ctx.fill()

    ctx.beginPath()
    ctx.arc(lineEndPoint.x, lineEndPoint.y, 3, 0, Math.PI * 2)
    ctx.fill()
  }, [lineStartPoint, lineEndPoint, color, brushSize])

  // Draw grid on canvas
  const drawGrid = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    if (!showGrid) return

    const gridSize = 20
    ctx.save()
    ctx.strokeStyle = "#ddd"
    ctx.lineWidth = 0.5

    // Draw vertical lines
    for (let x = 0; x <= width; x += gridSize) {
      ctx.beginPath()
      ctx.moveTo(x, 0)
      ctx.lineTo(x, height)
      ctx.stroke()
    }

    // Draw horizontal lines
    for (let y = 0; y <= height; y += gridSize) {
      ctx.beginPath()
      ctx.moveTo(0, y)
      ctx.lineTo(width, y)
      ctx.stroke()
    }

    ctx.restore()
  }

  // Redraw the canvas
  const redrawCanvas = useCallback(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Clear the canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // Save the context state
    ctx.save()

    // Draw the grid
    drawGrid(ctx, canvas.width, canvas.height)

    // Draw all shapes
    shapes.forEach((shape) => {
      if (shape.type === "freehand") {
        // Draw freehand path
        ctx.lineJoin = "round"
        ctx.lineCap = "round"
        ctx.lineWidth = shape.brushSize
        ctx.strokeStyle = shape.color
        ctx.globalAlpha = 0.6

        ctx.beginPath()
        if (shape.points && shape.points.length > 0) {
          const firstPoint = shape.points[0]
          ctx.moveTo(firstPoint.x, firstPoint.y)

          for (let i = 1; i < shape.points.length; i++) {
            const point = shape.points[i]
            ctx.lineTo(point.x, point.y)
          }

          ctx.stroke()
        }
      } else if (shape.type === "line") {
        // Draw line
        ctx.lineJoin = "round"
        ctx.lineCap = "round"
        ctx.lineWidth = shape.brushSize
        ctx.strokeStyle = shape.color
        ctx.globalAlpha = 0.6

        if (shape.startPoint && shape.endPoint) {
          ctx.beginPath()
          ctx.moveTo(shape.startPoint.x, shape.startPoint.y)
          ctx.lineTo(shape.endPoint.x, shape.endPoint.y)
          ctx.stroke()

          // Draw points at the ends
          ctx.fillStyle = shape.color
          ctx.beginPath()
          ctx.arc(shape.startPoint.x, shape.startPoint.y, 3, 0, Math.PI * 2)
          ctx.fill()

          ctx.beginPath()
          ctx.arc(shape.endPoint.x, shape.endPoint.y, 3, 0, Math.PI * 2)
          ctx.fill()
        }
      }
    })

    // Draw the preview line if in line mode
    if (drawingMode === "line" && isDrawingLine && lineStartPoint && lineEndPoint) {
      drawLine()
    }

    // Restore the context state
    ctx.restore()
  }, [drawingMode, isDrawingLine, lineStartPoint, lineEndPoint, shapes, showGrid, drawLine])

  // Start drawing
  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current
    if (!canvas) return

    const rect = canvas.getBoundingClientRect()
    let clientX: number, clientY: number

    if ("touches" in e) {
      clientX = e.touches[0].clientX
      clientY = e.touches[0].clientY
    } else {
      clientX = e.clientX
      clientY = e.clientY
    }

    const point = {
      x: clientX - rect.left,
      y: clientY - rect.top,
    }

    if (drawingMode === "freehand") {
      setIsDrawing(true)
      currentPathRef.current = [point]

      // Cancel any existing animation frame
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }

      // Start the animation loop
      const animate = () => {
        drawPath()
        animationFrameRef.current = requestAnimationFrame(animate)
      }
      animationFrameRef.current = requestAnimationFrame(animate)
    } else if (drawingMode === "line") {
      setIsDrawingLine(true)
      setLineStartPoint(point)
      setLineEndPoint(point)
    }
  }

  // Continue drawing
  const draw = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current
    if (!canvas) return

    const rect = canvas.getBoundingClientRect()
    let clientX: number, clientY: number

    if ("touches" in e) {
      clientX = e.touches[0].clientX
      clientY = e.touches[0].clientY
      // Prevent scrolling when drawing
      e.preventDefault()
    } else {
      clientX = e.clientX
      clientY = e.clientY
    }

    const point = {
      x: clientX - rect.left,
      y: clientY - rect.top,
    }

    if (drawingMode === "freehand" && isDrawing) {
      // Add the new point to the current path
      currentPathRef.current.push(point)
    } else if (drawingMode === "line" && isDrawingLine) {
      setLineEndPoint(point)
      redrawCanvas()
    }
  }

  // End drawing
  const endDrawing = () => {
    if (drawingMode === "freehand" && isDrawing) {
      setIsDrawing(false)

      // Cancel the animation frame
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
        animationFrameRef.current = null
      }

      if (currentPathRef.current.length > 1) {
        // Save the path as a shape
        const newShape = {
          id: `shape_${Date.now()}`,
          type: "freehand",
          points: [...currentPathRef.current],
          color,
          brushSize,
        }

        setShapes([...shapes, newShape])
      }

      // Clear the current path
      currentPathRef.current = []
    } else if (drawingMode === "line" && isDrawingLine) {
      setIsDrawingLine(false)

      if (lineStartPoint && lineEndPoint) {
        // Save the line as a shape
        const newShape = {
          id: `shape_${Date.now()}`,
          type: "line",
          startPoint: lineStartPoint,
          endPoint: lineEndPoint,
          color,
          brushSize,
        }

        setShapes([...shapes, newShape])
      }
    }
  }

  // Redraw the canvas when it becomes active or when dependencies change
  useEffect(() => {
    if (isActive) {
      // Make sure the canvas is properly initialized
      const canvas = canvasRef.current
      if (canvas) {
        canvas.width = window.innerWidth
        canvas.height = window.innerHeight

        // Redraw after a short delay to ensure the canvas is ready
        setTimeout(() => {
          redrawCanvas()
        }, 50)
      }
    }
  }, [isActive, redrawCanvas])

  if (!isActive) return null

  return (
    <canvas
      ref={canvasRef}
      onMouseDown={startDrawing}
      onMouseMove={draw}
      onMouseUp={endDrawing}
      onMouseLeave={endDrawing}
      onTouchStart={startDrawing}
      onTouchMove={draw}
      onTouchEnd={endDrawing}
      className="fixed top-0 left-0 w-full h-full z-[1050] touch-none"
      style={{ pointerEvents: "all" }}
    />
  )
}
