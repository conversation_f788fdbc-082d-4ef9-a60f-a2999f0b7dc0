"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Loader2, Wand2 } from "lucide-react"

interface HtmlGeneratorTestProps {
  slideNumber?: number
  unitNumber?: string
  lessonNumber?: string
  gradeLevel?: string
}

export default function HtmlGeneratorTest({
  slideNumber,
  unitNumber,
  lessonNumber,
  gradeLevel
}: HtmlGeneratorTestProps) {
  const [prompt, setPrompt] = useState("")
  const [generatedHtml, setGeneratedHtml] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const [availableModels, setAvailableModels] = useState<string[]>([])
  const [showModels, setShowModels] = useState(false)

  const generateHtml = async () => {
    if (!prompt.trim()) {
      setError("Please enter a prompt")
      return
    }

    setIsLoading(true)
    setError("")
    setGeneratedHtml("")

    try {
      const response = await fetch('/api/generate-html', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: prompt.trim(),
          slideNumber,
          unitNumber,
          lessonNumber,
          gradeLevel,
          curriculum: 'CCSS', // Default curriculum for testing
          lang: 'en' // Default language for testing
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to generate HTML')
      }

      setGeneratedHtml(data.html)
      console.log('Generated HTML:', data)
    } catch (err) {
      console.error('Error generating HTML:', err)
      setError(err instanceof Error ? err.message : 'Failed to generate HTML')
    } finally {
      setIsLoading(false)
    }
  }

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(generatedHtml)
      alert('HTML copied to clipboard!')
    } catch (err) {
      console.error('Failed to copy:', err)
    }
  }

  const checkAvailableModels = async () => {
    try {
      const response = await fetch('/api/list-models')
      const data = await response.json()
      if (data.success) {
        setAvailableModels(data.modelNames || [])
        setShowModels(true)
      } else {
        setError('Failed to fetch available models: ' + data.error)
      }
    } catch (err) {
      setError('Failed to check available models')
    }
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-bold mb-4 flex items-center gap-2" style={{ fontFamily: 'Inter, sans-serif' }}>
          <Wand2 className="h-6 w-6 text-blue-600" />
          HTML Generator (Gemini AI)
        </h2>

        {/* Context Info */}
        {(slideNumber || unitNumber || lessonNumber || gradeLevel) && (
          <div className="bg-blue-50 rounded-lg p-4 mb-4">
            <h3 className="font-semibold text-blue-800 mb-2" style={{ fontFamily: 'Inter, sans-serif' }}>Context:</h3>
            <div className="text-sm text-blue-700 space-y-1" style={{ fontFamily: 'Inter, sans-serif' }}>
              {slideNumber && <div>Slide: {slideNumber}</div>}
              {unitNumber && <div>Unit: {unitNumber}</div>}
              {lessonNumber && <div>Lesson: {lessonNumber}</div>}
              {gradeLevel && <div>Grade: {gradeLevel}</div>}
            </div>
          </div>
        )}

        {/* Prompt Input */}
        <div className="space-y-2">
          <label htmlFor="prompt" className="block text-sm font-medium text-gray-700" style={{ fontFamily: 'Inter, sans-serif' }}>
            Describe what you want to create:
          </label>
          <Textarea
            id="prompt"
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            placeholder="Example: Create an interactive math problem about adding fractions with visual representations"
            className="min-h-[100px]"
            style={{ fontFamily: 'Inter, sans-serif' }}
          />
        </div>

        {/* Generate Button */}
        <div className="flex gap-2 mt-4">
          <Button
            onClick={generateHtml}
            disabled={isLoading || !prompt.trim()}
            className="flex-1"
            style={{ fontFamily: 'Inter, sans-serif' }}
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Generating HTML...
              </>
            ) : (
              <>
                <Wand2 className="h-4 w-4 mr-2" />
                Generate HTML
              </>
            )}
          </Button>
          <Button
            onClick={checkAvailableModels}
            variant="outline"
            className="px-4"
            style={{ fontFamily: 'Inter, sans-serif' }}
            title="Check available Gemini models"
          >
            Check Models
          </Button>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-700 text-sm font-medium" style={{ fontFamily: 'Inter, sans-serif' }}>Error:</p>
            <p className="text-red-700 text-sm" style={{ fontFamily: 'Inter, sans-serif' }}>{error}</p>
            {error.includes('models') && (
              <div className="mt-2 text-xs text-red-600" style={{ fontFamily: 'Inter, sans-serif' }}>
                <p>Troubleshooting tips:</p>
                <ul className="list-disc list-inside mt-1 space-y-1">
                  <li>Check that your GEMINI_API_KEY is set correctly</li>
                  <li>Visit <a href="/api/list-models" target="_blank" className="underline">api/list-models</a> to see available models</li>
                  <li>Make sure your API key has access to Gemini models</li>
                </ul>
              </div>
            )}
          </div>
        )}

        {/* Available Models Display */}
        {showModels && availableModels.length > 0 && (
          <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
            <p className="text-green-700 text-sm font-medium mb-2" style={{ fontFamily: 'Inter, sans-serif' }}>
              Available Gemini Models:
            </p>
            <div className="text-sm text-green-600" style={{ fontFamily: 'Inter, sans-serif' }}>
              {availableModels.map((model, index) => (
                <div key={index} className="py-1">
                  • {model}
                </div>
              ))}
            </div>
            <button
              onClick={() => setShowModels(false)}
              className="text-xs text-green-500 hover:text-green-700 mt-2"
              style={{ fontFamily: 'Inter, sans-serif' }}
            >
              Hide models
            </button>
          </div>
        )}
      </div>

      {/* Generated HTML Display */}
      {generatedHtml && (
        <div className="space-y-4">
          {/* Preview */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-semibold" style={{ fontFamily: 'Inter, sans-serif' }}>Preview</h3>
              <Button onClick={copyToClipboard} variant="outline" size="sm" style={{ fontFamily: 'Inter, sans-serif' }}>
                Copy HTML
              </Button>
            </div>
            <div
              className="border rounded-lg p-4 bg-gray-50"
              dangerouslySetInnerHTML={{ __html: generatedHtml }}
            />
          </div>

          {/* HTML Code */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-xl font-semibold mb-4" style={{ fontFamily: 'Inter, sans-serif' }}>Generated HTML</h3>
            <pre className="bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto text-sm">
              <code>{generatedHtml}</code>
            </pre>
          </div>
        </div>
      )}
    </div>
  )
}