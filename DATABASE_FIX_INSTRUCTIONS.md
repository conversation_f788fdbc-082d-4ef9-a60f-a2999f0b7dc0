# Database Fix Instructions for Translation Support

## Problem
The current database has a unique index on `documentId` field, which prevents having multiple language versions of the same lesson (e.g., English and Spanish versions with the same documentId but different `lang` values).

## Solution
Replace the unique `documentId` index with a compound unique index on `documentId + lang`.

## Steps to Fix

### Option 1: Using MongoDB Shell (Recommended)

1. **Connect to your MongoDB database:**
   ```bash
   # For local development
   mongosh mongodb://localhost:27017/math-local-dev
   
   # Or connect to your specific database
   mongosh "your-mongodb-connection-string"
   ```

2. **Switch to your database:**
   ```javascript
   use math-local-dev  // or your database name
   ```

3. **Check current indexes:**
   ```javascript
   db.jsondocuments.getIndexes()
   ```

4. **Drop the old unique documentId index:**
   ```javascript
   db.jsondocuments.dropIndex("documentId_1")
   ```

5. **Create new compound unique index:**
   ```javascript
   db.jsondocuments.createIndex(
     { "documentId": 1, "lang": 1 }, 
     { "unique": true, "name": "documentId_lang_unique" }
   )
   ```

6. **Verify the new index:**
   ```javascript
   db.jsondocuments.getIndexes()
   ```

### Option 2: Using Node.js Script

1. **Run the provided script:**
   ```bash
   cd scripts
   node fix-database-indexes.js
   ```

### Option 3: Manual Database Management Tool

If you're using MongoDB Compass or another GUI tool:

1. Navigate to your `jsondocuments` collection
2. Go to the "Indexes" tab
3. Delete the `documentId_1` index
4. Create a new compound index:
   - Fields: `documentId: 1, lang: 1`
   - Options: `unique: true`
   - Name: `documentId_lang_unique`

## Verification

After fixing the indexes, verify the changes:

### Check for Duplicate DocumentIds
```javascript
db.jsondocuments.aggregate([
  { 
    $group: { 
      _id: "$documentId", 
      count: { $sum: 1 }, 
      docs: { $push: { _id: "$_id", lang: "$lang", grade_level: "$grade_level" } } 
    }
  },
  { $match: { count: { $gt: 1 } }}
])
```

### Expected Result
You should see documents like:
```javascript
// English version
{
  documentId: "6-49-Grade 1",
  lang: "en",
  grade_level: "Grade 1"
}

// Spanish version  
{
  documentId: "6-49-Grade 1",
  lang: "esp", 
  grade_level: "Grado 1"
}
```

## After Database Fix

1. **Remove any incorrect Spanish documents:**
   ```javascript
   // Remove Spanish document with wrong documentId format
   db.jsondocuments.deleteOne({
     documentId: "6-49-Grado 1",
     lang: "esp"
   })
   ```

2. **Re-upload the Spanish translation file:**
   - Go to `/upload` in your application
   - Upload `Grade_1_Lesson_49_DUMMY_Translated_ES.json` again
   - Should now work without duplicate key errors

3. **Verify the upload:**
   - Check that Spanish document has `documentId: "6-49-Grade 1"`
   - Check that HTML content was copied from English version
   - Test that language button becomes enabled

## Troubleshooting

### If you get "Index already exists" error:
```javascript
// Check what indexes exist
db.jsondocuments.getIndexes()

// Drop all indexes except _id (be careful!)
db.jsondocuments.dropIndexes()

// Recreate the indexes you need
db.jsondocuments.createIndex({ "documentId": 1, "lang": 1 }, { unique: true })
db.jsondocuments.createIndex({ "unit_number": 1, "lesson_number": 1, "grade_level": 1, "lang": 1 })
```

### If you have existing duplicate data:
```javascript
// Find and remove duplicates manually
db.jsondocuments.find({ documentId: "6-49-Grade 1" })

// Keep the one you want, delete the others
db.jsondocuments.deleteOne({ _id: ObjectId("id-to-delete") })
```

## Expected Final State

After completing these steps:
- ✅ Database allows multiple language versions per lesson
- ✅ Spanish translation uploads work without errors  
- ✅ Language button enables for lessons with translations
- ✅ Language switching works correctly
- ✅ URL parameters persist language selection
