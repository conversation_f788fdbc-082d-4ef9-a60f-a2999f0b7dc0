import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { getToken } from 'next-auth/jwt'

// This function can be marked `async` if using `await` inside
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Check if the pathname is for authentication pages
  const isAuthPage = pathname.startsWith('/login') || pathname.startsWith('/register')

  // Get the token
  const token = await getToken({ req: request, secret: process.env.NEXTAUTH_SECRET })

  // If the user is authenticated and trying to access auth pages, redirect to root
  if (token && isAuthPage) {
    return NextResponse.redirect(new URL('/', request.url))
  }

  // If the user is already on the root path and authenticated, don't redirect
  if (token && pathname === '/') {
    return NextResponse.next()
  }

  // Define protected routes - protect only the root page
  const protectedRoutes = ['/']
  const isProtectedRoute = pathname === '/' || pathname.startsWith('/audience') || pathname.startsWith('/presenter')

  // If the user is not authenticated and trying to access protected routes, redirect to login
  if (!token && isProtectedRoute) {
    const url = new URL('/login', request.url)
    url.searchParams.set('callbackUrl', pathname)
    return NextResponse.redirect(url)
  }

  return NextResponse.next()
}

// See "Matching Paths" below to learn more
export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)'],
}
