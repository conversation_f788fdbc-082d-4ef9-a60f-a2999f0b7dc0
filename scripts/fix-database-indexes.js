// Script to fix database indexes for translation support
// Run this in MongoDB shell or through Node.js

const { MongoClient } = require('mongodb');

async function fixDatabaseIndexes() {
  const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017/math-local-dev';
  const client = new MongoClient(uri);

  try {
    await client.connect();
    console.log('Connected to MongoDB');

    const db = client.db();
    const collection = db.collection('jsondocuments');

    // 1. List current indexes
    console.log('\n=== Current Indexes ===');
    const indexes = await collection.indexes();
    indexes.forEach(index => {
      console.log(`Index: ${index.name}`, index.key, index.unique ? '(UNIQUE)' : '');
    });

    // 2. Drop the old unique documentId index if it exists
    try {
      console.log('\n=== Dropping old documentId index ===');
      await collection.dropIndex('documentId_1');
      console.log('✅ Successfully dropped documentId_1 index');
    } catch (error) {
      if (error.code === 27) {
        console.log('ℹ️  documentId_1 index does not exist (already dropped)');
      } else {
        console.log('❌ Error dropping index:', error.message);
      }
    }

    // 3. Create new compound unique index for documentId + lang
    try {
      console.log('\n=== Creating new compound unique index ===');
      await collection.createIndex(
        { documentId: 1, lang: 1 },
        { unique: true, name: 'documentId_lang_unique' }
      );
      console.log('✅ Successfully created documentId + lang unique index');
    } catch (error) {
      if (error.code === 85) {
        console.log('ℹ️  Compound unique index already exists');
      } else {
        console.log('❌ Error creating index:', error.message);
      }
    }

    // 4. List indexes after changes
    console.log('\n=== Updated Indexes ===');
    const updatedIndexes = await collection.indexes();
    updatedIndexes.forEach(index => {
      console.log(`Index: ${index.name}`, index.key, index.unique ? '(UNIQUE)' : '');
    });

    // 5. Check for duplicate documentId entries
    console.log('\n=== Checking for duplicate documentId entries ===');
    const duplicates = await collection.aggregate([
      {
        $group: {
          _id: '$documentId',
          count: { $sum: 1 },
          docs: { $push: { _id: '$_id', lang: '$lang', grade_level: '$grade_level' } }
        }
      },
      {
        $match: { count: { $gt: 1 } }
      }
    ]).toArray();

    if (duplicates.length > 0) {
      console.log('Found duplicate documentId entries:');
      duplicates.forEach(dup => {
        console.log(`DocumentId: ${dup._id} (${dup.count} documents)`);
        dup.docs.forEach(doc => {
          console.log(`  - ${doc._id} (lang: ${doc.lang}, grade: ${doc.grade_level})`);
        });
      });
    } else {
      console.log('✅ No duplicate documentId entries found');
    }

    console.log('\n=== Database index fix completed ===');

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await client.close();
  }
}

// MongoDB shell commands (alternative to Node.js script)
console.log(`
=== MongoDB Shell Commands ===
To run these commands directly in MongoDB shell:

// 1. Connect to your database
use math-local-dev

// 2. Drop old unique index
db.jsondocuments.dropIndex("documentId_1")

// 3. Create new compound unique index
db.jsondocuments.createIndex(
  { "documentId": 1, "lang": 1 }, 
  { "unique": true, "name": "documentId_lang_unique" }
)

// 4. Check current indexes
db.jsondocuments.getIndexes()

// 5. Find duplicate documentId entries
db.jsondocuments.aggregate([
  { $group: { 
    _id: "$documentId", 
    count: { $sum: 1 }, 
    docs: { $push: { _id: "$_id", lang: "$lang" } } 
  }},
  { $match: { count: { $gt: 1 } }}
])
`);

// Run the script if called directly
if (require.main === module) {
  fixDatabaseIndexes().catch(console.error);
}

module.exports = { fixDatabaseIndexes };
