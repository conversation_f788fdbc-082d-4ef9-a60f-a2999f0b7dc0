# Gemini Model Configuration

This document explains how to configure and switch between different Gemini AI models for HTML generation.

## Available Models

The application supports two Gemini models:

### 1. Gemini 2.0 Flash Experimental (`gemini-2.0-flash-exp`)
- **Max Output Tokens**: 8,192
- **Description**: Latest experimental model with improved performance
- **Recommended for**: General use, faster responses
- **Default**: Yes

### 2. Gemini 2.5 Flash Preview (`gemini-2.5-flash-preview-05-20`)
- **Max Output Tokens**: 32,768
- **Description**: Previous generation with higher token limit
- **Recommended for**: Complex HTML generation requiring more tokens
- **Default**: No

## Configuration

### Environment Variable

The model is configured using the `GEMINI_MODEL` environment variable in your `.env` file:

```env
# Use Gemini 2.0 Flash Experimental (default)
GEMINI_MODEL=gemini-2.0-flash-exp

# OR use Gemini 2.5 Flash Preview
GEMINI_MODEL=gemini-2.5-flash-preview-05-20
```

### Switching Models

To switch between models:

1. **Edit your `.env` file**:
   ```env
   # Change this line to your preferred model
   GEMINI_MODEL=gemini-2.5-flash-preview-05-20
   ```

2. **Restart your development server**:
   ```bash
   npm run dev
   # or
   yarn dev
   ```

3. **Verify the change** in the console logs when making API calls to `/api/generate-html`

## Model Selection Guidelines

### Use Gemini 2.0 Flash Experimental when:
- ✅ You need faster response times
- ✅ Your HTML generation prompts are relatively simple
- ✅ You want the latest model improvements
- ✅ Token limit of 8,192 is sufficient

### Use Gemini 2.5 Flash Preview when:
- ✅ You need higher token limits (32,768)
- ✅ Your prompts are very complex or long
- ✅ You're generating very detailed HTML with extensive styling
- ✅ You encounter token limit errors with the 2.0 model

## Error Handling

If you specify an unsupported model, the API will return:

```json
{
  "error": "Unsupported model: your-model-name",
  "suggestion": "Supported models: gemini-2.0-flash-exp, gemini-2.5-flash-preview-05-20",
  "details": "Check GEMINI_MODEL environment variable"
}
```

## Console Output

When the API starts, you'll see logs indicating which model is being used:

```
Using gemini-2.0-flash-exp (Gemini 2.0 Flash Experimental) with max tokens: 8192
```

or

```
Using gemini-2.5-flash-preview-05-20 (Gemini 2.5 Flash Preview) with max tokens: 32768
```

## Default Behavior

If the `GEMINI_MODEL` environment variable is not set, the application defaults to `gemini-2.0-flash-exp`.
