# Translation System - Final Fix

## Problem Solved
Fixed the duplicate key error by changing the documentId format for translations to include language suffix.

## New DocumentId Format

### English Documents
- Format: `"6-49-Grade 1"`
- Example: `documentId: "6-49-Grade 1", lang: "en"`

### Spanish Documents  
- Format: `"6-49-Grade 1-esp"`
- Example: `documentId: "6-49-Grade 1-esp", lang: "esp"`

## Files Modified

### 1. Upload API (`app/api/upload-json-document/route.ts`)
- Spanish documents now get `-esp` suffix in documentId
- HTML copying function looks for English version using base documentId
- Fixed function calls to use correct parameters

### 2. Translation Check API (`app/api/check-translation/route.ts`)
- Updated to work with new documentId format
- Checks both English and Spanish versions correctly
- Returns proper availability status

### 3. Lesson Data API (`app/api/get-lesson-data/route.ts`)
- Converts base documentId to actual documentId based on language
- Spanish requests get `-esp` suffix automatically

### 4. Slide Service (`services/slideService.ts`)
- Updated caching to use correct documentId format
- Both loadSlideData and getSlide functions fixed

## Testing Steps

### 1. Clean Up Old Data (if needed)
```javascript
// Remove old Spanish document with wrong format
db.jsondocuments.deleteOne({
  documentId: "6-49-Grado 1",
  lang: "esp"
})
```

### 2. Upload Spanish Translation
1. Go to `/upload`
2. Upload `Grade_1_Lesson_49_DUMMY_Translated_ES.json`
3. **Expected Result**: Success message with HTML copied

### 3. Verify Database State
Check that you have:
```javascript
// English document
{
  documentId: "6-49-Grade 1",
  lang: "en",
  grade_level: "Grade 1"
}

// Spanish document
{
  documentId: "6-49-Grade 1-esp", 
  lang: "esp",
  grade_level: "Grado 1"
}
```

### 4. Test Language Button
1. Navigate to Grade 1, Unit 6, Lesson 49
2. **Expected**: Language button should be enabled
3. Click language button → Select "Español"
4. **Expected**: Content switches to Spanish

### 5. Test URL Persistence
1. Copy URL with `?lang=esp`
2. Open in new tab or refresh
3. **Expected**: Page loads in Spanish

## Expected Console Messages

### During Upload
```
Looking for English version of document: 6-49-Grade 1
Found English version, copying HTML content...
Copied HTML content from English to Spanish for Slide 1: Quick_Review
Copied HTML content for X slides from English to Spanish version of 6-49-Grade 1
```

### During Translation Check
```
Checking translation availability for: { baseDocumentId: '6-49-Grade 1', lang: 'en' }
Translation check results: { 
  englishExists: true, 
  spanishExists: true, 
  requestedLang: 'en',
  exists: true, 
  hasTranslation: true 
}
```

### During Language Switch
```
📄 Loading lesson data for baseDocumentId: 6-49-Grade 1 actualDocumentId: 6-49-Grade 1-esp lang: esp
✅ Document found: {
  baseDocumentId: '6-49-Grade 1',
  actualDocumentId: '6-49-Grade 1-esp',
  hasContent: true
}
```

## Key Benefits of This Approach

1. **No Database Schema Changes** - Works with existing unique index
2. **Clear Separation** - Each language has distinct documentId
3. **Backward Compatibility** - English documents unchanged
4. **Easy Debugging** - Clear documentId format shows language
5. **Scalable** - Can add more languages (e.g., `-fr`, `-de`)

## API Behavior Summary

| API Endpoint | Input | DocumentId Used |
|--------------|-------|-----------------|
| `/api/upload-json-document` | Spanish JSON | `6-49-Grade 1-esp` |
| `/api/check-translation` | `lang=esp` | Checks both formats |
| `/api/get-lesson-data` | `documentId=6-49-Grade 1&lang=esp` | `6-49-Grade 1-esp` |
| `/api/json-documents` | `lang=esp` | `6-49-Grade 1-esp` |

## Success Criteria

- ✅ Spanish translation uploads without duplicate key errors
- ✅ HTML content is copied from English version
- ✅ Language button enables for lessons with translations
- ✅ Language switching works correctly
- ✅ URL parameters persist language selection
- ✅ Page refreshes maintain language choice
- ✅ Caching works correctly for both languages

## Troubleshooting

### If language button still not enabled:
1. Check console for translation check API calls
2. Verify Spanish document exists with correct documentId format
3. Clear browser cache and reload

### If content doesn't switch:
1. Check console for lesson data API calls
2. Verify documentId format in API responses
3. Check that HTML content was copied correctly

### If upload still fails:
1. Verify no old Spanish documents exist
2. Check that English document exists first
3. Look for console errors during HTML copying

The system should now work correctly with the new documentId format!
