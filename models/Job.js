import mongoose from 'mongoose'

const jobSchema = new mongoose.Schema({
  // Job identification
  jobId: {
    type: String,
    required: true,
    unique: true
  },

  // Lesson context
  documentId: {
    type: String,
    required: true // Format: "unit-lesson-grade"
  },
  unitNumber: {
    type: String,
    required: true
  },
  lessonNumber: {
    type: String,
    required: true
  },
  gradeLevel: {
    type: String,
    required: true
  },

  // Slide context
  slideNumber: {
    type: Number,
    required: true
  },
  slideKey: {
    type: String,
    required: true // e.g., "Slide 1: Quick_Review"
  },
  slideType: {
    type: String,
    required: true // e.g., "INTRODUCTION", "HOOK", "TEACH", etc.
  },

  // Job details
  prompt: {
    type: String,
    required: true // Generated prompt for HTML creation
  },
  originalPrompt: {
    type: String,
    required: false // Original html_css_description_of_image content
  },
  status: {
    type: String,
    enum: ['pending', 'processing', 'success', 'failed', 'archived'],
    default: 'pending'
  },

  // Results
  generatedHtml: {
    type: String,
    default: null
  },
  errorMessage: {
    type: String,
    default: null
  },

  // Metadata
  createdAt: {
    type: Date,
    default: Date.now
  },
  processedAt: {
    type: Date,
    default: null
  },
  processingStartedAt: {
    type: Date,
    default: null
  },
  modelVersion: {
    type: String,
    default: null
  },

  // Processing info
  retryCount: {
    type: Number,
    default: 0
  },
  maxRetries: {
    type: Number,
    default: 3
  }
}, {
  timestamps: true
})

// Indexes for efficient querying
jobSchema.index({ documentId: 1, slideNumber: 1 })
jobSchema.index({ status: 1 })
jobSchema.index({ createdAt: -1 })

// Static method to generate job ID
jobSchema.statics.generateJobId = function(documentId, slideNumber) {
  return `${documentId}-slide-${slideNumber}-${Date.now()}`
}

// Instance method to generate prompt based on slide data
jobSchema.methods.generatePrompt = function(slideData) {
  const { slide_pedagogical_name, type, script, teacher_tips } = slideData

  let prompt = `Create an interactive HTML visualization for a ${this.gradeLevel} math lesson.

Slide Context:
- Slide: ${this.slideNumber} (${slide_pedagogical_name})
- Type: ${type}
- Unit: ${this.unitNumber}, Lesson: ${this.lessonNumber}

Requirements:
- Create educational content appropriate for ${this.gradeLevel}
- Make it visually engaging and interactive
- Use mathematical concepts and visual representations
- Include animations or interactive elements where appropriate`

  if (script) {
    prompt += `\n\nScript context: ${script.substring(0, 200)}...`
  }

  if (teacher_tips) {
    prompt += `\n\nTeacher tips: ${teacher_tips.substring(0, 200)}...`
  }

  prompt += `\n\nGenerate complete HTML with embedded CSS and JavaScript that creates an educational visualization.`

  return prompt
}

// Instance method to update status
jobSchema.methods.updateStatus = function(status, additionalData = {}) {
  this.status = status

  if (status === 'processing') {
    this.processingStartedAt = new Date()
  } else if (status === 'success' || status === 'failed') {
    this.processedAt = new Date()
    this.processingStartedAt = null // Clear processing start time
  }

  Object.assign(this, additionalData)
  return this.save()
}

const Job = mongoose.models.Job || mongoose.model('Job', jobSchema, 'jobs')

export default Job
