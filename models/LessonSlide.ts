import mongoose from 'mongoose';
import { SlideType } from '@/types/slideTypes';

// Schema for teacher tips
const TeacherTipsSchema = new mongoose.Schema({
  general_tip: {
    type: String,
    default: null
  },
  misconception_tip: {
    type: String,
    default: null
  }
}, { _id: false });

// Schema for lesson slides
const LessonSlideSchema = new mongoose.Schema({
  // Metadata fields for searching
  unit_number: {
    type: String,
    required: [true, 'Please provide a unit number'],
    index: true
  },
  unit_title: {
    type: String,
    required: [true, 'Please provide a unit title'],
    index: true
  },
  lesson_number: {
    type: String,
    required: [true, 'Please provide a lesson number'],
    index: true
  },
  grade_level: {
    type: String,
    required: [true, 'Please provide a grade level'],
    index: true
  },
  lang: {
    type: String,
    default: 'en',
    index: true
  },
  
  // Slide specific fields
  slide_number: {
    type: Number,
    required: [true, 'Please provide a slide number'],
    index: true
  },
  slide_type: {
    type: String,
    enum: Object.values(SlideType),
    required: [true, 'Please provide a slide type']
  },
  slide_pedagogical_name: {
    type: String,
    required: [true, 'Please provide a slide pedagogical name']
  },
  
  // Content fields
  slide_text_1: {
    type: String,
    default: null
  },
  slide_text_2: {
    type: String,
    default: null
  },
  slide_text_3: {
    type: String,
    default: null
  },
  slide_text_4: {
    type: String,
    default: null
  },
  html_css_description_of_image: {
    type: String,
    default: null
  },
  script: {
    type: String,
    default: null
  },
  teacher_tips: {
    type: TeacherTipsSchema,
    default: () => ({})
  },
  
  // Quick Review fields
  q1: {
    type: String,
    default: null
  },
  a1: {
    type: String,
    default: null
  },
  q2: {
    type: String,
    default: null
  },
  a2: {
    type: String,
    default: null
  },
  q3: {
    type: String,
    default: null
  },
  a3: {
    type: String,
    default: null
  },
  
  // Learning Goals fields
  lg_1: {
    type: String,
    default: null
  },
  lg_1_content: {
    type: String,
    default: null
  },
  lg_2: {
    type: String,
    default: null
  },
  lg_2_content: {
    type: String,
    default: null
  },
  lg_3: {
    type: String,
    default: null
  },
  lg_3_content: {
    type: String,
    default: null
  },
  standards_alignment: {
    type: String,
    default: null
  },
  
  // Vocabulary fields
  term_1: {
    type: String,
    default: null
  },
  definition_1_with_emoji: {
    type: String,
    default: null
  },
  term_2: {
    type: String,
    default: null
  },
  definition_2_with_emoji: {
    type: String,
    default: null
  },
  term_3: {
    type: String,
    default: null
  },
  definition_3_with_emoji: {
    type: String,
    default: null
  },
  term_4: {
    type: String,
    default: null
  },
  definition_4_with_emoji: {
    type: String,
    default: null
  },
  manipulative_used: {
    type: String,
    default: null
  },
  
  // Printables fields
  link_to_lesson_guide_pdf: {
    type: String,
    default: null
  },
  link_to_practice_pdf: {
    type: String,
    default: null
  },
  link_to_accelerator_pdf: {
    type: String,
    default: null
  },
  
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Create compound index for efficient searching
LessonSlideSchema.index({ 
  unit_number: 1, 
  unit_title: 1, 
  lesson_number: 1, 
  grade_level: 1,
  lang: 1
});

// Create a model from the schema
export default mongoose.models.LessonSlide || mongoose.model('LessonSlide', LessonSlideSchema);
