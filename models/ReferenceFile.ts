import mongoose from 'mongoose'

const ReferenceFileSchema = new mongoose.Schema({
  filename: {
    type: String,
    required: true
  },
  content: {
    type: String,
    required: true
  },
  description: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    enum: ['good', 'bad'],
    required: true
  },
  uploadedAt: {
    type: Date,
    default: Date.now
  },
  uploadedBy: {
    type: String,
    required: true
  }
}, {
  timestamps: true
})

// Create indexes for better performance
ReferenceFileSchema.index({ type: 1 })
ReferenceFileSchema.index({ uploadedAt: -1 })

const ReferenceFile = mongoose.models.ReferenceFile || mongoose.model('ReferenceFile', ReferenceFileSchema)

export default ReferenceFile
