import mongoose from 'mongoose'

const SettingsSchema = new mongoose.Schema({
  key: {
    type: String,
    required: true,
    unique: true,
    enum: ['STYLE_GUIDE', 'INSTRUCTIONS']
  },
  value: {
    type: String,
    required: true
  },
  description: {
    type: String,
    required: true
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  updatedBy: {
    type: String,
    default: 'system'
  }
}, {
  timestamps: true
})

// Update the updatedAt field before saving
SettingsSchema.pre('save', function(next) {
  this.updatedAt = new Date()
  next()
})

export default mongoose.models.Settings || mongoose.model('Settings', SettingsSchema)
