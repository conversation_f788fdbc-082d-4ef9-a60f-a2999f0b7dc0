import mongoose from 'mongoose';

// Schema for JSON documents
const JsonDocumentSchema = new mongoose.Schema({
  // Document ID (not unique by itself, but unique with lang)
  documentId: {
    type: String,
    required: [true, 'Please provide a document ID'],
    index: true
  },

  // Metadata fields for searching
  unit_number: {
    type: String,
    required: [true, 'Please provide a unit number'],
    index: true
  },
  lesson_number: {
    type: String,
    required: [true, 'Please provide a lesson number'],
    index: true
  },
  grade_level: {
    type: String,
    required: [true, 'Please provide a grade level'],
    index: true
  },

  // Optional metadata
  unit_title: {
    type: String,
    default: null
  },
  lesson_title: {
    type: String,
    default: null
  },
  lang: {
    type: String,
    default: 'en'
  },

  // The actual JSON content
  content: {
    type: mongoose.Schema.Types.Mixed,
    required: [true, 'Please provide JSON content']
  },

  // Original filename
  filename: {
    type: String,
    default: null
  },

  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Create compound unique index for documentId + lang (allows multiple languages per lesson)
JsonDocumentSchema.index({
  documentId: 1,
  lang: 1
}, { unique: true });

// Create compound index for efficient searching
JsonDocumentSchema.index({
  unit_number: 1,
  lesson_number: 1,
  grade_level: 1,
  lang: 1
});

// Create a model from the schema
export default mongoose.models.JsonDocument || mongoose.model('JsonDocument', JsonDocumentSchema);
