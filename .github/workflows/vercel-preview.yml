name: Vercel Deployment
env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}
on:
  push:
    branches:
      - main
      - demo
jobs:
  Deploy-Vercel:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Install Vercel CLI
        run: npm install --global vercel@latest
      - name: Pull Vercel Environment Information
        run: vercel pull --yes --token ${{ secrets.VERCEL_TOKEN }}
      - name: Deploy to Vercel (Production)
        if: github.ref == 'refs/heads/main'
        run: vercel --prod --yes --token ${{ secrets.VERCEL_TOKEN }}
      - name: Deploy to Vercel (Preview)
        if: github.ref != 'refs/heads/main'
        run: vercel --yes --token ${{ secrets.VERCEL_TOKEN }}