"use client";

import { SlideData, SlidePoint, SlideType } from "@/types/slideTypes";

// Cache for loaded slides to avoid repeated fetches
let slidesCache = new Map<string, Record<string, SlideData>>();

// Create a global memory cache for the browser session
if (typeof window !== 'undefined' && !(window as any).__slidesGlobalCache) {
  (window as any).__slidesGlobalCache = {
    documents: new Map<string, Record<string, SlideData>>(),
    slides: new Map<string, SlideData>(),
    lastAccessed: new Map<string, number>(),
    maxSize: 50, // Maximum number of documents to cache
  };
}

/**
 * Clears the slides cache to force a fresh load
 * @param documentId Optional document ID to clear only a specific document from cache
 */
export function clearSlidesCache(documentId?: string) {
  if (documentId) {
    console.log(`Clearing slides cache for document: ${documentId}`);
    slidesCache.delete(documentId);

    // Also clear any slide cache entries that match this document
    for (const key of slideCache.keys()) {
      if (key.includes(documentId)) {
        slideCache.delete(key);
      }
    }

    // Clear from global cache if available
    if (typeof window !== 'undefined' && (window as any).__slidesGlobalCache) {
      (window as any).__slidesGlobalCache.documents.delete(documentId);

      // Clear individual slides from this document
      const slidesToRemove = [];
      for (const [key, _] of (window as any).__slidesGlobalCache.slides.entries()) {
        if (key.includes(documentId)) {
          slidesToRemove.push(key);
        }
      }

      for (const key of slidesToRemove) {
        (window as any).__slidesGlobalCache.slides.delete(key);
        (window as any).__slidesGlobalCache.lastAccessed.delete(key);
      }
    }
  } else {
    console.log('Clearing all slides cache');
    slidesCache.clear();
    slideCache.clear();

    // Clear global cache if available
    if (typeof window !== 'undefined' && (window as any).__slidesGlobalCache) {
      (window as any).__slidesGlobalCache.documents.clear();
      (window as any).__slidesGlobalCache.slides.clear();
      (window as any).__slidesGlobalCache.lastAccessed.clear();
    }
  }
}

/**
 * Loads slide data from the database or falls back to JSON file
 * @param unitNumber Optional unit number to load specific unit data
 * @param lessonNumber Optional lesson number to load specific lesson data
 * @param gradeLevel Optional grade level to load specific grade data
 * @param lang Optional language code (en, esp) to load specific language data
 * @returns Promise with the slide data
 */
export async function loadSlideData(
  unitNumber: string = "2",
  lessonNumber: string = "1",
  gradeLevel: string = "Grade 1",
  lang?: string
): Promise<Record<string, SlideData>> {
  // Log the parameters to help with debugging
  console.log('loadSlideData called with parameters:', {
    unitNumber,
    lessonNumber,
    gradeLevel,
    lang
  });
  // Create a document ID based on parameters including language
  const baseDocumentId = `${unitNumber}-${lessonNumber}-${gradeLevel}`;
  const documentId = lang === 'esp' ? `${baseDocumentId}-esp` : baseDocumentId;
  const cacheKey = `${documentId}${lang ? `-${lang}` : ''}`;

  console.log(`Looking for document with ID: ${documentId}, cache key: ${cacheKey}`);

  // Check global cache first (persists across component mounts)
  if (typeof window !== 'undefined' && (window as any).__slidesGlobalCache) {
    const globalCache = (window as any).__slidesGlobalCache;

    if (globalCache.documents.has(cacheKey)) {
      const cachedData = globalCache.documents.get(cacheKey);
      if (cachedData) {
        console.log(`Using global cached data for ${cacheKey}`);

        // Update last accessed time
        globalCache.lastAccessed.set(cacheKey, Date.now());

        // Also update local cache
        slidesCache.set(cacheKey, cachedData);

        return cachedData;
      }
    }
  }

  // If we already have the data cached for this cache key in local cache, return it
  if (slidesCache.has(cacheKey)) {
    const cachedData = slidesCache.get(cacheKey);
    if (cachedData) {
      console.log(`Using local cached data for ${cacheKey}`);

      // Update global cache
      if (typeof window !== 'undefined' && (window as any).__slidesGlobalCache) {
        (window as any).__slidesGlobalCache.documents.set(cacheKey, cachedData);
        (window as any).__slidesGlobalCache.lastAccessed.set(cacheKey, Date.now());
      }

      return cachedData;
    }
  }

  console.log(`Cache miss for ${cacheKey}, fetching new data...`);

  try {
    // Try to fetch from JSON documents API first
    const params = new URLSearchParams();
    params.append('unit_number', unitNumber);
    params.append('lesson_number', lessonNumber);
    params.append('grade_level', gradeLevel);
    if (lang && lang !== 'en') params.append('lang', lang);

    console.log(`Fetching slides from JSON documents API with params: ${params.toString()}`);

    const response = await fetch(`/api/json-documents?${params.toString()}`);

    if (!response.ok) {
      console.log(`JSON documents API returned ${response.status}, trying with different parameters...`);

      // Try with just unit_number and lesson_number
      const simpleParams = new URLSearchParams();
      simpleParams.append('unit_number', unitNumber);
      simpleParams.append('lesson_number', lessonNumber);

      console.log(`Trying with simplified params: ${simpleParams.toString()}`);

      const simpleResponse = await fetch(`/api/json-documents?${simpleParams.toString()}`);

      if (!simpleResponse.ok) {
        console.log(`Still no success, trying with just unit_number...`);

        // Try with just unit_number
        const unitParams = new URLSearchParams();
        unitParams.append('unit_number', unitNumber);

        console.log(`Trying with unit param only: ${unitParams.toString()}`);

        const unitResponse = await fetch(`/api/json-documents?${unitParams.toString()}`);

        if (!unitResponse.ok) {
          console.log(`JSON documents API failed with all parameter combinations, trying slides API...`);

          // If JSON documents API fails with all parameter combinations, try the slides API
          const slidesResponse = await fetch(`/api/slides?${params.toString()}`);

          if (slidesResponse.ok) {
            const data = await slidesResponse.json();

            // Add document ID for future reference
            (data as any)._documentId = documentId;

            // Cache the data
            slidesCache.set(cacheKey, data);

            // Update global cache
            if (typeof window !== 'undefined' && (window as any).__slidesGlobalCache) {
              (window as any).__slidesGlobalCache.documents.set(cacheKey, data);
              (window as any).__slidesGlobalCache.lastAccessed.set(cacheKey, Date.now());
            }

            console.log(`Loaded ${Object.keys(data).length - 5} slides from slides API`); // -5 for metadata fields

            return data;
          }
        } else {
          // Unit-only search succeeded
          const data = await unitResponse.json();

          // Add document ID for future reference
          (data as any)._documentId = documentId;

          // Cache the data
          slidesCache.set(cacheKey, data);

          // Update global cache
          if (typeof window !== 'undefined' && (window as any).__slidesGlobalCache) {
            (window as any).__slidesGlobalCache.documents.set(cacheKey, data);
            (window as any).__slidesGlobalCache.lastAccessed.set(cacheKey, Date.now());
          }

          console.log(`Loaded ${Object.keys(data).length - 5} slides from API with unit_number only`);

          return data;
        }
      } else {
        // Simple search succeeded
        const data = await simpleResponse.json();

        // Add document ID for future reference
        (data as any)._documentId = documentId;

        // Cache the data
        slidesCache.set(cacheKey, data);

        console.log(`Loaded ${Object.keys(data).length - 5} slides from API with unit_number and lesson_number`);

        return data;
      }
    }

    if (response.ok) {
      const data = await response.json();

      // Add document ID for future reference
      (data as any)._documentId = documentId;

      // Cache the data
      slidesCache.set(cacheKey, data);

      console.log(`Loaded ${Object.keys(data).length - 5} slides from API with exact parameters`);

      return data;
    }

    // If API fails or returns no data, fall back to JSON files
    console.warn('Falling back to JSON files for slide data');

    // Determine which JSON file to load based on unit number
    const jsonFile = unitNumber === "5" ? '/unit5-slides.json' : '/current-slides.json';

    // Fetch the JSON file
    const jsonResponse = await fetch(jsonFile);

    if (!jsonResponse.ok) {
      throw new Error(`Failed to load slide data: ${jsonResponse.status} ${jsonResponse.statusText}`);
    }

    // Parse the JSON
    const data = await jsonResponse.json();

    // Add document ID for future reference
    (data as any)._documentId = documentId;

    // Cache the data for future use
    slidesCache.set(cacheKey, data);

    console.log(`Loaded ${Object.keys(data).length - 5} slides from JSON file`); // -5 for metadata fields

    return data;
  } catch (error) {
    console.error('Error loading slide data:', error);
    throw error;
  }
}

/**
 * Gets a specific slide from the slide data
 * @param slideNumber The slide number to retrieve
 * @param unitNumber Optional unit number to load specific unit data
 * @param lessonNumber Optional lesson number to load specific lesson data
 * @param gradeLevel Optional grade level to load specific grade data
 * @param lang Optional language code (en, esp) to load specific language data
 * @returns The slide data or undefined if not found
 */
// Cache for individual slides to avoid repeated fetches
const slideCache = new Map<string, SlideData>();

/**
 * Clears the cache for a specific slide
 * @param slideNumber The slide number to clear from cache
 */
export function clearSlideCache(slideNumber: number) {
  console.log(`Clearing cache for slide ${slideNumber}`);

  // Remove all entries for this slide number
  for (const key of slideCache.keys()) {
    if (key.includes(`slide-${slideNumber}-`)) {
      console.log(`Removing cache entry: ${key}`);
      slideCache.delete(key);
    }
  }
}

export async function getSlide(
  slideNumber: number,
  unitNumber: string = "2",
  lessonNumber: string = "1",
  gradeLevel: string = "Grade 1",
  lang?: string,
  bypassCache: boolean = false
): Promise<SlideData | undefined> {
  // Log the parameters to help with debugging
  console.log('getSlide called with parameters:', {
    slideNumber,
    unitNumber,
    lessonNumber,
    gradeLevel,
    lang,
    bypassCache
  });
  // Create a document ID and cache key for this specific slide
  const baseDocumentId = `${unitNumber}-${lessonNumber}-${gradeLevel}`;
  const documentId = lang === 'esp' ? `${baseDocumentId}-esp` : baseDocumentId;
  const slideKey = `slide-${slideNumber}-${documentId}${lang ? `-${lang}` : ''}`;

  console.log(`getSlide: Looking for slide ${slideNumber} in document ${documentId}`);
  console.log(`getSlide: Cache key is ${slideKey}`);

  // Check global cache first (persists across component mounts)
  if (!bypassCache && typeof window !== 'undefined' && (window as any).__slidesGlobalCache) {
    const globalCache = (window as any).__slidesGlobalCache;

    if (globalCache.slides.has(slideKey)) {
      const cachedSlide = globalCache.slides.get(slideKey);
      if (cachedSlide) {
        console.log(`Using global cached slide data for ${slideKey}`);
        console.log(`Cached slide has script:`, cachedSlide.script ? 'Yes' : 'No');
        if (cachedSlide.script) {
          console.log(`Script preview:`, cachedSlide.script.substring(0, 50) + '...');
        }

        // Update last accessed time
        globalCache.lastAccessed.set(slideKey, Date.now());

        // Also update local cache
        slideCache.set(slideKey, cachedSlide);

        return cachedSlide;
      }
    }
  }

  // Check if we have this slide in the local cache
  if (!bypassCache && slideCache.has(slideKey)) {
    console.log(`Using local cached slide data for ${slideKey}`);
    const cachedSlide = slideCache.get(slideKey);

    console.log(`Local cached slide has script:`, cachedSlide?.script ? 'Yes' : 'No');
    if (cachedSlide?.script) {
      console.log(`Script preview:`, cachedSlide.script.substring(0, 50) + '...');
    }

    // Update global cache
    if (typeof window !== 'undefined' && (window as any).__slidesGlobalCache) {
      (window as any).__slidesGlobalCache.slides.set(slideKey, cachedSlide!);
      (window as any).__slidesGlobalCache.lastAccessed.set(slideKey, Date.now());
    }

    return cachedSlide;
  }

  // Clear the cache if bypassCache is true
  if (bypassCache) {
    console.log('Bypassing cache for slide data');
    // Only clear the specific document from cache using the correct cache key
    const cacheKey = `${documentId}${lang ? `-${lang}` : ''}`;
    if (slidesCache.has(cacheKey)) {
      slidesCache.delete(cacheKey);
    }
    // Also clear this slide from the slide cache
    slideCache.delete(slideKey);

    // Clear from global cache if available
    if (typeof window !== 'undefined' && (window as any).__slidesGlobalCache) {
      (window as any).__slidesGlobalCache.documents.delete(cacheKey);
      (window as any).__slidesGlobalCache.slides.delete(slideKey);
      (window as any).__slidesGlobalCache.lastAccessed.delete(slideKey);
    }
  }

  try {
    // Try to load slide data with the exact parameters
    let slideData = await loadSlideData(unitNumber, lessonNumber, gradeLevel, lang);

    // If we don't have any slide data, try with different unit numbers
    if (!slideData || Object.keys(slideData).length <= 5) { // 5 is the number of metadata fields
      console.log(`No slide data found for unit ${unitNumber}, lesson ${lessonNumber}, grade ${gradeLevel}. Trying unit 3...`);

      // Try with unit 3
      slideData = await loadSlideData("3", lessonNumber, gradeLevel, lang);

      // If still no data, try with unit 2
      if (!slideData || Object.keys(slideData).length <= 5) {
        console.log(`No slide data found for unit 3, lesson ${lessonNumber}, grade ${gradeLevel}. Trying unit 2...`);
        slideData = await loadSlideData("2", lessonNumber, gradeLevel, lang);
      }
    }

    // Try both formats of slide keys
    const slideTypeKey = `Slide ${slideNumber}: ${getSlideType(slideNumber)}`;
    const simpleKey = `Slide ${slideNumber}`;

    console.log(`Looking for slide with keys: "${slideTypeKey}" or "${simpleKey}"`);
    console.log(`Available keys in slideData:`, Object.keys(slideData));

    // Get the slide data using the appropriate key
    const slide = slideData[slideTypeKey] || slideData[simpleKey];

    console.log(`Found slide:`, slide ? 'Yes' : 'No');
    if (slide) {
      console.log(`Slide has script:`, slide.script ? 'Yes' : 'No');
      if (slide.script) {
        console.log(`Script preview:`, slide.script.substring(0, 50) + '...');
      }
      console.log(`Slide type:`, slide.type);
      console.log(`Slide pedagogical name:`, slide.slide_pedagogical_name);
    }

    // Cache the slide data if found
    if (slide) {
      // Ensure the slide has the correct type based on slide number (language-independent)
      if (!slide.type) {
        slide.type = getSlideTypeEnum(slideNumber);
        console.log(`Set slide type to ${slide.type} based on slide number ${slideNumber}`);
      }

      // Update local cache
      slideCache.set(slideKey, slide);
      console.log(`Cached slide in local cache with key: ${slideKey}`);

      // Update global cache if available
      if (typeof window !== 'undefined' && (window as any).__slidesGlobalCache) {
        (window as any).__slidesGlobalCache.slides.set(slideKey, slide);
        (window as any).__slidesGlobalCache.lastAccessed.set(slideKey, Date.now());

        // Manage cache size - remove oldest entries if we exceed maxSize
        const globalCache = (window as any).__slidesGlobalCache;
        if (globalCache.slides.size > globalCache.maxSize) {
          // Find the oldest accessed slide
          let oldestKey = '';
          let oldestTime = Date.now();

          for (const [key, time] of globalCache.lastAccessed.entries()) {
            if (time < oldestTime) {
              oldestTime = time;
              oldestKey = key;
            }
          }

          // Remove the oldest slide
          if (oldestKey) {
            console.log(`Removing oldest slide from cache: ${oldestKey}`);
            globalCache.slides.delete(oldestKey);
            globalCache.lastAccessed.delete(oldestKey);
          }
        }
      }

      return slide;
    }

    // If we still don't have a slide, try to find any slide with the same number
    for (const key of Object.keys(slideData)) {
      if (key.startsWith(`Slide ${slideNumber}:`)) {
        console.log(`Found slide with key ${key}`);
        const foundSlide = slideData[key];

        // Ensure the slide has the correct type based on slide number (language-independent)
        if (!foundSlide.type) {
          foundSlide.type = getSlideTypeEnum(slideNumber);
          console.log(`Set slide type to ${foundSlide.type} based on slide number ${slideNumber}`);
        }

        // Update local cache
        slideCache.set(slideKey, foundSlide);

        // Update global cache if available
        if (typeof window !== 'undefined' && (window as any).__slidesGlobalCache) {
          (window as any).__slidesGlobalCache.slides.set(slideKey, foundSlide);
          (window as any).__slidesGlobalCache.lastAccessed.set(slideKey, Date.now());
        }

        return foundSlide;
      }
    }

    console.log(`No slide found for slide number ${slideNumber}`);
    return undefined;
  } catch (error) {
    console.error(`Error getting slide ${slideNumber}:`, error);
    return undefined;
  }
}

/**
 * Gets the total number of slides available for the current lesson
 * @param unitNumber Optional unit number to load specific unit data
 * @param lessonNumber Optional lesson number to load specific lesson data
 * @param gradeLevel Optional grade level to load specific grade data
 * @param lang Optional language code (en, esp) to load specific language data
 * @returns Promise with the total number of slides
 */
export async function getTotalSlides(
  unitNumber: string = "2",
  lessonNumber: string = "1",
  gradeLevel: string = "Grade 1",
  lang?: string
): Promise<number> {
  const slideData = await loadSlideData(unitNumber, lessonNumber, gradeLevel, lang);

  // Count the number of slide keys (excluding metadata fields)
  const slideKeys = Object.keys(slideData).filter(key => key.startsWith('Slide '));

  return slideKeys.length;
}

/**
 * Gets the slide type based on the slide number
 * @param slideNumber The slide number
 * @returns The slide type
 */
export function getSlideType(slideNumber: number): string {
  // Map slide numbers to their types
  switch (slideNumber) {
    case 1:
      return "Quick_Review";
    case 2:
      return "Learning_Goals";
    case 3:
      return "Vocabulary";
    case 4:
      return "hook_1";
    case 5:
      return "hook_2";
    case 6:
      return "hook_3";
    case 7:
      return "teach_1_1";
    case 8:
      return "teach_1_2";
    case 9:
      return "teach_1_3";
    case 10:
      return "talk_1_1";
    case 11:
      return "talk_1_2";
    case 12:
      return "talk_1_3";
    case 13:
      return "try_1";
    case 14:
      return "teach_2_1";
    case 15:
      return "teach_2_2";
    case 16:
      return "teach_2_3";
    case 17:
      return "talk_2_1";
    case 18:
      return "talk_2_2";
    case 19:
      return "talk_2_3";
    case 20:
      return "try_2";
    case 21:
      return "practice";
    case 22:
      return "on_ramp_teach_1";
    case 23:
      return "on_ramp_talk_1";
    case 24:
      return "on_ramp_try_1";
    case 25:
      return "Lesson_Guide_PDF_Link";
    case 26:
      return "Practice_PDF_Link";
    case 27:
      return "Accelerator_PDF_Link";
    default:
      return "unknown";
  }
}

/**
 * Gets the SlideType enum value based on the slide number (index-based, language-independent)
 * @param slideNumber The slide number
 * @returns The SlideType enum value
 */
export function getSlideTypeEnum(slideNumber: number): SlideType {
  // Map slide numbers to SlideType enum values based on position, not content
  switch (slideNumber) {
    case 1:
    case 2:
    case 3:
      return SlideType.INTRODUCTION;
    case 4:
    case 5:
    case 6:
      return SlideType.HOOK;
    case 7:
    case 8:
    case 9:
      return SlideType.TEACH_1;
    case 10:
    case 11:
    case 12:
      return SlideType.TALK_1;
    case 13:
      return SlideType.TRY_1;
    case 14:
    case 15:
    case 16:
      return SlideType.TEACH_2;
    case 17:
    case 18:
    case 19:
      return SlideType.TALK_2;
    case 20:
      return SlideType.TRY_2;
    case 21:
      return SlideType.PRACTICE;
    case 22:
    case 23:
    case 24:
      return SlideType.ON_RAMP;
    case 25:
    case 26:
    case 27:
      return SlideType.PRINTABLES;
    default:
      return SlideType.HOOK; // Default fallback
  }
}

/**
 * Gets the slide subtype based on the slide number (for Introduction slides)
 * @param slideNumber The slide number
 * @returns The slide subtype
 */
export function getIntroductionSlideSubtype(slideNumber: number): string {
  switch (slideNumber) {
    case 1:
      return "quick-review";
    case 2:
      return "learning-goals";
    case 3:
      return "vocabulary";
    default:
      return "quick-review";
  }
}

/**
 * Gets the slide title based on the slide data
 * @param slideData The slide data
 * @returns The slide title
 */
export function getSlideTitle(slideData: SlideData): string {
  // Always use the data from the JSON file
  // Make sure the title matches the type
  if (slideData?.slide_pedagogical_name && slideData?.type) {
    // For talk slides, make sure the title starts with "Talk"
    if (slideData.type === 'talk_1' || slideData.type === 'talk_2') {
      if (!slideData.slide_pedagogical_name.startsWith('Talk')) {
        return `Talk: ${slideData.slide_pedagogical_name}`;
      }
    }

    // For try slides, make sure the title starts with "Try"
    if (slideData.type === 'try_1' || slideData.type === 'try_2') {
      if (!slideData.slide_pedagogical_name.startsWith('Try')) {
        return `Try: ${slideData.slide_pedagogical_name}`;
      }
    }
  }

  return slideData?.slide_pedagogical_name || "Slide";
}

/**
 * Gets the slide points based on the slide data
 * @param slideData The slide data
 * @returns Array of points with title and content
 */
export function getSlidePoints(slideData: SlideData & { slideNumber?: number }): SlidePoint[] {
  // Check if this is a talk slide (use slideNumber for on_ramp slides to avoid language dependency)
  const isOnRampTalk = slideData?.type === 'on_ramp' && slideData?.slideNumber === 23;
  if (slideData?.type === 'talk_1' || slideData?.type === 'talk_2' || isOnRampTalk) {
    const points = [];

    // For talk slides, use slide_q and slide_a as a pair
    if (slideData?.slide_q) {
      points.push({
        title: 'Question',
        content: slideData.slide_q,
        isQuestion: true,
        hasAnswer: !!slideData.slide_a
      });
    }

    if (slideData?.slide_a) {
      points.push({
        title: 'Answer',
        content: slideData.slide_a,
        isAnswer: true,
        questionIndex: 0 // This answer belongs to the first question
      });
    }

    // If we have script content (especially for on_ramp_talk slides), add it as a point
    if (slideData?.script && points.length === 0) {
      points.push({
        title: 'Instructions',
        content: slideData.script
      });
    }

    return points;
  }

  // Check if this is a practice slide
  if (slideData?.type === 'practice') {
    const points = [];

    // For practice slides, use all available q/a pairs (up to q12/a12)
    for (let i = 1; i <= 12; i++) {
      const qKey = `q${i}` as keyof SlideData;
      const aKey = `a${i}` as keyof SlideData;

      if (slideData[qKey]) {
        points.push({
          title: `Question ${i}`,
          content: slideData[qKey] as string,
          isQuestion: true,
          hasAnswer: !!slideData[aKey]
        });

        if (slideData[aKey]) {
          points.push({
            title: `Answer ${i}`,
            content: slideData[aKey] as string,
            isAnswer: true,
            questionIndex: points.length - 1 // This answer belongs to the previous question
          });
        }
      }
    }

    // If we have script content and no other points, add it as a point
    if (slideData?.script && points.length === 0) {
      points.push({
        title: 'Instructions',
        content: slideData.script
      });
    } else if (slideData?.script) {
      // If we have script content and other points, add it as the first point
      points.unshift({
        title: 'Instructions',
        content: slideData.script
      });
    }

    return points;
  }

  // Check if this is a try slide (use slideNumber for on_ramp slides to avoid language dependency)
  const isOnRampTry = slideData?.type === 'on_ramp' && slideData?.slideNumber === 24;
  if (slideData?.type === 'try_1' || slideData?.type === 'try_2' || isOnRampTry) {
    const points = [];

    // For try slides, use q1, a1, q2, a2, q3, a3 as pairs
    if (slideData?.q1) {
      points.push({
        title: 'Question 1',
        content: slideData.q1,
        isQuestion: true,
        hasAnswer: !!slideData.a1
      });
    }

    if (slideData?.a1) {
      points.push({
        title: 'Answer 1',
        content: slideData.a1,
        isAnswer: true,
        questionIndex: 0 // This answer belongs to the first question
      });
    }

    if (slideData?.q2) {
      points.push({
        title: 'Question 2',
        content: slideData.q2,
        isQuestion: true,
        hasAnswer: !!slideData.a2
      });
    }

    if (slideData?.a2) {
      points.push({
        title: 'Answer 2',
        content: slideData.a2,
        isAnswer: true,
        questionIndex: 2 // This answer belongs to the second question (index 2 in the array)
      });
    }

    if (slideData?.q3) {
      points.push({
        title: 'Question 3',
        content: slideData.q3,
        isQuestion: true,
        hasAnswer: !!slideData.a3
      });
    }

    if (slideData?.a3) {
      points.push({
        title: 'Answer 3',
        content: slideData.a3,
        isAnswer: true,
        questionIndex: 4 // This answer belongs to the third question (index 4 in the array)
      });
    }

    // If we have script content and no other points, add it as a point
    if (slideData?.script && points.length === 0) {
      points.push({
        title: 'Instructions',
        content: slideData.script
      });
    }

    return points;
  }

  // For other slide types, use slide_text_1, slide_text_2, etc.
  const points = [];

  if (slideData?.slide_text_1) {
    points.push({
      title: slideData.slide_text_1.split('.')[0] || slideData.slide_text_1,
      content: slideData.slide_text_1
    });
  }

  if (slideData?.slide_text_2) {
    points.push({
      title: slideData.slide_text_2.split('.')[0] || slideData.slide_text_2,
      content: slideData.slide_text_2
    });
  }

  if (slideData?.slide_text_3) {
    points.push({
      title: slideData.slide_text_3.split('.')[0] || slideData.slide_text_3,
      content: slideData.slide_text_3
    });
  }

  if (slideData?.slide_text_4) {
    points.push({
      title: slideData.slide_text_4.split('.')[0] || slideData.slide_text_4,
      content: slideData.slide_text_4
    });
  }

  return points;
}

/**
 * Gets the slide activities based on the slide data
 * @param slideData The slide data
 * @returns Array of activities with question and answer
 */
export function getSlideActivities(slideData: any): { question: string; answer: string }[] {
  const activities = [];

  // For practice slides, include all available q/a pairs (up to q12/a12)
  if (slideData?.type === 'practice') {
    for (let i = 1; i <= 12; i++) {
      const qKey = `q${i}`;
      const aKey = `a${i}`;

      if (slideData[qKey]) {
        activities.push({
          question: slideData[qKey],
          answer: slideData[aKey] || 'Answer not available'
        });
      }
    }

    // If we have script content, add it as the first activity
    if (slideData?.script && activities.length > 0) {
      activities.unshift({
        question: 'Instructions',
        answer: slideData.script
      });
    }

    return activities;
  }

  // For other slide types, use the standard q1/a1, q2/a2, q3/a3 format
  if (slideData?.q1 && slideData?.a1) {
    activities.push({
      question: slideData.q1,
      answer: slideData.a1
    });
  }

  if (slideData?.q2 && slideData?.a2) {
    activities.push({
      question: slideData.q2,
      answer: slideData.a2
    });
  }

  if (slideData?.q3 && slideData?.a3) {
    activities.push({
      question: slideData.q3,
      answer: slideData.a3
    });
  }

  return activities;
}

/**
 * Gets the slide image URL based on the slide data
 * @param slideData The slide data
 * @returns The image URL or undefined if not found
 */
export function getSlideImageUrl(slideData: SlideData): string | undefined {
  // If html_css_description_of_image is a URL, return it
  if (slideData?.html_css_description_of_image?.startsWith('http')) {
    return slideData.html_css_description_of_image;
  }

  // Return undefined for all other cases
  return undefined;
}

/**
 * Gets the learning goals based on the slide data
 * @param slideData The slide data
 * @returns Array of learning goals with title and content
 */
export function getLearningGoals(slideData: any): { title: string; content: string }[] {
  const goals = [];

  if (slideData?.lg_1 && slideData?.lg_1_content) {
    goals.push({
      title: slideData.lg_1,
      content: slideData.lg_1_content
    });
  }

  if (slideData?.lg_2 && slideData?.lg_2_content) {
    goals.push({
      title: slideData.lg_2,
      content: slideData.lg_2_content
    });
  }

  if (slideData?.lg_3 && slideData?.lg_3_content) {
    goals.push({
      title: slideData.lg_3,
      content: slideData.lg_3_content
    });
  }

  return goals;
}

/**
 * Gets the vocabulary terms based on the slide data
 * @param slideData The slide data
 * @returns Array of vocabulary terms with term and definition
 */
export function getVocabularyTerms(slideData: any): { term: string; definition: string }[] {
  const terms = [];

  if (slideData?.term_1 && slideData?.definition_1_with_emoji) {
    terms.push({
      term: slideData.term_1,
      definition: slideData.definition_1_with_emoji
    });
  }

  if (slideData?.term_2 && slideData?.definition_2_with_emoji) {
    terms.push({
      term: slideData.term_2,
      definition: slideData.definition_2_with_emoji
    });
  }

  if (slideData?.term_3 && slideData?.definition_3_with_emoji) {
    terms.push({
      term: slideData.term_3,
      definition: slideData.definition_3_with_emoji
    });
  }

  if (slideData?.term_4 && slideData?.definition_4_with_emoji) {
    terms.push({
      term: slideData.term_4,
      definition: slideData.definition_4_with_emoji
    });
  }

  return terms;
}

/**
 * Gets the quick review questions based on the slide data
 * @param slideData The slide data
 * @returns Array of quick review questions with question and answer
 */
export function getQuickReviewQuestions(slideData: any): { question: string; answer: string }[] {
  const questions = [];

  if (slideData?.q1 && slideData?.a1) {
    questions.push({
      question: slideData.q1,
      answer: slideData.a1
    });
  }

  if (slideData?.q2 && slideData?.a2) {
    questions.push({
      question: slideData.q2,
      answer: slideData.a2
    });
  }

  if (slideData?.q3 && slideData?.a3) {
    questions.push({
      question: slideData.q3,
      answer: slideData.a3
    });
  }

  return questions;
}

/**
 * Gets the talk slide question and answer based on the slide data
 * @param slideData The slide data
 * @returns The question and answer or undefined if not found
 */
export function getTalkSlideContent(slideData: any): { question: string; answer: string } | undefined {
  if (slideData?.slide_q && slideData?.slide_a) {
    return {
      question: slideData.slide_q,
      answer: slideData.slide_a
    };
  }

  return undefined;
}

/**
 * Gets the printable link based on the slide data
 * @param slideData The slide data
 * @returns The link or undefined if not found
 */
export function getPrintableLink(slideData: any): string | undefined {
  if (slideData?.link_to_lesson_guide_pdf) {
    return slideData.link_to_lesson_guide_pdf;
  }

  if (slideData?.link_to_practice_pdf) {
    return slideData.link_to_practice_pdf;
  }

  if (slideData?.link_to_accelerator_pdf) {
    return slideData.link_to_accelerator_pdf;
  }

  return undefined;
}
