import connectToDatabase from '@/lib/mongodb';
import JsonDocument from '@/models/JsonDocument';

/**
 * Fetches JSON documents from the database based on search criteria
 * @param params Search parameters
 * @returns Promise with the document data
 */
export async function fetchJsonDocuments({
  unit_number,
  lesson_number,
  grade_level,
  lang
}: {
  unit_number?: string;
  lesson_number?: string;
  grade_level?: string;
  lang?: string;
}) {
  try {
    await connectToDatabase();

    // Build query object based on provided parameters
    const query: any = {};

    if (unit_number) query.unit_number = unit_number.toString();
    if (lesson_number) query.lesson_number = lesson_number.toString();
    if (grade_level) {
      // Handle URL-encoded spaces in grade_level
      const decodedGradeLevel = grade_level.replace(/\+/g, ' ');
      query.grade_level = decodedGradeLevel;
    }
    if (lang) query.lang = lang;

    console.log('Database query:', JSON.stringify(query));

    // Execute query
    const documents = await JsonDocument.find(query);

    console.log(`Database returned ${documents.length} documents`);

    return documents;
  } catch (error) {
    console.error('Error fetching documents from database:', error);
    throw error;
  }
}

/**
 * Fetches a specific JSON document from the database
 * @param params Search parameters
 * @returns Promise with the document data
 */
export async function fetchJsonDocument({
  unit_number,
  lesson_number,
  grade_level,
  lang
}: {
  unit_number: string;
  lesson_number: string;
  grade_level: string;
  lang?: string;
}) {
  try {
    await connectToDatabase();

    // Create document ID based on language
    const baseDocumentId = `${unit_number}-${lesson_number}-${grade_level}`;
    const documentId = lang === 'esp' ? `${baseDocumentId}-esp` : baseDocumentId;

    console.log(`🔍 Searching for document with documentId: ${documentId}, lang: ${lang}`);

    const query: any = { documentId };
    if (lang) query.lang = lang;

    const document = await JsonDocument.findOne(query);

    if (document) {
      console.log(`✅ Found document: ${document.documentId}`);
    } else {
      console.log(`❌ No document found with documentId: ${documentId}, lang: ${lang}`);
    }

    return document;
  } catch (error) {
    console.error('Error fetching document from database:', error);
    throw error;
  }
}

/**
 * Formats JSON document data to match the expected format for slides
 * @param document JSON document
 * @returns Formatted slides data
 */
export function formatJsonDocumentData(document: any) {
  if (!document) {
    return {};
  }

  // Convert MongoDB document to plain object
  const plainDocument = document.toObject ? document.toObject() : document;

  // Include metadata fields along with content
  const result = plainDocument.content || {};

  // Add metadata fields to the result
  if (plainDocument.lesson_title) {
    result.lesson_title = plainDocument.lesson_title;
  }
  if (plainDocument.unit_title) {
    result.unit_title = plainDocument.unit_title;
  }
  if (plainDocument.unit_number) {
    result.unit_number = plainDocument.unit_number;
  }
  if (plainDocument.lesson_number) {
    result.lesson_number = plainDocument.lesson_number;
  }
  if (plainDocument.grade_level) {
    result.grade_level = plainDocument.grade_level;
  }

  return result;
}
