import connectToDatabase from '@/lib/mongodb';
import LessonSlide from '@/models/LessonSlide';
import { SlideType } from '@/types/slideTypes';

/**
 * Fetches slides from the database based on search criteria
 * @param params Search parameters
 * @returns Promise with the slide data
 */
export async function fetchSlides({
  unit_number,
  unit_title,
  lesson_number,
  grade_level,
  slide_number,
  lang
}: {
  unit_number?: string;
  unit_title?: string;
  lesson_number?: string;
  grade_level?: string;
  slide_number?: number;
  lang?: string;
}) {
  try {
    await connectToDatabase();

    // Build query object based on provided parameters
    const query: any = {};

    if (unit_number) query.unit_number = unit_number.toString();
    if (unit_title) query.unit_title = unit_title;
    if (lesson_number) query.lesson_number = lesson_number.toString();
    if (grade_level) {
      // Handle URL-encoded spaces in grade_level
      const decodedGradeLevel = grade_level.replace(/\+/g, ' ');
      query.grade_level = decodedGradeLevel;
    }
    if (slide_number) query.slide_number = slide_number;
    if (lang) query.lang = lang;

    console.log('Database query:', JSON.stringify(query));

    // Execute query
    const slides = await LessonSlide.find(query).sort({ slide_number: 1 });

    console.log(`Database returned ${slides.length} slides`);

    return slides;
  } catch (error) {
    console.error('Error fetching slides from database:', error);
    throw error;
  }
}

/**
 * Fetches a specific slide from the database
 * @param params Search parameters
 * @returns Promise with the slide data
 */
export async function fetchSlide({
  unit_number,
  lesson_number,
  slide_number,
  lang
}: {
  unit_number: string;
  lesson_number: string;
  slide_number: number;
  lang?: string;
}) {
  try {
    await connectToDatabase();

    const query: any = {
      unit_number,
      lesson_number,
      slide_number
    };

    if (lang) query.lang = lang;

    const slide = await LessonSlide.findOne(query);

    return slide;
  } catch (error) {
    console.error('Error fetching slide from database:', error);
    throw error;
  }
}

/**
 * Adds a new slide to the database
 * @param slideData The slide data to add
 * @returns Promise with the created slide
 */
export async function addSlide(slideData: any) {
  try {
    await connectToDatabase();

    const slide = await LessonSlide.create(slideData);

    return slide;
  } catch (error) {
    console.error('Error adding slide to database:', error);
    throw error;
  }
}

/**
 * Updates an existing slide in the database
 * @param id The slide ID
 * @param slideData The updated slide data
 * @returns Promise with the updated slide
 */
export async function updateSlide(id: string, slideData: any) {
  try {
    await connectToDatabase();

    const slide = await LessonSlide.findByIdAndUpdate(
      id,
      { ...slideData, updatedAt: Date.now() },
      { new: true, runValidators: true }
    );

    return slide;
  } catch (error) {
    console.error('Error updating slide in database:', error);
    throw error;
  }
}

/**
 * Deletes a slide from the database
 * @param id The slide ID
 * @returns Promise with the deletion result
 */
export async function deleteSlide(id: string) {
  try {
    await connectToDatabase();

    const result = await LessonSlide.findByIdAndDelete(id);

    return result;
  } catch (error) {
    console.error('Error deleting slide from database:', error);
    throw error;
  }
}

/**
 * Formats slides data to match the expected JSON format
 * @param slides Array of slide documents
 * @returns Formatted slides data
 */
export function formatSlidesData(slides: any[]) {
  if (!slides || slides.length === 0) {
    return {};
  }

  // Convert MongoDB documents to plain objects
  const plainSlides = slides.map(slide => {
    // If it's a Mongoose document, convert to plain object
    if (slide.toObject) {
      return slide.toObject();
    } else if (slide._doc) {
      return slide._doc;
    }
    return slide;
  });

  const formattedData: Record<string, any> = {};

  // Add metadata
  formattedData.unit_number = plainSlides[0].unit_number;
  formattedData.unit_title = plainSlides[0].unit_title || "Unknown Unit";
  formattedData.lesson_number = plainSlides[0].lesson_number;
  formattedData.grade_level = plainSlides[0].grade_level || "Grade 1";

  // Add language if available
  if (plainSlides[0].lang) {
    formattedData.lang = plainSlides[0].lang;
  }

  // Check if we're dealing with MongoDB-style documents or JSON structure
  const isMongoDBFormat = Object.keys(plainSlides[0]).some(key =>
    key === 'slide_number' || key === 'slide_type' || key === 'slide_pedagogical_name'
  );

  if (isMongoDBFormat) {
    // Process MongoDB-style documents
    plainSlides.forEach(slide => {
      // Format the key based on slide number and type
      let slideType = "";

      // Map slide types to the expected format
      if (slide.slide_number === 1) {
        slideType = "Quick_Review";
      } else if (slide.slide_number === 2) {
        slideType = "Learning_Goals";
      } else if (slide.slide_number === 3) {
        slideType = "Vocabulary";
      } else if (slide.slide_type === SlideType.HOOK) {
        slideType = `hook_${slide.slide_number - 3}`;
      } else if (slide.slide_type === SlideType.TEACH_1) {
        slideType = `teach_1_${slide.slide_number - 6}`;
      } else if (slide.slide_type === SlideType.TALK_1) {
        slideType = `talk_1_${slide.slide_number - 9}`;
      } else if (slide.slide_type === SlideType.TRY_1) {
        slideType = `try_1`;
      } else if (slide.slide_type === SlideType.TEACH_2) {
        slideType = `teach_2_${slide.slide_number - 13}`;
      } else if (slide.slide_type === SlideType.TALK_2) {
        slideType = `talk_2_${slide.slide_number - 16}`;
      } else if (slide.slide_type === SlideType.TRY_2) {
        slideType = `try_2`;
      } else if (slide.slide_type === SlideType.PRACTICE) {
        slideType = `practice`;
      } else if (slide.slide_type === SlideType.ON_RAMP) {
        slideType = `on_ramp_${slide.slide_number - 21}`;
      } else if (slide.slide_type === SlideType.PRINTABLES) {
        slideType = `printables`;
      } else {
        slideType = slide.slide_type;
      }

      const slideKey = `Slide ${slide.slide_number}: ${slideType}`;

      // Create slide data object with the expected structure
      const slideData: Record<string, any> = {
        slide_pedagogical_name: slide.slide_pedagogical_name,
        type: slide.slide_type,
        script: slide.script || "",
        teacher_tips: {
          general_tip: slide.teacher_tips?.general_tip || "",
          misconception_tip: slide.teacher_tips?.misconception_tip || ""
        },
        html_css_description_of_image: slide.html_css_description_of_image || ""
      };

      // Add other fields based on slide type
      if (slide.q1) slideData.q1 = slide.q1;
      if (slide.a1) slideData.a1 = slide.a1;
      if (slide.q2) slideData.q2 = slide.q2;
      if (slide.a2) slideData.a2 = slide.a2;
      if (slide.q3) slideData.q3 = slide.q3;
      if (slide.a3) slideData.a3 = slide.a3;

      if (slide.lg_1) slideData.lg_1 = slide.lg_1;
      if (slide.lg_1_content) slideData.lg_1_content = slide.lg_1_content;
      if (slide.lg_2) slideData.lg_2 = slide.lg_2;
      if (slide.lg_2_content) slideData.lg_2_content = slide.lg_2_content;
      if (slide.lg_3) slideData.lg_3 = slide.lg_3;
      if (slide.lg_3_content) slideData.lg_3_content = slide.lg_3_content;
      if (slide.standards_alignment) slideData.standards_alignment = slide.standards_alignment;

      if (slide.term_1) slideData.term_1 = slide.term_1;
      if (slide.definition_1_with_emoji) slideData.definition_1_with_emoji = slide.definition_1_with_emoji;
      if (slide.term_2) slideData.term_2 = slide.term_2;
      if (slide.definition_2_with_emoji) slideData.definition_2_with_emoji = slide.definition_2_with_emoji;
      if (slide.term_3) slideData.term_3 = slide.term_3;
      if (slide.definition_3_with_emoji) slideData.definition_3_with_emoji = slide.definition_3_with_emoji;
      if (slide.term_4) slideData.term_4 = slide.term_4;
      if (slide.definition_4_with_emoji) slideData.definition_4_with_emoji = slide.definition_4_with_emoji;
      if (slide.manipulative_used) slideData.manipulative_used = slide.manipulative_used;

      if (slide.slide_text_1) slideData.slide_text_1 = slide.slide_text_1;
      if (slide.slide_text_2) slideData.slide_text_2 = slide.slide_text_2;
      if (slide.slide_text_3) slideData.slide_text_3 = slide.slide_text_3;
      if (slide.slide_text_4) slideData.slide_text_4 = slide.slide_text_4;

      if (slide.link_to_lesson_guide_pdf) slideData.link_to_lesson_guide_pdf = slide.link_to_lesson_guide_pdf;
      if (slide.link_to_practice_pdf) slideData.link_to_practice_pdf = slide.link_to_practice_pdf;
      if (slide.link_to_accelerator_pdf) slideData.link_to_accelerator_pdf = slide.link_to_accelerator_pdf;

      // Add the slide to the response
      formattedData[slideKey] = slideData;
    });
  } else {
    // Process JSON-style structure (keys like "Slide 1: Quick_Review")
    Object.entries(plainSlides[0]).forEach(([key, value]) => {
      // Skip metadata fields
      if (['_id', 'unit_number', 'unit_title', 'lesson_number', 'grade_level', 'lang'].includes(key)) {
        return;
      }

      // Add the slide data directly
      formattedData[key] = value;
    });
  }

  return formattedData;
}
