"use client"

import { useState, useEffect } from 'react'

/**
 * A safe alternative to useSearchParams that works on both client and server
 * without requiring Suspense boundaries
 *
 * @returns An object with methods to safely access URL search parameters
 */
export function useSafeSearchParams() {
  const [searchParams, setSearchParams] = useState<URLSearchParams | null>(null)
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    // Only run on client-side
    setIsClient(true)

    // Initialize search params from window.location
    if (typeof window !== 'undefined') {
      setSearchParams(new URLSearchParams(window.location.search))

      // Update search params when URL changes
      const handleRouteChange = () => {
        setSearchParams(new URLSearchParams(window.location.search))
      }

      // Listen for popstate events (browser back/forward)
      window.addEventListener('popstate', handleRouteChange)

      return () => {
        window.removeEventListener('popstate', handleRouteChange)
      }
    }
  }, [])

  /**
   * Get a search parameter value by name
   * @param name The parameter name to get
   * @returns The parameter value or null if not found
   */
  const get = (name: string): string | null => {
    if (!isClient || !searchParams) return null
    return searchParams.get(name)
  }

  /**
   * Get all values for a search parameter
   * @param name The parameter name to get
   * @returns Array of values or empty array if not found
   */
  const getAll = (name: string): string[] => {
    if (!isClient || !searchParams) return []
    return searchParams.getAll(name)
  }

  /**
   * Check if a search parameter exists
   * @param name The parameter name to check
   * @returns True if the parameter exists
   */
  const has = (name: string): boolean => {
    if (!isClient || !searchParams) return false
    return searchParams.has(name)
  }

  /**
   * Get a URLSearchParams object for the current URL
   * @returns URLSearchParams object or null if on server
   */
  const getURLSearchParams = (): URLSearchParams | null => {
    if (!isClient) return null
    return searchParams
  }

  /**
   * Get all search parameters as a Record
   * @returns Object with all search parameters
   */
  const getAllParams = (): Record<string, string> => {
    if (!isClient || !searchParams) return {}

    const result: Record<string, string> = {}
    for (const [key, value] of searchParams.entries()) {
      result[key] = value
    }
    return result
  }

  return {
    get,
    getAll,
    has,
    getURLSearchParams,
    getAllParams,
    isReady: isClient && searchParams !== null
  }
}
