import mongoose from 'mongoose';

const MONGODB_URI = process.env.MONGODB_URI;

if (!MONGODB_URI) {
  throw new Error('Please define the MONGODB_URI environment variable inside .env');
}

/**
 * Global is used here to maintain a cached connection across hot reloads
 * in development. This prevents connections growing exponentially
 * during API Route usage.
 */
let cached = global.mongoose;

if (!cached) {
  cached = global.mongoose = { conn: null, promise: null };
}

async function connectToDatabase() {
  if (cached.conn) {
    return cached.conn;
  }

  if (!cached.promise) {
    const opts = {
      bufferCommands: false,
      // Optimized for Vercel serverless functions
      serverSelectionTimeoutMS: 5000, // 5 seconds (faster timeout)
      socketTimeoutMS: 20000, // 20 seconds (shorter for serverless)
      connectTimeoutMS: 5000, // 5 seconds
      // Smaller connection pool for serverless
      maxPoolSize: 5, // Reduced from 10
      minPoolSize: 1, // Reduced from 5
      maxIdleTimeMS: 10000, // Shorter idle time
      // Heartbeat settings
      heartbeatFrequencyMS: 30000, // Less frequent heartbeats
      // <PERSON><PERSON> writes
      retryWrites: true,
      retryReads: true,
      // Additional serverless optimizations
      maxConnecting: 2, // Limit concurrent connections
      waitQueueTimeoutMS: 5000, // Don't wait too long for connections
    };

    cached.promise = mongoose.connect(MONGODB_URI!, opts).then((mongoose) => {
      console.log('✅ MongoDB connected successfully');
      return mongoose;
    }).catch((error) => {
      console.error('❌ MongoDB connection error:', error);
      cached.promise = null; // Reset promise on error
      throw error;
    });
  }

  try {
    cached.conn = await cached.promise;

    // Add connection event listeners
    mongoose.connection.on('error', (error) => {
      console.error('❌ MongoDB connection error:', error);
    });

    mongoose.connection.on('disconnected', () => {
      console.warn('⚠️ MongoDB disconnected');
      cached.conn = null;
      cached.promise = null;
    });

    mongoose.connection.on('reconnected', () => {
      console.log('🔄 MongoDB reconnected');
    });

  } catch (e) {
    console.error('❌ Failed to connect to MongoDB:', e);
    cached.promise = null;
    cached.conn = null;
    throw e;
  }

  return cached.conn;
}

// Function to check database connection health
export async function checkDatabaseHealth() {
  try {
    const connection = await connectToDatabase();

    // Test the connection with a simple ping
    await mongoose.connection.db?.admin().ping();

    return {
      status: 'healthy',
      readyState: mongoose.connection.readyState,
      host: mongoose.connection.host,
      name: mongoose.connection.name
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Unknown error',
      readyState: mongoose.connection.readyState
    };
  }
}

// Function to safely disconnect
export async function disconnectFromDatabase() {
  try {
    if (cached.conn) {
      await mongoose.disconnect();
      cached.conn = null;
      cached.promise = null;
      console.log('✅ MongoDB disconnected safely');
    }
  } catch (error) {
    console.error('❌ Error disconnecting from MongoDB:', error);
  }
}

// Safe connection function with retry logic for API routes
export async function connectWithRetry(maxRetries = 3) {
  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🔌 Database connection attempt ${attempt}/${maxRetries}`);
      const connection = await connectToDatabase();
      console.log(`✅ Database connected successfully on attempt ${attempt}`);
      return connection;
    } catch (error) {
      lastError = error instanceof Error ? error : new Error('Unknown connection error');
      console.error(`❌ Connection attempt ${attempt} failed:`, lastError.message);

      if (attempt < maxRetries) {
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000); // Exponential backoff, max 5s
        console.log(`⏳ Waiting ${delay}ms before retry...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  throw new Error(`Failed to connect to database after ${maxRetries} attempts: ${lastError?.message}`);
}

export default connectToDatabase;
