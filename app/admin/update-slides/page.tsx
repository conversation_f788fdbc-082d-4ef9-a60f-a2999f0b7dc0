"use client"

import React, { useState } from 'react'
import Link from 'next/link'
import { ArrowLeft } from 'lucide-react'

export default function UpdateSlidesPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [results, setResults] = useState<any[]>([])
  const [error, setError] = useState<string | null>(null)
  const [updateType, setUpdateType] = useState<string>('all')
  const [unitNumber, setUnitNumber] = useState<string>('')
  const [lessonNumber, setLessonNumber] = useState<string>('')
  const [gradeLevel, setGradeLevel] = useState<string>('')

  const handleUpdateSlides = async () => {
    try {
      setIsLoading(true)
      setError(null)
      setResults([])

      // Prepare the query parameters
      const params = new URLSearchParams()
      params.append('updateType', updateType)
      
      if (updateType === 'specific' && unitNumber) {
        params.append('unitNumber', unitNumber)
      }
      
      if (updateType === 'specific' && lessonNumber) {
        params.append('lessonNumber', lessonNumber)
      }
      
      if (updateType === 'specific' && gradeLevel) {
        params.append('gradeLevel', gradeLevel)
      }

      // Call the API to update slides
      const response = await fetch(`/api/admin/update-slides?${params.toString()}`)
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to update slides')
      }

      const data = await response.json()
      setResults(data.results || [])
    } catch (err: any) {
      console.error('Error updating slides:', err)
      setError(err.message || 'An error occurred while updating slides')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-600 via-blue-500 to-cyan-400">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="mb-6">
            <Link href="/" className="inline-flex items-center text-white hover:text-blue-100 transition-colors">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Home
            </Link>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl p-6 md:p-8">
            <h1 className="text-2xl md:text-3xl font-bold mb-2 text-gray-900 dark:text-white">
              Update All Slides
            </h1>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              This utility will scan all documents in the database and update them to ensure they have the correct structure for HTML content.
            </p>

            <div className="mb-6">
              <h2 className="text-xl font-semibold mb-3 text-gray-800 dark:text-white">Update Options</h2>
              
              <div className="mb-4">
                <label className="block text-gray-700 dark:text-gray-300 mb-2">Update Type</label>
                <select 
                  value={updateType}
                  onChange={(e) => setUpdateType(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                >
                  <option value="all">All Documents</option>
                  <option value="specific">Specific Document</option>
                </select>
              </div>

              {updateType === 'specific' && (
                <div className="space-y-4">
                  <div>
                    <label className="block text-gray-700 dark:text-gray-300 mb-2">Unit Number</label>
                    <input 
                      type="text" 
                      value={unitNumber}
                      onChange={(e) => setUnitNumber(e.target.value)}
                      placeholder="e.g., 2"
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-gray-700 dark:text-gray-300 mb-2">Lesson Number</label>
                    <input 
                      type="text" 
                      value={lessonNumber}
                      onChange={(e) => setLessonNumber(e.target.value)}
                      placeholder="e.g., 1"
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-gray-700 dark:text-gray-300 mb-2">Grade Level</label>
                    <input 
                      type="text" 
                      value={gradeLevel}
                      onChange={(e) => setGradeLevel(e.target.value)}
                      placeholder="e.g., Grade 1"
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    />
                  </div>
                </div>
              )}
            </div>

            <button
              onClick={handleUpdateSlides}
              disabled={isLoading}
              className={`w-full py-3 px-4 rounded-md text-white font-medium transition-colors ${
                isLoading
                  ? 'bg-blue-400 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700'
              }`}
            >
              {isLoading ? 'Updating Slides...' : 'Update Slides'}
            </button>

            {error && (
              <div className="mt-6 p-4 bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-300 rounded-lg">
                <p className="font-medium">Error</p>
                <p className="text-sm">{error}</p>
              </div>
            )}

            {results.length > 0 && (
              <div className="mt-6">
                <h2 className="text-xl font-semibold mb-3 text-gray-800 dark:text-white">Results</h2>
                <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 max-h-96 overflow-auto">
                  <ul className="space-y-2">
                    {results.map((result, index) => (
                      <li 
                        key={index}
                        className={`p-3 rounded-md ${
                          result.status === 'success'
                            ? 'bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-300'
                            : 'bg-yellow-50 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-300'
                        }`}
                      >
                        <p className="font-medium">{result.documentId}</p>
                        <p className="text-sm">{result.message}</p>
                        {result.updatedSlides && (
                          <p className="text-xs mt-1">Updated {result.updatedSlides} slides</p>
                        )}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
