"use client"

import { Suspense } from 'react'
import Link from 'next/link'

// Separate component for the login form that uses useSearchParams
import LoginForm from './login-form'

export default function LoginPage() {
  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-8">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-bold tracking-tight text-gray-900">
            Sign in to your account
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Or{' '}
            <Link href="/register" className="font-medium text-blue-600 hover:text-blue-500">
              create a new account
            </Link>
          </p>
        </div>

        <Suspense fallback={<div className="mt-8 text-center">Loading login form...</div>}>
          <LoginForm />
        </Suspense>
      </div>
    </div>
  )
}
