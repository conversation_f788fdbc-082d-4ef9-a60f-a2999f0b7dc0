"use client"

import type React from "react"

import { useState, useEffect, useCallback, Suspense } from "react"
import { useSearchParams } from "next/navigation"
// Import the necessary icons
import { ChevronLeft, ChevronRight, Globe } from "lucide-react"
import DrawingButton from "@/components/drawing/drawing-button"
import { Sidebar } from "@/components/sidebar"
import { SlideContent } from "@/components/slide-content"
import { loadSlideData, getTotalSlides, clearSlidesCache } from "@/services/slideService"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useLessonContext } from "@/components/lesson-context"
import MainHeader from "@/components/main-header"

// Separate component that uses useSearchParams
function MathLessonContent() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const searchParams = useSearchParams()

  const [currentSlide, setCurrentSlide] = useState(1)
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [highContrast, setHighContrast] = useState(false)
  const [userMenuOpen, setUserMenuOpen] = useState(false)
  const [paramsProcessed, setParamsProcessed] = useState(false)

  // Use lesson context instead of local state
  const {
    unitNumber, setUnitNumber,
    lessonNumber, setLessonNumber,
    gradeLevel, setGradeLevel,
    lang, setLang,
    totalSlides, setTotalSlides,
    hasTranslation
  } = useLessonContext()

  // Only redirect to login if not authenticated
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login")
    }
  }, [status, router])

  // localStorage refresh logic - check if we need to refresh and restore params
  useEffect(() => {
    const checkRefreshFlag = () => {
      console.log('🔄 REFRESH: Checking localStorage refresh flag...');

      try {
        const refreshFlag = localStorage.getItem('refresh_requested');

        if (refreshFlag === 'true') {
          console.log('🔄 REFRESH: Found refresh flag, processing...');

          // Set flag to false to prevent infinite loop
          localStorage.setItem('refresh_requested', 'false');

          // Clear lesson context
          setGradeLevel('')
          setUnitNumber('')
          setLessonNumber('')
          setLang('')
          setTotalSlides(0)
          setCurrentSlide(1)

          // Clear all slides cache
          clearSlidesCache();

          // Clear lesson localStorage
          try {
            localStorage.removeItem('lesson_unit_number');
            localStorage.removeItem('lesson_lesson_number');
            localStorage.removeItem('lesson_grade_level');
            localStorage.removeItem('lesson_current_slide');
            localStorage.removeItem('lesson_lang');
          } catch (error) {
            console.error('Error clearing lesson localStorage:', error);
          }

          // Reset params processed
          setParamsProcessed(false)

          console.log('🔄 REFRESH: Reloading page...');
          window.location.reload();
          return;
        }

        // Check if we need to restore params after refresh
        const savedParams = localStorage.getItem('back_to_home_params');

        if (savedParams) {
          console.log('🔄 RESTORE: Found saved params to restore:', savedParams);

          const params = JSON.parse(savedParams);

          // Build new URL with saved params (excluding refresh)
          const newParams = new URLSearchParams();
          ['grade', 'unit', 'lesson', 'slide', 'curriculum', 'lang'].forEach(param => {
            if (params[param]) {
              newParams.set(param, params[param]);
            }
          });

          const newUrl = newParams.toString()
            ? `${window.location.pathname}?${newParams.toString()}`
            : window.location.pathname;

          console.log('🔄 RESTORE: Restoring URL to:', newUrl);

          // Clear saved params
          localStorage.removeItem('back_to_home_params');
          localStorage.setItem('refresh_requested', 'null');

          // Update URL and reload
          window.history.replaceState({}, '', newUrl);
          window.location.reload();
          return;
        }

      } catch (error) {
        console.error('🔄 REFRESH: Error during refresh check:', error);
      }
    };

    // Check immediately
    checkRefreshFlag();

    // Also check after a short delay
    const timer = setTimeout(checkRefreshFlag, 1000);

    return () => clearTimeout(timer);
  }, []); // Empty dependency array - run only once on mount

  // Process URL parameters once - this runs first to set up the context
  useEffect(() => {
    if (!paramsProcessed) {
      setParamsProcessed(true)

      // Get parameters from URL
      const unit = searchParams.get('unit')
      const lesson = searchParams.get('lesson')
      const grade = searchParams.get('grade')
      const slide = searchParams.get('slide')
      const language = searchParams.get('lang')
      const curriculum = searchParams.get('curriculum')

      // Create a stable object for parameters to avoid unnecessary re-renders
      const params = {
        unit: unit || '',
        lesson: lesson || '',
        grade: grade || '',
        slide: slide || '',
        language: language || '',
        curriculum: curriculum || ''
      };

      console.log('🏠 Home: Loading parameters from URL:', params)
      console.log('🏠 Home: Current window.location.href:', window.location.href)
      console.log('🏠 Home: searchParams.toString():', searchParams.toString())

      // Batch state updates to reduce re-renders
      const stateUpdates = async () => {
        // First set the lesson context parameters
        if (grade) setGradeLevel(grade)
        if (unit) setUnitNumber(unit)
        if (lesson) setLessonNumber(lesson)
        if (language) setLang(language)

        // Wait a moment for the context to update
        await new Promise(resolve => setTimeout(resolve, 50));

        // Then set the slide number
        if (slide) {
          const slideNumber = parseInt(slide, 10)
          if (!isNaN(slideNumber)) {
            setCurrentSlide(slideNumber)
          }
        }
      };

      // If we have parameters, use them
      if (unit || lesson || grade || slide) {
        stateUpdates();
      } else {
        // Fallback to localStorage if URL parameters are missing
        try {
          const storedUnit = localStorage.getItem('lesson_unit_number')
          const storedLesson = localStorage.getItem('lesson_lesson_number')
          const storedGrade = localStorage.getItem('lesson_grade_level')
          const storedSlide = localStorage.getItem('lesson_current_slide')
          const storedLang = localStorage.getItem('lesson_lang')

          // Only use localStorage if we have meaningful values
          if (storedUnit || storedLesson || storedGrade) {
            console.log('Home: Fallback to localStorage:', {
              storedUnit, storedLesson, storedGrade, storedSlide, storedLang
            })

            if (storedGrade) setGradeLevel(storedGrade)
            if (storedUnit) setUnitNumber(storedUnit)
            if (storedLesson) setLessonNumber(storedLesson)
            if (storedLang) setLang(storedLang)

            if (storedSlide) {
              const slideNumber = parseInt(storedSlide, 10)
              if (!isNaN(slideNumber)) {
                setTimeout(() => setCurrentSlide(slideNumber), 100)
              }
            }

            // Update URL with stored parameters for better persistence
            if (storedUnit || storedLesson || storedGrade) {
              const newParams = new URLSearchParams();
              if (storedUnit) newParams.set('unit', storedUnit);
              if (storedLesson) newParams.set('lesson', storedLesson);
              if (storedGrade) newParams.set('grade', storedGrade);
              if (storedSlide) newParams.set('slide', storedSlide);
              if (storedLang) newParams.set('lang', storedLang);

              // IMPORTANT: Preserve curriculum from URL if it exists
              const curriculumFromUrl = searchParams.get('curriculum');
              if (curriculumFromUrl) {
                newParams.set('curriculum', curriculumFromUrl);
              }

              // Update URL without reloading the page
              const newUrl = `${window.location.pathname}?${newParams.toString()}`;
              window.history.replaceState({}, '', newUrl);
            }
          }
        } catch (error) {
          console.error('Error loading from localStorage:', error)
        }
      }
    }
  }, [searchParams, setUnitNumber, setLessonNumber, setGradeLevel, setLang, paramsProcessed, setParamsProcessed, setCurrentSlide])

  // Load total slides count on initial load
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        // Get total slides count and update context
        const count = await getTotalSlides(unitNumber, lessonNumber, gradeLevel, lang)
        setTotalSlides(count)
        console.log(`Initial total slides: ${count}`)
      } catch (error) {
        console.error('Error loading initial slide count:', error)
      }
    }

    loadInitialData()
  }, [unitNumber, lessonNumber, gradeLevel, lang, setTotalSlides])





  const [showSettingsModal, setShowSettingsModal] = useState(false)
  const [userName, setUserName] = useState("Teacher Smith")
  const [isEditingName, setIsEditingName] = useState(false)
  const [userSettings, setUserSettings] = useState({
    school: "Lincoln Elementary",
    district: "Springfield School District",
    curriculum: "CCSS",
    grade: "5",
  })

  // Language state is now handled by lang and setLang
  const [languageMenuOpen, setLanguageMenuOpen] = useState(false)
  const [curriculumMenuOpen, setCurriculumMenuOpen] = useState(false)
  const [gradeMenuOpen, setGradeMenuOpen] = useState(false)
  const [unitMenuOpen, setUnitMenuOpen] = useState(false)
  const [lessonMenuOpen, setLessonMenuOpen] = useState(false)



  // State to track revealed items on the current slide
  const [revealedItems, setRevealedItems] = useState<number[]>([])
  // State to track the total number of revealable items on the current slide
  const [totalRevealableItems, setTotalRevealableItems] = useState(0)

  const [showDashboardModal, setShowDashboardModal] = useState(false)

  // Reset revealed items when changing slides
  useEffect(() => {
    setRevealedItems([])
  }, [currentSlide])



  const handlePrevious = useCallback(() => {
    setCurrentSlide((prev) => {
      const newSlide = Math.max(1, prev - 1);

      // Update URL parameter for slide
      try {
        const url = new URL(window.location.href);
        url.searchParams.set('slide', newSlide.toString());
        window.history.replaceState({}, '', url.toString());
        console.log('Updated slide in URL (previous):', newSlide);
      } catch (error) {
        console.error("Error updating slide in URL:", error);
      }

      return newSlide;
    });
  }, [])



  const handleNext = useCallback(() => {
    setCurrentSlide((prev) => {
      const newSlide = Math.min(totalSlides, prev + 1);

      // Update URL parameter for slide
      try {
        const url = new URL(window.location.href);
        url.searchParams.set('slide', newSlide.toString());
        window.history.replaceState({}, '', url.toString());
        console.log('Updated slide in URL (next):', newSlide);
      } catch (error) {
        console.error("Error updating slide in URL:", error);
      }

      return newSlide;
    });
  }, [totalSlides])

  // Function to reveal the next item
  const revealNextItem = useCallback(() => {
    if (revealedItems.length < totalRevealableItems) {
      const nextItemIndex = revealedItems.length
      setRevealedItems((prev) => [...prev, nextItemIndex])
      return true // Item was revealed
    }
    return false // No more items to reveal
  }, [revealedItems, totalRevealableItems])

  // Function to hide the last revealed item
  const hideLastItem = useCallback(() => {
    if (revealedItems.length > 0) {
      setRevealedItems((prev) => prev.slice(0, -1))
      return true // Item was hidden
    }
    return false // No items to hide
  }, [revealedItems])

  // Handle keyboard navigation - modified to use only arrow keys
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Get the currently focused element
      const target = e.target as HTMLElement
      const isEditableElement = target.tagName === "INPUT" || target.tagName === "TEXTAREA" || target.isContentEditable

      // Only prevent default for navigation keys when not in editable elements
      if ((e.code === "ArrowLeft" || e.code === "ArrowRight") && !isEditableElement) {
        e.preventDefault()
      }

      // Only prevent spacebar default when not in editable elements and not a button
      if (e.code === "Space" && !isEditableElement && target.tagName !== "BUTTON") {
        e.preventDefault()
      }

      // Only process arrow keys for navigation when not in editable elements
      if (!isEditableElement) {
        if (e.code === "ArrowRight") {
          // Try to reveal next item, if none left, go to next slide
          const revealed = revealNextItem()
          if (!revealed) {
            handleNext()
          }
        } else if (e.code === "ArrowLeft") {
          // Try to hide last item, if none left, go to previous slide
          const hidden = hideLastItem()
          if (!hidden) {
            handlePrevious()
          }
        }
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => {
      window.removeEventListener("keydown", handleKeyDown)
    }
  }, [revealNextItem, hideLastItem, handleNext, handlePrevious])

  // Function to be called from slide components to register their revealable items
  const registerRevealableItems = useCallback((count: number) => {
    setTotalRevealableItems(count)
  }, [])

  // Close user menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      if (userMenuOpen && !target.closest(".user-menu-container")) {
        setUserMenuOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [userMenuOpen])

  // Close language menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      if (languageMenuOpen && !target.closest(".language-menu-container")) {
        setLanguageMenuOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [languageMenuOpen])

  // Close curriculum dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      if (curriculumMenuOpen && !target.closest(".curriculum-menu-container")) {
        setCurriculumMenuOpen(false)
      }
      if (gradeMenuOpen && !target.closest(".grade-menu-container")) {
        setGradeMenuOpen(false)
      }
      if (unitMenuOpen && !target.closest(".unit-menu-container")) {
        setUnitMenuOpen(false)
      }
      if (lessonMenuOpen && !target.closest(".lesson-menu-container")) {
        setLessonMenuOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [curriculumMenuOpen, gradeMenuOpen, unitMenuOpen, lessonMenuOpen])

  // Calculate header height for positioning
  useEffect(() => {
    const updateHeaderHeight = () => {
      const header = document.querySelector("header")
      if (header) {
        const headerHeight = header.offsetHeight
        document.documentElement.style.setProperty("--computed-header-height", `${headerHeight}px`)

        // Force a repaint to ensure buttons are positioned correctly
        const languageButton = document.querySelector(".language-menu-container") as HTMLElement
        const sidebarButton = document.querySelector(".fixed.top-\\[calc\\(var\\(--header-height\\)\\+32px\\)\\].left-8") as HTMLElement

        if (languageButton) {
          languageButton.style.transform = 'translateZ(0)'
          setTimeout(() => {
            languageButton.style.transform = ''
          }, 0)
        }

        if (sidebarButton) {
          sidebarButton.style.transform = 'translateZ(0)'
          setTimeout(() => {
            sidebarButton.style.transform = ''
          }, 0)
        }
      }
    }

    // Initial update
    updateHeaderHeight()

    // Update on resize
    window.addEventListener("resize", updateHeaderHeight)

    // Listen for slide content loaded events (especially for slides without HTML)
    const handleSlideContentLoaded = (event: CustomEvent) => {
      console.log('Slide content loaded, updating header height:', event.detail)
      setTimeout(updateHeaderHeight, 100)
    }

    window.addEventListener('slideContentLoaded', handleSlideContentLoaded as EventListener)

    // Update when high contrast mode changes (this might affect header height)
    const timer = setTimeout(updateHeaderHeight, 100)

    // Also update when DOM is fully loaded
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', updateHeaderHeight)
    }

    return () => {
      window.removeEventListener("resize", updateHeaderHeight)
      window.removeEventListener('slideContentLoaded', handleSlideContentLoaded as EventListener)
      document.removeEventListener('DOMContentLoaded', updateHeaderHeight)
      clearTimeout(timer)
    }
  }, [highContrast]) // Add highContrast as dependency

  // If still loading auth state, show loading indicator
  if (status === "loading") {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <div className="mb-4 h-12 w-12 animate-spin rounded-full border-4 border-blue-600 border-t-transparent mx-auto"></div>
          <p className="text-lg">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div
      className="flex !z-[500] h-screen flex-col bg-white"
      style={{ "--header-height": "var(--computed-header-height, 116px)" } as React.CSSProperties}
    >
      {/* Header */}
      <MainHeader
        isDashboard={false}
        onGradeChange={(grade) => {
          console.log('Main page: Grade changed to', grade)
          setGradeLevel(grade)
        }}
        onUnitChange={(unit) => {
          console.log('Main page: Unit changed to', unit)
          setUnitNumber(unit)
        }}
        onLessonChange={(lesson) => {
          console.log('Main page: Lesson changed to', lesson)
          setLessonNumber(lesson)
        }}
        onSlideChange={(slide) => {
          console.log('Main page: Slide changed to', slide)
          setCurrentSlide(slide)
        }}
        initialGrade={gradeLevel}
        initialUnit={unitNumber}
        initialLesson={lessonNumber}
        initialSlide={currentSlide}
      />



      {/* Language Selector */}
      <div className="language-menu-container fixed top-[calc(var(--header-height)+32px)] right-8 z-50">
        <button
          onClick={() => hasTranslation ? setLanguageMenuOpen(!languageMenuOpen) : undefined}
          className={`flex h-10 w-10 items-center justify-center rounded-full text-white transition-colors  shadow-md ${
            hasTranslation
              ? "bg-white/20 hover:bg-white/30 cursor-pointer"
              : "bg-white/10 cursor-not-allowed opacity-50"
          }`}
          aria-label={hasTranslation ? "Change language" : "Language (English only)"}
          title={hasTranslation ? "Change language" : "Language: English only"}
          disabled={!hasTranslation}
        >
          <Globe className="h-5 w-5" />
        </button>

        {languageMenuOpen && (
          <div className="absolute right-0 mt-2 w-40 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 z-50">
            <div className="py-1">
              <button
                className={`flex w-full items-center px-4 py-2 text-sm text-left ${lang === undefined || lang === "en" ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                onClick={() => {
                  setLang(undefined) // Default to English
                  setLanguageMenuOpen(false)
                  // Keep current slide when changing language
                  setRevealedItems([]) // Reset revealed items

                  // Update URL to preserve current slide
                  try {
                    const url = new URL(window.location.href);
                    url.searchParams.delete('lang'); // Remove lang parameter for English
                    window.history.replaceState({}, '', url.toString());
                    console.log('Updated language in URL (English)');
                  } catch (error) {
                    console.error("Error updating language in URL:", error);
                  }
                }}
              >
                English
              </button>
              <button
                className={`flex w-full items-center px-4 py-2 text-sm text-left ${lang === "esp" ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                onClick={() => {
                  setLang("esp")
                  setLanguageMenuOpen(false)
                  // Keep current slide when changing language
                  setRevealedItems([]) // Reset revealed items

                  // Update URL to preserve current slide
                  try {
                    const url = new URL(window.location.href);
                    url.searchParams.set('lang', 'esp');
                    window.history.replaceState({}, '', url.toString());
                    console.log('Updated language in URL (Spanish)');
                  } catch (error) {
                    console.error("Error updating language in URL:", error);
                  }
                }}
              >
                Español
              </button>
            </div>
          </div>
        )}
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar */}
        <Sidebar
          sidebarOpen={sidebarOpen}
          setSidebarOpen={setSidebarOpen}
          currentSlide={currentSlide}
          setCurrentSlide={setCurrentSlide}
          unitNumber={unitNumber}
          lessonNumber={lessonNumber}
          gradeLevel={gradeLevel}
          lang={lang}
        />

        {/* Main content */}
        <div
          className={`flex-1 overflow-y-auto ${
            highContrast ? "bg-white text-black" : "bg-[linear-gradient(150deg,#2B6DFE_0%,#2B6DFE_70%,#00F2FF_100%)]"
          } p-0 text-lg`}
          style={
            highContrast
              ? ({
                  "--fadb9a": "#000000",
                  "--concept-number-bg": "#ffffff",
                  "--concept-number-text": "#000000",
                  "--concept-content-bg": "#ffffff",
                  "--concept-content-text": "#000000",
                } as React.CSSProperties)
              : {}
          }
        >
          {/* Sidebar toggle button - fixed position like language menu, hidden when sidebar is open or curriculum dropdown is open */}
          {!sidebarOpen && !curriculumMenuOpen && (
            <div className="fixed top-[calc(var(--header-height)+32px)] left-8 z-50">
              <button
                onClick={() => setSidebarOpen(true)}
                className="flex h-10 w-10 items-center justify-center rounded-full bg-white/20 text-white hover:bg-white/30 transition-colors  shadow-md"
                aria-label="Open sidebar"
              >
                <ChevronRight size={16} />
              </button>
            </div>
          )}

          <div className={`h-full ${highContrast ? "high-contrast-content" : ""}`}>
            <SlideContent
              slideNumber={currentSlide}
              highContrast={highContrast}
              revealedItems={revealedItems}
              registerRevealableItems={registerRevealableItems}
              setRevealedItems={setRevealedItems}
              unitNumber={unitNumber}
              lessonNumber={lessonNumber}
              gradeLevel={gradeLevel}
              lang={lang}
            />
          </div>
        </div>
      </div>

      {/* Footer navigation */}
      <div className="flex items-center justify-between border-t bg-white px-6 py-3">
        <button
          onClick={handlePrevious}
          className="flex items-center gap-2 rounded-md border px-4 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50"
        >
          <ChevronLeft size={16} />
          Previous
        </button>

        <div className="text-sm text-gray-500">
          Slide {currentSlide} of {totalSlides}
        </div>

        <button
          onClick={handleNext}
          className="flex items-center gap-2 rounded-md bg-[#2B6DFE] px-4 py-2 text-sm font-medium text-white hover:bg-blue-600"
        >
          Next
          <ChevronRight size={16} />
        </button>
      </div>

      {/* Dashboard Promotional Modal */}
      {showDashboardModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-full max-w-3xl shadow-xl overflow-hidden">
            {/* Close button */}
            <div className="absolute top-4 right-4">
              <button
                onClick={() => setShowDashboardModal(false)}
                className="text-gray-500 hover:text-gray-700 transition-colors"
                aria-label="Close"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="flex flex-col md:flex-row">
              {/* Left side - Illustration */}
              <div className="bg-gradient-to-br from-[#2B6DFE] to-[#00F2FF] p-8 text-white md:w-2/5 flex flex-col items-center justify-center">
                <div className="mb-6">
                  <img src="/images/embrs-logo.png" alt="EMBRS Logo" className="h-12 w-12" />
                </div>
                <div className="relative h-64 w-64">
                  {/* Dashboard illustration */}
                  <div className="absolute inset-0 bg-white/10 rounded-lg border border-white/20 backdrop-blur-sm shadow-xl"></div>
                  <div className="absolute top-4 left-4 right-4 h-8 bg-white/20 rounded-md flex items-center px-3">
                    <div className="w-2 h-2 rounded-full bg-white mr-2"></div>
                    <div className="w-2 h-2 rounded-full bg-white/50 mr-2"></div>
                    <div className="w-2 h-2 rounded-full bg-white/50"></div>
                  </div>
                  <div className="absolute top-16 left-4 right-4 h-20 bg-white/20 rounded-md"></div>
                  <div className="absolute top-40 left-4 w-1/2 h-16 bg-white/20 rounded-md"></div>
                  <div className="absolute top-40 right-4 w-1/3 h-16 bg-white/20 rounded-md"></div>
                  <div className="absolute bottom-4 left-4 right-4 h-12 bg-white/20 rounded-md"></div>

                  {/* Analytics graph */}
                  <div className="absolute bottom-20 left-8 right-8 h-24 flex items-end justify-between">
                    <div className="w-2 h-12 bg-white/80 rounded-t-sm"></div>
                    <div className="w-2 h-16 bg-white/80 rounded-t-sm"></div>
                    <div className="w-2 h-8 bg-white/80 rounded-t-sm"></div>
                    <div className="w-2 h-20 bg-white/80 rounded-t-sm"></div>
                    <div className="w-2 h-14 bg-white/80 rounded-t-sm"></div>
                    <div className="w-2 h-10 bg-white/80 rounded-t-sm"></div>
                  </div>
                </div>
              </div>

              {/* Right side - Content */}
              <div className="p-8 md:w-3/5">
                <div className="flex items-center mb-4">
                  <h2 className="text-2xl font-montserrat font-[900] text-gray-600 ml-3">Dashboard Coming Soon!</h2>
                </div>

                <p className="text-gray-600 mb-6">
                  We're building a powerful dashboard to enhance your teaching experience. Stay tuned for these exciting
                  features:
                </p>

                <div className="space-y-4">
                  <div className="flex items-start">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-[#2B6DFE]/10 mt-0.5">
                      <svg className="h-4 w-4 text-[#2B6DFE]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                        />
                      </svg>
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-bold font-montserrat text-gray-600">Interactive Scope & Sequence</h3>
                      <p className="text-gray-600">Plan your instruction with our intuitive scope and sequence.</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-[#2B6DFE]/10 mt-0.5">
                      <svg className="h-4 w-4 text-[#2B6DFE]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-bold font-montserrat text-gray-600">Quizzes & Tests</h3>
                      <p className="text-gray-600">Create comprehensive assessments for your students.</p>
                    </div>
                  </div>
                </div>

                <div className="mt-8 flex justify-end">
                  <button
                    onClick={() => setShowDashboardModal(false)}
                    className="px-4 py-2 bg-[#2B6DFE] rounded-md text-white hover:bg-[#2B6DFE]/90 transition-colors"
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Settings Modal */}
      {showSettingsModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-[linear-gradient(150deg,#2B6DFE_0%,#2B6DFE_70%,#00F2FF_100%)] rounded-lg w-full max-w-md shadow-xl text-white overflow-hidden">
            <div className="flex justify-between items-center p-6 border-b border-white/10">
              <h2 className="text-xl font-semibold text-white">Settings</h2>
              <button
                onClick={() => setShowSettingsModal(false)}
                className="text-white/70 hover:text-white transition-colors"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="bg-gray-50 p-6 space-y-5 text-gray-700 text-sm">
              {/* Name - Editable */}
              <div className="space-y-1">
                <label className="block font-medium text-gray-600">Name</label>
                <div className="flex items-center">
                  {isEditingName ? (
                    <input
                      type="text"
                      value={userName}
                      onChange={(e) => setUserName(e.target.value)}
                      className="flex-1 bg-white border border-gray-200 rounded-md px-3 py-2 text-gray-700 focus:outline-none focus:ring-2 focus:ring-[#2B6DFE]"
                      autoFocus
                    />
                  ) : (
                    <span className="flex-1 py-2 text-gray-700">{userName}</span>
                  )}
                  <button
                    onClick={() => setIsEditingName(!isEditingName)}
                    className="ml-2 text-gray-400 hover:text-[#2B6DFE] transition-colors"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
                      />
                    </svg>
                  </button>
                </div>
              </div>

              {/* School - Not Editable */}
              <div className="space-y-1">
                <label className="block font-medium text-gray-600">School</label>
                <div className="py-2 px-3 bg-gray-100 rounded-md border border-gray-200 text-gray-500">
                  {userSettings.school}
                </div>
              </div>

              {/* High Contrast Toggle */}
              <div className="space-y-1">
                <label className="block font-medium text-gray-600">Display Mode</label>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">High Contrast Mode</span>
                  <button
                    onClick={() => setHighContrast(!highContrast)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-[#2B6DFE] focus:ring-offset-2 ${
                      highContrast ? "bg-[#2B6DFE]" : "bg-gray-200"
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        highContrast ? "translate-x-6" : "translate-x-1"
                      }`}
                    />
                  </button>
                </div>
              </div>
            </div>

            <div className="p-6 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
              <button
                onClick={() => setShowSettingsModal(false)}
                className="px-3 py-1.5 border border-gray-200 rounded-md text-gray-700 hover:bg-gray-100 transition-colors text-sm"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  // Save settings logic would go here
                  setShowSettingsModal(false)
                  setIsEditingName(false)
                }}
                className="px-3 py-1.5 bg-gray-700 text-white font-medium rounded-md hover:bg-gray-600 transition-colors text-sm"
              >
                Save Changes
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Drawing Tool */}
      <DrawingButton />
    </div>
  )
}

// Main component with Suspense boundary
export default function MathLessonSlider() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <MathLessonContent />
    </Suspense>
  )
}
