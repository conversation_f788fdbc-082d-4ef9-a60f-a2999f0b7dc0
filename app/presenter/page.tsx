"use client"

import { useEffect, useRef, Suspense, useState } from "react"
import { EmbrsPresenterView } from "@/components/embrs-presenter-view"
import { usePresenter } from "@/components/presenter-context"
import { useLessonContext } from "@/components/lesson-context"
import { useSearchParams } from "next/navigation"

// Separate component that uses useSearchParams
function PresenterContent() {
  const { setIsPresenterMode, setCurrentSlide, currentSlide, setRevealedItems, revealedItems } = usePresenter()
  const {
    unitNumber, lessonNumber, gradeLevel, lang,
    setUnitNumber, setLessonNumber, setGradeLevel, setLang
  } = useLessonContext()

  const searchParams = useSearchParams()
  const initializedRef = useRef(false)
  const paramsProcessedRef = useRef(false)
  const [curriculum, setCurriculum] = useState<string>('')

  // Process URL parameters once - this runs first to set up the context
  useEffect(() => {
    if (!paramsProcessedRef.current) {
      paramsProcessedRef.current = true

      // Store the current URL in sessionStorage to persist across page refreshes
      try {
        sessionStorage.setItem('presenter_url', window.location.href);
      } catch (error) {
        console.error('Error saving URL to sessionStorage:', error);
      }

      // Get parameters from URL
      const unit = searchParams.get('unit')
      const lesson = searchParams.get('lesson')
      const grade = searchParams.get('grade')
      const slide = searchParams.get('slide')
      const language = searchParams.get('lang')
      const curriculumFromUrl = searchParams.get('curriculum')

      // Update curriculum state
      if (curriculumFromUrl) {
        setCurriculum(curriculumFromUrl)
      }

      // Create a stable object for parameters to avoid unnecessary re-renders
      const params = {
        unit: unit || '',
        lesson: lesson || '',
        grade: grade || '',
        slide: slide || '',
        language: language || '',
        curriculum: curriculumFromUrl || ''
      };

      console.log('Presenter: Loading parameters from URL:', params)

      // Batch state updates to reduce re-renders
      const stateUpdates = async () => {
        // First set the lesson context parameters
        if (grade) setGradeLevel(grade)
        if (unit) setUnitNumber(unit)
        if (lesson) setLessonNumber(lesson)
        if (language) setLang(language)

        // Wait a moment for the context to update
        await new Promise(resolve => setTimeout(resolve, 50));

        // Then set the slide number
        if (slide) {
          const slideNumber = parseInt(slide, 10)
          if (!isNaN(slideNumber)) {
            setCurrentSlide(slideNumber)
          }
        }
      };

      // If we have all required parameters, use them
      if (unit && lesson && grade) {
        stateUpdates();
      } else {
        // Fallback to localStorage if URL parameters are missing
        try {
          const storedUnit = localStorage.getItem('lesson_unit_number')
          const storedLesson = localStorage.getItem('lesson_lesson_number')
          const storedGrade = localStorage.getItem('lesson_grade_level')
          const storedSlide = localStorage.getItem('lesson_current_slide')
          const storedLang = localStorage.getItem('lesson_lang')

          console.log('Presenter: Fallback to localStorage:', {
            storedUnit, storedLesson, storedGrade, storedSlide, storedLang
          })

          // Update URL with stored parameters for better persistence
          if (storedUnit || storedLesson || storedGrade || storedSlide) {
            const newParams = new URLSearchParams(window.location.search);
            if (storedUnit && !unit) {
              newParams.set('unit', storedUnit);
              setUnitNumber(storedUnit);
            }
            if (storedLesson && !lesson) {
              newParams.set('lesson', storedLesson);
              setLessonNumber(storedLesson);
            }
            if (storedGrade && !grade) {
              newParams.set('grade', storedGrade);
              setGradeLevel(storedGrade);
            }
            if (storedSlide && !slide) {
              newParams.set('slide', storedSlide);
              // Set slide after a short delay to ensure other context is loaded
              setTimeout(() => {
                const slideNumber = parseInt(storedSlide, 10)
                if (!isNaN(slideNumber)) {
                  setCurrentSlide(slideNumber)
                }
              }, 100);
            }
            if (storedLang && !language) {
              newParams.set('lang', storedLang);
              setLang(storedLang);
            }

            // Update URL without reloading the page
            const newUrl = `${window.location.pathname}?${newParams.toString()}`;
            window.history.replaceState({}, '', newUrl);
          }
        } catch (error) {
          console.error('Error loading from localStorage:', error)
        }
      }
    }
  }, [searchParams, setUnitNumber, setLessonNumber, setGradeLevel, setLang, setCurrentSlide])

  // Set presenter mode immediately and initialize state
  useEffect(() => {
    console.log("Presenter view initialized")
    setIsPresenterMode(true)

    // Store a reference to any windows we open
    if (typeof window !== "undefined") {
      ;(window as any)._openedWindows = (window as any)._openedWindows || []
    }

    return () => setIsPresenterMode(false)
  }, [setIsPresenterMode])

  // Separate effect for state persistence to avoid unnecessary re-renders
  useEffect(() => {
    // Only run this effect if we have all the necessary parameters
    if (!unitNumber || !lessonNumber || !gradeLevel) {
      return;
    }

    // Only send the initial update once or when critical values change
    if (!initializedRef.current ||
        localStorage.getItem("presenter_slide") !== currentSlide.toString() ||
        localStorage.getItem("lesson_unit_number") !== unitNumber ||
        localStorage.getItem("lesson_lesson_number") !== lessonNumber ||
        localStorage.getItem("lesson_grade_level") !== gradeLevel) {

      initializedRef.current = true;

      // Store state with a small delay to ensure all context is properly loaded
      const timer = setTimeout(() => {
        console.log("Persisting presenter state to localStorage:", {
          slide: currentSlide,
          unit: unitNumber,
          lesson: lessonNumber,
          grade: gradeLevel
        });

        try {
          // Store slide and revealed items for presenter/audience sync
          localStorage.setItem("presenter_slide", currentSlide.toString());
          localStorage.setItem("presenter_revealed_items", JSON.stringify(revealedItems));

          // Store lesson context for persistence
          localStorage.setItem('lesson_unit_number', unitNumber);
          localStorage.setItem('lesson_lesson_number', lessonNumber);
          localStorage.setItem('lesson_grade_level', gradeLevel);
          localStorage.setItem('lesson_current_slide', currentSlide.toString());
          if (lang) {
            localStorage.setItem('lesson_lang', lang);
          }

          // Update URL with current state for better persistence
          const newParams = new URLSearchParams();
          newParams.set('unit', unitNumber);
          newParams.set('lesson', lessonNumber);
          newParams.set('grade', gradeLevel);
          newParams.set('slide', currentSlide.toString());
          if (lang) {
            newParams.set('lang', lang);
          }
          if (curriculum) {
            newParams.set('curriculum', curriculum);
          }

          // Update URL without reloading the page
          const newUrl = `${window.location.pathname}?${newParams.toString()}`;
          if (window.location.href !== newUrl) {
            window.history.replaceState({}, '', newUrl);
          }
        } catch (error) {
          console.error("Error updating localStorage:", error);
        }
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [currentSlide, revealedItems, unitNumber, lessonNumber, gradeLevel, lang, curriculum])

  return <EmbrsPresenterView />
}

// Main component with Suspense boundary
export default function PresenterPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <PresenterContent />
    </Suspense>
  )
}
