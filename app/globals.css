@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221 98% 58%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221 98% 58%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 221 98% 58%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 221 98% 58%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Global cursor styles for interactive elements */
  select:not(:disabled) {
    cursor: pointer;
  }

  select:disabled {
    cursor: not-allowed;
  }

  button:not(:disabled) {
    cursor: pointer;
  }

  button:disabled {
    cursor: not-allowed;
  }

  /* Ensure fixed positioned elements maintain proper positioning */
  .language-menu-container,
  .fixed[class*="top-[calc(var(--header-height)"] {
    will-change: transform;
    backface-visibility: hidden;
    transform: translateZ(0);
  }

  /* Force proper positioning after layout changes */
  .language-menu-container {
    position: fixed !important;
    top: calc(var(--header-height, 116px) + 32px) !important;
    right: 2rem !important;
    z-index: 50 !important;
  }
}

.slide-title {
  font-family: var(--font-montserrat), sans-serif;
  font-weight: 800;
  font-size: 2.5rem;
  margin-bottom: 2rem;
  text-align: center;
}

.blue-gradient {
  background: linear-gradient(150deg, #2b6dfe 0%, #2b6dfe 70%, #00f2ff 100%);
  color: white;
}

.concept-slide {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  padding: 1rem;
}

.concept-point {
  display: flex;
  align-items: flex-start;
  margin-bottom: 1.5rem;
}

.concept-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  background-color: #4169e1;
  color: white;
  border-radius: 50%;
  font-size: 1.5rem;
  font-weight: bold;
  margin-right: 1rem;
  flex-shrink: 0;
}

.concept-content {
  font-size: 1.25rem;
}

.illustration-box {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  overflow: hidden;
}

.illustration-box img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  background-color: transparent;
  border-radius: 0.5rem;
}

/* Fix for slides 1, 5, 6, and 8 images */
.slide-image-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border-radius: 0.5rem;
  background-color: transparent;
}

.slide-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  background-color: transparent;
}

.illustration-title {
  font-size: 1.75rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.illustration-text {
  font-size: 1.25rem;
}

/* Projector-friendly styles */
.text-lg {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-2xl {
  font-size: 1.75rem;
  line-height: 2.25rem;
}

.text-3xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

/* High contrast mode styles - Apply to entire site */
.high-contrast-content {
  background-color: white !important;
  color: black !important;
}

/* Text elements */
.high-contrast-content h1,
.high-contrast-content h2,
.high-contrast-content h3,
.high-contrast-content h4,
.high-contrast-content h5,
.high-contrast-content h6,
.high-contrast-content p,
.high-contrast-content span,
.high-contrast-content li,
.high-contrast-content div,
.high-contrast-content label,
.high-contrast-content a {
  color: black !important;
}

/* Header styles */
.high-contrast-content header {
  background: white !important;
  border-bottom: 3px solid black !important;
}

.high-contrast-content .blue-gradient {
  background: white !important;
  color: black !important;
  border: 2px solid black !important;
}

/* Navigation and menu items */
.high-contrast-content nav,
.high-contrast-content .nav-item {
  background-color: white !important;
  color: black !important;
}

/* Buttons */
.high-contrast-content button {
  background-color: white !important;
  color: black !important;
  border: 2px solid black !important;
}

.high-contrast-content button:hover {
  background-color: #f0f0f0 !important;
  color: black !important;
}

/* Dropdowns and selects */
.high-contrast-content select,
.high-contrast-content input {
  background-color: white !important;
  color: black !important;
  border: 2px solid black !important;
}

/* Cards and containers */
.high-contrast-content .bg-white\/10,
.high-contrast-content .bg-white\/20,
.high-contrast-content .bg-gray-50,
.high-contrast-content .bg-gray-100,
.high-contrast-content .bg-blue-50 {
  background-color: white !important;
  border: 1px solid black !important;
}

/* Modals and overlays */
.high-contrast-content .modal,
.high-contrast-content .overlay {
  background-color: white !important;
  border: 3px solid black !important;
}

/* Slide content */
.high-contrast-content .slide-title {
  color: black !important;
}

.high-contrast-content .concept-number {
  background-color: white !important;
  color: black !important;
  border: 2px solid black !important;
}

.high-contrast-content .illustration-box {
  background-color: white !important;
  border: 2px solid black !important;
}

/* Rounded elements */
.high-contrast-content .rounded-lg,
.high-contrast-content .rounded-xl,
.high-contrast-content .rounded-md,
.high-contrast-content .rounded-full {
  border: 1px solid black !important;
}

/* Specific color overrides */
.high-contrast-content [class*="text-white"],
.high-contrast-content [class*="text-gray"],
.high-contrast-content [class*="text-blue"] {
  color: black !important;
}

.high-contrast-content [class*="bg-gradient"],
.high-contrast-content [class*="gradient"] {
  background: white !important;
  color: black !important;
  border: 2px solid black !important;
}

/* Icons */
.high-contrast-content svg {
  color: black !important;
  fill: black !important;
}

/* Links */
.high-contrast-content a {
  color: black !important;
  text-decoration: underline !important;
}

.high-contrast-content a:hover {
  background-color: #f0f0f0 !important;
}

/* Tables */
.high-contrast-content table,
.high-contrast-content th,
.high-contrast-content td {
  border: 1px solid black !important;
  background-color: white !important;
  color: black !important;
}

/* Form elements */
.high-contrast-content input[type="text"],
.high-contrast-content input[type="email"],
.high-contrast-content input[type="password"],
.high-contrast-content textarea {
  background-color: white !important;
  color: black !important;
  border: 2px solid black !important;
}

/* Specific component overrides */
.high-contrast-content .bg-\[\#fadb9a\] {
  background-color: white !important;
  color: black !important;
  border: 2px solid black !important;
}

.high-contrast-content .border-\[\#fadb9a\] {
  border-color: black !important;
}

.high-contrast-content [class~="border-\\[\\#fadb9a\\]"] {
  border-color: black !important;
}

/* Header specific styles */
.high-contrast-content .main-header {
  background: white !important;
  border-bottom: 3px solid black !important;
}

/* User menu dropdown */
.high-contrast-content .user-menu,
.high-contrast-content .dropdown-menu {
  background-color: white !important;
  border: 2px solid black !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
}

/* Logo and branding */
.high-contrast-content .logo-text {
  color: black !important;
}

/* Filter dropdowns */
.high-contrast-content .filter-dropdown {
  background-color: white !important;
  border: 2px solid black !important;
  color: black !important;
}

/* Tabs */
.high-contrast-content .tab-active {
  background-color: black !important;
  color: white !important;
  border: 2px solid black !important;
}

.high-contrast-content .tab-inactive {
  background-color: white !important;
  color: black !important;
  border: 2px solid black !important;
}

/* Modal backgrounds */
.high-contrast-content .modal-backdrop {
  background-color: rgba(0, 0, 0, 0.8) !important;
}

.high-contrast-content .modal-content {
  background-color: white !important;
  border: 3px solid black !important;
}

/* Slide navigation */
.high-contrast-content .slide-nav-button {
  background-color: white !important;
  color: black !important;
  border: 2px solid black !important;
}

/* Progress indicators */
.high-contrast-content .progress-bar {
  background-color: white !important;
  border: 1px solid black !important;
}

.high-contrast-content .progress-fill {
  background-color: black !important;
}

/* Tooltips */
.high-contrast-content .tooltip {
  background-color: black !important;
  color: white !important;
  border: 1px solid white !important;
}

/* Focus states */
.high-contrast-content *:focus {
  outline: 3px solid black !important;
  outline-offset: 2px !important;
}

/* Disabled states */
.high-contrast-content .disabled,
.high-contrast-content :disabled {
  background-color: #f5f5f5 !important;
  color: #666 !important;
  border: 2px solid #999 !important;
}

/* Shadows - remove or make them black */
.high-contrast-content [class*="shadow"] {
  box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5) !important;
}

/* Backdrop blur effects */
.high-contrast-content [class*="backdrop-blur"] {
  backdrop-filter: none !important;
  background-color: white !important;
  border: 2px solid black !important;
}

/* Tailwind CSS class overrides for high contrast */
.high-contrast-content .text-white {
  color: black !important;
}

.high-contrast-content .text-gray-500,
.high-contrast-content .text-gray-600,
.high-contrast-content .text-gray-700,
.high-contrast-content .text-gray-800,
.high-contrast-content .text-gray-900 {
  color: black !important;
}

.high-contrast-content .bg-white {
  background-color: white !important;
  border: 1px solid black !important;
}

.high-contrast-content .bg-gray-50,
.high-contrast-content .bg-gray-100,
.high-contrast-content .bg-gray-200 {
  background-color: white !important;
  border: 1px solid black !important;
}

.high-contrast-content .bg-blue-500,
.high-contrast-content .bg-blue-600,
.high-contrast-content .bg-blue-700 {
  background-color: white !important;
  color: black !important;
  border: 2px solid black !important;
}

.high-contrast-content .border-gray-200,
.high-contrast-content .border-gray-300 {
  border-color: black !important;
}

.high-contrast-content .hover\:bg-gray-100:hover,
.high-contrast-content .hover\:bg-gray-50:hover {
  background-color: #f0f0f0 !important;
}

/* Specific component patterns */
.high-contrast-content .fixed,
.high-contrast-content .absolute {
  background-color: white !important;
  border: 2px solid black !important;
}

/* Menu items and navigation */
.high-contrast-content .menu-item {
  background-color: white !important;
  color: black !important;
  border-bottom: 1px solid black !important;
}

.high-contrast-content .menu-item:hover {
  background-color: #f0f0f0 !important;
}

/* Card components */
.high-contrast-content .card {
  background-color: white !important;
  border: 2px solid black !important;
  box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3) !important;
}

/* Input focus states */
.high-contrast-content input:focus,
.high-contrast-content select:focus,
.high-contrast-content textarea:focus {
  outline: 3px solid black !important;
  outline-offset: 1px !important;
  border-color: black !important;
}

/* Toggle switches */
.high-contrast-content .toggle-switch {
  background-color: white !important;
  border: 2px solid black !important;
}

.high-contrast-content .toggle-switch.active {
  background-color: black !important;
}

.high-contrast-content .toggle-switch .toggle-thumb {
  background-color: white !important;
  border: 1px solid black !important;
}

/* Senior-friendly styles */
.senior-friendly-text {
  font-size: 1.125rem !important;
  line-height: 1.5 !important;
  font-weight: 500 !important;
}

/* Logo text style */
.logo-text {
  font-family: var(--font-montserrat), sans-serif !important;
  font-weight: 900 !important;
  letter-spacing: -0.025em !important;
}

.senior-friendly-heading {
  font-size: 1.5rem !important;
  line-height: 1.3 !important;
  font-weight: 700 !important;
}

.senior-friendly-button {
  font-size: 1.125rem !important;
  font-weight: 600 !important;
  padding: 0.75rem 1rem !important;
  border-radius: 0.5rem !important;
  min-height: 3rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.2s ease-in-out !important;
  gap: 0.5rem !important;
}

.senior-friendly-icon {
  width: 1.5rem !important;
  height: 1.5rem !important;
  margin-right: 0.75rem !important;
}

.senior-friendly-card {
  border-radius: 0.75rem !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
  border: 2px solid #e5e7eb !important;
}

.senior-friendly-tabs {
  gap: 0.5rem !important;
}

.senior-friendly-tab {
  font-size: 1.125rem !important;
  font-weight: 600 !important;
  padding: 0.75rem 1rem !important;
  border-radius: 0.5rem !important;
  min-height: 3rem !important;
  border: 2px solid transparent !important;
}

.senior-friendly-tab[data-state="active"] {
  background: linear-gradient(to right, #3b82f6, #60a5fa) !important;
  color: white !important;
  border: 2px solid #2563eb !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.senior-friendly-tab:not([data-state="active"]) {
  background-color: #f3f4f6 !important;
  color: #1f2937 !important;
  border: 2px solid #e5e7eb !important;
}

.senior-friendly-tab:hover:not([data-state="active"]) {
  background-color: #e5e7eb !important;
}

.senior-friendly-textarea {
  line-height: 1.6 !important;
  padding: 0.75rem !important;
  border-radius: 0.5rem !important;
  border: 2px solid #e5e7eb !important;
}

.senior-friendly-input {
  font-size: 1.125rem !important;
  height: 3rem !important;
  padding: 0 0.75rem !important;
  border-radius: 0.5rem !important;
  border: 2px solid #e5e7eb !important;
}

/* Responsive text for overlays */
.responsive-text {
  transition: font-size 0.3s ease;
}

@media (max-width: 640px) {
  .responsive-text.text-4xl {
    font-size: 2rem !important;
  }
  .responsive-text.text-xl {
    font-size: 1rem !important;
  }
}

@media (max-width: 480px) {
  .responsive-text.text-4xl {
    font-size: 1.75rem !important;
  }
  .responsive-text.text-xl {
    font-size: 0.875rem !important;
  }
}

/* HTML Content Wrapper improvements */
.generated-html-container {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  min-height: 0 !important; /* CRITICAL: Remove min-height constraints */
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
  box-sizing: border-box !important;
}

.generated-html-container iframe {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  border: none !important;
  background: white;
  flex: 1 !important;
  object-fit: contain !important;
  display: block !important;
  box-sizing: border-box !important;
}

/* Ensure proper aspect ratio for HTML content */
.html-content-wrapper {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
  box-sizing: border-box !important;
}

/* Fix for slide content scaling - remove min-height constraints */
.slide-content-container .generated-html-container {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
}

/* Ensure iframe fits within slide bounds */
.slide-content-container iframe {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  object-fit: contain !important;
}

/* Force proper sizing for HTML content in slides */
[class*="HtmlContentWrapper"] {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  overflow: hidden !important;
  box-sizing: border-box !important;
}

/* Specific fixes for iframe scaling in slides */
.slide-content iframe,
.slide-preview iframe,
[data-slide] iframe {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  min-height: 0 !important;
  min-width: 0 !important;
  object-fit: contain !important;
  transform: scale(1) !important;
  transform-origin: top left !important;
  overflow: hidden !important;
  border: none !important;
  display: block !important;
  box-sizing: border-box !important;
}

/* Remove any conflicting aspect ratio constraints in slide context */
.slide-content iframe[style*="aspect-ratio"],
.slide-preview iframe[style*="aspect-ratio"] {
  aspect-ratio: unset !important;
}

/* CRITICAL: Override inline min-height styles that cause overflow */
.generated-html-container[style*="min-height"] {
  min-height: 0 !important;
}

/* Force fit content within available space */
.slide-content .generated-html-container,
.slide-preview .generated-html-container,
[data-slide] .generated-html-container {
  height: 100% !important;
  max-height: 100% !important;
  min-height: 0 !important;
  width: 100% !important;
  max-width: 100% !important;
  object-fit: contain !important;
  transform: scale(1) !important;
  transform-origin: top left !important;
}

/* Ensure iframe scales to fit container */
.slide-content .generated-html-container iframe,
.slide-preview .generated-html-container iframe {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  min-width: 0 !important;
  min-height: 0 !important;
  object-fit: contain !important;
  transform: scale(1) !important;
}

/* ULTRA SPECIFIC: Override any remaining min-height constraints */
div[style*="min-height: 400px"] .generated-html-container,
.generated-html-container[style*="min-height: 400px"],
.slide-content div[style*="min-height"],
.slide-preview div[style*="min-height"] {
  min-height: 0 !important;
  height: 100% !important;
  max-height: 100% !important;
}

/* Custom slow blinking animation for selection message */
@keyframes slow-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
}

.slow-blink {
  animation: slow-pulse 3s ease-in-out infinite;
}

/* Force all HTML containers in slides to fit properly */
.slide-content .generated-html-container,
.slide-preview .generated-html-container,
.concept-slide .generated-html-container,
.introduction-slide .generated-html-container {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  min-height: 0 !important;
  min-width: 0 !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
  box-sizing: border-box !important;
}


