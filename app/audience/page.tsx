"use client"

import { useEffect, useRef, Suspense } from "react"
import { AudienceView } from "@/components/audience-view"
import { usePresenter } from "@/components/presenter-context"
import { useLessonContext } from "@/components/lesson-context"
import { useSearchParams } from "next/navigation"

// Separate component that uses useSearchParams
function AudienceContent() {
  const { setIsAudienceView, setCurrentSlide } = usePresenter()
  const {
    unitNumber, lessonNumber, gradeLevel, lang,
    setUnitNumber, setLessonNumber, setGradeLevel, setLang
  } = useLessonContext()

  const searchParams = useSearchParams()
  const pollingRef = useRef<NodeJS.Timeout | null>(null)
  const paramsProcessedRef = useRef(false)

  // Rest of the component logic...

  // Process URL parameters once - this runs first to set up the context
  useEffect(() => {
    if (!paramsProcessedRef.current) {
      paramsProcessedRef.current = true

      // Store the current URL in sessionStorage to persist across page refreshes
      try {
        sessionStorage.setItem('audience_url', window.location.href);
      } catch (error) {
        console.error('Error saving URL to sessionStorage:', error);
      }

      // Get parameters from URL
      const unit = searchParams.get('unit')
      const lesson = searchParams.get('lesson')
      const grade = searchParams.get('grade')
      const slide = searchParams.get('slide')
      const language = searchParams.get('lang')
      const curriculum = searchParams.get('curriculum')

      // Create a stable object for parameters to avoid unnecessary re-renders
      const params = {
        unit: unit || '',
        lesson: lesson || '',
        grade: grade || '',
        slide: slide || '',
        language: language || '',
        curriculum: curriculum || ''
      };

      console.log('Audience: Loading parameters from URL:', params)

      // Batch state updates to reduce re-renders
      const stateUpdates = async () => {
        // First set the lesson context parameters
        if (grade) setGradeLevel(grade)
        if (unit) setUnitNumber(unit)
        if (lesson) setLessonNumber(lesson)
        if (language) setLang(language)

        // Wait a moment for the context to update
        await new Promise(resolve => setTimeout(resolve, 50));

        // Then set the slide number
        if (slide) {
          const slideNumber = parseInt(slide, 10)
          if (!isNaN(slideNumber)) {
            setCurrentSlide(slideNumber)
          }
        }
      };

      // If we have all required parameters, use them
      if (unit && lesson && grade) {
        stateUpdates();
      } else {
        // Fallback to localStorage if URL parameters are missing
        try {
          const storedUnit = localStorage.getItem('lesson_unit_number')
          const storedLesson = localStorage.getItem('lesson_lesson_number')
          const storedGrade = localStorage.getItem('lesson_grade_level')
          const storedSlide = localStorage.getItem('lesson_current_slide')
          const storedLang = localStorage.getItem('lesson_lang')

          console.log('Audience: Fallback to localStorage:', {
            storedUnit, storedLesson, storedGrade, storedSlide, storedLang
          })

          // Update URL with stored parameters for better persistence
          if (storedUnit || storedLesson || storedGrade || storedSlide) {
            const newParams = new URLSearchParams(window.location.search);
            if (storedUnit && !unit) {
              newParams.set('unit', storedUnit);
              setUnitNumber(storedUnit);
            }
            if (storedLesson && !lesson) {
              newParams.set('lesson', storedLesson);
              setLessonNumber(storedLesson);
            }
            if (storedGrade && !grade) {
              newParams.set('grade', storedGrade);
              setGradeLevel(storedGrade);
            }
            if (storedSlide && !slide) {
              newParams.set('slide', storedSlide);
              // Set slide after a short delay to ensure other context is loaded
              setTimeout(() => {
                const slideNumber = parseInt(storedSlide, 10)
                if (!isNaN(slideNumber)) {
                  setCurrentSlide(slideNumber)
                }
              }, 100);
            }
            if (storedLang && !language) {
              newParams.set('lang', storedLang);
              setLang(storedLang);
            }

            // Update URL without reloading the page
            const newUrl = `${window.location.pathname}?${newParams.toString()}`;
            window.history.replaceState({}, '', newUrl);
          }
        } catch (error) {
          console.error('Error loading from localStorage:', error)
        }
      }
    }
  }, [searchParams, setUnitNumber, setLessonNumber, setGradeLevel, setLang, setCurrentSlide])

  // Set audience view mode immediately
  useEffect(() => {
    console.log("Audience view initialized")
    setIsAudienceView(true)

    // Listen for window close message
    const handleBeforeUnload = () => {
      try {
        // Save current state to localStorage before closing
        if (unitNumber && lessonNumber && gradeLevel) {
          localStorage.setItem('lesson_unit_number', unitNumber)
          localStorage.setItem('lesson_lesson_number', lessonNumber)
          localStorage.setItem('lesson_grade_level', gradeLevel)
          if (lang) {
            localStorage.setItem('lesson_lang', lang)
          }
        }
      } catch (error) {
        console.error('Error saving to localStorage before unload:', error)
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)

    return () => {
      setIsAudienceView(false)
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [setIsAudienceView])

  // Separate effect for polling to avoid unnecessary re-renders
  useEffect(() => {
    // Only set up polling if we have the necessary parameters
    if (!unitNumber || !lessonNumber || !gradeLevel) {
      return;
    }

    console.log("Setting up audience polling with parameters:", {
      unit: unitNumber,
      lesson: lessonNumber,
      grade: gradeLevel
    });

    // Set up polling as an additional fallback
    pollingRef.current = setInterval(() => {
      try {
        const storedSlide = localStorage.getItem("presenter_slide")
        if (storedSlide) {
          const slideNum = Number.parseInt(storedSlide, 10)
          if (!isNaN(slideNum)) {
            // Only update if the slide has changed
            if (slideNum !== parseInt(localStorage.getItem("audience_current_slide") || "0", 10)) {
              console.log("Polling: updating slide from", localStorage.getItem("audience_current_slide"), "to", slideNum)
              setCurrentSlide(slideNum)
              localStorage.setItem("audience_current_slide", slideNum.toString())
            }
          }
        }

        // Also check for revealed items updates
        const storedItems = localStorage.getItem("presenter_revealed_items")
        if (storedItems) {
          try {
            const items = JSON.parse(storedItems)
            if (Array.isArray(items)) {
              // We could update revealed items here if needed
              // This would require passing setRevealedItems to this component
            }
          } catch (e) {
            console.error("Error parsing revealed items from localStorage", e)
          }
        }
      } catch (error) {
        console.error("Error in polling:", error)
      }
    }, 1000)

    return () => {
      if (pollingRef.current) {
        clearInterval(pollingRef.current)
        pollingRef.current = null
      }
    }
  }, [unitNumber, lessonNumber, gradeLevel, setCurrentSlide])

  return <AudienceView />
}

// Main component with Suspense boundary
export default function AudiencePage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <AudienceContent />
    </Suspense>
  )
}
