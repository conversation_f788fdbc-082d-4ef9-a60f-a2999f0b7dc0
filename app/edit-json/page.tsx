"use client"

import { useState, useEffect, Suspense } from "react"
import { useSearchParams } from "next/navigation"
import { ChevronLeft, ChevronRight, Save, ArrowLeft } from "lucide-react"
import Link from "next/link"

interface Slide {
  slide_number: number
  [key: string]: any
}

interface Document {
  _id: string
  slides: Slide[]
  [key: string]: any
}

// Define all possible fields for different slide types based on slide number and type
const getAllPossibleFields = (slideNumber: number, slideType?: string, currentSlideData?: any) => {
  const baseFields = [
    'slide_pedagogical_name',
    'type',
    'script',
    'teacher_tips'
  ]

  // Slides that should NOT show html_css_description_of_image field
  const slidesWithoutHtmlField = [2, 3, 13, 20, 21, 24, 25, 26, 27]

  // Add html_css_description_of_image only for slides that should have it
  if (!slidesWithoutHtmlField.includes(slideNumber)) {
    baseFields.push('html_css_description_of_image')
  }

  // Never show generated_html_content as it's auto-generated

  // Determine slide type based on slide number if not provided
  const getSlideTypeByNumber = (num: number): string => {
    if (num === 1) return "Quick_Review"
    if (num === 2) return "Learning_Goals"
    if (num === 3) return "Vocabulary"
    if (num >= 4 && num <= 6) return "hook"
    if (num >= 7 && num <= 9) return "teach_1"
    if (num >= 10 && num <= 12) return "talk_1"
    if (num === 13) return "try_1"
    if (num >= 14 && num <= 16) return "teach_2"
    if (num >= 17 && num <= 19) return "talk_2"
    if (num === 20) return "try_2"
    if (num === 21) return "practice"
    if (num >= 22 && num <= 24) return "on_ramp"
    if (num >= 25) return "printables"
    return "concept"
  }

  const effectiveSlideType = slideType || getSlideTypeByNumber(slideNumber)
  let specificFields: string[] = []

  switch (effectiveSlideType.toLowerCase()) {
    case 'quick_review':
    case 'introduction':
      // Quick Review slides typically have 3 questions
      specificFields = ['q1', 'a1', 'q2', 'a2', 'q3', 'a3']
      break

    case 'learning_goals':
      // Learning Goals slides have learning goal fields
      specificFields = ['lg_1', 'lg_2', 'lg_3', 'lg_4']
      break

    case 'vocabulary':
      // Vocabulary slides have term and definition fields
      specificFields = [
        'term_1', 'definition_1_with_emoji',
        'term_2', 'definition_2_with_emoji',
        'term_3', 'definition_3_with_emoji',
        'term_4', 'definition_4_with_emoji',
        'manipulative_used'
      ]
      break

    case 'hook':
    case 'teach_1':
    case 'teach_2':
    case 'concept':
      // Hook and Teach slides have slide_text fields
      specificFields = ['slide_text_1', 'slide_text_2', 'slide_text_3', 'slide_text_4']
      break

    case 'talk_1':
    case 'talk_2':
    case 'talk':
      // Talk slides have slide_q and slide_a
      specificFields = ['slide_q', 'slide_a']
      break

    case 'try_1':
    case 'try_2':
    case 'try':
      // Try slides typically have 3 questions
      specificFields = ['q1', 'a1', 'q2', 'a2', 'q3', 'a3']
      break

    case 'practice':
      // Practice slides - determine number of questions from existing data
      let maxQuestions = 20 // Default maximum
      if (currentSlideData) {
        // Check both top level and content level for questions
        const dataToCheck = currentSlideData.content || currentSlideData

        // Find the highest numbered question that exists
        let highestQ = 0
        for (let i = 1; i <= 20; i++) {
          if (dataToCheck[`q${i}`] !== undefined && dataToCheck[`q${i}`] !== null && dataToCheck[`q${i}`] !== '') {
            highestQ = i
          }
        }
        // Show at least the existing questions + a few extra for adding new ones
        maxQuestions = Math.max(highestQ + 3, 12) // Minimum 12, or existing + 3
        console.log(`🔍 Practice slide: found ${highestQ} questions, showing ${maxQuestions} fields`)
      }
      for (let i = 1; i <= maxQuestions; i++) {
        specificFields.push(`q${i}`, `a${i}`)
      }
      break

    case 'on_ramp':
      // On-ramp slides can have different fields depending on subtype
      if (currentSlideData) {
        const dataToCheck = currentSlideData.content || currentSlideData
        const pedagogicalName = (dataToCheck.slide_pedagogical_name || currentSlideData.slide_pedagogical_name)?.toLowerCase() || ''
        if (pedagogicalName.includes('teach')) {
          // On-ramp teach slides have slide_text fields
          specificFields = ['slide_text_1', 'slide_text_2', 'slide_text_3', 'slide_text_4']
        } else if (pedagogicalName.includes('talk')) {
          // On-ramp talk slides have slide_q and slide_a
          specificFields = ['slide_q', 'slide_a']
        } else if (pedagogicalName.includes('try')) {
          // On-ramp try slides have questions
          specificFields = ['q1', 'a1', 'q2', 'a2', 'q3', 'a3']
        } else {
          // Default: include both types
          specificFields = [
            'slide_text_1', 'slide_text_2', 'slide_text_3', 'slide_text_4',
            'slide_q', 'slide_a',
            'q1', 'a1', 'q2', 'a2', 'q3', 'a3'
          ]
        }
      } else {
        // Default: include both types
        specificFields = [
          'slide_text_1', 'slide_text_2', 'slide_text_3', 'slide_text_4',
          'slide_q', 'slide_a',
          'q1', 'a1', 'q2', 'a2', 'q3', 'a3'
        ]
      }
      break

    case 'printables':
      // Printables slides have different PDF links based on slide number
      if (slideNumber === 25) {
        specificFields = ['link_to_lesson_guide_pdf']
      } else if (slideNumber === 26) {
        specificFields = ['link_to_practice_pdf']
      } else if (slideNumber === 27) {
        specificFields = ['link_to_accelerator_pdf']
      } else {
        // Default for other printables slides (if any)
        specificFields = [
          'link_to_lesson_guide_pdf',
          'link_to_practice_pdf',
          'link_to_accelerator_pdf'
        ]
      }
      break

    default:
      // Default case - include common fields
      specificFields = [
        'slide_text_1', 'slide_text_2', 'slide_text_3', 'slide_text_4',
        'q1', 'a1', 'q2', 'a2', 'q3', 'a3'
      ]
  }

  const allFields = [...baseFields, ...specificFields]
  console.log(`🔍 Slide ${slideNumber}: Generated fields:`, allFields)
  return allFields
}

// Get appropriate placeholder text for different field types
const getPlaceholderText = (key: string): string => {
  if (key.startsWith('q')) return `Enter question ${key.slice(1)}`
  if (key.startsWith('a')) return `Enter answer ${key.slice(1)}`
  if (key.startsWith('slide_text_')) return `Enter slide text ${key.slice(-1)}`
  if (key.startsWith('term_')) return `Enter vocabulary term ${key.slice(-1)}`
  if (key.startsWith('definition_')) return `Enter definition ${key.slice(-1)}`
  if (key.startsWith('lg_')) return `Enter learning goal ${key.slice(-1)}`
  if (key === 'slide_q') return 'Enter talk question'
  if (key === 'slide_a') return 'Enter talk answer'
  if (key === 'manipulative_used') return 'Enter manipulative name (e.g., Base-10 blocks)'
  if (key.startsWith('link_to_')) return 'Enter PDF link (e.g., /path/to/file.pdf)'
  if (key === 'slide_pedagogical_name') return 'Enter slide name (e.g., Quick Review)'
  if (key === 'type') return 'Enter slide type (e.g., introduction, hook, teach_1)'
  if (key === 'script') return 'Enter teacher script with numbered steps...'
  if (key === 'html_css_description_of_image') return 'Enter HTML/CSS description for image generation...'
  return `Enter ${key.replace(/_/g, ' ')}`
}

function EditJsonContent() {
  const searchParams = useSearchParams()
  const [document, setDocument] = useState<Document | null>(null)
  const [editedSlides, setEditedSlides] = useState<Slide[]>([])
  const [selectedSlide, setSelectedSlide] = useState(0)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  const documentId = searchParams.get('documentId')
  const grade = searchParams.get('grade')
  const unit = searchParams.get('unit')
  const lesson = searchParams.get('lesson')
  const curriculum = searchParams.get('curriculum')
  const lang = searchParams.get('lang')

  console.log('Frontend: EditJsonContent loaded with params:', { documentId, grade, unit, lesson, curriculum, lang })

  const fetchDocument = async (caller = 'unknown') => {
    const fetchId = Math.random().toString(36).substring(2, 11)
    try {
      console.log(`Frontend: fetchDocument called by ${caller} (ID: ${fetchId}) with:`, { documentId, grade, unit, lesson, curriculum, lang })

      if (!grade || !unit || !lesson) {
        console.log('Frontend: Missing required parameters')
        setLoading(false)
        return
      }

      console.log('Frontend: Calling fetchDocument...')

      const params = new URLSearchParams()
      if (documentId) params.append('id', documentId)
      if (grade) params.append('grade', grade)
      if (unit) params.append('unit', unit)
      if (lesson) params.append('lesson', lesson)
      if (curriculum) params.append('curriculum', curriculum)
      if (lang) params.append('lang', lang)

      const url = `/api/edit-json?${params.toString()}`
      console.log('Frontend: Fetching document from URL:', url)

      const response = await fetch(url)
      console.log('Frontend: Response status:', response.status)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      console.log('Frontend: Received data:', data)
      console.log('Frontend: Document data keys:', Object.keys(data.document))
      console.log('Frontend: Slides count:', data.document.slides?.length || 0)

      // Log first slide structure for debugging
      if (data.document.slides && data.document.slides.length > 0) {
        console.log('🔍 Frontend: First slide structure:', data.document.slides[0])
        console.log('🔍 Frontend: First slide keys:', Object.keys(data.document.slides[0]))
      }

      // Only update state if data actually changed
      const newDocumentString = JSON.stringify(data.document)
      const currentDocumentString = document ? JSON.stringify(document) : null

      if (newDocumentString !== currentDocumentString) {
        console.log(`Frontend: Data changed, updating state for fetch ${fetchId} by ${caller}`)
        setDocument(data.document)
        setEditedSlides(data.document.slides || [])
        setHasUnsavedChanges(false)
      } else {
        console.log(`Frontend: Data unchanged, skipping state update for fetch ${fetchId} by ${caller}`)
      }

      console.log(`Frontend: State processing completed for fetch ${fetchId} by ${caller}`)
    } catch (error) {
      console.error(`Frontend: Error fetching document for fetch ${fetchId} by ${caller}:`, error)
    } finally {
      setLoading(false)
      console.log(`Frontend: fetchDocument ${fetchId} by ${caller} completed`)
    }
  }

  // Track if we're currently saving to prevent unnecessary refetches
  const [isSaving, setIsSaving] = useState(false)

  // Track if there are unsaved changes
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  useEffect(() => {
    // Don't fetch if we're currently saving
    if (!isSaving) {
      fetchDocument('useEffect')
    } else {
      console.log('Skipping fetchDocument because save is in progress')
    }
  }, [documentId, grade, unit, lesson, curriculum, lang, isSaving])

  const handleSlideEdit = (slideIndex: number, field: string, value: any) => {
    console.log('🔧 Frontend: handleSlideEdit called:', { slideIndex, field, value })
    const newSlides = [...editedSlides]
    const currentSlide = newSlides[slideIndex]

    // Check if the field exists in content object
    if (currentSlide.content && currentSlide.content.hasOwnProperty(field)) {
      // Update in content object
      newSlides[slideIndex] = {
        ...currentSlide,
        content: {
          ...currentSlide.content,
          [field]: value
        }
      }
      console.log('🔧 Frontend: Updated field in content:', field)

      // Special logging for q1, a1 fields
      if (field === 'q1' || field === 'a1') {
        console.log(`🔧 Frontend: 🔍 SPECIAL DEBUG - Updated ${field} in content to:`, value)
        console.log(`🔧 Frontend: 🔍 SPECIAL DEBUG - Slide content now has:`, newSlides[slideIndex].content)
      }
    } else {
      // Update on the top level
      newSlides[slideIndex] = { ...currentSlide, [field]: value }
      console.log('🔧 Frontend: Updated field on top level:', field)

      // Special logging for q1, a1 fields
      if (field === 'q1' || field === 'a1') {
        console.log(`🔧 Frontend: 🔍 SPECIAL DEBUG - Updated ${field} on top level to:`, value)
        console.log(`🔧 Frontend: 🔍 SPECIAL DEBUG - Slide top level now has:`, { q1: newSlides[slideIndex].q1, a1: newSlides[slideIndex].a1 })
      }
    }

    console.log('🔧 Frontend: Updated slide:', newSlides[slideIndex])
    setEditedSlides(newSlides)
    setHasUnsavedChanges(true)
  }

  const saveDocument = async () => {
    if (!document) {
      alert('No document loaded to save')
      return
    }

    if (!editedSlides || editedSlides.length === 0) {
      alert('No slides to save')
      return
    }

    setSaving(true)
    setIsSaving(true)
    try {
      // Use documentId field (not _id) as this is how documents are identified
      const docId = document.documentId || document._id
      console.log('Frontend: Saving document with ID:', docId)
      console.log('Frontend: Document object keys:', Object.keys(document))
      console.log('Frontend: Saving slides count:', editedSlides.length)

      // Log first slide being saved for debugging
      if (editedSlides.length > 0) {
        console.log('Frontend: First slide being saved:', JSON.stringify(editedSlides[0], null, 2))
      }

      const requestBody = {
        documentId: docId,
        slides: editedSlides,
      }

      console.log('Frontend: Request body size:', JSON.stringify(requestBody).length, 'characters')

      const response = await fetch('/api/edit-json', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      })

      console.log('Frontend: Save response status:', response.status)

      if (!response.ok) {
        const errorData = await response.json()
        console.error('Frontend: Save error response:', errorData)
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      console.log('Frontend: Save result:', result)

      if (result.success) {
        alert(`Document saved successfully! Modified ${result.modifiedCount} document(s).`)
        console.log('Frontend: Save completed successfully, keeping current edited state')
        setHasUnsavedChanges(false)
      } else {
        throw new Error(result.message || 'Save failed')
      }
    } catch (error) {
      console.error('Frontend: Error saving document:', error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      alert(`Error saving document: ${errorMessage}`)
    } finally {
      setSaving(false)
      setIsSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-900 via-blue-800 to-cyan-700">
        <div className="flex items-center justify-center h-screen">
          <div className="text-white text-xl">Loading document...</div>
        </div>
      </div>
    )
  }

  if (!document) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-900 via-blue-800 to-cyan-700">
        <div className="flex items-center justify-center h-screen">
          <div className="text-white text-xl">Document not found</div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-max overflow-y-auto max-h-screen pb-3 bg-gradient-to-br from-blue-900 via-blue-800 to-cyan-700">
      {/* Header */}
      <div className="bg-white/10 backdrop-blur-sm border-b border-white/20 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link
              href={`/?grade=${encodeURIComponent(grade || '')}&unit=${unit}&lesson=${lesson}&slide=1${curriculum ? `&curriculum=${curriculum}` : ''}${lang ? `&lang=${lang}` : ''}&refresh=true`}
              className="flex items-center gap-2 text-white hover:text-white/80 transition-colors"
              onClick={(e) => {
                console.log('🏠 CLICK: Back to Home clicked, saving params to localStorage...');

                // Save current URL params to localStorage
                const paramsToSave: Record<string, string> = {};
                if (grade) paramsToSave.grade = grade;
                if (unit) paramsToSave.unit = unit;
                if (lesson) paramsToSave.lesson = lesson;
                paramsToSave.slide = '1'; // Always slide 1
                if (curriculum) paramsToSave.curriculum = curriculum;
                if (lang) paramsToSave.lang = lang;

                console.log('🏠 CLICK: Saving params:', paramsToSave);
                localStorage.setItem('back_to_home_params', JSON.stringify(paramsToSave));
                localStorage.setItem('refresh_requested', 'true');
              }}
            >
              <ArrowLeft className="h-5 w-5" />
              <span>Back to Home</span>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-white flex items-center gap-2">
                Edit JSON Document
                {hasUnsavedChanges && (
                  <span className="text-sm bg-yellow-600 text-white px-2 py-1 rounded">
                    Unsaved Changes
                  </span>
                )}
              </h1>
              <p className="text-white/70">
                {grade} - Unit {unit} - Lesson {lesson} ({editedSlides.length} slides)
              </p>
            </div>
          </div>
          <div className="flex gap-2">
            <button
              onClick={() => fetchDocument('manualRefresh')}
              disabled={loading}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 text-white rounded-lg transition-colors"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              )}
              {loading ? 'Refreshing...' : 'Refresh'}
            </button>
            <button
              onClick={saveDocument}
              disabled={saving}
              className="flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-green-800 text-white rounded-lg transition-colors"
            >
              {saving ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <Save className="w-4 h-4" />
              )}
              {saving ? 'Saving...' : 'Save Document'}
            </button>
          </div>
        </div>
      </div>

      <div className="flex h-full">
        {/* Slides List */}
        <div className="w-80 bg-white/5 backdrop-blur-sm max-h-[calc(100vh-105px)] border-r border-white/20 overflow-y-auto">
          <div className="p-4">
            <h2 className="text-lg font-semibold text-white mb-4">Slides</h2>
            <div className="space-y-2">
              {editedSlides.map((slide, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedSlide(index)}
                  className={`w-full text-left p-3 rounded-lg transition-colors ${
                    selectedSlide === index
                      ? 'bg-cyan-600 text-white'
                      : 'bg-white/10 text-white/80 hover:bg-white/20'
                  }`}
                >
                  <div className="font-medium">Slide {slide.slide_number}</div>
                  <div className="text-sm opacity-75">{(slide as any).slide_type || (slide as any).type}</div>
                  <div className="text-xs opacity-60 truncate">{(slide as any).title || (slide as any).slide_pedagogical_name || (slide as any).slide_name}</div>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Edit Form */}
        <div className="flex-1 h-full">
          <div className="p-6 h-full">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-white">
                Edit Slide {editedSlides[selectedSlide]?.slide_number}
              </h2>
              <div className="flex gap-2">
                <button
                  onClick={() => setSelectedSlide(Math.max(0, selectedSlide - 1))}
                  disabled={selectedSlide === 0}
                  className="p-2 bg-white/10 hover:bg-white/20 disabled:bg-white/5 disabled:opacity-50 text-white rounded-lg transition-colors"
                >
                  <ChevronLeft className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setSelectedSlide(Math.min(editedSlides.length - 1, selectedSlide + 1))}
                  disabled={selectedSlide === editedSlides.length - 1}
                  className="p-2 bg-white/10 hover:bg-white/20 disabled:bg-white/5 disabled:opacity-50 text-white rounded-lg transition-colors"
                >
                  <ChevronRight className="w-4 h-4" />
                </button>
              </div>
            </div>

            {editedSlides[selectedSlide] && (
              <div className="space-y-4 max-h-[calc(100vh-213px)] pr-4 overflow-y-auto">
                {/* Render all possible fields for this slide */}
                {(() => {
                  const currentSlide = editedSlides[selectedSlide]
                  const slideType = currentSlide.type || currentSlide.slide_pedagogical_name

                  // Debug logging
                  console.log('🔍 Current slide data:', currentSlide)
                  console.log('🔍 Slide content:', currentSlide.content)
                  console.log('🔍 Slide type:', slideType)

                  const allFields = getAllPossibleFields(currentSlide.slide_number, slideType, currentSlide)

                  return allFields.map((key) => {
                    // Skip internal fields and auto-generated content
                    if (key === 'slide_number' || key === 'slide_name' || key === 'content' || key === 'generated_html_content') {
                      return null
                    }

                    // Get value from content object if it exists, otherwise from top level
                    const value = currentSlide.content?.[key] ?? currentSlide[key]
                    console.log('🎨 Frontend: Rendering field:', { key, value, type: typeof value, fromContent: !!currentSlide.content?.[key] })

                    return (
                      <div key={`${selectedSlide}-${key}`}>
                        <label className="block text-white text-sm font-medium mb-2 capitalize">
                          {key.replace(/_/g, ' ')}
                        </label>

                        {/* Handle different field types */}
                        {key === 'teacher_tips' ? (
                          // Special handling for teacher_tips object
                          <textarea
                            value={value ? JSON.stringify(value, null, 2) : '{\n  "general_tip": "",\n  "misconception_tip": ""\n}'}
                            onChange={(e) => {
                              try {
                                const parsed = JSON.parse(e.target.value)
                                handleSlideEdit(selectedSlide, key, parsed)
                              } catch (error) {
                                // Invalid JSON, but still update to show user's input
                                handleSlideEdit(selectedSlide, key, e.target.value)
                              }
                            }}
                            rows={6}
                            className="w-full px-3 py-2 bg-white/10 text-white rounded-lg border border-white/20 focus:border-cyan-400 focus:outline-none font-mono text-sm"
                          />
                        ) : key === 'script' || key === 'html_css_description_of_image' || (typeof value === 'string' && value && value.length > 100) ? (
                          // Long text - use textarea
                          <textarea
                            value={value || ''}
                            onChange={(e) => handleSlideEdit(selectedSlide, key, e.target.value)}
                            rows={Math.min(10, Math.max(3, (value || '').split('\n').length))}
                            className="w-full px-3 py-2 bg-white/10 text-white rounded-lg border border-white/20 focus:border-cyan-400 focus:outline-none font-mono text-sm"
                            placeholder={getPlaceholderText(key)}
                          />
                        ) : typeof value === 'object' && value !== null ? (
                          // Object - use JSON textarea
                          <textarea
                            value={JSON.stringify(value, null, 2)}
                            onChange={(e) => {
                              try {
                                const parsed = JSON.parse(e.target.value)
                                handleSlideEdit(selectedSlide, key, parsed)
                              } catch (error) {
                                // Invalid JSON, but still update to show user's input
                                handleSlideEdit(selectedSlide, key, e.target.value)
                              }
                            }}
                            rows={Math.min(15, Math.max(5, JSON.stringify(value, null, 2).split('\n').length))}
                            className="w-full px-3 py-2 bg-white/10 text-white rounded-lg border border-white/20 focus:border-cyan-400 focus:outline-none font-mono text-sm"
                          />
                        ) : (
                          // Simple text - use input
                          <input
                            type="text"
                            value={value?.toString() || ''}
                            onChange={(e) => handleSlideEdit(selectedSlide, key, e.target.value)}
                            className="w-full px-3 py-2 bg-white/10 text-white rounded-lg border border-white/20 focus:border-cyan-400 focus:outline-none"
                            placeholder={getPlaceholderText(key)}
                          />
                        )}
                      </div>
                    )
                  })
                })()}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default function EditJsonPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-blue-900 via-blue-800 to-cyan-700">
        <div className="flex items-center justify-center h-screen">
          <div className="text-white text-xl">Loading...</div>
        </div>
      </div>
    }>
      <EditJsonContent />
    </Suspense>
  )
}