"use client"

import React, { useState } from 'react'
import FileUploader from '@/components/FileUploader'
import UploadResults from '@/components/UploadResults'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'

export default function UploadPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [results, setResults] = useState<any[]>([])
  const [error, setError] = useState<string | null>(null)

  const handleFilesSelected = async (files: File[]) => {
    if (files.length === 0) return

    setIsLoading(true)
    setError(null)
    setResults([])

    try {
      // Prepare documents array
      const documents = []

      // Process each file
      for (const file of files) {
        try {
          // Read the file content
          const content = await readFileAsJSON(file)

          // Validate the file content
          if (!content) {
            throw new Error(`Invalid JSON format in file: ${file.name}`)
          }

          // Validate slide types
          const validationErrors = validateSlideTypes(content, file.name)
          if (validationErrors.length > 0) {
            // Add validation errors to results
            setResults(prev => [
              ...prev,
              ...validationErrors.map(error => ({
                status: 'error',
                message: error,
              }))
            ])
            continue // Skip this file
          }

          // Add to documents array
          documents.push({
            content,
            filename: file.name
          })
        } catch (fileError: any) {
          console.error(`Error processing file ${file.name}:`, fileError)
          setResults(prev => [
            ...prev,
            {
              status: 'error',
              message: `Error processing file ${file.name}: ${fileError?.message}`,
            },
          ])
        }
      }

      if (documents.length > 0) {
        // Upload the documents
        const response = await fetch('/api/upload-json-document', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ documents }),
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.message || 'Failed to upload files')
        }

        const responseData = await response.json()

        // Add the results
        setResults(prev => [...prev, ...responseData.results])
      }
    } catch (err: any) {
      console.error('Error uploading files:', err)
      setError(err?.message || 'An error occurred while uploading files')
    } finally {
      setIsLoading(false)
    }
  }

  // Valid slide types mapping
  const validSlideTypes = {
    "Slide 1: Quick_Review": "introduction",
    "Slide 2: Learning_Goals": "introduction",
    "Slide 3: Vocabulary": "introduction",
    "Slide 4: hook_1": "hook",
    "Slide 5: hook_2": "hook",
    "Slide 6: hook_3": "hook",
    "Slide 7: teach_1_1": "teach_1",
    "Slide 8: teach_1_2": "teach_1",
    "Slide 9: teach_1_3": "teach_1",
    "Slide 10: talk_1_1": "talk_1",
    "Slide 11: talk_1_2": "talk_1",
    "Slide 12: talk_1_3": "talk_1",
    "Slide 13: try_1": "try_1",
    "Slide 14: teach_2_1": "teach_2",
    "Slide 15: teach_2_2": "teach_2",
    "Slide 16: teach_2_3": "teach_2",
    "Slide 17: talk_2_1": "talk_2",
    "Slide 18: talk_2_2": "talk_2",
    "Slide 19: talk_2_3": "talk_2",
    "Slide 20: try_2": "try_2",
    "Slide 21: practice": "practice",
    "Slide 22: on_ramp_teach_1": "on_ramp",
    "Slide 23: on_ramp_talk_1": "on_ramp",
    "Slide 24: on_ramp_try_1": "on_ramp",
    "Slide 25: Lesson_Guide_PDF_Link": "printables",
    "Slide 26: Practice_PDF_Link": "printables",
    "Slide 27: Accelerator_PDF_Link": "printables"
  }

  // Function to validate slide types
  const validateSlideTypes = (content: any, filename: string): string[] => {
    const errors: string[] = []

    for (const [slideKey, slideData] of Object.entries(content)) {
      // Skip non-slide properties
      if (!slideKey.startsWith('Slide ') || typeof slideData !== 'object' || slideData === null) {
        continue
      }

      const slide = slideData as any
      const expectedType = validSlideTypes[slideKey as keyof typeof validSlideTypes]

      if (!expectedType) {
        errors.push(`${filename}: ${slideKey} - невідомий ключ слайду`)
        continue
      }

      // Check type only (slide_pedagogical_name can be translated)
      if (!slide.type) {
        errors.push(`${filename}: ${slideKey} - відсутнє поле type`)
      } else if (slide.type !== expectedType) {
        errors.push(`${filename}: ${slideKey} - invalid slide type "${slide.type}", expected "${expectedType}"`)
      }
    }

    return errors
  }

  // Helper function to read a file as JSON
  const readFileAsJSON = (file: File): Promise<any> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()

      reader.onload = (event) => {
        try {
          const json = JSON.parse(event.target?.result as string)
          resolve(json)
        } catch (err) {
          reject(new Error(`Invalid JSON format in file: ${file.name}`))
        }
      }

      reader.onerror = () => {
        reject(new Error(`Failed to read file: ${file.name}`))
      }

      reader.readAsText(file)
    })
  }



  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-600 via-blue-500 to-cyan-400">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="mb-6">
            <Link href="/?refresh=true" className="inline-flex items-center text-white hover:text-blue-100 transition-colors">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Home
            </Link>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl p-6 md:p-8">
            <h1 className="text-2xl md:text-3xl font-bold mb-2 text-gray-900 dark:text-white">
              Upload Lesson JSON Files
            </h1>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Upload JSON files containing lesson data. Files will be stored in the database exactly as they are.
              If a document with the same unit number, lesson number, and grade level already exists, it will be updated.
            </p>

            <FileUploader onFilesSelected={handleFilesSelected} />

            {error && (
              <div className="mt-6 p-4 bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-300 rounded-lg">
                <p className="font-medium">Error</p>
                <p className="text-sm">{error}</p>
              </div>
            )}

            <UploadResults results={results} isLoading={isLoading} />
          </div>
        </div>
      </div>
    </div>
  )
}
