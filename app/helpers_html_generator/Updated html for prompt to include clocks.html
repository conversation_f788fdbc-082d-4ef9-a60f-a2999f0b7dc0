<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta http-equiv="Content-Style-Type" content="text/css">
  <title></title>
  <meta name="Generator" content="Cocoa HTML Writer">
  <meta name="CocoaVersion" content="2575.6">
  <style type="text/css">
    p.p1 {margin: 0.0px 0.0px 0.0px 0.0px; font: 14.0px <PERSON><PERSON>; color: #4689cc; -webkit-text-stroke: #4689cc}
    p.p2 {margin: 0.0px 0.0px 0.0px 0.0px; font: 14.0px <PERSON><PERSON>; color: #8cd3fe; -webkit-text-stroke: #8cd3fe}
    p.p3 {margin: 0.0px 0.0px 0.0px 0.0px; font: 14.0px <PERSON><PERSON>; color: #c27e65; -webkit-text-stroke: #c27e65}
    p.p4 {margin: 0.0px 0.0px 0.0px 0.0px; font: 14.0px Menlo; color: #cacaca; -webkit-text-stroke: #cacaca}
    p.p5 {margin: 0.0px 0.0px 0.0px 0.0px; font: 14.0px Menlo; color: #6d7378; -webkit-text-stroke: #6d7378}
    p.p6 {margin: 0.0px 0.0px 0.0px 0.0px; font: 14.0px Menlo; color: #cacaca; -webkit-text-stroke: #cacaca; min-height: 16.0px}
    p.p7 {margin: 0.0px 0.0px 0.0px 0.0px; font: 14.0px Menlo; color: #36c0a0; -webkit-text-stroke: #36c0a0}
    p.p8 {margin: 0.0px 0.0px 0.0px 0.0px; font: 14.0px Menlo; color: #cacaca; -webkit-text-stroke: #cacaca; background-color: #171818}
    span.s1 {font-kerning: none; background-color: #171818}
    span.s2 {font-kerning: none; color: #8cd3fe; background-color: #171818; -webkit-text-stroke: 0px #8cd3fe}
    span.s3 {font-kerning: none; color: #6d6d6d; background-color: #171818; -webkit-text-stroke: 0px #6d6d6d}
    span.s4 {font-kerning: none; color: #cacaca; background-color: #171818; -webkit-text-stroke: 0px #cacaca}
    span.s5 {font-kerning: none; color: #c27e65; background-color: #171818; -webkit-text-stroke: 0px #c27e65}
    span.s6 {font-kerning: none; color: #4689cc; background-color: #171818; -webkit-text-stroke: 0px #4689cc}
    span.s7 {font-kerning: none; color: #d4d4d4; background-color: #171818; -webkit-text-stroke: 0px #d4d4d4}
    span.s8 {font-kerning: none; color: #a7c598; background-color: #171818; -webkit-text-stroke: 0px #a7c598}
    span.s9 {font-kerning: none; color: #71c083; background-color: #171818; -webkit-text-stroke: 0px #71c083}
    span.s10 {font-kerning: none; color: #b76ff7; background-color: #171818; -webkit-text-stroke: 0px #b76ff7}
    span.s11 {font-kerning: none}
    span.s12 {font-kerning: none; color: #f67c30; background-color: #171818; -webkit-text-stroke: 0px #f67c30}
    span.s13 {font-kerning: none; color: #6d7378; background-color: #171818; -webkit-text-stroke: 0px #6d7378}
    span.s14 {font-kerning: none; color: #a34f83; background-color: #171818; -webkit-text-stroke: 0px #a34f83}
    span.s15 {font-kerning: none; color: #36c0a0; background-color: #171818; -webkit-text-stroke: 0px #36c0a0}
    span.s16 {font-kerning: none; color: #b76ff7; -webkit-text-stroke: 0px #b76ff7}
    span.s17 {font-kerning: none; color: #d4d4d4; -webkit-text-stroke: 0px #d4d4d4}
    span.s18 {font-kerning: none; color: #36c0a0; -webkit-text-stroke: 0px #36c0a0}
    span.s19 {font-kerning: none; color: #f67c30; -webkit-text-stroke: 0px #f67c30}
  </style>
</head>
<body>
<p class="p1"><span class="s1">&lt;!DOCTYPE</span><span class="s2"> html</span><span class="s1">&gt;</span></p>
<p class="p1"><span class="s3">&lt;</span><span class="s1">html</span><span class="s4"> </span><span class="s2">lang</span><span class="s3">=</span><span class="s5">"en"</span><span class="s3">&gt;</span></p>
<p class="p1"><span class="s3">&lt;</span><span class="s1">head</span><span class="s3">&gt;</span></p>
<p class="p2"><span class="s4"><span class="Apple-converted-space">    </span></span><span class="s3">&lt;</span><span class="s6">meta</span><span class="s4"> </span><span class="s1">charset</span><span class="s3">=</span><span class="s5">"UTF-8"</span><span class="s3">&gt;</span></p>
<p class="p3"><span class="s4"><span class="Apple-converted-space">    </span></span><span class="s3">&lt;</span><span class="s6">meta</span><span class="s4"> </span><span class="s2">name</span><span class="s3">=</span><span class="s1">"viewport"</span><span class="s4"> </span><span class="s2">content</span><span class="s3">=</span><span class="s1">"width=device-width, initial-scale=1.0"</span><span class="s3">&gt;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">    </span></span><span class="s3">&lt;</span><span class="s6">title</span><span class="s3">&gt;</span><span class="s1">Movable Canvas - Static Clock Update</span><span class="s3">&lt;/</span><span class="s6">title</span><span class="s3">&gt;</span></p>
<p class="p1"><span class="s4"><span class="Apple-converted-space">    </span></span><span class="s3">&lt;</span><span class="s1">style</span><span class="s3">&gt;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s6">body</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s2">margin:</span><span class="s1"> </span><span class="s8">0</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s2">overflow:</span><span class="s1"> </span><span class="s5">hidden</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s2">background-color:</span><span class="s1"> #f0f0f0</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s2">display:</span><span class="s1"> </span><span class="s5">flex</span><span class="s7">;</span></p>
<p class="p2"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s1">justify-content:</span><span class="s4"> </span><span class="s5">center</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s2">align-items:</span><span class="s1"> </span><span class="s5">center</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s2">height:</span><span class="s1"> </span><span class="s8">100vh</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s2">font-family:</span><span class="s1"> </span><span class="s9">'Inter'</span><span class="s7">,</span><span class="s1"> </span><span class="s5">sans-serif</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s7">}</span></p>
<p class="p1"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">#myCubeCanvas</span><span class="s4"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s2">background-color:</span><span class="s1"> #ffffff</span><span class="s7">;</span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s2">cursor:</span><span class="s4"> </span><span class="s5">default</span><span class="s7">;</span><span class="s4"> </span><span class="s1">/* Default cursor, will change on grab */</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s2">display:</span><span class="s1"> </span><span class="s5">block</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s2">width:</span><span class="s1"> </span><span class="s8">100vw</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s2">height:</span><span class="s1"> </span><span class="s8">100vh</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s7">}</span></p>
<p class="p1"><span class="s4"><span class="Apple-converted-space">    </span></span><span class="s3">&lt;/</span><span class="s1">style</span><span class="s3">&gt;</span></p>
<p class="p1"><span class="s3">&lt;/</span><span class="s1">head</span><span class="s3">&gt;</span></p>
<p class="p1"><span class="s3">&lt;</span><span class="s1">body</span><span class="s3">&gt;</span></p>
<p class="p3"><span class="s4"><span class="Apple-converted-space">    </span></span><span class="s3">&lt;</span><span class="s6">canvas</span><span class="s4"> </span><span class="s2">id</span><span class="s3">=</span><span class="s1">"myCubeCanvas"</span><span class="s3">&gt;&lt;/</span><span class="s6">canvas</span><span class="s3">&gt;</span></p>
<p class="p1"><span class="s4"><span class="Apple-converted-space">    </span></span><span class="s3">&lt;</span><span class="s1">script</span><span class="s3">&gt;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> canvas </span><span class="s7">=</span><span class="s1"> document</span><span class="s7">.</span><span class="s1">getElementById</span><span class="s7">(</span><span class="s9">'myCubeCanvas'</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> ctx </span><span class="s7">=</span><span class="s1"> canvas</span><span class="s7">.</span><span class="s1">getContext</span><span class="s7">(</span><span class="s9">'2d'</span><span class="s7">);</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">// --- Base size for a single unit cube's side ---</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">let</span><span class="s1"> baseSize </span><span class="s7">=</span><span class="s1"> </span><span class="s12">16</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">let</span><span class="s1"> canvasBgColor </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'#f0f0f0'</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> baseTenTransparency </span><span class="s7">=</span><span class="s1"> </span><span class="s12">0.75</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> geometricShapeTransparency </span><span class="s7">=</span><span class="s1"> </span><span class="s12">0.75</span><span class="s7">;</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">// --- Consistent 3D Perspective Ratio (for base ten blocks) ---</span></p>
<p class="p7"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s4"> </span><span class="s1">DEPTH_PERSPECTIVE_RATIO</span><span class="s4"> </span><span class="s7">=</span><span class="s4"> </span><span class="s12">0.5</span><span class="s7">;</span><span class="s4"><span class="Apple-converted-space"> </span></span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">// --- Color assignments (Classic Base Ten) ---</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> unitCubeColor </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'#FFEB3B'</span><span class="s7">;</span><span class="s1"> </span><span class="s13">// Yellow</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> rodColor </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'#4CAF50'</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space">      </span></span><span class="s13">// Green</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> flatColor </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'#2196F3'</span><span class="s7">;</span><span class="s1"> <span class="Apple-converted-space">    </span></span><span class="s13">// Blue</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> block1000Color </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'#F44336'</span><span class="s7">;</span><span class="s13">// Red</span></p>
<p class="p6"><span class="s1"><span class="Apple-converted-space">        </span></span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">// --- New Geometric Shapes Colors ---</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> circleColor </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'#0040DD'</span><span class="s7">;</span><span class="s1"> <span class="Apple-converted-space">      </span></span><span class="s13">// Dark Blue</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> equilateralTriangleColor </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'#FF9800'</span><span class="s7">;</span><span class="s1"> </span><span class="s13">// Orange</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> pentagonColor </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'#00BCD4'</span><span class="s7">;</span><span class="s1"> <span class="Apple-converted-space">    </span></span><span class="s13">// Cyan</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> hexagonColor </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'#E91E63'</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space">      </span></span><span class="s13">// Pink</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> squareColor </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'#9E9E9E'</span><span class="s7">;</span><span class="s1"> <span class="Apple-converted-space">      </span></span><span class="s13">// Grey</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> parallelogramColor </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'#673AB7'</span><span class="s7">;</span><span class="s1"> </span><span class="s13">// Deep Purple</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> rhombusColor </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'#00796B'</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space">      </span></span><span class="s13">// Dark Teal</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> rightTriangleColor </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'#CDDC39'</span><span class="s7">;</span><span class="s13">// Lime</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> diamondColor </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'#795548'</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space">      </span></span><span class="s13">// Brown (for Kite)</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> octagonColor </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'#3F51B5'</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space">      </span></span><span class="s13">// Indigo</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> clockFaceColor </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'#FFFFFF'</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space">    </span></span><span class="s13">// White for clock face</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">// --- Line Styling ---</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> mainOutlineColor </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'#000000'</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> mainOutlineWidth </span><span class="s7">=</span><span class="s1"> </span><span class="s12">1.5</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space">       </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> internalLineColor </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'#000000'</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> internalLineWidth </span><span class="s7">=</span><span class="s1"> </span><span class="s12">1</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space">         </span></span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">// --- Shadow Styling for 2D shapes ---</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> shadowOffsetX </span><span class="s7">=</span><span class="s1"> </span><span class="s12">3</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> shadowOffsetY </span><span class="s7">=</span><span class="s1"> </span><span class="s12">3</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> shadowBlur </span><span class="s7">=</span><span class="s1"> </span><span class="s12">5</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> shadowColor </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'rgba(0, 0, 0, 0.4)'</span><span class="s7">;</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">// --- Rod (Tens) appearance ---</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> rodLengthUnits </span><span class="s7">=</span><span class="s1"> </span><span class="s12">10</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> rodDisplaySegmentsCount </span><span class="s7">=</span><span class="s1"> </span><span class="s12">10</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">// --- Flat (Hundreds) appearance ---</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> flatSideUnits </span><span class="s7">=</span><span class="s1"> </span><span class="s12">10</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> flatDisplaySegmentsCount </span><span class="s7">=</span><span class="s1"> </span><span class="s12">10</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">// --- 1000s Block appearance ---</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> block1000SideUnits </span><span class="s7">=</span><span class="s1"> </span><span class="s12">10</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> block1000GridSegments </span><span class="s7">=</span><span class="s1"> </span><span class="s12">10</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">// --- Data for shapes ---</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">let</span><span class="s1"> unitCube </span><span class="s7">=</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> x</span><span class="s7">:</span><span class="s1"> </span><span class="s12">0</span><span class="s7">,</span><span class="s1"> y</span><span class="s7">:</span><span class="s1"> </span><span class="s12">0</span><span class="s7">,</span><span class="s1"> size</span><span class="s7">:</span><span class="s1"> baseSize</span><span class="s7">,</span><span class="s1"> color</span><span class="s7">:</span><span class="s1"> unitCubeColor</span><span class="s7">,</span><span class="s1"> type</span><span class="s7">:</span><span class="s1"> </span><span class="s9">'cube'</span><span class="s1"> </span><span class="s7">};</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">let</span><span class="s1"> rod </span><span class="s7">=</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> x</span><span class="s7">:</span><span class="s1"> </span><span class="s12">0</span><span class="s7">,</span><span class="s1"> y</span><span class="s7">:</span><span class="s1"> </span><span class="s12">0</span><span class="s7">,</span><span class="s1"> length</span><span class="s7">:</span><span class="s1"> </span><span class="s12">0</span><span class="s7">,</span><span class="s1"> height</span><span class="s7">:</span><span class="s1"> </span><span class="s12">0</span><span class="s7">,</span><span class="s1"> depth</span><span class="s7">:</span><span class="s1"> </span><span class="s12">0</span><span class="s7">,</span><span class="s1"> color</span><span class="s7">:</span><span class="s1"> rodColor</span><span class="s7">,</span><span class="s1"> type</span><span class="s7">:</span><span class="s1"> </span><span class="s9">'rod'</span><span class="s1"> </span><span class="s7">};</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">let</span><span class="s1"> flat </span><span class="s7">=</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> x</span><span class="s7">:</span><span class="s1"> </span><span class="s12">0</span><span class="s7">,</span><span class="s1"> y</span><span class="s7">:</span><span class="s1"> </span><span class="s12">0</span><span class="s7">,</span><span class="s1"> sideLength</span><span class="s7">:</span><span class="s1"> </span><span class="s12">0</span><span class="s7">,</span><span class="s1"> thickness</span><span class="s7">:</span><span class="s1"> </span><span class="s12">0</span><span class="s7">,</span><span class="s1"> color</span><span class="s7">:</span><span class="s1"> flatColor</span><span class="s7">,</span><span class="s1"> type</span><span class="s7">:</span><span class="s1"> </span><span class="s9">'flat'</span><span class="s1"> </span><span class="s7">};</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">let</span><span class="s1"> block1000 </span><span class="s7">=</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> x</span><span class="s7">:</span><span class="s1"> </span><span class="s12">0</span><span class="s7">,</span><span class="s1"> y</span><span class="s7">:</span><span class="s1"> </span><span class="s12">0</span><span class="s7">,</span><span class="s1"> sideLength</span><span class="s7">:</span><span class="s1"> </span><span class="s12">0</span><span class="s7">,</span><span class="s1"> color</span><span class="s7">:</span><span class="s1"> block1000Color</span><span class="s7">,</span><span class="s1"> type</span><span class="s7">:</span><span class="s1"> </span><span class="s9">'block1000'</span><span class="s1"> </span><span class="s7">};</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">let</span><span class="s1"> circle </span><span class="s7">=</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> x</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> y</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> radius</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> color</span><span class="s7">:</span><span class="s1"> circleColor</span><span class="s7">,</span><span class="s1"> type</span><span class="s7">:</span><span class="s1"> </span><span class="s9">'circle'</span><span class="s1"> </span><span class="s7">};</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">let</span><span class="s1"> equilateralTriangle </span><span class="s7">=</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> x</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> y</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> sideLength</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> color</span><span class="s7">:</span><span class="s1"> equilateralTriangleColor</span><span class="s7">,</span><span class="s1"> type</span><span class="s7">:</span><span class="s1"> </span><span class="s9">'equilateralTriangle'</span><span class="s1"> </span><span class="s7">};</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">let</span><span class="s1"> pentagon </span><span class="s7">=</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> x</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> y</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> sideLength</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> color</span><span class="s7">:</span><span class="s1"> pentagonColor</span><span class="s7">,</span><span class="s1"> type</span><span class="s7">:</span><span class="s1"> </span><span class="s9">'pentagon'</span><span class="s1"> </span><span class="s7">};</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">let</span><span class="s1"> hexagon </span><span class="s7">=</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> x</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> y</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> sideLength</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> color</span><span class="s7">:</span><span class="s1"> hexagonColor</span><span class="s7">,</span><span class="s1"> type</span><span class="s7">:</span><span class="s1"> </span><span class="s9">'hexagon'</span><span class="s1"> </span><span class="s7">};</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">let</span><span class="s1"> square </span><span class="s7">=</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> x</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> y</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> sideLength</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> color</span><span class="s7">:</span><span class="s1"> squareColor</span><span class="s7">,</span><span class="s1"> type</span><span class="s7">:</span><span class="s1"> </span><span class="s9">'square'</span><span class="s1"> </span><span class="s7">};</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">let</span><span class="s1"> parallelogram </span><span class="s7">=</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> x</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> y</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> width</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> height</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> skew</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> color</span><span class="s7">:</span><span class="s1"> parallelogramColor</span><span class="s7">,</span><span class="s1"> type</span><span class="s7">:</span><span class="s1"> </span><span class="s9">'parallelogram'</span><span class="s1"> </span><span class="s7">};</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">let</span><span class="s1"> rhombus </span><span class="s7">=</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> x</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> y</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> d1</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> d2</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> color</span><span class="s7">:</span><span class="s1"> rhombusColor</span><span class="s7">,</span><span class="s1"> type</span><span class="s7">:</span><span class="s1"> </span><span class="s9">'rhombus'</span><span class="s1"> </span><span class="s7">};</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">let</span><span class="s1"> rightTriangle </span><span class="s7">=</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> x</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> y</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> base</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> height</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> color</span><span class="s7">:</span><span class="s1"> rightTriangleColor</span><span class="s7">,</span><span class="s1"> type</span><span class="s7">:</span><span class="s1"> </span><span class="s9">'rightTriangle'</span><span class="s1"> </span><span class="s7">};</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">let</span><span class="s1"> diamond </span><span class="s7">=</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> x</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> y</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> width</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> height</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> color</span><span class="s7">:</span><span class="s1"> diamondColor</span><span class="s7">,</span><span class="s1"> type</span><span class="s7">:</span><span class="s1"> </span><span class="s9">'diamond'</span><span class="s1"> </span><span class="s7">};</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">let</span><span class="s1"> octagon </span><span class="s7">=</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> x</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> y</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> sideLength</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> color</span><span class="s7">:</span><span class="s1"> octagonColor</span><span class="s7">,</span><span class="s1"> type</span><span class="s7">:</span><span class="s1"> </span><span class="s9">'octagon'</span><span class="s1"> </span><span class="s7">};</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">let</span><span class="s1"> clock </span><span class="s7">=</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> x</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> y</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> radius</span><span class="s7">:</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> color</span><span class="s7">:</span><span class="s1"> clockFaceColor</span><span class="s7">,</span><span class="s1"> type</span><span class="s7">:</span><span class="s1"> </span><span class="s9">'clock'</span><span class="s7">};</span></p>
<p class="p6"><span class="s1"><span class="Apple-converted-space">        </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> allDrawableItems </span><span class="s7">=</span><span class="s1"> </span><span class="s7">[</span><span class="s1">unitCube</span><span class="s7">,</span><span class="s1"> rod</span><span class="s7">,</span><span class="s1"> flat</span><span class="s7">,</span><span class="s1"> block1000</span><span class="s7">,</span><span class="s1"> circle</span><span class="s7">,</span><span class="s1"> equilateralTriangle</span><span class="s7">,</span><span class="s1"> pentagon</span><span class="s7">,</span><span class="s1"> hexagon</span><span class="s7">,</span><span class="s1"> square</span><span class="s7">,</span><span class="s1"> parallelogram</span><span class="s7">,</span><span class="s1"> rhombus</span><span class="s7">,</span><span class="s1"> rightTriangle</span><span class="s7">,</span><span class="s1"> diamond</span><span class="s7">,</span><span class="s1"> octagon</span><span class="s7">,</span><span class="s1"> clock</span><span class="s7">];</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">// --- Dragging and Selection variables ---</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">let</span><span class="s1"> isDragging </span><span class="s7">=</span><span class="s1"> </span><span class="s10">false</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">let</span><span class="s1"> isLassoing </span><span class="s7">=</span><span class="s1"> </span><span class="s10">false</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">let</span><span class="s1"> draggedItem </span><span class="s7">=</span><span class="s1"> </span><span class="s10">null</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">let</span><span class="s1"> selectedItems </span><span class="s7">=</span><span class="s1"> </span><span class="s7">[];</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">let</span><span class="s1"> lassoRect </span><span class="s7">=</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> startX</span><span class="s7">:</span><span class="s1"> </span><span class="s12">0</span><span class="s7">,</span><span class="s1"> startY</span><span class="s7">:</span><span class="s1"> </span><span class="s12">0</span><span class="s7">,</span><span class="s1"> currentX</span><span class="s7">:</span><span class="s1"> </span><span class="s12">0</span><span class="s7">,</span><span class="s1"> currentY</span><span class="s7">:</span><span class="s1"> </span><span class="s12">0</span><span class="s7">,</span><span class="s1"> active</span><span class="s7">:</span><span class="s1"> </span><span class="s10">false</span><span class="s1"> </span><span class="s7">};</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">let</span><span class="s1"> dragStartOffsets </span><span class="s7">=</span><span class="s1"> </span><span class="s7">[];</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">let</span><span class="s1"> lastMouseX</span><span class="s7">,</span><span class="s1"> lastMouseY</span><span class="s7">;</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">// --- Variables to store old canvas dimensions for resize normalization ---</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">let</span><span class="s1"> oldCanvasWidth </span><span class="s7">=</span><span class="s1"> </span><span class="s12">0</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">let</span><span class="s1"> oldCanvasHeight </span><span class="s7">=</span><span class="s1"> </span><span class="s12">0</span><span class="s7">;</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">// --- Helper functions for color manipulation (unchanged) ---</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">function</span><span class="s1"> hexToRgb</span><span class="s7">(</span><span class="s1">hex</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> </span><span class="s10">const</span><span class="s1"> result </span><span class="s7">=</span><span class="s1"> </span><span class="s14">/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/</span><span class="s10">i</span><span class="s7">.</span><span class="s1">exec</span><span class="s7">(</span><span class="s1">hex</span><span class="s7">);</span><span class="s1"> </span><span class="s10">return</span><span class="s1"> result </span><span class="s7">?</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> r</span><span class="s7">:</span><span class="s1"> parseInt</span><span class="s7">(</span><span class="s1">result</span><span class="s7">[</span><span class="s12">1</span><span class="s7">],</span><span class="s1"> </span><span class="s12">16</span><span class="s7">),</span><span class="s1"> g</span><span class="s7">:</span><span class="s1"> parseInt</span><span class="s7">(</span><span class="s1">result</span><span class="s7">[</span><span class="s12">2</span><span class="s7">],</span><span class="s1"> </span><span class="s12">16</span><span class="s7">),</span><span class="s1"> b</span><span class="s7">:</span><span class="s1"> parseInt</span><span class="s7">(</span><span class="s1">result</span><span class="s7">[</span><span class="s12">3</span><span class="s7">],</span><span class="s1"> </span><span class="s12">16</span><span class="s7">)</span><span class="s1"> </span><span class="s7">}</span><span class="s1"> </span><span class="s7">:</span><span class="s1"> </span><span class="s10">null</span><span class="s7">;}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">function</span><span class="s1"> rgbToHex</span><span class="s7">(</span><span class="s1">r</span><span class="s7">,</span><span class="s1"> g</span><span class="s7">,</span><span class="s1"> b</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> </span><span class="s10">return</span><span class="s1"> </span><span class="s9">"#"</span><span class="s1"> </span><span class="s7">+</span><span class="s1"> </span><span class="s7">((</span><span class="s12">1</span><span class="s1"> </span><span class="s7">&lt;&lt;</span><span class="s1"> </span><span class="s12">24</span><span class="s7">)</span><span class="s1"> </span><span class="s7">+</span><span class="s1"> </span><span class="s7">(</span><span class="s1">r </span><span class="s7">&lt;&lt;</span><span class="s1"> </span><span class="s12">16</span><span class="s7">)</span><span class="s1"> </span><span class="s7">+</span><span class="s1"> </span><span class="s7">(</span><span class="s1">g </span><span class="s7">&lt;&lt;</span><span class="s1"> </span><span class="s12">8</span><span class="s7">)</span><span class="s1"> </span><span class="s7">+</span><span class="s1"> b</span><span class="s7">).</span><span class="s1">toString</span><span class="s7">(</span><span class="s12">16</span><span class="s7">).</span><span class="s1">slice</span><span class="s7">(</span><span class="s12">1</span><span class="s7">).</span><span class="s1">padStart</span><span class="s7">(</span><span class="s12">6</span><span class="s7">,</span><span class="s1"> </span><span class="s9">'0'</span><span class="s7">);}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">function</span><span class="s1"> lightenColor</span><span class="s7">(</span><span class="s1">hex</span><span class="s7">,</span><span class="s1"> percent</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> </span><span class="s10">const</span><span class="s1"> rgb </span><span class="s7">=</span><span class="s1"> hexToRgb</span><span class="s7">(</span><span class="s1">hex</span><span class="s7">);</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(!</span><span class="s1">rgb</span><span class="s7">)</span><span class="s1"> </span><span class="s10">return</span><span class="s1"> hex</span><span class="s7">;</span><span class="s1"> </span><span class="s10">const</span><span class="s1"> factor </span><span class="s7">=</span><span class="s1"> percent </span><span class="s7">/</span><span class="s1"> </span><span class="s12">100</span><span class="s7">;</span><span class="s1"> rgb</span><span class="s7">.</span><span class="s1">r </span><span class="s7">=</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">min</span><span class="s7">(</span><span class="s12">255</span><span class="s7">,</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">max</span><span class="s7">(</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">round</span><span class="s7">(</span><span class="s1">rgb</span><span class="s7">.</span><span class="s1">r </span><span class="s7">+</span><span class="s1"> </span><span class="s7">(</span><span class="s12">255</span><span class="s1"> </span><span class="s7">-</span><span class="s1"> rgb</span><span class="s7">.</span><span class="s1">r</span><span class="s7">)</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> factor</span><span class="s7">)));</span><span class="s1"> rgb</span><span class="s7">.</span><span class="s1">g </span><span class="s7">=</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">min</span><span class="s7">(</span><span class="s12">255</span><span class="s7">,</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">max</span><span class="s7">(</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">round</span><span class="s7">(</span><span class="s1">rgb</span><span class="s7">.</span><span class="s1">g </span><span class="s7">+</span><span class="s1"> </span><span class="s7">(</span><span class="s12">255</span><span class="s1"> </span><span class="s7">-</span><span class="s1"> rgb</span><span class="s7">.</span><span class="s1">g</span><span class="s7">)</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> factor</span><span class="s7">)));</span><span class="s1"> rgb</span><span class="s7">.</span><span class="s1">b </span><span class="s7">=</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">min</span><span class="s7">(</span><span class="s12">255</span><span class="s7">,</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">max</span><span class="s7">(</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">round</span><span class="s7">(</span><span class="s1">rgb</span><span class="s7">.</span><span class="s1">b </span><span class="s7">+</span><span class="s1"> </span><span class="s7">(</span><span class="s12">255</span><span class="s1"> </span><span class="s7">-</span><span class="s1"> rgb</span><span class="s7">.</span><span class="s1">b</span><span class="s7">)</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> factor</span><span class="s7">)));</span><span class="s1"> </span><span class="s10">return</span><span class="s1"> rgbToHex</span><span class="s7">(</span><span class="s1">rgb</span><span class="s7">.</span><span class="s1">r</span><span class="s7">,</span><span class="s1"> rgb</span><span class="s7">.</span><span class="s1">g</span><span class="s7">,</span><span class="s1"> rgb</span><span class="s7">.</span><span class="s1">b</span><span class="s7">);}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">function</span><span class="s1"> darkenColor</span><span class="s7">(</span><span class="s1">hex</span><span class="s7">,</span><span class="s1"> percent</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> </span><span class="s10">const</span><span class="s1"> rgb </span><span class="s7">=</span><span class="s1"> hexToRgb</span><span class="s7">(</span><span class="s1">hex</span><span class="s7">);</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(!</span><span class="s1">rgb</span><span class="s7">)</span><span class="s1"> </span><span class="s10">return</span><span class="s1"> hex</span><span class="s7">;</span><span class="s1"> </span><span class="s10">const</span><span class="s1"> factor </span><span class="s7">=</span><span class="s1"> percent </span><span class="s7">/</span><span class="s1"> </span><span class="s12">100</span><span class="s7">;</span><span class="s1"> rgb</span><span class="s7">.</span><span class="s1">r </span><span class="s7">=</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">min</span><span class="s7">(</span><span class="s12">255</span><span class="s7">,</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">max</span><span class="s7">(</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">round</span><span class="s7">(</span><span class="s1">rgb</span><span class="s7">.</span><span class="s1">r </span><span class="s7">*</span><span class="s1"> </span><span class="s7">(</span><span class="s12">1</span><span class="s1"> </span><span class="s7">-</span><span class="s1"> factor</span><span class="s7">))));</span><span class="s1"> rgb</span><span class="s7">.</span><span class="s1">g </span><span class="s7">=</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">min</span><span class="s7">(</span><span class="s12">255</span><span class="s7">,</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">max</span><span class="s7">(</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">round</span><span class="s7">(</span><span class="s1">rgb</span><span class="s7">.</span><span class="s1">g </span><span class="s7">*</span><span class="s1"> </span><span class="s7">(</span><span class="s12">1</span><span class="s1"> </span><span class="s7">-</span><span class="s1"> factor</span><span class="s7">))));</span><span class="s1"> rgb</span><span class="s7">.</span><span class="s1">b </span><span class="s7">=</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">min</span><span class="s7">(</span><span class="s12">255</span><span class="s7">,</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">max</span><span class="s7">(</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">round</span><span class="s7">(</span><span class="s1">rgb</span><span class="s7">.</span><span class="s1">b </span><span class="s7">*</span><span class="s1"> </span><span class="s7">(</span><span class="s12">1</span><span class="s1"> </span><span class="s7">-</span><span class="s1"> factor</span><span class="s7">))));</span><span class="s1"> </span><span class="s10">return</span><span class="s1"> rgbToHex</span><span class="s7">(</span><span class="s1">rgb</span><span class="s7">.</span><span class="s1">r</span><span class="s7">,</span><span class="s1"> rgb</span><span class="s7">.</span><span class="s1">g</span><span class="s7">,</span><span class="s1"> rgb</span><span class="s7">.</span><span class="s1">b</span><span class="s7">);}</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">// --- Render Base Ten Blocks (unchanged) ---</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">function</span><span class="s1"> renderUnitCube</span><span class="s7">(</span><span class="s1">cube</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">save</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">globalAlpha </span><span class="s7">=</span><span class="s1"> baseTenTransparency</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">translate</span><span class="s7">(</span><span class="s1">cube</span><span class="s7">.</span><span class="s1">x</span><span class="s7">,</span><span class="s1"> cube</span><span class="s7">.</span><span class="s1">y</span><span class="s7">);</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">strokeStyle </span><span class="s7">=</span><span class="s1"> mainOutlineColor</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">lineWidth </span><span class="s7">=</span><span class="s1"> mainOutlineWidth</span><span class="s7">;</span><span class="s1"> </span><span class="s10">const</span><span class="s1"> faceSize </span><span class="s7">=</span><span class="s1"> cube</span><span class="s7">.</span><span class="s1">size</span><span class="s7">;</span><span class="s1"> </span><span class="s10">const</span><span class="s1"> po </span><span class="s7">=</span><span class="s1"> faceSize </span><span class="s7">*</span><span class="s1"> </span><span class="s15">DEPTH_PERSPECTIVE_RATIO</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">beginPath</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">fillStyle </span><span class="s7">=</span><span class="s1"> cube</span><span class="s7">.</span><span class="s1">color</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">rect</span><span class="s7">(-</span><span class="s1">faceSize </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s7">,</span><span class="s1"> </span><span class="s7">-</span><span class="s1">faceSize </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s7">,</span><span class="s1"> faceSize</span><span class="s7">,</span><span class="s1"> faceSize</span><span class="s7">);</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">fill</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">stroke</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">beginPath</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">fillStyle </span><span class="s7">=</span><span class="s1"> lightenColor</span><span class="s7">(</span><span class="s1">cube</span><span class="s7">.</span><span class="s1">color</span><span class="s7">,</span><span class="s1"> </span><span class="s12">20</span><span class="s7">);</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">moveTo</span><span class="s7">(-</span><span class="s1">faceSize </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s7">,</span><span class="s1"> </span><span class="s7">-</span><span class="s1">faceSize </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s7">);</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">lineTo</span><span class="s7">(-</span><span class="s1">faceSize </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s1"> </span><span class="s7">+</span><span class="s1"> po</span><span class="s7">,</span><span class="s1"> </span><span class="s7">-</span><span class="s1">faceSize </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s1"> </span><span class="s7">-</span><span class="s1"> po</span><span class="s7">);</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">lineTo</span><span class="s7">(</span><span class="s1">faceSize </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s1"> </span><span class="s7">+</span><span class="s1"> po</span><span class="s7">,</span><span class="s1"> </span><span class="s7">-</span><span class="s1">faceSize </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s1"> </span><span class="s7">-</span><span class="s1"> po</span><span class="s7">);</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">lineTo</span><span class="s7">(</span><span class="s1">faceSize </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s7">,</span><span class="s1"> </span><span class="s7">-</span><span class="s1">faceSize </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s7">);</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">closePath</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">fill</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">stroke</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">beginPath</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">fillStyle </span><span class="s7">=</span><span class="s1"> darkenColor</span><span class="s7">(</span><span class="s1">cube</span><span class="s7">.</span><span class="s1">color</span><span class="s7">,</span><span class="s1"> </span><span class="s12">20</span><span class="s7">);</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">moveTo</span><span class="s7">(</span><span class="s1">faceSize </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s7">,</span><span class="s1"> </span><span class="s7">-</span><span class="s1">faceSize </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s7">);</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">lineTo</span><span class="s7">(</span><span class="s1">faceSize </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s1"> </span><span class="s7">+</span><span class="s1"> po</span><span class="s7">,</span><span class="s1"> </span><span class="s7">-</span><span class="s1">faceSize </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s1"> </span><span class="s7">-</span><span class="s1"> po</span><span class="s7">);</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">lineTo</span><span class="s7">(</span><span class="s1">faceSize </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s1"> </span><span class="s7">+</span><span class="s1"> po</span><span class="s7">,</span><span class="s1"> faceSize </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s1"> </span><span class="s7">-</span><span class="s1"> po</span><span class="s7">);</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">lineTo</span><span class="s7">(</span><span class="s1">faceSize </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s7">,</span><span class="s1"> faceSize </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s7">);</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">closePath</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">fill</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">stroke</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">globalAlpha </span><span class="s7">=</span><span class="s1"> </span><span class="s12">1.0</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">restore</span><span class="s7">();</span><span class="s1"> </span><span class="s7">}</span></p>
<p class="p8"><span class="s11"><span class="Apple-converted-space">        </span></span><span class="s16">function</span><span class="s11"> renderRod</span><span class="s17">(</span><span class="s11">rodObject</span><span class="s17">)</span><span class="s11"> </span><span class="s17">{</span><span class="s11"> </span><span class="s16">const</span><span class="s11"> x </span><span class="s17">=</span><span class="s11"> rodObject</span><span class="s17">.</span><span class="s11">x</span><span class="s17">,</span><span class="s11"> y </span><span class="s17">=</span><span class="s11"> rodObject</span><span class="s17">.</span><span class="s11">y</span><span class="s17">,</span><span class="s11"> w </span><span class="s17">=</span><span class="s11"> rodObject</span><span class="s17">.</span><span class="s11">length</span><span class="s17">,</span><span class="s11"> h </span><span class="s17">=</span><span class="s11"> rodObject</span><span class="s17">.</span><span class="s11">height</span><span class="s17">,</span><span class="s11"> c </span><span class="s17">=</span><span class="s11"> rodObject</span><span class="s17">.</span><span class="s11">color</span><span class="s17">,</span><span class="s11"> d </span><span class="s17">=</span><span class="s11"> rodObject</span><span class="s17">.</span><span class="s11">depth</span><span class="s17">;</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">save</span><span class="s17">();</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">globalAlpha </span><span class="s17">=</span><span class="s11"> baseTenTransparency</span><span class="s17">;</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">translate</span><span class="s17">(</span><span class="s11">x</span><span class="s17">,</span><span class="s11">y</span><span class="s17">);</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">strokeStyle </span><span class="s17">=</span><span class="s11"> mainOutlineColor</span><span class="s17">;</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">lineWidth </span><span class="s17">=</span><span class="s11"> mainOutlineWidth</span><span class="s17">;</span><span class="s11"> </span><span class="s16">const</span><span class="s11"> po </span><span class="s17">=</span><span class="s11"> d </span><span class="s17">*</span><span class="s11"> </span><span class="s18">DEPTH_PERSPECTIVE_RATIO</span><span class="s17">;</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">beginPath</span><span class="s17">();</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">fillStyle </span><span class="s17">=</span><span class="s11"> c</span><span class="s17">;</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">rect</span><span class="s17">(-</span><span class="s11">w</span><span class="s17">/</span><span class="s19">2</span><span class="s17">,</span><span class="s11"> </span><span class="s17">-</span><span class="s11">h</span><span class="s17">/</span><span class="s19">2</span><span class="s17">,</span><span class="s11"> w</span><span class="s17">,</span><span class="s11"> h</span><span class="s17">);</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">fill</span><span class="s17">();</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">stroke</span><span class="s17">();</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">strokeStyle </span><span class="s17">=</span><span class="s11"> internalLineColor</span><span class="s17">;</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">lineWidth </span><span class="s17">=</span><span class="s11"> internalLineWidth</span><span class="s17">;</span><span class="s11"> </span><span class="s16">let</span><span class="s11"> segW </span><span class="s17">=</span><span class="s11"> w </span><span class="s17">/</span><span class="s11"> rodDisplaySegmentsCount</span><span class="s17">;</span><span class="s11"> </span><span class="s16">for</span><span class="s11"> </span><span class="s17">(</span><span class="s16">let</span><span class="s11"> k</span><span class="s17">=</span><span class="s19">1</span><span class="s17">;</span><span class="s11"> k</span><span class="s17">&lt;</span><span class="s11">rodDisplaySegmentsCount</span><span class="s17">;</span><span class="s11"> k</span><span class="s17">++)</span><span class="s11"> </span><span class="s17">{</span><span class="s11"> </span><span class="s16">const</span><span class="s11"> lx </span><span class="s17">=</span><span class="s11"> </span><span class="s17">-</span><span class="s11">w</span><span class="s17">/</span><span class="s19">2</span><span class="s11"> </span><span class="s17">+</span><span class="s11"> k</span><span class="s17">*</span><span class="s11">segW</span><span class="s17">;</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">beginPath</span><span class="s17">();</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">moveTo</span><span class="s17">(</span><span class="s11">lx</span><span class="s17">,</span><span class="s11"> </span><span class="s17">-</span><span class="s11">h</span><span class="s17">/</span><span class="s19">2</span><span class="s17">);</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">lineTo</span><span class="s17">(</span><span class="s11">lx</span><span class="s17">,</span><span class="s11"> h</span><span class="s17">/</span><span class="s19">2</span><span class="s17">);</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">stroke</span><span class="s17">();</span><span class="s11"> </span><span class="s17">}</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">strokeStyle </span><span class="s17">=</span><span class="s11"> mainOutlineColor</span><span class="s17">;</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">lineWidth </span><span class="s17">=</span><span class="s11"> mainOutlineWidth</span><span class="s17">;</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">beginPath</span><span class="s17">();</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">fillStyle </span><span class="s17">=</span><span class="s11"> lightenColor</span><span class="s17">(</span><span class="s11">c</span><span class="s17">,</span><span class="s19">20</span><span class="s17">);</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">moveTo</span><span class="s17">(-</span><span class="s11">w</span><span class="s17">/</span><span class="s19">2</span><span class="s17">,</span><span class="s11"> </span><span class="s17">-</span><span class="s11">h</span><span class="s17">/</span><span class="s19">2</span><span class="s17">);</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">lineTo</span><span class="s17">(-</span><span class="s11">w</span><span class="s17">/</span><span class="s19">2</span><span class="s17">+</span><span class="s11">po</span><span class="s17">,</span><span class="s11"> </span><span class="s17">-</span><span class="s11">h</span><span class="s17">/</span><span class="s19">2</span><span class="s17">-</span><span class="s11">po</span><span class="s17">);</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">lineTo</span><span class="s17">(</span><span class="s11">w</span><span class="s17">/</span><span class="s19">2</span><span class="s17">+</span><span class="s11">po</span><span class="s17">,</span><span class="s11"> </span><span class="s17">-</span><span class="s11">h</span><span class="s17">/</span><span class="s19">2</span><span class="s17">-</span><span class="s11">po</span><span class="s17">);</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">lineTo</span><span class="s17">(</span><span class="s11">w</span><span class="s17">/</span><span class="s19">2</span><span class="s17">,</span><span class="s11"> </span><span class="s17">-</span><span class="s11">h</span><span class="s17">/</span><span class="s19">2</span><span class="s17">);</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">closePath</span><span class="s17">();</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">fill</span><span class="s17">();</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">stroke</span><span class="s17">();</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">strokeStyle </span><span class="s17">=</span><span class="s11"> internalLineColor</span><span class="s17">;</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">lineWidth </span><span class="s17">=</span><span class="s11"> internalLineWidth</span><span class="s17">;</span><span class="s11"> </span><span class="s16">for</span><span class="s11"> </span><span class="s17">(</span><span class="s16">let</span><span class="s11"> k</span><span class="s17">=</span><span class="s19">1</span><span class="s17">;</span><span class="s11"> k</span><span class="s17">&lt;</span><span class="s11">rodDisplaySegmentsCount</span><span class="s17">;</span><span class="s11"> k</span><span class="s17">++)</span><span class="s11"> </span><span class="s17">{</span><span class="s11"> </span><span class="s16">const</span><span class="s11"> p1x </span><span class="s17">=</span><span class="s11"> </span><span class="s17">-</span><span class="s11">w</span><span class="s17">/</span><span class="s19">2</span><span class="s11"> </span><span class="s17">+</span><span class="s11"> k</span><span class="s17">*</span><span class="s11">segW</span><span class="s17">,</span><span class="s11"> p1y </span><span class="s17">=</span><span class="s11"> </span><span class="s17">-</span><span class="s11">h</span><span class="s17">/</span><span class="s19">2</span><span class="s17">,</span><span class="s11"> p2x </span><span class="s17">=</span><span class="s11"> </span><span class="s17">-</span><span class="s11">w</span><span class="s17">/</span><span class="s19">2</span><span class="s17">+</span><span class="s11">po </span><span class="s17">+</span><span class="s11"> k</span><span class="s17">*</span><span class="s11">segW</span><span class="s17">,</span><span class="s11"> p2y </span><span class="s17">=</span><span class="s11"> </span><span class="s17">-</span><span class="s11">h</span><span class="s17">/</span><span class="s19">2</span><span class="s17">-</span><span class="s11">po</span><span class="s17">;</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">beginPath</span><span class="s17">();</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">moveTo</span><span class="s17">(</span><span class="s11">p1x</span><span class="s17">,</span><span class="s11">p1y</span><span class="s17">);</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">lineTo</span><span class="s17">(</span><span class="s11">p2x</span><span class="s17">,</span><span class="s11">p2y</span><span class="s17">);</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">stroke</span><span class="s17">();</span><span class="s11"> </span><span class="s17">}</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">strokeStyle </span><span class="s17">=</span><span class="s11"> mainOutlineColor</span><span class="s17">;</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">lineWidth </span><span class="s17">=</span><span class="s11"> mainOutlineWidth</span><span class="s17">;</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">beginPath</span><span class="s17">();</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">fillStyle </span><span class="s17">=</span><span class="s11"> darkenColor</span><span class="s17">(</span><span class="s11">c</span><span class="s17">,</span><span class="s19">20</span><span class="s17">);</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">moveTo</span><span class="s17">(</span><span class="s11">w</span><span class="s17">/</span><span class="s19">2</span><span class="s17">,</span><span class="s11"> </span><span class="s17">-</span><span class="s11">h</span><span class="s17">/</span><span class="s19">2</span><span class="s17">);</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">lineTo</span><span class="s17">(</span><span class="s11">w</span><span class="s17">/</span><span class="s19">2</span><span class="s17">+</span><span class="s11">po</span><span class="s17">,</span><span class="s11"> </span><span class="s17">-</span><span class="s11">h</span><span class="s17">/</span><span class="s19">2</span><span class="s17">-</span><span class="s11">po</span><span class="s17">);</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">lineTo</span><span class="s17">(</span><span class="s11">w</span><span class="s17">/</span><span class="s19">2</span><span class="s17">+</span><span class="s11">po</span><span class="s17">,</span><span class="s11"> h</span><span class="s17">/</span><span class="s19">2</span><span class="s17">-</span><span class="s11">po</span><span class="s17">);</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">lineTo</span><span class="s17">(</span><span class="s11">w</span><span class="s17">/</span><span class="s19">2</span><span class="s17">,</span><span class="s11"> h</span><span class="s17">/</span><span class="s19">2</span><span class="s17">);</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">closePath</span><span class="s17">();</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">fill</span><span class="s17">();</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">stroke</span><span class="s17">();</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">globalAlpha </span><span class="s17">=</span><span class="s11"> </span><span class="s19">1.0</span><span class="s17">;</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">restore</span><span class="s17">();</span><span class="s11"> </span><span class="s17">}</span></p>
<p class="p8"><span class="s11"><span class="Apple-converted-space">        </span></span><span class="s16">function</span><span class="s11"> renderFlat</span><span class="s17">(</span><span class="s11">flatObject</span><span class="s17">)</span><span class="s11"> </span><span class="s17">{</span><span class="s11"> </span><span class="s16">const</span><span class="s11"> x </span><span class="s17">=</span><span class="s11"> flatObject</span><span class="s17">.</span><span class="s11">x</span><span class="s17">,</span><span class="s11"> y </span><span class="s17">=</span><span class="s11"> flatObject</span><span class="s17">.</span><span class="s11">y</span><span class="s17">,</span><span class="s11"> s </span><span class="s17">=</span><span class="s11"> flatObject</span><span class="s17">.</span><span class="s11">sideLength</span><span class="s17">,</span><span class="s11"> th </span><span class="s17">=</span><span class="s11"> flatObject</span><span class="s17">.</span><span class="s11">thickness</span><span class="s17">,</span><span class="s11"> c </span><span class="s17">=</span><span class="s11"> flatObject</span><span class="s17">.</span><span class="s11">color</span><span class="s17">;</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">save</span><span class="s17">();</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">globalAlpha </span><span class="s17">=</span><span class="s11"> baseTenTransparency</span><span class="s17">;</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">translate</span><span class="s17">(</span><span class="s11">x</span><span class="s17">,</span><span class="s11">y</span><span class="s17">);</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">strokeStyle </span><span class="s17">=</span><span class="s11"> mainOutlineColor</span><span class="s17">;</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">lineWidth </span><span class="s17">=</span><span class="s11"> mainOutlineWidth</span><span class="s17">;</span><span class="s11"> </span><span class="s16">const</span><span class="s11"> po </span><span class="s17">=</span><span class="s11"> th </span><span class="s17">*</span><span class="s11"> </span><span class="s18">DEPTH_PERSPECTIVE_RATIO</span><span class="s17">;</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">beginPath</span><span class="s17">();</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">fillStyle </span><span class="s17">=</span><span class="s11"> c</span><span class="s17">;</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">rect</span><span class="s17">(-</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">,</span><span class="s11"> </span><span class="s17">-</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">,</span><span class="s11"> s</span><span class="s17">,</span><span class="s11"> s</span><span class="s17">);</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">fill</span><span class="s17">();</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">stroke</span><span class="s17">();</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">strokeStyle </span><span class="s17">=</span><span class="s11"> internalLineColor</span><span class="s17">;</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">lineWidth </span><span class="s17">=</span><span class="s11"> internalLineWidth</span><span class="s17">;</span><span class="s11"> </span><span class="s16">let</span><span class="s11"> segS </span><span class="s17">=</span><span class="s11"> s</span><span class="s17">/</span><span class="s19">10</span><span class="s17">;</span><span class="s11"> </span><span class="s16">for</span><span class="s11"> </span><span class="s17">(</span><span class="s16">let</span><span class="s11"> i</span><span class="s17">=</span><span class="s19">1</span><span class="s17">;</span><span class="s11"> i</span><span class="s17">&lt;</span><span class="s19">10</span><span class="s17">;</span><span class="s11"> i</span><span class="s17">++)</span><span class="s11"> </span><span class="s17">{</span><span class="s11"> </span><span class="s16">let</span><span class="s11"> lx </span><span class="s17">=</span><span class="s11"> </span><span class="s17">-</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">+</span><span class="s11">i</span><span class="s17">*</span><span class="s11">segS</span><span class="s17">;</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">beginPath</span><span class="s17">();</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">moveTo</span><span class="s17">(</span><span class="s11">lx</span><span class="s17">,</span><span class="s11"> </span><span class="s17">-</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">);</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">lineTo</span><span class="s17">(</span><span class="s11">lx</span><span class="s17">,</span><span class="s11"> s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">);</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">stroke</span><span class="s17">();</span><span class="s11"> </span><span class="s16">let</span><span class="s11"> ly </span><span class="s17">=</span><span class="s11"> </span><span class="s17">-</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">+</span><span class="s11">i</span><span class="s17">*</span><span class="s11">segS</span><span class="s17">;</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">beginPath</span><span class="s17">();</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">moveTo</span><span class="s17">(-</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">,</span><span class="s11"> ly</span><span class="s17">);</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">lineTo</span><span class="s17">(</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">,</span><span class="s11"> ly</span><span class="s17">);</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">stroke</span><span class="s17">();</span><span class="s11"> </span><span class="s17">}</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">strokeStyle </span><span class="s17">=</span><span class="s11"> mainOutlineColor</span><span class="s17">;</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">lineWidth </span><span class="s17">=</span><span class="s11"> mainOutlineWidth</span><span class="s17">;</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">beginPath</span><span class="s17">();</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">fillStyle </span><span class="s17">=</span><span class="s11"> lightenColor</span><span class="s17">(</span><span class="s11">c</span><span class="s17">,</span><span class="s19">15</span><span class="s17">);</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">moveTo</span><span class="s17">(-</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">,-</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">);</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">lineTo</span><span class="s17">(-</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">+</span><span class="s11">po</span><span class="s17">,</span><span class="s11"> </span><span class="s17">-</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">-</span><span class="s11">po</span><span class="s17">);</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">lineTo</span><span class="s17">(</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">+</span><span class="s11">po</span><span class="s17">,</span><span class="s11"> </span><span class="s17">-</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">-</span><span class="s11">po</span><span class="s17">);</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">lineTo</span><span class="s17">(</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">,-</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">);</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">closePath</span><span class="s17">();</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">fill</span><span class="s17">();</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">stroke</span><span class="s17">();</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">strokeStyle </span><span class="s17">=</span><span class="s11"> internalLineColor</span><span class="s17">;</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">lineWidth </span><span class="s17">=</span><span class="s11"> internalLineWidth</span><span class="s17">;</span><span class="s11"> segS </span><span class="s17">=</span><span class="s11"> s</span><span class="s17">/</span><span class="s11">flatDisplaySegmentsCount</span><span class="s17">;</span><span class="s11"> </span><span class="s16">for</span><span class="s17">(</span><span class="s16">let</span><span class="s11"> k</span><span class="s17">=</span><span class="s19">1</span><span class="s17">;</span><span class="s11">k</span><span class="s17">&lt;</span><span class="s11">flatDisplaySegmentsCount</span><span class="s17">;</span><span class="s11">k</span><span class="s17">++){</span><span class="s16">const</span><span class="s11"> p1x_v=-s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">+</span><span class="s11">k</span><span class="s17">*</span><span class="s11">segS</span><span class="s17">,</span><span class="s11">p1y_v=-s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">,</span><span class="s11">p2x_v=-s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">+</span><span class="s11">po</span><span class="s17">+</span><span class="s11">k</span><span class="s17">*</span><span class="s11">segS</span><span class="s17">,</span><span class="s11">p2y_v=-s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">-</span><span class="s11">po</span><span class="s17">;</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">beginPath</span><span class="s17">();</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">moveTo</span><span class="s17">(</span><span class="s11">p1x_v</span><span class="s17">,</span><span class="s11">p1y_v</span><span class="s17">);</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">lineTo</span><span class="s17">(</span><span class="s11">p2x_v</span><span class="s17">,</span><span class="s11">p2y_v</span><span class="s17">);</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">stroke</span><span class="s17">();}</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">strokeStyle </span><span class="s17">=</span><span class="s11"> mainOutlineColor</span><span class="s17">;</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">lineWidth </span><span class="s17">=</span><span class="s11"> mainOutlineWidth</span><span class="s17">;</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">beginPath</span><span class="s17">();</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">fillStyle </span><span class="s17">=</span><span class="s11"> darkenColor</span><span class="s17">(</span><span class="s11">c</span><span class="s17">,</span><span class="s19">15</span><span class="s17">);</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">moveTo</span><span class="s17">(</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">,-</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">);</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">lineTo</span><span class="s17">(</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">+</span><span class="s11">po</span><span class="s17">,-</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">-</span><span class="s11">po</span><span class="s17">);</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">lineTo</span><span class="s17">(</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">+</span><span class="s11">po</span><span class="s17">,</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">-</span><span class="s11">po</span><span class="s17">);</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">lineTo</span><span class="s17">(</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">,</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">);</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">closePath</span><span class="s17">();</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">fill</span><span class="s17">();</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">stroke</span><span class="s17">();</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">strokeStyle</span><span class="s17">=</span><span class="s11">internalLineColor</span><span class="s17">;</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">lineWidth</span><span class="s17">=</span><span class="s11">internalLineWidth</span><span class="s17">;</span><span class="s11">segS</span><span class="s17">=</span><span class="s11">s</span><span class="s17">/</span><span class="s11">flatDisplaySegmentsCount</span><span class="s17">;</span><span class="s16">for</span><span class="s17">(</span><span class="s16">let</span><span class="s11"> k</span><span class="s17">=</span><span class="s19">1</span><span class="s17">;</span><span class="s11">k</span><span class="s17">&lt;</span><span class="s11">flatDisplaySegmentsCount</span><span class="s17">;</span><span class="s11">k</span><span class="s17">++){</span><span class="s16">const</span><span class="s11"> p1x_h_side</span><span class="s17">=</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">,</span><span class="s11">p1y_h_side=-s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">+</span><span class="s11">k</span><span class="s17">*</span><span class="s11">segS</span><span class="s17">,</span><span class="s11">p2x_h_side</span><span class="s17">=</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">+</span><span class="s11">po</span><span class="s17">,</span><span class="s11">p2y_h_side=-s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">-</span><span class="s11">po</span><span class="s17">+</span><span class="s11">k</span><span class="s17">*</span><span class="s11">segS</span><span class="s17">;</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">beginPath</span><span class="s17">();</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">moveTo</span><span class="s17">(</span><span class="s11">p1x_h_side</span><span class="s17">,</span><span class="s11">p1y_h_side</span><span class="s17">);</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">lineTo</span><span class="s17">(</span><span class="s11">p2x_h_side</span><span class="s17">,</span><span class="s11">p2y_h_side</span><span class="s17">);</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">stroke</span><span class="s17">();}</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">globalAlpha </span><span class="s17">=</span><span class="s11"> </span><span class="s19">1.0</span><span class="s17">;</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">restore</span><span class="s17">();</span><span class="s11"> </span><span class="s17">}</span></p>
<p class="p8"><span class="s11"><span class="Apple-converted-space">        </span></span><span class="s16">function</span><span class="s11"> renderBlock1000</span><span class="s17">(</span><span class="s11">blockObject</span><span class="s17">)</span><span class="s11"> </span><span class="s17">{</span><span class="s11"> </span><span class="s16">const</span><span class="s11"> x</span><span class="s17">=</span><span class="s11">blockObject</span><span class="s17">.</span><span class="s11">x</span><span class="s17">,</span><span class="s11">y</span><span class="s17">=</span><span class="s11">blockObject</span><span class="s17">.</span><span class="s11">y</span><span class="s17">,</span><span class="s11">s</span><span class="s17">=</span><span class="s11">blockObject</span><span class="s17">.</span><span class="s11">sideLength</span><span class="s17">,</span><span class="s11">c</span><span class="s17">=</span><span class="s11">blockObject</span><span class="s17">.</span><span class="s11">color</span><span class="s17">,</span><span class="s11">d</span><span class="s17">=</span><span class="s11">s</span><span class="s17">;</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">save</span><span class="s17">();</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">globalAlpha</span><span class="s17">=</span><span class="s11">baseTenTransparency</span><span class="s17">;</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">translate</span><span class="s17">(</span><span class="s11">x</span><span class="s17">,</span><span class="s11">y</span><span class="s17">);</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">strokeStyle</span><span class="s17">=</span><span class="s11">mainOutlineColor</span><span class="s17">;</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">lineWidth</span><span class="s17">=</span><span class="s11">mainOutlineWidth</span><span class="s17">;</span><span class="s11"> </span><span class="s16">const</span><span class="s11"> po</span><span class="s17">=</span><span class="s11">d</span><span class="s17">*</span><span class="s18">DEPTH_PERSPECTIVE_RATIO</span><span class="s17">;</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">beginPath</span><span class="s17">();</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">fillStyle</span><span class="s17">=</span><span class="s11">c</span><span class="s17">;</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">rect</span><span class="s17">(-</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">,-</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">,</span><span class="s11">s</span><span class="s17">,</span><span class="s11">s</span><span class="s17">);</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">fill</span><span class="s17">();</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">stroke</span><span class="s17">();</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">strokeStyle</span><span class="s17">=</span><span class="s11">internalLineColor</span><span class="s17">;</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">lineWidth</span><span class="s17">=</span><span class="s11">internalLineWidth</span><span class="s17">;</span><span class="s11"> </span><span class="s16">let</span><span class="s11"> seg</span><span class="s17">=</span><span class="s11">s</span><span class="s17">/</span><span class="s11">block1000GridSegments</span><span class="s17">;</span><span class="s11"> </span><span class="s16">for</span><span class="s17">(</span><span class="s16">let</span><span class="s11"> i</span><span class="s17">=</span><span class="s19">1</span><span class="s17">;</span><span class="s11">i</span><span class="s17">&lt;</span><span class="s11">block1000GridSegments</span><span class="s17">;</span><span class="s11">i</span><span class="s17">++){</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">beginPath</span><span class="s17">();</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">moveTo</span><span class="s17">(-</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">+</span><span class="s11">i</span><span class="s17">*</span><span class="s11">seg</span><span class="s17">,-</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">);</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">lineTo</span><span class="s17">(-</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">+</span><span class="s11">i</span><span class="s17">*</span><span class="s11">seg</span><span class="s17">,</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">);</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">stroke</span><span class="s17">();</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">beginPath</span><span class="s17">();</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">moveTo</span><span class="s17">(-</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">,-</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">+</span><span class="s11">i</span><span class="s17">*</span><span class="s11">seg</span><span class="s17">);</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">lineTo</span><span class="s17">(</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">,-</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">+</span><span class="s11">i</span><span class="s17">*</span><span class="s11">seg</span><span class="s17">);</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">stroke</span><span class="s17">();</span><span class="s11"> </span><span class="s17">}</span><span class="s11"> ctx</span><span class="s17">.</span><span class="s11">strokeStyle</span><span class="s17">=</span><span class="s11">mainOutlineColor</span><span class="s17">;</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">lineWidth</span><span class="s17">=</span><span class="s11">mainOutlineWidth</span><span class="s17">;</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">fillStyle</span><span class="s17">=</span><span class="s11">lightenColor</span><span class="s17">(</span><span class="s11">c</span><span class="s17">,</span><span class="s19">20</span><span class="s17">);</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">beginPath</span><span class="s17">();</span><span class="s16">const</span><span class="s11"> tf_x1=-s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">,</span><span class="s11">tf_y1=-s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">,</span><span class="s11">tf_x2</span><span class="s17">=</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">,</span><span class="s11">tf_y2=-s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">,</span><span class="s11">tf_x3</span><span class="s17">=</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">+</span><span class="s11">po</span><span class="s17">,</span><span class="s11">tf_y3=-s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">-</span><span class="s11">po</span><span class="s17">,</span><span class="s11">tf_x4=-s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">+</span><span class="s11">po</span><span class="s17">,</span><span class="s11">tf_y4=-s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">-</span><span class="s11">po</span><span class="s17">;</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">moveTo</span><span class="s17">(</span><span class="s11">tf_x1</span><span class="s17">,</span><span class="s11">tf_y1</span><span class="s17">);</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">lineTo</span><span class="s17">(</span><span class="s11">tf_x4</span><span class="s17">,</span><span class="s11">tf_y4</span><span class="s17">);</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">lineTo</span><span class="s17">(</span><span class="s11">tf_x3</span><span class="s17">,</span><span class="s11">tf_y3</span><span class="s17">);</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">lineTo</span><span class="s17">(</span><span class="s11">tf_x2</span><span class="s17">,</span><span class="s11">tf_y2</span><span class="s17">);</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">closePath</span><span class="s17">();</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">fill</span><span class="s17">();</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">stroke</span><span class="s17">();</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">strokeStyle</span><span class="s17">=</span><span class="s11">internalLineColor</span><span class="s17">;</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">lineWidth</span><span class="s17">=</span><span class="s11">internalLineWidth</span><span class="s17">;</span><span class="s16">for</span><span class="s17">(</span><span class="s16">let</span><span class="s11"> k</span><span class="s17">=</span><span class="s19">1</span><span class="s17">;</span><span class="s11">k</span><span class="s17">&lt;</span><span class="s11">block1000GridSegments</span><span class="s17">;</span><span class="s11">k</span><span class="s17">++){</span><span class="s16">const</span><span class="s11"> t</span><span class="s17">=</span><span class="s11">k</span><span class="s17">/</span><span class="s11">block1000GridSegments</span><span class="s17">;</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">beginPath</span><span class="s17">();</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">moveTo</span><span class="s17">(</span><span class="s11">tf_x1</span><span class="s17">+</span><span class="s11">t</span><span class="s17">*(</span><span class="s11">tf_x2</span><span class="s17">-</span><span class="s11">tf_x1</span><span class="s17">),</span><span class="s11">tf_y1</span><span class="s17">+</span><span class="s11">t</span><span class="s17">*(</span><span class="s11">tf_y2</span><span class="s17">-</span><span class="s11">tf_y1</span><span class="s17">));</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">lineTo</span><span class="s17">(</span><span class="s11">tf_x4</span><span class="s17">+</span><span class="s11">t</span><span class="s17">*(</span><span class="s11">tf_x3</span><span class="s17">-</span><span class="s11">tf_x4</span><span class="s17">),</span><span class="s11">tf_y4</span><span class="s17">+</span><span class="s11">t</span><span class="s17">*(</span><span class="s11">tf_y3</span><span class="s17">-</span><span class="s11">tf_y4</span><span class="s17">));</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">stroke</span><span class="s17">();</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">beginPath</span><span class="s17">();</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">moveTo</span><span class="s17">(</span><span class="s11">tf_x1</span><span class="s17">+</span><span class="s11">t</span><span class="s17">*(</span><span class="s11">tf_x4</span><span class="s17">-</span><span class="s11">tf_x1</span><span class="s17">),</span><span class="s11">tf_y1</span><span class="s17">+</span><span class="s11">t</span><span class="s17">*(</span><span class="s11">tf_y4</span><span class="s17">-</span><span class="s11">tf_y1</span><span class="s17">));</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">lineTo</span><span class="s17">(</span><span class="s11">tf_x2</span><span class="s17">+</span><span class="s11">t</span><span class="s17">*(</span><span class="s11">tf_x3</span><span class="s17">-</span><span class="s11">tf_x2</span><span class="s17">),</span><span class="s11">tf_y2</span><span class="s17">+</span><span class="s11">t</span><span class="s17">*(</span><span class="s11">tf_y3</span><span class="s17">-</span><span class="s11">tf_y2</span><span class="s17">));</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">stroke</span><span class="s17">();}</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">strokeStyle</span><span class="s17">=</span><span class="s11">mainOutlineColor</span><span class="s17">;</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">lineWidth</span><span class="s17">=</span><span class="s11">mainOutlineWidth</span><span class="s17">;</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">fillStyle</span><span class="s17">=</span><span class="s11">darkenColor</span><span class="s17">(</span><span class="s11">c</span><span class="s17">,</span><span class="s19">20</span><span class="s17">);</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">beginPath</span><span class="s17">();</span><span class="s16">const</span><span class="s11"> rf_x1</span><span class="s17">=</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">,</span><span class="s11">rf_y1=-s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">,</span><span class="s11">rf_x2</span><span class="s17">=</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">,</span><span class="s11">rf_y2</span><span class="s17">=</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">,</span><span class="s11">rf_x3</span><span class="s17">=</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">+</span><span class="s11">po</span><span class="s17">,</span><span class="s11">rf_y3</span><span class="s17">=</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">-</span><span class="s11">po</span><span class="s17">,</span><span class="s11">rf_x4</span><span class="s17">=</span><span class="s11">s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">+</span><span class="s11">po</span><span class="s17">,</span><span class="s11">rf_y4=-s</span><span class="s17">/</span><span class="s19">2</span><span class="s17">-</span><span class="s11">po</span><span class="s17">;</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">moveTo</span><span class="s17">(</span><span class="s11">rf_x1</span><span class="s17">,</span><span class="s11">rf_y1</span><span class="s17">);</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">lineTo</span><span class="s17">(</span><span class="s11">rf_x4</span><span class="s17">,</span><span class="s11">rf_y4</span><span class="s17">);</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">lineTo</span><span class="s17">(</span><span class="s11">rf_x3</span><span class="s17">,</span><span class="s11">rf_y3</span><span class="s17">);</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">lineTo</span><span class="s17">(</span><span class="s11">rf_x2</span><span class="s17">,</span><span class="s11">rf_y2</span><span class="s17">);</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">closePath</span><span class="s17">();</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">fill</span><span class="s17">();</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">stroke</span><span class="s17">();</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">strokeStyle</span><span class="s17">=</span><span class="s11">internalLineColor</span><span class="s17">;</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">lineWidth</span><span class="s17">=</span><span class="s11">internalLineWidth</span><span class="s17">;</span><span class="s16">for</span><span class="s17">(</span><span class="s16">let</span><span class="s11"> k</span><span class="s17">=</span><span class="s19">1</span><span class="s17">;</span><span class="s11">k</span><span class="s17">&lt;</span><span class="s11">block1000GridSegments</span><span class="s17">;</span><span class="s11">k</span><span class="s17">++){</span><span class="s16">const</span><span class="s11"> t</span><span class="s17">=</span><span class="s11">k</span><span class="s17">/</span><span class="s11">block1000GridSegments</span><span class="s17">;</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">beginPath</span><span class="s17">();</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">moveTo</span><span class="s17">(</span><span class="s11">rf_x1</span><span class="s17">+</span><span class="s11">t</span><span class="s17">*(</span><span class="s11">rf_x2</span><span class="s17">-</span><span class="s11">rf_x1</span><span class="s17">),</span><span class="s11">rf_y1</span><span class="s17">+</span><span class="s11">t</span><span class="s17">*(</span><span class="s11">rf_y2</span><span class="s17">-</span><span class="s11">rf_y1</span><span class="s17">));</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">lineTo</span><span class="s17">(</span><span class="s11">rf_x4</span><span class="s17">+</span><span class="s11">t</span><span class="s17">*(</span><span class="s11">rf_x3</span><span class="s17">-</span><span class="s11">rf_x4</span><span class="s17">),</span><span class="s11">rf_y4</span><span class="s17">+</span><span class="s11">t</span><span class="s17">*(</span><span class="s11">rf_y3</span><span class="s17">-</span><span class="s11">rf_y4</span><span class="s17">));</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">stroke</span><span class="s17">();</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">beginPath</span><span class="s17">();</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">moveTo</span><span class="s17">(</span><span class="s11">rf_x1</span><span class="s17">+</span><span class="s11">t</span><span class="s17">*(</span><span class="s11">rf_x4</span><span class="s17">-</span><span class="s11">rf_x1</span><span class="s17">),</span><span class="s11">rf_y1</span><span class="s17">+</span><span class="s11">t</span><span class="s17">*(</span><span class="s11">rf_y4</span><span class="s17">-</span><span class="s11">rf_y1</span><span class="s17">));</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">lineTo</span><span class="s17">(</span><span class="s11">rf_x2</span><span class="s17">+</span><span class="s11">t</span><span class="s17">*(</span><span class="s11">rf_x3</span><span class="s17">-</span><span class="s11">rf_x2</span><span class="s17">),</span><span class="s11">rf_y2</span><span class="s17">+</span><span class="s11">t</span><span class="s17">*(</span><span class="s11">rf_y3</span><span class="s17">-</span><span class="s11">rf_y2</span><span class="s17">));</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">stroke</span><span class="s17">();}</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">globalAlpha</span><span class="s17">=</span><span class="s19">1.0</span><span class="s17">;</span><span class="s11">ctx</span><span class="s17">.</span><span class="s11">restore</span><span class="s17">();}</span></p>
<p class="p6"><span class="s1"><span class="Apple-converted-space">        </span></span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">// --- RENDER NEW 2D GEOMETRIC SHAPES ---</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">function</span><span class="s1"> applyShadow</span><span class="s7">()</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">shadowColor </span><span class="s7">=</span><span class="s1"> shadowColor</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">shadowBlur </span><span class="s7">=</span><span class="s1"> shadowBlur</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">shadowOffsetX </span><span class="s7">=</span><span class="s1"> shadowOffsetX</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">shadowOffsetY </span><span class="s7">=</span><span class="s1"> shadowOffsetY</span><span class="s7">;</span><span class="s1"> </span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">function</span><span class="s1"> clearShadow</span><span class="s7">()</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">shadowColor </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'transparent'</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">shadowBlur </span><span class="s7">=</span><span class="s1"> </span><span class="s12">0</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">shadowOffsetX </span><span class="s7">=</span><span class="s1"> </span><span class="s12">0</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">shadowOffsetY </span><span class="s7">=</span><span class="s1"> </span><span class="s12">0</span><span class="s7">;</span><span class="s1"> </span><span class="s7">}</span></p>
<p class="p6"><span class="s1"><span class="Apple-converted-space">        </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">function</span><span class="s1"> draw2DPolygon</span><span class="s7">(</span><span class="s1">centerX</span><span class="s7">,</span><span class="s1"> centerY</span><span class="s7">,</span><span class="s1"> radius</span><span class="s7">,</span><span class="s1"> sides</span><span class="s7">,</span><span class="s1"> startAngle </span><span class="s7">=</span><span class="s1"> </span><span class="s12">0</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">beginPath</span><span class="s7">();</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">for</span><span class="s1"> </span><span class="s7">(</span><span class="s10">let</span><span class="s1"> i </span><span class="s7">=</span><span class="s1"> </span><span class="s12">0</span><span class="s7">;</span><span class="s1"> i </span><span class="s7">&lt;=</span><span class="s1"> sides</span><span class="s7">;</span><span class="s1"> i</span><span class="s7">++)</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">const</span><span class="s1"> angle </span><span class="s7">=</span><span class="s1"> startAngle </span><span class="s7">+</span><span class="s1"> </span><span class="s7">(</span><span class="s1">i </span><span class="s7">*</span><span class="s1"> </span><span class="s12">2</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s15">PI</span><span class="s1"> </span><span class="s7">/</span><span class="s1"> sides</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">const</span><span class="s1"> xPos </span><span class="s7">=</span><span class="s1"> centerX </span><span class="s7">+</span><span class="s1"> radius </span><span class="s7">*</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">cos</span><span class="s7">(</span><span class="s1">angle</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">const</span><span class="s1"> yPos </span><span class="s7">=</span><span class="s1"> centerY </span><span class="s7">+</span><span class="s1"> radius </span><span class="s7">*</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">sin</span><span class="s7">(</span><span class="s1">angle</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">i </span><span class="s7">===</span><span class="s1"> </span><span class="s12">0</span><span class="s7">)</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">moveTo</span><span class="s7">(</span><span class="s1">xPos</span><span class="s7">,</span><span class="s1"> yPos</span><span class="s7">);</span><span class="s1"> </span><span class="s10">else</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">lineTo</span><span class="s7">(</span><span class="s1">xPos</span><span class="s7">,</span><span class="s1"> yPos</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">closePath</span><span class="s7">();</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s7">}</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">function</span><span class="s1"> render2DCircle</span><span class="s7">(</span><span class="s1">item</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">save</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">translate</span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">x</span><span class="s7">,</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">y</span><span class="s7">);</span><span class="s1"> applyShadow</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">globalAlpha </span><span class="s7">=</span><span class="s1"> geometricShapeTransparency</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">fillStyle </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">color</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">strokeStyle </span><span class="s7">=</span><span class="s1"> mainOutlineColor</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">lineWidth </span><span class="s7">=</span><span class="s1"> mainOutlineWidth</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">beginPath</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">arc</span><span class="s7">(</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> </span><span class="s12">0</span><span class="s7">,</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">radius</span><span class="s7">,</span><span class="s1"> </span><span class="s12">0</span><span class="s7">,</span><span class="s1"> </span><span class="s12">2</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s15">PI</span><span class="s7">);</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">fill</span><span class="s7">();</span><span class="s1"> clearShadow</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">stroke</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">globalAlpha </span><span class="s7">=</span><span class="s1"> </span><span class="s12">1.0</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">restore</span><span class="s7">();</span><span class="s1"> </span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">function</span><span class="s1"> render2DSquare</span><span class="s7">(</span><span class="s1">item</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">save</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">translate</span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">x</span><span class="s7">,</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">y</span><span class="s7">);</span><span class="s1"> applyShadow</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">globalAlpha </span><span class="s7">=</span><span class="s1"> geometricShapeTransparency</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">fillStyle </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">color</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">strokeStyle </span><span class="s7">=</span><span class="s1"> mainOutlineColor</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">lineWidth </span><span class="s7">=</span><span class="s1"> mainOutlineWidth</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">beginPath</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">rect</span><span class="s7">(-</span><span class="s1">item</span><span class="s7">.</span><span class="s1">sideLength </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s7">,</span><span class="s1"> </span><span class="s7">-</span><span class="s1">item</span><span class="s7">.</span><span class="s1">sideLength </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s7">,</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">sideLength</span><span class="s7">,</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">sideLength</span><span class="s7">);</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">fill</span><span class="s7">();</span><span class="s1"> clearShadow</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">stroke</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">globalAlpha </span><span class="s7">=</span><span class="s1"> </span><span class="s12">1.0</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">restore</span><span class="s7">();</span><span class="s1"> </span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">function</span><span class="s1"> render2DEquilateralTriangle</span><span class="s7">(</span><span class="s1">item</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">save</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">translate</span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">x</span><span class="s7">,</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">y</span><span class="s7">);</span><span class="s1"> applyShadow</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">globalAlpha </span><span class="s7">=</span><span class="s1"> geometricShapeTransparency</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">fillStyle </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">color</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">strokeStyle </span><span class="s7">=</span><span class="s1"> mainOutlineColor</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">lineWidth </span><span class="s7">=</span><span class="s1"> mainOutlineWidth</span><span class="s7">;</span><span class="s1"> </span><span class="s10">const</span><span class="s1"> h </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">sideLength </span><span class="s7">*</span><span class="s1"> </span><span class="s7">(</span><span class="s15">Math</span><span class="s7">.</span><span class="s1">sqrt</span><span class="s7">(</span><span class="s12">3</span><span class="s7">)/</span><span class="s12">2</span><span class="s7">);</span><span class="s1"> draw2DPolygon</span><span class="s7">(</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> </span><span class="s7">-</span><span class="s1">h</span><span class="s7">/</span><span class="s12">3</span><span class="s7">,</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">sideLength </span><span class="s7">*</span><span class="s1"> </span><span class="s7">(</span><span class="s15">Math</span><span class="s7">.</span><span class="s1">sqrt</span><span class="s7">(</span><span class="s12">3</span><span class="s7">)/</span><span class="s12">3</span><span class="s7">)</span><span class="s1"> </span><span class="s7">,</span><span class="s1"> </span><span class="s12">3</span><span class="s7">,</span><span class="s1"> </span><span class="s7">-</span><span class="s15">Math</span><span class="s7">.</span><span class="s15">PI</span><span class="s7">/</span><span class="s12">2</span><span class="s7">);</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">fill</span><span class="s7">();</span><span class="s1"> clearShadow</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">stroke</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">globalAlpha </span><span class="s7">=</span><span class="s1"> </span><span class="s12">1.0</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">restore</span><span class="s7">();</span><span class="s1"> </span><span class="s7">}</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">function</span><span class="s1"> render2DParallelogram</span><span class="s7">(</span><span class="s1">item</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">save</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">translate</span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">x</span><span class="s7">,</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">y</span><span class="s7">);</span><span class="s1"> applyShadow</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">globalAlpha </span><span class="s7">=</span><span class="s1"> geometricShapeTransparency</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">fillStyle </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">color</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">strokeStyle </span><span class="s7">=</span><span class="s1"> mainOutlineColor</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">lineWidth </span><span class="s7">=</span><span class="s1"> mainOutlineWidth</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">beginPath</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">moveTo</span><span class="s7">(-</span><span class="s1">item</span><span class="s7">.</span><span class="s1">width</span><span class="s7">/</span><span class="s12">2</span><span class="s7">,</span><span class="s1"> </span><span class="s7">-</span><span class="s1">item</span><span class="s7">.</span><span class="s1">height</span><span class="s7">/</span><span class="s12">2</span><span class="s7">);</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">lineTo</span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">width</span><span class="s7">/</span><span class="s12">2</span><span class="s1"> </span><span class="s7">-</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">skew</span><span class="s7">,</span><span class="s1"> </span><span class="s7">-</span><span class="s1">item</span><span class="s7">.</span><span class="s1">height</span><span class="s7">/</span><span class="s12">2</span><span class="s7">);</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">lineTo</span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">width</span><span class="s7">/</span><span class="s12">2</span><span class="s7">,</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">height</span><span class="s7">/</span><span class="s12">2</span><span class="s7">);</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">lineTo</span><span class="s7">(-</span><span class="s1">item</span><span class="s7">.</span><span class="s1">width</span><span class="s7">/</span><span class="s12">2</span><span class="s1"> </span><span class="s7">+</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">skew</span><span class="s7">,</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">height</span><span class="s7">/</span><span class="s12">2</span><span class="s7">);</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">closePath</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">fill</span><span class="s7">();</span><span class="s1"> clearShadow</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">stroke</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">globalAlpha </span><span class="s7">=</span><span class="s1"> </span><span class="s12">1.0</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">restore</span><span class="s7">();</span><span class="s1"> </span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">function</span><span class="s1"> render2DRhombus</span><span class="s7">(</span><span class="s1">item</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">save</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">translate</span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">x</span><span class="s7">,</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">y</span><span class="s7">);</span><span class="s1"> applyShadow</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">globalAlpha </span><span class="s7">=</span><span class="s1"> geometricShapeTransparency</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">fillStyle </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">color</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">strokeStyle </span><span class="s7">=</span><span class="s1"> mainOutlineColor</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">lineWidth </span><span class="s7">=</span><span class="s1"> mainOutlineWidth</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">beginPath</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">moveTo</span><span class="s7">(</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> </span><span class="s7">-</span><span class="s1">item</span><span class="s7">.</span><span class="s1">d1</span><span class="s7">/</span><span class="s12">2</span><span class="s7">);</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">lineTo</span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">d2</span><span class="s7">/</span><span class="s12">2</span><span class="s7">,</span><span class="s1"> </span><span class="s12">0</span><span class="s7">);</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">lineTo</span><span class="s7">(</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">d1</span><span class="s7">/</span><span class="s12">2</span><span class="s7">);</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">lineTo</span><span class="s7">(-</span><span class="s1">item</span><span class="s7">.</span><span class="s1">d2</span><span class="s7">/</span><span class="s12">2</span><span class="s7">,</span><span class="s1"> </span><span class="s12">0</span><span class="s7">);</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">closePath</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">fill</span><span class="s7">();</span><span class="s1"> clearShadow</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">stroke</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">globalAlpha </span><span class="s7">=</span><span class="s1"> </span><span class="s12">1.0</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">restore</span><span class="s7">();</span><span class="s1"> </span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">function</span><span class="s1"> render2DRightTriangle</span><span class="s7">(</span><span class="s1">item</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">save</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">translate</span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">x</span><span class="s7">,</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">y</span><span class="s7">);</span><span class="s1"> applyShadow</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">globalAlpha </span><span class="s7">=</span><span class="s1"> geometricShapeTransparency</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">fillStyle </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">color</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">strokeStyle </span><span class="s7">=</span><span class="s1"> mainOutlineColor</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">lineWidth </span><span class="s7">=</span><span class="s1"> mainOutlineWidth</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">beginPath</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">moveTo</span><span class="s7">(-</span><span class="s1">item</span><span class="s7">.</span><span class="s1">base</span><span class="s7">/</span><span class="s12">2</span><span class="s7">,</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">height</span><span class="s7">/</span><span class="s12">2</span><span class="s7">);</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">lineTo</span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">base</span><span class="s7">/</span><span class="s12">2</span><span class="s7">,</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">height</span><span class="s7">/</span><span class="s12">2</span><span class="s7">);</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">lineTo</span><span class="s7">(-</span><span class="s1">item</span><span class="s7">.</span><span class="s1">base</span><span class="s7">/</span><span class="s12">2</span><span class="s7">,</span><span class="s1"> </span><span class="s7">-</span><span class="s1">item</span><span class="s7">.</span><span class="s1">height</span><span class="s7">/</span><span class="s12">2</span><span class="s7">);</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">closePath</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">fill</span><span class="s7">();</span><span class="s1"> clearShadow</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">stroke</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">globalAlpha </span><span class="s7">=</span><span class="s1"> </span><span class="s12">1.0</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">restore</span><span class="s7">();</span><span class="s1"> </span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">function</span><span class="s1"> render2DDiamond</span><span class="s7">(</span><span class="s1">item</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">save</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">translate</span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">x</span><span class="s7">,</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">y</span><span class="s7">);</span><span class="s1"> applyShadow</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">globalAlpha </span><span class="s7">=</span><span class="s1"> geometricShapeTransparency</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">fillStyle </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">color</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">strokeStyle </span><span class="s7">=</span><span class="s1"> mainOutlineColor</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">lineWidth </span><span class="s7">=</span><span class="s1"> mainOutlineWidth</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">beginPath</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">moveTo</span><span class="s7">(</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> </span><span class="s7">-</span><span class="s1">item</span><span class="s7">.</span><span class="s1">height</span><span class="s7">/</span><span class="s12">2</span><span class="s7">);</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">lineTo</span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">width</span><span class="s7">/</span><span class="s12">2</span><span class="s7">,</span><span class="s1"> </span><span class="s12">0</span><span class="s7">);</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">lineTo</span><span class="s7">(</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">height</span><span class="s7">/</span><span class="s12">2</span><span class="s7">);</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">lineTo</span><span class="s7">(-</span><span class="s1">item</span><span class="s7">.</span><span class="s1">width</span><span class="s7">/</span><span class="s12">2</span><span class="s7">,</span><span class="s1"> </span><span class="s12">0</span><span class="s7">);</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">closePath</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">fill</span><span class="s7">();</span><span class="s1"> clearShadow</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">stroke</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">globalAlpha </span><span class="s7">=</span><span class="s1"> </span><span class="s12">1.0</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">restore</span><span class="s7">();</span><span class="s1"> </span><span class="s7">}</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">function</span><span class="s1"> render2DRegularPolygon</span><span class="s7">(</span><span class="s1">item</span><span class="s7">,</span><span class="s1"> sides</span><span class="s7">,</span><span class="s1"> startAngleOffset </span><span class="s7">=</span><span class="s1"> </span><span class="s12">0</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">save</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">translate</span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">x</span><span class="s7">,</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">y</span><span class="s7">);</span><span class="s1"> applyShadow</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">globalAlpha </span><span class="s7">=</span><span class="s1"> geometricShapeTransparency</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">fillStyle </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">color</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">strokeStyle </span><span class="s7">=</span><span class="s1"> mainOutlineColor</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">lineWidth </span><span class="s7">=</span><span class="s1"> mainOutlineWidth</span><span class="s7">;</span><span class="s1"> </span><span class="s10">const</span><span class="s1"> radius </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">sideLength </span><span class="s7">/</span><span class="s1"> </span><span class="s7">(</span><span class="s12">2</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">sin</span><span class="s7">(</span><span class="s15">Math</span><span class="s7">.</span><span class="s15">PI</span><span class="s1"> </span><span class="s7">/</span><span class="s1"> sides</span><span class="s7">));</span><span class="s1"> draw2DPolygon</span><span class="s7">(</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> </span><span class="s12">0</span><span class="s7">,</span><span class="s1"> radius</span><span class="s7">,</span><span class="s1"> sides</span><span class="s7">,</span><span class="s1"> startAngleOffset</span><span class="s7">);</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">fill</span><span class="s7">();</span><span class="s1"> clearShadow</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">stroke</span><span class="s7">();</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">globalAlpha </span><span class="s7">=</span><span class="s1"> </span><span class="s12">1.0</span><span class="s7">;</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">restore</span><span class="s7">();</span><span class="s1"> </span><span class="s7">}</span></p>
<p class="p6"><span class="s1"><span class="Apple-converted-space">        </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">function</span><span class="s1"> renderClock</span><span class="s7">(</span><span class="s1">item</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">save</span><span class="s7">();</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">translate</span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">x</span><span class="s7">,</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">y</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>applyShadow</span><span class="s7">();</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">globalAlpha </span><span class="s7">=</span><span class="s1"> geometricShapeTransparency</span><span class="s7">;</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s1">// Clock face</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">fillStyle </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">color</span><span class="s7">;</span><span class="s1"> </span><span class="s13">// White background</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">strokeStyle </span><span class="s7">=</span><span class="s1"> mainOutlineColor</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">lineWidth </span><span class="s7">=</span><span class="s1"> mainOutlineWidth</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">beginPath</span><span class="s7">();</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">arc</span><span class="s7">(</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> </span><span class="s12">0</span><span class="s7">,</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">radius</span><span class="s7">,</span><span class="s1"> </span><span class="s12">0</span><span class="s7">,</span><span class="s1"> </span><span class="s12">2</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s15">PI</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">fill</span><span class="s7">();</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>clearShadow</span><span class="s7">();</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">stroke</span><span class="s7">();</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s13">// Numbers</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">fillStyle </span><span class="s7">=</span><span class="s1"> mainOutlineColor</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">font </span><span class="s7">=</span><span class="s1"> </span><span class="s9">`</span><span class="s7">${</span><span class="s1">item</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">*</span><span class="s1"> </span><span class="s12">0.25</span><span class="s7">}</span><span class="s9">px Arial`</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">textAlign </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'center'</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">textBaseline </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'middle'</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">for</span><span class="s1"> </span><span class="s7">(</span><span class="s10">let</span><span class="s1"> i </span><span class="s7">=</span><span class="s1"> </span><span class="s12">1</span><span class="s7">;</span><span class="s1"> i </span><span class="s7">&lt;=</span><span class="s1"> </span><span class="s12">12</span><span class="s7">;</span><span class="s1"> i</span><span class="s7">++)</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">const</span><span class="s1"> angle </span><span class="s7">=</span><span class="s1"> </span><span class="s7">(</span><span class="s1">i </span><span class="s7">-</span><span class="s1"> </span><span class="s12">3</span><span class="s7">)</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s7">(</span><span class="s15">Math</span><span class="s7">.</span><span class="s15">PI</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s12">2</span><span class="s7">)</span><span class="s1"> </span><span class="s7">/</span><span class="s1"> </span><span class="s12">12</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">const</span><span class="s1"> numX </span><span class="s7">=</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">cos</span><span class="s7">(</span><span class="s1">angle</span><span class="s7">)</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">*</span><span class="s1"> </span><span class="s12">0.8</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">const</span><span class="s1"> numY </span><span class="s7">=</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">sin</span><span class="s7">(</span><span class="s1">angle</span><span class="s7">)</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">*</span><span class="s1"> </span><span class="s12">0.8</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>ctx</span><span class="s7">.</span><span class="s1">fillText</span><span class="s7">(</span><span class="s1">i</span><span class="s7">.</span><span class="s1">toString</span><span class="s7">(),</span><span class="s1"> numX</span><span class="s7">,</span><span class="s1"> numY</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s7">}</span></p>
<p class="p6"><span class="s1"><span class="Apple-converted-space">            </span></span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s1">// Static Hour hand (e.g., pointing to 10)</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">const</span><span class="s1"> hourAngle </span><span class="s7">=</span><span class="s1"> </span><span class="s7">(</span><span class="s12">10</span><span class="s1"> </span><span class="s7">-</span><span class="s1"> </span><span class="s12">3</span><span class="s7">)</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s7">(</span><span class="s15">Math</span><span class="s7">.</span><span class="s15">PI</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s12">2</span><span class="s7">)</span><span class="s1"> </span><span class="s7">/</span><span class="s1"> </span><span class="s12">12</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">beginPath</span><span class="s7">();</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">lineWidth </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">*</span><span class="s1"> </span><span class="s12">0.07</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">strokeStyle </span><span class="s7">=</span><span class="s1"> mainOutlineColor</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">moveTo</span><span class="s7">(</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> </span><span class="s12">0</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">lineTo</span><span class="s7">(</span><span class="s15">Math</span><span class="s7">.</span><span class="s1">cos</span><span class="s7">(</span><span class="s1">hourAngle</span><span class="s7">)</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">*</span><span class="s1"> </span><span class="s12">0.5</span><span class="s7">,</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">sin</span><span class="s7">(</span><span class="s1">hourAngle</span><span class="s7">)</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">*</span><span class="s1"> </span><span class="s12">0.5</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">stroke</span><span class="s7">();</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s1">// Static Minute hand (e.g., pointing to 2 (for 10:10))</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">const</span><span class="s1"> minuteAngle </span><span class="s7">=</span><span class="s1"> </span><span class="s7">(</span><span class="s12">10</span><span class="s1"> </span><span class="s7">-</span><span class="s1"> </span><span class="s12">15</span><span class="s7">)</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s7">(</span><span class="s15">Math</span><span class="s7">.</span><span class="s15">PI</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s12">2</span><span class="s7">)</span><span class="s1"> </span><span class="s7">/</span><span class="s1"> </span><span class="s12">60</span><span class="s7">;</span><span class="s1"> </span><span class="s13">// 10 minutes is at the '2'</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">beginPath</span><span class="s7">();</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">lineWidth </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">*</span><span class="s1"> </span><span class="s12">0.05</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">moveTo</span><span class="s7">(</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> </span><span class="s12">0</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">lineTo</span><span class="s7">(</span><span class="s15">Math</span><span class="s7">.</span><span class="s1">cos</span><span class="s7">(</span><span class="s1">minuteAngle</span><span class="s7">)</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">*</span><span class="s1"> </span><span class="s12">0.7</span><span class="s7">,</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">sin</span><span class="s7">(</span><span class="s1">minuteAngle</span><span class="s7">)</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">*</span><span class="s1"> </span><span class="s12">0.7</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">stroke</span><span class="s7">();</span></p>
<p class="p6"><span class="s1"><span class="Apple-converted-space">            </span></span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s1">// Center dot</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">beginPath</span><span class="s7">();</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">fillStyle </span><span class="s7">=</span><span class="s1"> mainOutlineColor</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">arc</span><span class="s7">(</span><span class="s12">0</span><span class="s7">,</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">*</span><span class="s1"> </span><span class="s12">0.05</span><span class="s7">,</span><span class="s1"> </span><span class="s12">0</span><span class="s7">,</span><span class="s1"> </span><span class="s12">2</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s15">PI</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">fill</span><span class="s7">();</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">globalAlpha </span><span class="s7">=</span><span class="s1"> </span><span class="s12">1.0</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">restore</span><span class="s7">();</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s7">}</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">// --- Main drawing function for all elements ---</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">function</span><span class="s1"> drawScene</span><span class="s7">()</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>canvas</span><span class="s7">.</span><span class="s1">style</span><span class="s7">.</span><span class="s1">backgroundColor </span><span class="s7">=</span><span class="s1"> canvasBgColor</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">clearRect</span><span class="s7">(</span><span class="s12">0</span><span class="s7">,</span><span class="s1"> </span><span class="s12">0</span><span class="s7">,</span><span class="s1"> canvas</span><span class="s7">.</span><span class="s1">width</span><span class="s7">,</span><span class="s1"> canvas</span><span class="s7">.</span><span class="s1">height</span><span class="s7">);</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">const</span><span class="s1"> allItems </span><span class="s7">=</span><span class="s1"> </span><span class="s7">[</span><span class="s1">unitCube</span><span class="s7">,</span><span class="s1"> rod</span><span class="s7">,</span><span class="s1"> flat</span><span class="s7">,</span><span class="s1"> block1000</span><span class="s7">,</span><span class="s1"> circle</span><span class="s7">,</span><span class="s1"> equilateralTriangle</span><span class="s7">,</span><span class="s1"> pentagon</span><span class="s7">,</span><span class="s1"> hexagon</span><span class="s7">,</span><span class="s1"> square</span><span class="s7">,</span><span class="s1"> parallelogram</span><span class="s7">,</span><span class="s1"> rhombus</span><span class="s7">,</span><span class="s1"> rightTriangle</span><span class="s7">,</span><span class="s1"> diamond</span><span class="s7">,</span><span class="s1"> octagon</span><span class="s7">,</span><span class="s1"> clock</span><span class="s7">];</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p6"><span class="s1"><span class="Apple-converted-space">            </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>allItems</span><span class="s7">.</span><span class="s1">forEach</span><span class="s7">(</span><span class="s1">item </span><span class="s7">=&gt;</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(!</span><span class="s1">selectedItems</span><span class="s7">.</span><span class="s1">includes</span><span class="s7">(</span><span class="s1">item</span><span class="s7">))</span><span class="s1"> </span><span class="s7">{</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                    </span>drawItem</span><span class="s7">(</span><span class="s1">item</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s7">});</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>selectedItems</span><span class="s7">.</span><span class="s1">forEach</span><span class="s7">(</span><span class="s1">item </span><span class="s7">=&gt;</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>drawItem</span><span class="s7">(</span><span class="s1">item</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>highlightItem</span><span class="s7">(</span><span class="s1">item</span><span class="s7">);</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s7">});</span></p>
<p class="p6"><span class="s1"><span class="Apple-converted-space">            </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">lassoRect</span><span class="s7">.</span><span class="s1">active</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>ctx</span><span class="s7">.</span><span class="s1">save</span><span class="s7">();</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>ctx</span><span class="s7">.</span><span class="s1">strokeStyle </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'blue'</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>ctx</span><span class="s7">.</span><span class="s1">lineWidth </span><span class="s7">=</span><span class="s1"> </span><span class="s12">1</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>ctx</span><span class="s7">.</span><span class="s1">setLineDash</span><span class="s7">([</span><span class="s12">5</span><span class="s7">,</span><span class="s1"> </span><span class="s12">5</span><span class="s7">]);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">const</span><span class="s1"> rectX </span><span class="s7">=</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">min</span><span class="s7">(</span><span class="s1">lassoRect</span><span class="s7">.</span><span class="s1">startX</span><span class="s7">,</span><span class="s1"> lassoRect</span><span class="s7">.</span><span class="s1">currentX</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">const</span><span class="s1"> rectY </span><span class="s7">=</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">min</span><span class="s7">(</span><span class="s1">lassoRect</span><span class="s7">.</span><span class="s1">startY</span><span class="s7">,</span><span class="s1"> lassoRect</span><span class="s7">.</span><span class="s1">currentY</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">const</span><span class="s1"> rectWidth </span><span class="s7">=</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">abs</span><span class="s7">(</span><span class="s1">lassoRect</span><span class="s7">.</span><span class="s1">startX </span><span class="s7">-</span><span class="s1"> lassoRect</span><span class="s7">.</span><span class="s1">currentX</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">const</span><span class="s1"> rectHeight </span><span class="s7">=</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">abs</span><span class="s7">(</span><span class="s1">lassoRect</span><span class="s7">.</span><span class="s1">startY </span><span class="s7">-</span><span class="s1"> lassoRect</span><span class="s7">.</span><span class="s1">currentY</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>ctx</span><span class="s7">.</span><span class="s1">strokeRect</span><span class="s7">(</span><span class="s1">rectX</span><span class="s7">,</span><span class="s1"> rectY</span><span class="s7">,</span><span class="s1"> rectWidth</span><span class="s7">,</span><span class="s1"> rectHeight</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>ctx</span><span class="s7">.</span><span class="s1">restore</span><span class="s7">();</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s7">}</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">function</span><span class="s1"> drawItem</span><span class="s7">(</span><span class="s1">item</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(!</span><span class="s1">item</span><span class="s7">)</span><span class="s1"> </span><span class="s10">return</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'rod'</span><span class="s7">)</span><span class="s1"> renderRod</span><span class="s7">(</span><span class="s1">item</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'flat'</span><span class="s7">)</span><span class="s1"> renderFlat</span><span class="s7">(</span><span class="s1">item</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'block1000'</span><span class="s7">)</span><span class="s1"> renderBlock1000</span><span class="s7">(</span><span class="s1">item</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'cube'</span><span class="s7">)</span><span class="s1"> renderUnitCube</span><span class="s7">(</span><span class="s1">item</span><span class="s7">);</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'circle'</span><span class="s7">)</span><span class="s1"> render2DCircle</span><span class="s7">(</span><span class="s1">item</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'equilateralTriangle'</span><span class="s7">)</span><span class="s1"> render2DEquilateralTriangle</span><span class="s7">(</span><span class="s1">item</span><span class="s7">);</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'pentagon'</span><span class="s7">)</span><span class="s1"> render2DRegularPolygon</span><span class="s7">(</span><span class="s1">item</span><span class="s7">,</span><span class="s1"> </span><span class="s12">5</span><span class="s7">,</span><span class="s1"> </span><span class="s7">-</span><span class="s15">Math</span><span class="s7">.</span><span class="s15">PI</span><span class="s7">/</span><span class="s12">2</span><span class="s1"> </span><span class="s7">+</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s15">PI</span><span class="s1"> </span><span class="s7">/</span><span class="s1"> </span><span class="s12">10</span><span class="s7">);</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'hexagon'</span><span class="s7">)</span><span class="s1"> render2DRegularPolygon</span><span class="s7">(</span><span class="s1">item</span><span class="s7">,</span><span class="s1"> </span><span class="s12">6</span><span class="s7">,</span><span class="s1"> </span><span class="s12">0</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'square'</span><span class="s7">)</span><span class="s1"> render2DSquare</span><span class="s7">(</span><span class="s1">item</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'parallelogram'</span><span class="s7">)</span><span class="s1"> render2DParallelogram</span><span class="s7">(</span><span class="s1">item</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'rhombus'</span><span class="s7">)</span><span class="s1"> render2DRhombus</span><span class="s7">(</span><span class="s1">item</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'rightTriangle'</span><span class="s7">)</span><span class="s1"> render2DRightTriangle</span><span class="s7">(</span><span class="s1">item</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'diamond'</span><span class="s7">)</span><span class="s1"> render2DDiamond</span><span class="s7">(</span><span class="s1">item</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'octagon'</span><span class="s7">)</span><span class="s1"> render2DRegularPolygon</span><span class="s7">(</span><span class="s1">item</span><span class="s7">,</span><span class="s1"> </span><span class="s12">8</span><span class="s7">,</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s15">PI</span><span class="s7">/</span><span class="s12">8</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'clock'</span><span class="s7">)</span><span class="s1"> renderClock</span><span class="s7">(</span><span class="s1">item</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s7">}</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">function</span><span class="s1"> highlightItem</span><span class="s7">(</span><span class="s1">item</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(!</span><span class="s1">item</span><span class="s7">)</span><span class="s1"> </span><span class="s10">return</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">save</span><span class="s7">();</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">strokeStyle </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'rgba(0, 100, 255, 0.7)'</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">lineWidth </span><span class="s7">=</span><span class="s1"> </span><span class="s12">3</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">setLineDash</span><span class="s7">([</span><span class="s12">4</span><span class="s7">,</span><span class="s1"> </span><span class="s12">2</span><span class="s7">]);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">let</span><span class="s1"> x </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">x</span><span class="s7">,</span><span class="s1"> y </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">y</span><span class="s7">,</span><span class="s1"> w</span><span class="s7">,</span><span class="s1"> h</span><span class="s7">,</span><span class="s1"> po </span><span class="s7">=</span><span class="s1"> </span><span class="s12">0</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'cube'</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> w </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">size</span><span class="s7">;</span><span class="s1"> h </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">size</span><span class="s7">;</span><span class="s1"> po </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">size </span><span class="s7">*</span><span class="s1"> </span><span class="s15">DEPTH_PERSPECTIVE_RATIO</span><span class="s7">;</span><span class="s1"> </span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'rod'</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> w </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">length</span><span class="s7">;</span><span class="s1"> h </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">height</span><span class="s7">;</span><span class="s1"> po </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">depth </span><span class="s7">*</span><span class="s1"> </span><span class="s15">DEPTH_PERSPECTIVE_RATIO</span><span class="s7">;</span><span class="s1"> </span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'flat'</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> w </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">sideLength</span><span class="s7">;</span><span class="s1"> h </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">sideLength</span><span class="s7">;</span><span class="s1"> po </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">thickness </span><span class="s7">*</span><span class="s1"> </span><span class="s15">DEPTH_PERSPECTIVE_RATIO</span><span class="s7">;</span><span class="s1"> </span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'block1000'</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> w </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">sideLength</span><span class="s7">;</span><span class="s1"> h </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">sideLength</span><span class="s7">;</span><span class="s1"> po </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">sideLength </span><span class="s7">*</span><span class="s1"> </span><span class="s15">DEPTH_PERSPECTIVE_RATIO</span><span class="s7">;</span><span class="s1"> </span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'circle'</span><span class="s1"> </span><span class="s7">||</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'clock'</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> w </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">*</span><span class="s1"> </span><span class="s12">2</span><span class="s7">;</span><span class="s1"> h </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">*</span><span class="s1"> </span><span class="s12">2</span><span class="s7">;</span><span class="s1"> </span><span class="s7">}</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'square'</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> w </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">sideLength</span><span class="s7">;</span><span class="s1"> h </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">sideLength</span><span class="s7">;}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'equilateralTriangle'</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> w </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">sideLength</span><span class="s7">;</span><span class="s1"> h </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">sideLength </span><span class="s7">*</span><span class="s1"> </span><span class="s7">(</span><span class="s15">Math</span><span class="s7">.</span><span class="s1">sqrt</span><span class="s7">(</span><span class="s12">3</span><span class="s7">)/</span><span class="s12">2</span><span class="s7">);</span><span class="s1"> </span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'pentagon'</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> </span><span class="s10">const</span><span class="s1"> r </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">sideLength </span><span class="s7">/</span><span class="s1"> </span><span class="s7">(</span><span class="s12">2</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">sin</span><span class="s7">(</span><span class="s15">Math</span><span class="s7">.</span><span class="s15">PI</span><span class="s1"> </span><span class="s7">/</span><span class="s1"> </span><span class="s12">5</span><span class="s7">));</span><span class="s1"> w </span><span class="s7">=</span><span class="s1"> r</span><span class="s7">*</span><span class="s12">2</span><span class="s7">;</span><span class="s1"> h </span><span class="s7">=</span><span class="s1"> r</span><span class="s7">*</span><span class="s12">2</span><span class="s7">;</span><span class="s1"> </span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'hexagon'</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> </span><span class="s10">const</span><span class="s1"> r </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">sideLength </span><span class="s7">/</span><span class="s1"> </span><span class="s7">(</span><span class="s12">2</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">sin</span><span class="s7">(</span><span class="s15">Math</span><span class="s7">.</span><span class="s15">PI</span><span class="s1"> </span><span class="s7">/</span><span class="s1"> </span><span class="s12">6</span><span class="s7">));</span><span class="s1"> w </span><span class="s7">=</span><span class="s1"> r</span><span class="s7">*</span><span class="s12">2</span><span class="s7">;</span><span class="s1"> h </span><span class="s7">=</span><span class="s1"> r</span><span class="s7">*</span><span class="s1"> </span><span class="s7">(</span><span class="s15">Math</span><span class="s7">.</span><span class="s1">sqrt</span><span class="s7">(</span><span class="s12">3</span><span class="s7">));}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'parallelogram'</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> w </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">width</span><span class="s7">;</span><span class="s1"> h </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">height</span><span class="s7">;</span><span class="s1"> </span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'rhombus'</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> w </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">d2</span><span class="s7">;</span><span class="s1"> h </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">d1</span><span class="s7">;</span><span class="s1"> </span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'rightTriangle'</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> w </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">base</span><span class="s7">;</span><span class="s1"> h </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">height</span><span class="s7">;</span><span class="s1"> </span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'diamond'</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> w </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">width</span><span class="s7">;</span><span class="s1"> h </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">height</span><span class="s7">;</span><span class="s1"> </span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'octagon'</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> </span><span class="s10">const</span><span class="s1"> r </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">sideLength </span><span class="s7">/</span><span class="s1"> </span><span class="s7">(</span><span class="s12">2</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">sin</span><span class="s7">(</span><span class="s15">Math</span><span class="s7">.</span><span class="s15">PI</span><span class="s1"> </span><span class="s7">/</span><span class="s1"> </span><span class="s12">8</span><span class="s7">));</span><span class="s1"> w </span><span class="s7">=</span><span class="s1"> r</span><span class="s7">*</span><span class="s12">2</span><span class="s7">;</span><span class="s1"> h </span><span class="s7">=</span><span class="s1"> r</span><span class="s7">*</span><span class="s12">2</span><span class="s7">;</span><span class="s1"> </span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">restore</span><span class="s7">();</span><span class="s1"> </span><span class="s10">return</span><span class="s7">;</span><span class="s1"> </span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">const</span><span class="s1"> is3DBlock </span><span class="s7">=</span><span class="s1"> </span><span class="s7">[</span><span class="s9">'cube'</span><span class="s7">,</span><span class="s1"> </span><span class="s9">'rod'</span><span class="s7">,</span><span class="s1"> </span><span class="s9">'flat'</span><span class="s7">,</span><span class="s1"> </span><span class="s9">'block1000'</span><span class="s7">].</span><span class="s1">includes</span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">const</span><span class="s1"> visualWidth </span><span class="s7">=</span><span class="s1"> w </span><span class="s7">+</span><span class="s1"> </span><span class="s7">(</span><span class="s1">is3DBlock </span><span class="s7">?</span><span class="s1"> po </span><span class="s7">:</span><span class="s1"> shadowOffsetX</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">const</span><span class="s1"> visualHeight </span><span class="s7">=</span><span class="s1"> h </span><span class="s7">+</span><span class="s1"> </span><span class="s7">(</span><span class="s1">is3DBlock </span><span class="s7">?</span><span class="s1"> po </span><span class="s7">:</span><span class="s1"> shadowOffsetY</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">const</span><span class="s1"> yOffset </span><span class="s7">=</span><span class="s1"> is3DBlock </span><span class="s7">?</span><span class="s1"> po</span><span class="s7">/</span><span class="s12">2</span><span class="s1"> </span><span class="s7">:</span><span class="s1"> </span><span class="s12">0</span><span class="s7">;</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">strokeRect</span><span class="s7">(</span><span class="s1">x </span><span class="s7">-</span><span class="s1"> visualWidth </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s7">,</span><span class="s1"> y </span><span class="s7">-</span><span class="s1"> visualHeight </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s1"> </span><span class="s7">-</span><span class="s1"> yOffset </span><span class="s7">,</span><span class="s1"> visualWidth</span><span class="s7">,</span><span class="s1"> visualHeight</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">restore</span><span class="s7">();</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s7">}</span></p>
<p class="p6"><span class="s1"><span class="Apple-converted-space">        </span></span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">// --- Function to initialize or update all shapes for layout ---</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">function</span><span class="s1"> initializeShapesInLayout</span><span class="s7">()</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">const</span><span class="s1"> baseTenRow </span><span class="s7">=</span><span class="s1"> </span><span class="s7">[</span><span class="s1">unitCube</span><span class="s7">,</span><span class="s1"> rod</span><span class="s7">,</span><span class="s1"> flat</span><span class="s7">,</span><span class="s1"> block1000</span><span class="s7">];</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">const</span><span class="s1"> geoRow1 </span><span class="s7">=</span><span class="s1"> </span><span class="s7">[</span><span class="s1">circle</span><span class="s7">,</span><span class="s1"> equilateralTriangle</span><span class="s7">,</span><span class="s1"> pentagon</span><span class="s7">,</span><span class="s1"> hexagon</span><span class="s7">];</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">const</span><span class="s1"> geoRow2 </span><span class="s7">=</span><span class="s1"> </span><span class="s7">[</span><span class="s1">square</span><span class="s7">,</span><span class="s1"> parallelogram</span><span class="s7">,</span><span class="s1"> rhombus</span><span class="s7">,</span><span class="s1"> rightTriangle</span><span class="s7">,</span><span class="s1"> diamond</span><span class="s7">,</span><span class="s1"> octagon</span><span class="s7">,</span><span class="s1"> clock</span><span class="s7">];</span></p>
<p class="p6"><span class="s1"><span class="Apple-converted-space">            </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">const</span><span class="s1"> spacing </span><span class="s7">=</span><span class="s1"> baseSize </span><span class="s7">*</span><span class="s1"> </span><span class="s12">1.0</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">const</span><span class="s1"> rowSpacing </span><span class="s7">=</span><span class="s1"> baseSize </span><span class="s7">*</span><span class="s1"> </span><span class="s12">5</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s1">// Initialize Base Ten Blocks - Row 1</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">let</span><span class="s1"> totalWidthRow1 </span><span class="s7">=</span><span class="s1"> </span><span class="s12">0</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>baseTenRow</span><span class="s7">.</span><span class="s1">forEach</span><span class="s7">(</span><span class="s1">shape </span><span class="s7">=&gt;</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">shape</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'cube'</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> shape</span><span class="s7">.</span><span class="s1">size </span><span class="s7">=</span><span class="s1"> baseSize</span><span class="s7">;</span><span class="s1"> shape</span><span class="s7">.</span><span class="s1">color </span><span class="s7">=</span><span class="s1"> unitCubeColor</span><span class="s7">;</span><span class="s1"> totalWidthRow1 </span><span class="s7">+=</span><span class="s1"> shape</span><span class="s7">.</span><span class="s1">size</span><span class="s7">;}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">shape</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'rod'</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> shape</span><span class="s7">.</span><span class="s1">length </span><span class="s7">=</span><span class="s1"> rodLengthUnits </span><span class="s7">*</span><span class="s1"> baseSize</span><span class="s7">;</span><span class="s1"> shape</span><span class="s7">.</span><span class="s1">height </span><span class="s7">=</span><span class="s1"> baseSize</span><span class="s7">;</span><span class="s1"> shape</span><span class="s7">.</span><span class="s1">depth </span><span class="s7">=</span><span class="s1"> baseSize</span><span class="s7">;</span><span class="s1"> shape</span><span class="s7">.</span><span class="s1">color </span><span class="s7">=</span><span class="s1"> rodColor</span><span class="s7">;</span><span class="s1"> totalWidthRow1 </span><span class="s7">+=</span><span class="s1"> shape</span><span class="s7">.</span><span class="s1">length</span><span class="s7">;}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">shape</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'flat'</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> shape</span><span class="s7">.</span><span class="s1">sideLength </span><span class="s7">=</span><span class="s1"> flatSideUnits </span><span class="s7">*</span><span class="s1"> baseSize</span><span class="s7">;</span><span class="s1"> shape</span><span class="s7">.</span><span class="s1">thickness </span><span class="s7">=</span><span class="s1"> baseSize</span><span class="s7">;</span><span class="s1"> shape</span><span class="s7">.</span><span class="s1">color </span><span class="s7">=</span><span class="s1"> flatColor</span><span class="s7">;</span><span class="s1"> totalWidthRow1 </span><span class="s7">+=</span><span class="s1"> shape</span><span class="s7">.</span><span class="s1">sideLength</span><span class="s7">;}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">shape</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'block1000'</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> shape</span><span class="s7">.</span><span class="s1">sideLength </span><span class="s7">=</span><span class="s1"> block1000SideUnits </span><span class="s7">*</span><span class="s1"> baseSize</span><span class="s7">;</span><span class="s1"> shape</span><span class="s7">.</span><span class="s1">color </span><span class="s7">=</span><span class="s1"> block1000Color</span><span class="s7">;</span><span class="s1"> totalWidthRow1 </span><span class="s7">+=</span><span class="s1"> shape</span><span class="s7">.</span><span class="s1">sideLength</span><span class="s7">;}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s7">});</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>totalWidthRow1 </span><span class="s7">+=</span><span class="s1"> </span><span class="s7">(</span><span class="s1">baseTenRow</span><span class="s7">.</span><span class="s1">length </span><span class="s7">-</span><span class="s1"> </span><span class="s12">1</span><span class="s7">)</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> spacing</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">let</span><span class="s1"> currentXRow1 </span><span class="s7">=</span><span class="s1"> </span><span class="s7">(</span><span class="s1">totalWidthRow1 </span><span class="s7">&gt;</span><span class="s1"> canvas</span><span class="s7">.</span><span class="s1">width</span><span class="s7">)</span><span class="s1"> </span><span class="s7">?</span><span class="s1"> baseSize </span><span class="s7">*</span><span class="s1"> </span><span class="s12">0.5</span><span class="s1"> </span><span class="s7">:</span><span class="s1"> </span><span class="s7">(</span><span class="s1">canvas</span><span class="s7">.</span><span class="s1">width </span><span class="s7">-</span><span class="s1"> totalWidthRow1</span><span class="s7">)</span><span class="s1"> </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">const</span><span class="s1"> yPosRow1 </span><span class="s7">=</span><span class="s1"> canvas</span><span class="s7">.</span><span class="s1">height </span><span class="s7">*</span><span class="s1"> </span><span class="s12">0.20</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>baseTenRow</span><span class="s7">.</span><span class="s1">forEach</span><span class="s7">(</span><span class="s1">shape </span><span class="s7">=&gt;</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">let</span><span class="s1"> w </span><span class="s7">=</span><span class="s1"> </span><span class="s12">0</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">shape</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'cube'</span><span class="s7">)</span><span class="s1"> w </span><span class="s7">=</span><span class="s1"> shape</span><span class="s7">.</span><span class="s1">size</span><span class="s7">;</span><span class="s1"> </span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">shape</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'rod'</span><span class="s7">)</span><span class="s1"> w </span><span class="s7">=</span><span class="s1"> shape</span><span class="s7">.</span><span class="s1">length</span><span class="s7">;</span><span class="s1"> </span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">shape</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'flat'</span><span class="s1"> </span><span class="s7">||</span><span class="s1"> shape</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'block1000'</span><span class="s7">)</span><span class="s1"> w </span><span class="s7">=</span><span class="s1"> shape</span><span class="s7">.</span><span class="s1">sideLength</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                 </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(!</span><span class="s1">shape</span><span class="s7">.</span><span class="s1">hasBeenDragged </span><span class="s7">||</span><span class="s1"> oldCanvasWidth </span><span class="s7">!==</span><span class="s1"> canvas</span><span class="s7">.</span><span class="s1">width </span><span class="s7">||</span><span class="s1"> oldCanvasHeight </span><span class="s7">!==</span><span class="s1"> canvas</span><span class="s7">.</span><span class="s1">height</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                    </span>shape</span><span class="s7">.</span><span class="s1">x </span><span class="s7">=</span><span class="s1"> currentXRow1 </span><span class="s7">+</span><span class="s1"> w </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s7">;</span><span class="s1"> shape</span><span class="s7">.</span><span class="s1">y </span><span class="s7">=</span><span class="s1"> yPosRow1</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s7">}</span><span class="s1"> currentXRow1 </span><span class="s7">+=</span><span class="s1"> w </span><span class="s7">+</span><span class="s1"> spacing</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s7">});</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s1">// Initialize Geometric Shapes - Row 2</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">const</span><span class="s1"> geoShapeDim </span><span class="s7">=</span><span class="s1"> baseSize </span><span class="s7">*</span><span class="s1"> </span><span class="s12">3</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>circle</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">=</span><span class="s1"> geoShapeDim </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s7">;</span><span class="s1"> circle</span><span class="s7">.</span><span class="s1">color </span><span class="s7">=</span><span class="s1"> circleColor</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>equilateralTriangle</span><span class="s7">.</span><span class="s1">sideLength </span><span class="s7">=</span><span class="s1"> geoShapeDim</span><span class="s7">;</span><span class="s1"> equilateralTriangle</span><span class="s7">.</span><span class="s1">color </span><span class="s7">=</span><span class="s1"> equilateralTriangleColor</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>pentagon</span><span class="s7">.</span><span class="s1">sideLength </span><span class="s7">=</span><span class="s1"> geoShapeDim </span><span class="s7">*</span><span class="s1"> </span><span class="s12">0.7</span><span class="s7">;</span><span class="s1"> pentagon</span><span class="s7">.</span><span class="s1">color </span><span class="s7">=</span><span class="s1"> pentagonColor</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>hexagon</span><span class="s7">.</span><span class="s1">sideLength </span><span class="s7">=</span><span class="s1"> geoShapeDim </span><span class="s7">*</span><span class="s1"> </span><span class="s12">0.6</span><span class="s7">;</span><span class="s1"> hexagon</span><span class="s7">.</span><span class="s1">color </span><span class="s7">=</span><span class="s1"> hexagonColor</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">let</span><span class="s1"> totalWidthRow2 </span><span class="s7">=</span><span class="s1"> circle</span><span class="s7">.</span><span class="s1">radius</span><span class="s7">*</span><span class="s12">2</span><span class="s1"> </span><span class="s7">+</span><span class="s1"> equilateralTriangle</span><span class="s7">.</span><span class="s1">sideLength </span><span class="s7">+</span><span class="s1"> pentagon</span><span class="s7">.</span><span class="s1">sideLength</span><span class="s7">*</span><span class="s12">1.2</span><span class="s1"> </span><span class="s7">+</span><span class="s1"> hexagon</span><span class="s7">.</span><span class="s1">sideLength</span><span class="s7">*</span><span class="s12">1.15</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>totalWidthRow2 </span><span class="s7">+=</span><span class="s1"> </span><span class="s7">(</span><span class="s1">geoRow1</span><span class="s7">.</span><span class="s1">length </span><span class="s7">-</span><span class="s12">1</span><span class="s7">)</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> spacing</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">let</span><span class="s1"> currentXRow2 </span><span class="s7">=</span><span class="s1"> </span><span class="s7">(</span><span class="s1">totalWidthRow2 </span><span class="s7">&gt;</span><span class="s1"> canvas</span><span class="s7">.</span><span class="s1">width</span><span class="s7">)</span><span class="s1"> </span><span class="s7">?</span><span class="s1"> baseSize </span><span class="s7">*</span><span class="s1"> </span><span class="s12">0.5</span><span class="s1"> </span><span class="s7">:</span><span class="s1"> </span><span class="s7">(</span><span class="s1">canvas</span><span class="s7">.</span><span class="s1">width </span><span class="s7">-</span><span class="s1"> totalWidthRow2</span><span class="s7">)</span><span class="s1"> </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">const</span><span class="s1"> yPosRow2 </span><span class="s7">=</span><span class="s1"> canvas</span><span class="s7">.</span><span class="s1">height </span><span class="s7">*</span><span class="s1"> </span><span class="s12">0.50</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">             </span>geoRow1</span><span class="s7">.</span><span class="s1">forEach</span><span class="s7">(</span><span class="s1">shape </span><span class="s7">=&gt;</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">let</span><span class="s1"> w </span><span class="s7">=</span><span class="s1"> </span><span class="s12">0</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">if</span><span class="s7">(</span><span class="s1">shape</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'circle'</span><span class="s7">)</span><span class="s1"> w </span><span class="s7">=</span><span class="s1"> shape</span><span class="s7">.</span><span class="s1">radius</span><span class="s7">*</span><span class="s12">2</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s7">(</span><span class="s1">shape</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'equilateralTriangle'</span><span class="s7">)</span><span class="s1"> w </span><span class="s7">=</span><span class="s1"> shape</span><span class="s7">.</span><span class="s1">sideLength</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s7">(</span><span class="s1">shape</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'pentagon'</span><span class="s7">)</span><span class="s1"> w </span><span class="s7">=</span><span class="s1"> shape</span><span class="s7">.</span><span class="s1">sideLength </span><span class="s7">*</span><span class="s1"> </span><span class="s12">1.2</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s7">(</span><span class="s1">shape</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'hexagon'</span><span class="s7">)</span><span class="s1"> w </span><span class="s7">=</span><span class="s1"> shape</span><span class="s7">.</span><span class="s1">sideLength </span><span class="s7">*</span><span class="s1"> </span><span class="s12">1.15</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s12">2</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">cos</span><span class="s7">(</span><span class="s15">Math</span><span class="s7">.</span><span class="s15">PI</span><span class="s7">/</span><span class="s12">6</span><span class="s7">)</span><span class="s1"> </span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(!</span><span class="s1">shape</span><span class="s7">.</span><span class="s1">hasBeenDragged </span><span class="s7">||</span><span class="s1"> oldCanvasWidth </span><span class="s7">!==</span><span class="s1"> canvas</span><span class="s7">.</span><span class="s1">width </span><span class="s7">||</span><span class="s1"> oldCanvasHeight </span><span class="s7">!==</span><span class="s1"> canvas</span><span class="s7">.</span><span class="s1">height</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                     </span>shape</span><span class="s7">.</span><span class="s1">x </span><span class="s7">=</span><span class="s1"> currentXRow2 </span><span class="s7">+</span><span class="s1"> w </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s7">;</span><span class="s1"> shape</span><span class="s7">.</span><span class="s1">y </span><span class="s7">=</span><span class="s1"> yPosRow2</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s7">}</span><span class="s1"> currentXRow2 </span><span class="s7">+=</span><span class="s1"> w </span><span class="s7">+</span><span class="s1"> spacing</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s7">});</span></p>
<p class="p6"><span class="s1"><span class="Apple-converted-space">            </span></span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s1">// Initialize Geometric Shapes - Row 3</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>square</span><span class="s7">.</span><span class="s1">sideLength </span><span class="s7">=</span><span class="s1"> geoShapeDim</span><span class="s7">;</span><span class="s1"> square</span><span class="s7">.</span><span class="s1">color </span><span class="s7">=</span><span class="s1"> squareColor</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>parallelogram</span><span class="s7">.</span><span class="s1">width </span><span class="s7">=</span><span class="s1"> geoShapeDim </span><span class="s7">*</span><span class="s1"> </span><span class="s12">1.2</span><span class="s7">;</span><span class="s1"> parallelogram</span><span class="s7">.</span><span class="s1">height </span><span class="s7">=</span><span class="s1"> geoShapeDim </span><span class="s7">*</span><span class="s1"> </span><span class="s12">0.8</span><span class="s7">;</span><span class="s1"> parallelogram</span><span class="s7">.</span><span class="s1">skew </span><span class="s7">=</span><span class="s1"> geoShapeDim </span><span class="s7">*</span><span class="s1"> </span><span class="s12">0.3</span><span class="s7">;</span><span class="s1"> parallelogram</span><span class="s7">.</span><span class="s1">color </span><span class="s7">=</span><span class="s1"> parallelogramColor</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>rhombus</span><span class="s7">.</span><span class="s1">d1 </span><span class="s7">=</span><span class="s1"> geoShapeDim </span><span class="s7">*</span><span class="s1"> </span><span class="s12">1.2</span><span class="s7">;</span><span class="s1"> rhombus</span><span class="s7">.</span><span class="s1">d2 </span><span class="s7">=</span><span class="s1"> geoShapeDim </span><span class="s7">*</span><span class="s1"> </span><span class="s12">0.8</span><span class="s7">;</span><span class="s1"> rhombus</span><span class="s7">.</span><span class="s1">color </span><span class="s7">=</span><span class="s1"> rhombusColor</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>rightTriangle</span><span class="s7">.</span><span class="s1">base </span><span class="s7">=</span><span class="s1"> geoShapeDim</span><span class="s7">;</span><span class="s1"> rightTriangle</span><span class="s7">.</span><span class="s1">height </span><span class="s7">=</span><span class="s1"> geoShapeDim</span><span class="s7">;</span><span class="s1"> rightTriangle</span><span class="s7">.</span><span class="s1">color </span><span class="s7">=</span><span class="s1"> rightTriangleColor</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>diamond</span><span class="s7">.</span><span class="s1">width </span><span class="s7">=</span><span class="s1"> geoShapeDim </span><span class="s7">*</span><span class="s1"> </span><span class="s12">0.8</span><span class="s7">;</span><span class="s1"> diamond</span><span class="s7">.</span><span class="s1">height </span><span class="s7">=</span><span class="s1"> geoShapeDim </span><span class="s7">*</span><span class="s1"> </span><span class="s12">1.2</span><span class="s7">;</span><span class="s1"> diamond</span><span class="s7">.</span><span class="s1">color </span><span class="s7">=</span><span class="s1"> diamondColor</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>octagon</span><span class="s7">.</span><span class="s1">sideLength </span><span class="s7">=</span><span class="s1"> geoShapeDim </span><span class="s7">*</span><span class="s1"> </span><span class="s12">0.5</span><span class="s7">;</span><span class="s1"> octagon</span><span class="s7">.</span><span class="s1">color </span><span class="s7">=</span><span class="s1"> octagonColor</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>clock</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">=</span><span class="s1"> geoShapeDim </span><span class="s7">/</span><span class="s1"> </span><span class="s12">1.8</span><span class="s7">;</span><span class="s1"> clock</span><span class="s7">.</span><span class="s1">color </span><span class="s7">=</span><span class="s1"> clockFaceColor</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">let</span><span class="s1"> totalWidthRow3 </span><span class="s7">=</span><span class="s1"> square</span><span class="s7">.</span><span class="s1">sideLength </span><span class="s7">+</span><span class="s1"> parallelogram</span><span class="s7">.</span><span class="s1">width </span><span class="s7">+</span><span class="s1"> rhombus</span><span class="s7">.</span><span class="s1">d2 </span><span class="s7">+</span><span class="s1"> rightTriangle</span><span class="s7">.</span><span class="s1">base </span><span class="s7">+</span><span class="s1"> diamond</span><span class="s7">.</span><span class="s1">width </span><span class="s7">+</span><span class="s1"> octagon</span><span class="s7">.</span><span class="s1">sideLength </span><span class="s7">*</span><span class="s1"> </span><span class="s12">2.414</span><span class="s1"> </span><span class="s7">+</span><span class="s1"> clock</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">*</span><span class="s1"> </span><span class="s12">2</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>totalWidthRow3 </span><span class="s7">+=</span><span class="s1"> </span><span class="s7">(</span><span class="s1">geoRow2</span><span class="s7">.</span><span class="s1">length </span><span class="s7">-</span><span class="s1"> </span><span class="s12">1</span><span class="s7">)</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> spacing</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">let</span><span class="s1"> currentXRow3 </span><span class="s7">=</span><span class="s1"> </span><span class="s7">(</span><span class="s1">totalWidthRow3 </span><span class="s7">&gt;</span><span class="s1"> canvas</span><span class="s7">.</span><span class="s1">width</span><span class="s7">)</span><span class="s1"> </span><span class="s7">?</span><span class="s1"> baseSize </span><span class="s7">*</span><span class="s1"> </span><span class="s12">0.5</span><span class="s1"> </span><span class="s7">:</span><span class="s1"> </span><span class="s7">(</span><span class="s1">canvas</span><span class="s7">.</span><span class="s1">width </span><span class="s7">-</span><span class="s1"> totalWidthRow3</span><span class="s7">)</span><span class="s1"> </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">const</span><span class="s1"> yPosRow3 </span><span class="s7">=</span><span class="s1"> canvas</span><span class="s7">.</span><span class="s1">height </span><span class="s7">*</span><span class="s1"> </span><span class="s12">0.80</span><span class="s7">;</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>geoRow2</span><span class="s7">.</span><span class="s1">forEach</span><span class="s7">(</span><span class="s1">shape </span><span class="s7">=&gt;</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">let</span><span class="s1"> w </span><span class="s7">=</span><span class="s1"> </span><span class="s12">0</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">if</span><span class="s7">(</span><span class="s1">shape</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'square'</span><span class="s7">)</span><span class="s1"> w </span><span class="s7">=</span><span class="s1"> shape</span><span class="s7">.</span><span class="s1">sideLength</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s7">(</span><span class="s1">shape</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'parallelogram'</span><span class="s7">)</span><span class="s1"> w </span><span class="s7">=</span><span class="s1"> shape</span><span class="s7">.</span><span class="s1">width</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s7">(</span><span class="s1">shape</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'rhombus'</span><span class="s7">)</span><span class="s1"> w </span><span class="s7">=</span><span class="s1"> shape</span><span class="s7">.</span><span class="s1">d2</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s7">(</span><span class="s1">shape</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'rightTriangle'</span><span class="s7">)</span><span class="s1"> w </span><span class="s7">=</span><span class="s1"> shape</span><span class="s7">.</span><span class="s1">base</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s7">(</span><span class="s1">shape</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'diamond'</span><span class="s7">)</span><span class="s1"> w </span><span class="s7">=</span><span class="s1"> shape</span><span class="s7">.</span><span class="s1">width</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s7">(</span><span class="s1">shape</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'octagon'</span><span class="s7">)</span><span class="s1"> w </span><span class="s7">=</span><span class="s1"> shape</span><span class="s7">.</span><span class="s1">sideLength </span><span class="s7">*</span><span class="s1"> </span><span class="s7">(</span><span class="s12">1</span><span class="s1"> </span><span class="s7">+</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">sqrt</span><span class="s7">(</span><span class="s12">2</span><span class="s7">));</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s7">(</span><span class="s1">shape</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'clock'</span><span class="s7">)</span><span class="s1"> w </span><span class="s7">=</span><span class="s1"> shape</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">*</span><span class="s1"> </span><span class="s12">2</span><span class="s7">;</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(!</span><span class="s1">shape</span><span class="s7">.</span><span class="s1">hasBeenDragged </span><span class="s7">||</span><span class="s1"> oldCanvasWidth </span><span class="s7">!==</span><span class="s1"> canvas</span><span class="s7">.</span><span class="s1">width </span><span class="s7">||</span><span class="s1"> oldCanvasHeight </span><span class="s7">!==</span><span class="s1"> canvas</span><span class="s7">.</span><span class="s1">height</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                    </span>shape</span><span class="s7">.</span><span class="s1">x </span><span class="s7">=</span><span class="s1"> currentXRow3 </span><span class="s7">+</span><span class="s1"> w </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s7">;</span><span class="s1"> shape</span><span class="s7">.</span><span class="s1">y </span><span class="s7">=</span><span class="s1"> yPosRow3</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>currentXRow3 </span><span class="s7">+=</span><span class="s1"> w </span><span class="s7">+</span><span class="s1"> spacing</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s7">});</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">             </span>allDrawableItems</span><span class="s7">.</span><span class="s1">forEach</span><span class="s7">(</span><span class="s1">item </span><span class="s7">=&gt;</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">hasBeenDragged </span><span class="s7">=</span><span class="s1"> </span><span class="s10">false</span><span class="s7">);</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s7">}</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">// --- Function to handle canvas resizing and element position normalization ---</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">function</span><span class="s1"> resizeCanvasAndDraw</span><span class="s7">()</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>canvas</span><span class="s7">.</span><span class="s1">width </span><span class="s7">=</span><span class="s1"> window</span><span class="s7">.</span><span class="s1">innerWidth</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>canvas</span><span class="s7">.</span><span class="s1">height </span><span class="s7">=</span><span class="s1"> window</span><span class="s7">.</span><span class="s1">innerHeight</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>initializeShapesInLayout</span><span class="s7">();</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>oldCanvasWidth </span><span class="s7">=</span><span class="s1"> canvas</span><span class="s7">.</span><span class="s1">width</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>oldCanvasHeight </span><span class="s7">=</span><span class="s1"> canvas</span><span class="s7">.</span><span class="s1">height</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>drawScene</span><span class="s7">();</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s7">}</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">// --- Mouse Event Listeners for Dragging and Lasso ---</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span>canvas</span><span class="s7">.</span><span class="s1">addEventListener</span><span class="s7">(</span><span class="s9">'mousedown'</span><span class="s7">,</span><span class="s1"> </span><span class="s7">(</span><span class="s1">e</span><span class="s7">)</span><span class="s1"> </span><span class="s7">=&gt;</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>lastMouseX </span><span class="s7">=</span><span class="s1"> e</span><span class="s7">.</span><span class="s1">offsetX</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>lastMouseY </span><span class="s7">=</span><span class="s1"> e</span><span class="s7">.</span><span class="s1">offsetY</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">let</span><span class="s1"> clickedOnExistingSelection </span><span class="s7">=</span><span class="s1"> </span><span class="s10">false</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">let</span><span class="s1"> itemClicked </span><span class="s7">=</span><span class="s1"> </span><span class="s10">null</span><span class="s7">;</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">selectedItems</span><span class="s7">.</span><span class="s1">length </span><span class="s7">&gt;</span><span class="s1"> </span><span class="s12">0</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">for</span><span class="s1"> </span><span class="s7">(</span><span class="s10">const</span><span class="s1"> item </span><span class="s10">of</span><span class="s1"> selectedItems</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                     </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">isPointOverItem</span><span class="s7">(</span><span class="s1">lastMouseX</span><span class="s7">,</span><span class="s1"> lastMouseY</span><span class="s7">,</span><span class="s1"> item</span><span class="s7">))</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                        </span>clickedOnExistingSelection </span><span class="s7">=</span><span class="s1"> </span><span class="s10">true</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                        </span>itemClicked </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                        </span></span><span class="s10">break</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                    </span></span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s7">}</span></p>
<p class="p6"><span class="s1"><span class="Apple-converted-space">            </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">clickedOnExistingSelection</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>isDragging </span><span class="s7">=</span><span class="s1"> </span><span class="s10">true</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>draggedItem </span><span class="s7">=</span><span class="s1"> itemClicked</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>dragStartOffsets </span><span class="s7">=</span><span class="s1"> selectedItems</span><span class="s7">.</span><span class="s1">map</span><span class="s7">(</span><span class="s1">item </span><span class="s7">=&gt;</span><span class="s1"> </span><span class="s7">({</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                    </span>item</span><span class="s7">:</span><span class="s1"> item</span><span class="s7">,</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                    </span>dx</span><span class="s7">:</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">x </span><span class="s7">-</span><span class="s1"> lastMouseX</span><span class="s7">,</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                    </span>dy</span><span class="s7">:</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">y </span><span class="s7">-</span><span class="s1"> lastMouseY</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s7">}));</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s7">}</span><span class="s1"> </span><span class="s10">else</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>itemClicked </span><span class="s7">=</span><span class="s1"> </span><span class="s10">null</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">const</span><span class="s1"> itemsToCheck </span><span class="s7">=</span><span class="s1"> </span><span class="s7">[...</span><span class="s1">allDrawableItems</span><span class="s7">].</span><span class="s1">reverse</span><span class="s7">();</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">for</span><span class="s1"> </span><span class="s7">(</span><span class="s10">const</span><span class="s1"> item </span><span class="s10">of</span><span class="s1"> itemsToCheck</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                    </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">isPointOverItem</span><span class="s7">(</span><span class="s1">lastMouseX</span><span class="s7">,</span><span class="s1"> lastMouseY</span><span class="s7">,</span><span class="s1"> item</span><span class="s7">))</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                        </span>itemClicked </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                        </span></span><span class="s10">break</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                    </span></span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s7">}</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">itemClicked</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                    </span>selectedItems </span><span class="s7">=</span><span class="s1"> </span><span class="s7">[</span><span class="s1">itemClicked</span><span class="s7">];</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                    </span>isDragging </span><span class="s7">=</span><span class="s1"> </span><span class="s10">true</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                    </span>draggedItem </span><span class="s7">=</span><span class="s1"> itemClicked</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                    </span>dragStartOffsets </span><span class="s7">=</span><span class="s1"> </span><span class="s7">[{</span><span class="s1"> item</span><span class="s7">:</span><span class="s1"> itemClicked</span><span class="s7">,</span><span class="s1"> dx</span><span class="s7">:</span><span class="s1"> itemClicked</span><span class="s7">.</span><span class="s1">x </span><span class="s7">-</span><span class="s1"> lastMouseX</span><span class="s7">,</span><span class="s1"> dy</span><span class="s7">:</span><span class="s1"> itemClicked</span><span class="s7">.</span><span class="s1">y </span><span class="s7">-</span><span class="s1"> lastMouseY </span><span class="s7">}];</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s7">}</span><span class="s1"> </span><span class="s10">else</span><span class="s1"> </span><span class="s7">{</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                    </span>selectedItems </span><span class="s7">=</span><span class="s1"> </span><span class="s7">[];</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                    </span>draggedItem </span><span class="s7">=</span><span class="s1"> </span><span class="s10">null</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                    </span>isLassoing </span><span class="s7">=</span><span class="s1"> </span><span class="s10">true</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                    </span>lassoRect</span><span class="s7">.</span><span class="s1">startX </span><span class="s7">=</span><span class="s1"> lastMouseX</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                    </span>lassoRect</span><span class="s7">.</span><span class="s1">startY </span><span class="s7">=</span><span class="s1"> lastMouseY</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                    </span>lassoRect</span><span class="s7">.</span><span class="s1">currentX </span><span class="s7">=</span><span class="s1"> lastMouseX</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                    </span>lassoRect</span><span class="s7">.</span><span class="s1">currentY </span><span class="s7">=</span><span class="s1"> lastMouseY</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                    </span>lassoRect</span><span class="s7">.</span><span class="s1">active </span><span class="s7">=</span><span class="s1"> </span><span class="s10">true</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">if</span><span class="s7">(</span><span class="s1">draggedItem</span><span class="s7">)</span><span class="s1"> draggedItem</span><span class="s7">.</span><span class="s1">hasBeenDragged </span><span class="s7">=</span><span class="s1"> </span><span class="s10">true</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">if</span><span class="s7">(</span><span class="s1">selectedItems</span><span class="s7">.</span><span class="s1">length </span><span class="s7">&gt;</span><span class="s1"> </span><span class="s12">0</span><span class="s1"> </span><span class="s7">&amp;&amp;</span><span class="s1"> </span><span class="s7">!</span><span class="s1">draggedItem </span><span class="s7">&amp;&amp;</span><span class="s1"> clickedOnExistingSelection</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>draggedItem </span><span class="s7">=</span><span class="s1"> selectedItems</span><span class="s7">[</span><span class="s12">0</span><span class="s7">];</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>draggedItem</span><span class="s7">.</span><span class="s1">hasBeenDragged </span><span class="s7">=</span><span class="s1"> </span><span class="s10">true</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>selectedItems</span><span class="s7">.</span><span class="s1">forEach</span><span class="s7">(</span><span class="s1">item </span><span class="s7">=&gt;</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">hasBeenDragged </span><span class="s7">=</span><span class="s1"> </span><span class="s10">true</span><span class="s7">);</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>canvas</span><span class="s7">.</span><span class="s1">style</span><span class="s7">.</span><span class="s1">cursor </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'grabbing'</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>drawScene</span><span class="s7">();</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s7">});</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span>canvas</span><span class="s7">.</span><span class="s1">addEventListener</span><span class="s7">(</span><span class="s9">'mousemove'</span><span class="s7">,</span><span class="s1"> </span><span class="s7">(</span><span class="s1">e</span><span class="s7">)</span><span class="s1"> </span><span class="s7">=&gt;</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">const</span><span class="s1"> currentX </span><span class="s7">=</span><span class="s1"> e</span><span class="s7">.</span><span class="s1">offsetX</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">const</span><span class="s1"> currentY </span><span class="s7">=</span><span class="s1"> e</span><span class="s7">.</span><span class="s1">offsetY</span><span class="s7">;</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">isDragging</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">const</span><span class="s1"> masterDx </span><span class="s7">=</span><span class="s1"> currentX </span><span class="s7">-</span><span class="s1"> lastMouseX</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">const</span><span class="s1"> masterDy </span><span class="s7">=</span><span class="s1"> currentY </span><span class="s7">-</span><span class="s1"> lastMouseY</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>selectedItems</span><span class="s7">.</span><span class="s1">forEach</span><span class="s7">(</span><span class="s1">selItem </span><span class="s7">=&gt;</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                    </span>selItem</span><span class="s7">.</span><span class="s1">x </span><span class="s7">+=</span><span class="s1"> masterDx</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                    </span>selItem</span><span class="s7">.</span><span class="s1">y </span><span class="s7">+=</span><span class="s1"> masterDy</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s7">});</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>lastMouseX </span><span class="s7">=</span><span class="s1"> currentX</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>lastMouseY </span><span class="s7">=</span><span class="s1"> currentY</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s7">}</span><span class="s1"> </span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">isLassoing</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>lassoRect</span><span class="s7">.</span><span class="s1">currentX </span><span class="s7">=</span><span class="s1"> currentX</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>lassoRect</span><span class="s7">.</span><span class="s1">currentY </span><span class="s7">=</span><span class="s1"> currentY</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>drawScene</span><span class="s7">();</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s7">});</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span>canvas</span><span class="s7">.</span><span class="s1">addEventListener</span><span class="s7">(</span><span class="s9">'mouseup'</span><span class="s7">,</span><span class="s1"> </span><span class="s7">(</span><span class="s1">e</span><span class="s7">)</span><span class="s1"> </span><span class="s7">=&gt;</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">isDragging</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>isDragging </span><span class="s7">=</span><span class="s1"> </span><span class="s10">false</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s7">}</span><span class="s1"> </span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">isLassoing</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>isLassoing </span><span class="s7">=</span><span class="s1"> </span><span class="s10">false</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>lassoRect</span><span class="s7">.</span><span class="s1">active </span><span class="s7">=</span><span class="s1"> </span><span class="s10">false</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>selectedItems </span><span class="s7">=</span><span class="s1"> </span><span class="s7">[];</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">const</span><span class="s1"> rX </span><span class="s7">=</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">min</span><span class="s7">(</span><span class="s1">lassoRect</span><span class="s7">.</span><span class="s1">startX</span><span class="s7">,</span><span class="s1"> lassoRect</span><span class="s7">.</span><span class="s1">currentX</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">const</span><span class="s1"> rY </span><span class="s7">=</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">min</span><span class="s7">(</span><span class="s1">lassoRect</span><span class="s7">.</span><span class="s1">startY</span><span class="s7">,</span><span class="s1"> lassoRect</span><span class="s7">.</span><span class="s1">currentY</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">const</span><span class="s1"> rW </span><span class="s7">=</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">abs</span><span class="s7">(</span><span class="s1">lassoRect</span><span class="s7">.</span><span class="s1">startX </span><span class="s7">-</span><span class="s1"> lassoRect</span><span class="s7">.</span><span class="s1">currentX</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">const</span><span class="s1"> rH </span><span class="s7">=</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">abs</span><span class="s7">(</span><span class="s1">lassoRect</span><span class="s7">.</span><span class="s1">startY </span><span class="s7">-</span><span class="s1"> lassoRect</span><span class="s7">.</span><span class="s1">currentY</span><span class="s7">);</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>allDrawableItems</span><span class="s7">.</span><span class="s1">forEach</span><span class="s7">(</span><span class="s1">item </span><span class="s7">=&gt;</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                    </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">x </span><span class="s7">&gt;=</span><span class="s1"> rX </span><span class="s7">&amp;&amp;</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">x </span><span class="s7">&lt;=</span><span class="s1"> rX </span><span class="s7">+</span><span class="s1"> rW </span><span class="s7">&amp;&amp;</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">y </span><span class="s7">&gt;=</span><span class="s1"> rY </span><span class="s7">&amp;&amp;</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">y </span><span class="s7">&lt;=</span><span class="s1"> rY </span><span class="s7">+</span><span class="s1"> rH</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                        </span>selectedItems</span><span class="s7">.</span><span class="s1">push</span><span class="s7">(</span><span class="s1">item</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                        </span>item</span><span class="s7">.</span><span class="s1">hasBeenDragged </span><span class="s7">=</span><span class="s1"> </span><span class="s10">true</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                    </span></span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s7">});</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>canvas</span><span class="s7">.</span><span class="s1">style</span><span class="s7">.</span><span class="s1">cursor </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'default'</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>drawScene</span><span class="s7">();</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s7">});</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span>canvas</span><span class="s7">.</span><span class="s1">addEventListener</span><span class="s7">(</span><span class="s9">'mouseleave'</span><span class="s7">,</span><span class="s1"> </span><span class="s7">()</span><span class="s1"> </span><span class="s7">=&gt;</span><span class="s1"> </span><span class="s7">{</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">isDragging</span><span class="s7">)</span><span class="s1"> isDragging </span><span class="s7">=</span><span class="s1"> </span><span class="s10">false</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">isLassoing</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> isLassoing </span><span class="s7">=</span><span class="s1"> </span><span class="s10">false</span><span class="s7">;</span><span class="s1"> lassoRect</span><span class="s7">.</span><span class="s1">active </span><span class="s7">=</span><span class="s1"> </span><span class="s10">false</span><span class="s7">;</span><span class="s1"> </span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>canvas</span><span class="s7">.</span><span class="s1">style</span><span class="s7">.</span><span class="s1">cursor </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'default'</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>drawScene</span><span class="s7">();</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s7">});</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">function</span><span class="s1"> isPointOverItem</span><span class="s7">(</span><span class="s1">mouseX</span><span class="s7">,</span><span class="s1"> mouseY</span><span class="s7">,</span><span class="s1"> item</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(!</span><span class="s1">item</span><span class="s7">)</span><span class="s1"> </span><span class="s10">return</span><span class="s1"> </span><span class="s10">false</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">let</span><span class="s1"> xMin</span><span class="s7">,</span><span class="s1"> xMax</span><span class="s7">,</span><span class="s1"> yMin</span><span class="s7">,</span><span class="s1"> yMax</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">let</span><span class="s1"> halfFrontWidth</span><span class="s7">,</span><span class="s1"> halfFrontHeight</span><span class="s7">,</span><span class="s1"> itemPerspectiveOffset </span><span class="s7">=</span><span class="s1"> </span><span class="s12">0</span><span class="s7">,</span><span class="s1"> itemActualDepthDimension </span><span class="s7">=</span><span class="s1"> </span><span class="s12">0</span><span class="s7">;</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'cube'</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> halfFrontWidth </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">size </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s7">;</span><span class="s1"> halfFrontHeight </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">size </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s7">;</span><span class="s1"> itemActualDepthDimension </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">size</span><span class="s7">;</span><span class="s1"> itemPerspectiveOffset </span><span class="s7">=</span><span class="s1"> itemActualDepthDimension </span><span class="s7">*</span><span class="s1"> </span><span class="s15">DEPTH_PERSPECTIVE_RATIO</span><span class="s7">;}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'rod'</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> halfFrontWidth </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">length </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s7">;</span><span class="s1"> halfFrontHeight </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">height </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s7">;</span><span class="s1"> itemActualDepthDimension </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">depth</span><span class="s7">;</span><span class="s1"> itemPerspectiveOffset </span><span class="s7">=</span><span class="s1"> itemActualDepthDimension </span><span class="s7">*</span><span class="s1"> </span><span class="s15">DEPTH_PERSPECTIVE_RATIO</span><span class="s7">;}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'flat'</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> halfFrontWidth </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">sideLength </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s7">;</span><span class="s1"> halfFrontHeight </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">sideLength </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s7">;</span><span class="s1"> itemActualDepthDimension </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">thickness</span><span class="s7">;</span><span class="s1"> itemPerspectiveOffset </span><span class="s7">=</span><span class="s1"> itemActualDepthDimension </span><span class="s7">*</span><span class="s1"> </span><span class="s15">DEPTH_PERSPECTIVE_RATIO</span><span class="s7">;}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'block1000'</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> halfFrontWidth </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">sideLength </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s7">;</span><span class="s1"> halfFrontHeight </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">sideLength </span><span class="s7">/</span><span class="s1"> </span><span class="s12">2</span><span class="s7">;</span><span class="s1"> itemActualDepthDimension </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">sideLength</span><span class="s7">;</span><span class="s1"> itemPerspectiveOffset </span><span class="s7">=</span><span class="s1"> itemActualDepthDimension </span><span class="s7">*</span><span class="s1"> </span><span class="s15">DEPTH_PERSPECTIVE_RATIO</span><span class="s7">;}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'circle'</span><span class="s1"> </span><span class="s7">||</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'clock'</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> </span><span class="s10">const</span><span class="s1"> distance </span><span class="s7">=</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">sqrt</span><span class="s7">(</span><span class="s15">Math</span><span class="s7">.</span><span class="s1">pow</span><span class="s7">(</span><span class="s1">mouseX </span><span class="s7">-</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">x</span><span class="s7">,</span><span class="s1"> </span><span class="s12">2</span><span class="s7">)</span><span class="s1"> </span><span class="s7">+</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">pow</span><span class="s7">(</span><span class="s1">mouseY </span><span class="s7">-</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">y</span><span class="s7">,</span><span class="s1"> </span><span class="s12">2</span><span class="s7">));</span><span class="s1"> </span><span class="s10">return</span><span class="s1"> distance </span><span class="s7">&lt;=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">radius</span><span class="s7">;</span><span class="s1"> </span><span class="s7">}</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'square'</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> halfFrontWidth </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">sideLength</span><span class="s7">/</span><span class="s12">2</span><span class="s7">;</span><span class="s1"> halfFrontHeight </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">sideLength</span><span class="s7">/</span><span class="s12">2</span><span class="s7">;</span><span class="s1"> </span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'equilateralTriangle'</span><span class="s1"> </span><span class="s7">||</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'pentagon'</span><span class="s1"> </span><span class="s7">||</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'hexagon'</span><span class="s1"> </span><span class="s7">||</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'octagon'</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> </span><span class="s10">const</span><span class="s1"> numSides </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'equilateralTriangle'</span><span class="s1"> </span><span class="s7">?</span><span class="s1"> </span><span class="s12">3</span><span class="s1"> </span><span class="s7">:</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'pentagon'</span><span class="s1"> </span><span class="s7">?</span><span class="s1"> </span><span class="s12">5</span><span class="s1"> </span><span class="s7">:</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'hexagon'</span><span class="s1"> </span><span class="s7">?</span><span class="s1"> </span><span class="s12">6</span><span class="s1"> </span><span class="s7">:</span><span class="s1"> </span><span class="s12">8</span><span class="s7">;</span><span class="s1"> </span><span class="s10">const</span><span class="s1"> r </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">sideLength </span><span class="s7">/</span><span class="s1"> </span><span class="s7">(</span><span class="s12">2</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s15">Math</span><span class="s7">.</span><span class="s1">sin</span><span class="s7">(</span><span class="s15">Math</span><span class="s7">.</span><span class="s15">PI</span><span class="s1"> </span><span class="s7">/</span><span class="s1"> numSides</span><span class="s7">));</span><span class="s1"> halfFrontWidth </span><span class="s7">=</span><span class="s1"> r</span><span class="s7">;</span><span class="s1"> halfFrontHeight </span><span class="s7">=</span><span class="s1"> r</span><span class="s7">;}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'parallelogram'</span><span class="s7">){</span><span class="s1"> halfFrontWidth </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">width</span><span class="s7">/</span><span class="s12">2</span><span class="s7">;</span><span class="s1"> halfFrontHeight </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">height</span><span class="s7">/</span><span class="s12">2</span><span class="s7">;}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'rhombus'</span><span class="s7">){</span><span class="s1"> halfFrontWidth </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">d2</span><span class="s7">/</span><span class="s12">2</span><span class="s7">;</span><span class="s1"> halfFrontHeight </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">d1</span><span class="s7">/</span><span class="s12">2</span><span class="s7">;}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'rightTriangle'</span><span class="s7">){</span><span class="s1"> halfFrontWidth </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">base</span><span class="s7">/</span><span class="s12">2</span><span class="s7">;</span><span class="s1"> halfFrontHeight </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">height</span><span class="s7">/</span><span class="s12">2</span><span class="s7">;}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'diamond'</span><span class="s7">){</span><span class="s1"> halfFrontWidth </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">width</span><span class="s7">/</span><span class="s12">2</span><span class="s7">;</span><span class="s1"> halfFrontHeight </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">height</span><span class="s7">/</span><span class="s12">2</span><span class="s7">;}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">else</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> </span><span class="s10">return</span><span class="s1"> </span><span class="s10">false</span><span class="s7">;</span><span class="s1"> </span><span class="s7">}</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>xMin </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">x </span><span class="s7">-</span><span class="s1"> halfFrontWidth</span><span class="s7">;</span><span class="s1"> xMax </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">x </span><span class="s7">+</span><span class="s1"> halfFrontWidth </span><span class="s7">+</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type</span><span class="s7">.</span><span class="s1">includes</span><span class="s7">(</span><span class="s9">'Prism'</span><span class="s7">)</span><span class="s1"> </span><span class="s7">||</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">type</span><span class="s7">.</span><span class="s1">includes</span><span class="s7">(</span><span class="s9">'cylinder'</span><span class="s7">)</span><span class="s1"> </span><span class="s7">||</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'circle'</span><span class="s1"> </span><span class="s7">||</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'clock'</span><span class="s1"> </span><span class="s7">?</span><span class="s1"> </span><span class="s12">0</span><span class="s1"> </span><span class="s7">:</span><span class="s1"> itemPerspectiveOffset</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>yMin </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">y </span><span class="s7">-</span><span class="s1"> halfFrontHeight </span><span class="s7">-</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">type</span><span class="s7">.</span><span class="s1">includes</span><span class="s7">(</span><span class="s9">'Prism'</span><span class="s7">)</span><span class="s1"> </span><span class="s7">||</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">type</span><span class="s7">.</span><span class="s1">includes</span><span class="s7">(</span><span class="s9">'cylinder'</span><span class="s7">)</span><span class="s1"> </span><span class="s7">||</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'circle'</span><span class="s1"> </span><span class="s7">||</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'clock'</span><span class="s1"> </span><span class="s7">?</span><span class="s1"> </span><span class="s12">0</span><span class="s1"> </span><span class="s7">:</span><span class="s1"> itemPerspectiveOffset</span><span class="s7">);</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>yMax </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">y </span><span class="s7">+</span><span class="s1"> halfFrontHeight</span><span class="s7">;</span></p>
<p class="p6"><span class="s1"><span class="Apple-converted-space">            </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">return</span><span class="s1"> </span><span class="s7">(</span><span class="s1">mouseX </span><span class="s7">&gt;=</span><span class="s1"> xMin </span><span class="s7">&amp;&amp;</span><span class="s1"> mouseX </span><span class="s7">&lt;=</span><span class="s1"> xMax </span><span class="s7">&amp;&amp;</span><span class="s1"> mouseY </span><span class="s7">&gt;=</span><span class="s1"> yMin </span><span class="s7">&amp;&amp;</span><span class="s1"> mouseY </span><span class="s7">&lt;=</span><span class="s1"> yMax</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s7">}</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">// --- Initial setup ---</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span>window</span><span class="s7">.</span><span class="s1">onload </span><span class="s7">=</span><span class="s1"> </span><span class="s7">()</span><span class="s1"> </span><span class="s7">=&gt;</span><span class="s1"> </span><span class="s7">{</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>oldCanvasWidth </span><span class="s7">=</span><span class="s1"> window</span><span class="s7">.</span><span class="s1">innerWidth</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>oldCanvasHeight </span><span class="s7">=</span><span class="s1"> window</span><span class="s7">.</span><span class="s1">innerHeight</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>resizeCanvasAndDraw</span><span class="s7">();</span><span class="s1"><span class="Apple-converted-space"> </span></span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s1">// Removed setInterval for clock, as it's now static</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s7">};</span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">// --- Handle window resize ---</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span>window</span><span class="s7">.</span><span class="s1">addEventListener</span><span class="s7">(</span><span class="s9">'resize'</span><span class="s7">,</span><span class="s1"> resizeCanvasAndDraw</span><span class="s7">);</span></p>
<p class="p1"><span class="s4"><span class="Apple-converted-space">    </span></span><span class="s3">&lt;/</span><span class="s1">script</span><span class="s3">&gt;</span></p>
<p class="p1"><span class="s3">&lt;/</span><span class="s1">body</span><span class="s3">&gt;</span></p>
<p class="p1"><span class="s3">&lt;/</span><span class="s1">html</span><span class="s3">&gt;</span></p>
<p class="p6"><span class="s11"></span><br></p>
</body>
</html>
