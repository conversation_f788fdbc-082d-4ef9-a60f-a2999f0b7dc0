{\rtf1\ansi\ansicpg1252\cocoartf2822
\cocoatextscaling0\cocoaplatform0{\fonttbl\f0\fnil\fcharset0 Menlo-Regular;\f1\fnil\fcharset0 Menlo-Bold;}
{\colortbl;\red255\green255\blue255;\red183\green111\blue247;\red23\green24\blue24;\red202\green202\blue202;
\red99\green159\blue215;}
{\*\expandedcolortbl;;\cssrgb\c77255\c54118\c97647;\cssrgb\c11765\c12157\c12549;\cssrgb\c83137\c83137\c83137;
\cssrgb\c45490\c69020\c87451;}
\margl1440\margr1440\vieww11520\viewh8400\viewkind0
\deftab720
\pard\pardeftab720\partightenfactor0

\f0\fs28 \cf2 \cb3 \expnd0\expndtw0\kerning0
\outl0\strokewidth0 \strokec2 # MBRS MATH - Illustration Style Guide V25 (White Background - Condensed)\cf4 \cb1 \strokec4 \
\
\pard\pardeftab720\partightenfactor0

\f1\b \cf4 \cb3 \strokec4 **Core Principle:**
\f0\b0 \cf4 \cb3 \strokec4  Illustrations use a 13x8 HTML/CSS grid (invisible in final output). The main container (.grid-container) MUST be transparent against the primary page background (
\f1\b \cf4 \cb3 \strokec4 **#FFFFFF**
\f0\b0 \cf4 \cb3 \strokec4 ). Prioritize JavaScript for precision. Elements MUST be very large, prominent, and grid-scaled.\cb1 \
\

\f1\b \cf4 \cb3 \strokec4 **Visual Benchmark:**
\f0\b0 \cf4 \cb3 \strokec4  User screenshots guide flat style/UI. Screenshot 2025-05-25 (2.30.24 PM) shows desired flatness but is an ANTI-BENCHMARK for container, object fidelity, arrow, and tile color (follow this guide instead).\cb1 \
\
\pard\pardeftab720\partightenfactor0
\cf2 \cb3 \strokec2 ## I. Core Brand Philosophy:\cf4 \cb1 \strokec4 \
\pard\pardeftab720\partightenfactor0
\cf4 \cb3 Make K-12 math accessible and engaging. Illustrations are educational tools: Clear, Flat, Diagrammatic, Grid-Planned, Mathematically Precise, Symmetrically Aligned. Objects MUST be significantly large (see IV.B).\cb1 \
\
\pard\pardeftab720\partightenfactor0
\cf2 \cb3 \strokec2 ## II. Target Audience:\cf4 \cb1 \strokec4 \
\pard\pardeftab720\partightenfactor0
\cf4 \cb3 K-12 students and educators. Illustrations must be clear and instill confidence.\cb1 \
\
\pard\pardeftab720\partightenfactor0
\cf2 \cb3 \strokec2 ## III. Desired Emotional Impact:\cf4 \cb1 \strokec4 \
\pard\pardeftab720\partightenfactor0
\cf4 \cb3 Clarity & Understanding, Engagement, Trust & Professionalism, Order, Structure & Precision.\cb1 \
\
\pard\pardeftab720\partightenfactor0
\cf2 \cb3 \strokec2 ## IV. Artistic Style & Execution:\cf4 \cb1 \strokec4 \
\
\cf2 \cb3 \strokec2 ### A. Overall Look: Clean, Flat, Diagrammatic, Grid-Native\cf4 \cb1 \strokec4 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **Style:**
\f0\b0 \cf4 \cb3 \strokec4  Crisp, flat design on 
\f1\b \cf4 \cb3 \strokec4 **#FFFFFF**
\f0\b0 \cf4 \cb3 \strokec4  background. Text, numbers, symbols, arrows are 
\f1\b \cf4 \cb3 \strokec4 **black (#000000)**
\f0\b0 \cf4 \cb3 \strokec4 .\cb1 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **Primary Shape Source (CRITICAL):**
\f0\b0 \cf4 \cb3 \strokec4  All illustrative objects MUST be derived from the shapes defined in "HTML Illustration Basics.html" (e.g., unit cube, rod, flat, block1000, circle, various polygons). Exceptions include mandatory non-shape elements like numbers, arrows (SVG as defined), number lines, 100s charts, and fraction strips, which follow their own specifications.\cb1 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **Object Colors:**
\f0\b0 \cf4 \cb3 \strokec4  Use characteristic/distinct colors with strong contrast against white/black (see VIII. User-Provided Palette).\cb1 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **Object Identity (CRITICAL):**
\f0\b0 \cf4 \cb3 \strokec4  When using the defined shapes to represent real-world items, ensure the representation is clear and contextually appropriate (e.g., if a unit cube represents an apple, this should be clear from context or labeling). Simplification should not lead to ambiguity.\cb1 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **Lines & Shapes:**
\f0\b0 \cf4 \cb3 \strokec4  Clean, intentional. Core illustration lines (borders, clock hands) are typically 
\f1\b \cf4 \cb3 \strokec4 **black**
\f0\b0 \cf4 \cb3 \strokec4 . Subtle object outlines (as defined by the shape rendering code).\cb1 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **Finish:**
\f0\b0 \cf4 \cb3 \strokec4  Expertly crafted, uncluttered, precisely aligned.\cb1 \
\
\cf2 \cb3 \strokec2 ### B. Layout System & Element Sizing:\cf4 \cb1 \strokec4 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **Grid:**
\f0\b0 \cf4 \cb3 \strokec4  13-column, 8-row (for development, invisible in output). Main .grid-container is transparent. Internal "cards" must contrast with 
\f1\b \cf4 \cb3 \strokec4 **black**
\f0\b0 \cf4 \cb3 \strokec4  text.\cb1 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **Placement:**
\f0\b0 \cf4 \cb3 \strokec4  Use \cf5 \cb3 \strokec5 `grid-column`\cf4 \cb3 \strokec4 /\cf5 \cb3 \strokec5 `grid-row`\cf4 \cb3 \strokec4 .\cb1 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **Maximize Space & Prominence (CRITICAL):**
\f0\b0 \cf4 \cb1 \strokec4 \
\cf2 \cb3 \strokec2     * 
\f1\b \cf4 \cb3 \strokec4 **Overall Area:**
\f0\b0 \cf4 \cb3 \strokec4  Active content should use 70-80% of 13x8 grid.\cb1 \
\cf2 \cb3 \strokec2     * 
\f1\b \cf4 \cb3 \strokec4 **Major Objects/Groups:**
\f0\b0 \cf4 \cb3 \strokec4  Span significantly (e.g., single focus: 6-9 cols wide, 4-6 rows high; collections: 8-11 cols wide, 3-5 rows high).\cb1 \
\cf2 \cb3 \strokec2     * 
\f1\b \cf4 \cb3 \strokec4 **Components:**
\f0\b0 \cf4 \cb1 \strokec4 \
\cf2 \cb3 \strokec2         * \cf4 \strokec4 Base-ten "ten rod": Height ~1-2 grid rows. Width proportional.\cb1 \
\cf2 \cb3 \strokec2         * \cf4 \strokec4 Base-ten "unit cubes": Proportional to rods, side ~1/4-1/3 grid column width.\cb1 \
\cf2 \cb3 \strokec2         * \cf4 \strokec4 Other countable items (represented by primary shapes): Fill their conceptual "slot" significantly.\cb1 \
\cf2 \cb3 \strokec2     * 
\f1\b \cf4 \cb3 \strokec4 **Numbers & Labels:**
\f0\b0 \cf4 \cb3 \strokec4  Prominent standalone numbers: text height ~1-1.5 grid rows. Labels: min. 34px, scaled legibly.\cb1 \
\cf2 \cb3 \strokec2     * 
\f1\b \cf4 \cb3 \strokec4 **Principle:**
\f0\b0 \cf4 \cb3 \strokec4  Elements must be "way way bigger"; use grid spans as a guide. Avoid "lost" elements unless whitespace is intentional.\cb1 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **JavaScript:**
\f0\b0 \cf4 \cb3 \strokec4  Use for precision, dynamic sizing, and maintaining aspect ratios within grid cells.\cb1 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **Responsive Wrapper:**
\f0\b0 \cf4 \cb3 \strokec4  If used, outer .illustration-wrapper should have aspect ratio ~13/8.\cb1 \
\
\cf2 \cb3 \strokec2 ### C. Element Styling (within Grid Cells):\cf4 \cb1 \strokec4 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **Internal Cards:**
\f0\b0 \cf4 \cb3 \strokec4  Backgrounds contrast with 
\f1\b \cf4 \cb3 \strokec4 **black**
\f0\b0 \cf4 \cb3 \strokec4  text, fit 
\f1\b \cf4 \cb3 \strokec4 **white/black**
\f0\b0 \cf4 \cb3 \strokec4  theme.\cb1 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **Symbols/Arrows:**
\f0\b0 \cf4 \cb3 \strokec4  Prominent (min. 34px, ~0.5-1 grid cell height/width). Color MUST be 
\f1\b \cf4 \cb3 \strokec4 **black**
\f0\b0 \cf4 \cb3 \strokec4 . Arrows use standard SVG (see X).\cb1 \
\
\cf2 \cb3 \strokec2 ### D. Complexity & Detail:\cf4 \cb1 \strokec4 \
\pard\pardeftab720\partightenfactor0
\cf4 \cb3 Minimalist. Detail serves clarity and object recognition, primarily through the chosen shape and its color.\cb1 \
\
\pard\pardeftab720\partightenfactor0
\cf2 \cb3 \strokec2 ### E. "Cute" and "Fun":\cf4 \cb1 \strokec4 \
\pard\pardeftab720\partightenfactor0
\cf4 \cb3 Via clear geometrics (from the defined shape set), appealing colors, engaging grid compositions.\cb1 \
\
\pard\pardeftab720\partightenfactor0
\cf2 \cb3 \strokec2 ### F. "Professional":\cf4 \cb1 \strokec4 \
\pard\pardeftab720\partightenfactor0
\cf4 \cb3 Defined by precise grid alignment, clean execution, consistency.\cb1 \
\
\pard\pardeftab720\partightenfactor0
\cf2 \cb3 \strokec2 ### G. Perspective, Light & Dimension:\cf4 \cb1 \strokec4 \
\pard\pardeftab720\partightenfactor0
\cf4 \cb3 Primarily 2D and flat for overall composition. The provided shapes (cubes, rods, etc.) have their own inherent 3D-like rendering which should be used as-is. Minimize additional shadows beyond what the shapes provide.\cb1 \
\
\pard\pardeftab720\partightenfactor0
\cf2 \cb3 \strokec2 ## V. Indicative Elements (Symbols, Arrows):\cf4 \cb1 \strokec4 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **Size:**
\f0\b0 \cf4 \cb3 \strokec4  Extra large (min. 34px, ~0.5-1 grid cell height/width).\cb1 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **Color:**
\f0\b0 \cf4 \cb3 \strokec4  Solid 
\f1\b \cf4 \cb3 \strokec4 **black (#000000)**
\f0\b0 \cf4 \cb3 \strokec4 .\cb1 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **Style:**
\f0\b0 \cf4 \cb3 \strokec4  Bold, clear. Arrows use standard SVG (stroke-width="8", specific arrowhead).\cb1 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **Placement:**
\f0\b0 \cf4 \cb3 \strokec4  Precise.\cb1 \
\
\cf2 \cb3 \strokec2 ## VI. Layout, Alignment, and Symmetry:\cf4 \cb1 \strokec4 \
\pard\pardeftab720\partightenfactor0
\cf4 \cb3 (No changes from original guide)\cb1 \
\
\pard\pardeftab720\partightenfactor0
\cf2 \cb3 \strokec2 ## VII. Mathematical Accuracy & Clarity (Non-Negotiable):\cf4 \cb1 \strokec4 \
\pard\pardeftab720\partightenfactor0
\cf4 \cb3 (No changes from original guide)\cb1 \
\
\pard\pardeftab720\partightenfactor0
\cf2 \cb3 \strokec2 ## VIII. Color Palette:\cf4 \cb1 \strokec4 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **Primary Illustrative Background:**
\f0\b0 \cf4 \cb3 \strokec4  
\f1\b \cf4 \cb3 \strokec4 **#FFFFFF**
\f0\b0 \cf4 \cb3 \strokec4  (Solid White).\cb1 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **Primary Text, Number, Symbol, Arrow:**
\f0\b0 \cf4 \cb3 \strokec4  Solid 
\f1\b \cf4 \cb3 \strokec4 **Black: #000000**
\f0\b0 \cf4 \cb3 \strokec4 .\cb1 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **User-Provided Object Color Palette (Priority):**
\f0\b0 \cf4 \cb3 \strokec4  Use for objects (the defined shapes), ensuring strong contrast on 
\f1\b \cf4 \cb3 \strokec4 **#FFFFFF**
\f0\b0 \cf4 \cb3 \strokec4 .\cb1 \
\cf2 \cb3 \strokec2     * 
\f1\b \cf4 \cb3 \strokec4 **Categories:**
\f0\b0 \cf4 \cb3 \strokec4  Grays/Whites (Black is primary for text), Reds/Pinks/Purples, Blues/Teals, Greens/Yellows/Oranges.\cb1 \
\cf2 \cb3 \strokec2     * \cf4 \strokec4 (Select standard web colors matching user's visual, ensuring high contrast.)\cb1 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **Other Accents:**
\f0\b0 \cf4 \cb3 \strokec4  Use if needed (e.g., --brand-green-primary: #5CB85C; --brand-yellow-orange: #FFC300;).\cb1 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **Highlighting:**
\f0\b0 \cf4 \cb3 \strokec4  
\f1\b \cf4 \cb3 \strokec4 **rgba(0, 0, 0, 0.1)**
\f0\b0 \cf4 \cb3 \strokec4  (subtle shadow/highlight on white).\cb1 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **Negative/Attention:**
\f0\b0 \cf4 \cb3 \strokec4  Bright Red (e.g., #E63946), X symbol is black.\cb1 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **Contrast: CRITICAL.**
\f0\b0 \cf4 \cb3 \strokec4  Ensure legibility of 
\f1\b \cf4 \cb3 \strokec4 **black**
\f0\b0 \cf4 \cb3 \strokec4  text and object visibility on 
\f1\b \cf4 \cb3 \strokec4 **white**
\f0\b0 \cf4 \cb3 \strokec4 .\cb1 \
\
\cf2 \cb3 \strokec2 ## IX. Typography in Illustrations (Text & Numbers):\cf4 \cb1 \strokec4 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **Font:**
\f0\b0 \cf4 \cb3 \strokec4  Montserrat, Extra Bold (900).\cb1 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **Size:**
\f0\b0 \cf4 \cb3 \strokec4  Extra large (min. 34px). Prominent numbers: text height ~1-1.5 grid rows. Scale proportionally.\cb1 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **Color:**
\f0\b0 \cf4 \cb3 \strokec4  Solid 
\f1\b \cf4 \cb3 \strokec4 **black (#000000)**
\f0\b0 \cf4 \cb3 \strokec4 .\cb1 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **Presentation:**
\f0\b0 \cf4 \cb3 \strokec4  Directly on 
\f1\b \cf4 \cb3 \strokec4 **#FFFFFF**
\f0\b0 \cf4 \cb3 \strokec4  or contrasted cards.\cb1 \
\
\cf2 \cb3 \strokec2 ## X. Technical Requirements:\cf4 \cb1 \strokec4 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **Foundation:**
\f0\b0 \cf4 \cb3 \strokec4  HTML (.grid-container: 13-col, transparent; .cell classes). Body/root wrapper: 
\f1\b \cf4 \cb3 \strokec4 **#FFFFFF**
\f0\b0 \cf4 \cb3 \strokec4  background. JavaScript for shape rendering from "HTML Illustration Basics.html" is primary.\cb1 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **JavaScript:**
\f0\b0 \cf4 \cb3 \strokec4  Major priority for precision/dynamic sizing.\cb1 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **SVG Arrows:**
\f0\b0 \cf4 \cb3 \strokec4  Inline, standard definition, 
\f1\b \cf4 \cb3 \strokec4 **black**
\f0\b0 \cf4 \cb3 \strokec4 .\cb1 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **Main Container:**
\f0\b0 \cf4 \cb3 \strokec4  .grid-container MUST be transparent.\cb1 \
\
\cf2 \cb3 \strokec2 ## XI. What to Strictly Avoid:\cf4 \cb1 \strokec4 \
\cf2 \cb3 \strokec2 * \cf4 \strokec4 Visible development grid lines.\cb1 \
\cf2 \cb3 \strokec2 * \cf4 \strokec4 .grid-container with visible background.\cb1 \
\cf2 \cb3 \strokec2 * \cf4 \strokec4 Small/lost elements relative to grid; non-prominent objects.\cb1 \
\cf2 \cb3 \strokec2 * \cf4 \strokec4 Poor contrast (object colors on white, non-black text/symbols).\cb1 \
\cf2 \cb3 \strokec2 * \cf4 \strokec4 Clutter, excessive decoration.\cb1 \
\cf2 \cb3 \strokec2 * \cf4 \strokec4 Text/symbols/arrows smaller than minimums or not scaled for impact.\cb1 \
\cf2 \cb3 \strokec2 * \cf4 \strokec4 Over-reliance on static CSS if it breaks responsiveness/proportionality.\cb1 \
\cf2 \cb3 \strokec2 * \cf4 \strokec4 Using shapes not defined in "HTML Illustration Basics.html" for primary illustrative objects (exceptions noted in IV.A).\cb1 \
\
\cf2 \cb3 \strokec2 ## XII. Inspiration & Benchmarks:\cf4 \cb1 \strokec4 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **Grid/Base Styles:**
\f0\b0 \cf4 \cb3 \strokec4  User-provided HTML/CSS.\cb1 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **Flat Style/Card UI:**
\f0\b0 \cf4 \cb3 \strokec4  User screenshots.\cb1 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **Standard Arrow (SVG):**
\f0\b0 \cf4 \cb3 \strokec4  stroke-width="8", specific arrowhead, 
\f1\b \cf4 \cb3 \strokec4 **black**
\f0\b0 \cf4 \cb3 \strokec4 .\cb1 \
\cf2 \cb3 \strokec2 * 
\f1\b \cf4 \cb3 \strokec4 **Shape Definitions:**
\f0\b0 \cf4 \cb3 \strokec4  "HTML Illustration Basics.html" for all primary illustrative objects.\cb1 \
\
}