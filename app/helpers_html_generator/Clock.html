<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta http-equiv="Content-Style-Type" content="text/css">
  <title></title>
  <meta name="Generator" content="Cocoa HTML Writer">
  <meta name="CocoaVersion" content="2575.6">
  <style type="text/css">
    p.p1 {margin: 0.0px 0.0px 0.0px 0.0px; font: 14.0px <PERSON><PERSON>; color: #4689cc; -webkit-text-stroke: #4689cc}
    p.p2 {margin: 0.0px 0.0px 0.0px 0.0px; font: 14.0px <PERSON><PERSON>; color: #8cd3fe; -webkit-text-stroke: #8cd3fe}
    p.p3 {margin: 0.0px 0.0px 0.0px 0.0px; font: 14.0px <PERSON><PERSON>; color: #c27e65; -webkit-text-stroke: #c27e65}
    p.p4 {margin: 0.0px 0.0px 0.0px 0.0px; font: 14.0px Menlo; color: #cacaca; -webkit-text-stroke: #cacaca}
    p.p5 {margin: 0.0px 0.0px 0.0px 0.0px; font: 14.0px Menlo; color: #6d7378; -webkit-text-stroke: #6d7378}
    p.p6 {margin: 0.0px 0.0px 0.0px 0.0px; font: 14.0px Menlo; color: #cacaca; -webkit-text-stroke: #cacaca; min-height: 16.0px}
    p.p7 {margin: 0.0px 0.0px 0.0px 0.0px; font: 14.0px Menlo; color: #71c083; -webkit-text-stroke: #71c083}
    span.s1 {font-kerning: none; background-color: #171818}
    span.s2 {font-kerning: none; color: #8cd3fe; background-color: #171818; -webkit-text-stroke: 0px #8cd3fe}
    span.s3 {font-kerning: none; color: #6d6d6d; background-color: #171818; -webkit-text-stroke: 0px #6d6d6d}
    span.s4 {font-kerning: none; color: #cacaca; background-color: #171818; -webkit-text-stroke: 0px #cacaca}
    span.s5 {font-kerning: none; color: #c27e65; background-color: #171818; -webkit-text-stroke: 0px #c27e65}
    span.s6 {font-kerning: none; color: #4689cc; background-color: #171818; -webkit-text-stroke: 0px #4689cc}
    span.s7 {font-kerning: none; color: #d4d4d4; background-color: #171818; -webkit-text-stroke: 0px #d4d4d4}
    span.s8 {font-kerning: none; color: #a7c598; background-color: #171818; -webkit-text-stroke: 0px #a7c598}
    span.s9 {font-kerning: none; color: #71c083; background-color: #171818; -webkit-text-stroke: 0px #71c083}
    span.s10 {font-kerning: none; color: #b76ff7; background-color: #171818; -webkit-text-stroke: 0px #b76ff7}
    span.s11 {font-kerning: none}
    span.s12 {font-kerning: none; color: #6d7378; background-color: #171818; -webkit-text-stroke: 0px #6d7378}
    span.s13 {font-kerning: none; color: #f67c30; background-color: #171818; -webkit-text-stroke: 0px #f67c30}
    span.s14 {font-kerning: none; color: #36c0a0; background-color: #171818; -webkit-text-stroke: 0px #36c0a0}
  </style>
</head>
<body>
<p class="p1"><span class="s1">&lt;!DOCTYPE</span><span class="s2"> html</span><span class="s1">&gt;</span></p>
<p class="p1"><span class="s3">&lt;</span><span class="s1">html</span><span class="s4"> </span><span class="s2">lang</span><span class="s3">=</span><span class="s5">"en"</span><span class="s3">&gt;</span></p>
<p class="p1"><span class="s3">&lt;</span><span class="s1">head</span><span class="s3">&gt;</span></p>
<p class="p2"><span class="s4"><span class="Apple-converted-space">    </span></span><span class="s3">&lt;</span><span class="s6">meta</span><span class="s4"> </span><span class="s1">charset</span><span class="s3">=</span><span class="s5">"UTF-8"</span><span class="s3">&gt;</span></p>
<p class="p3"><span class="s4"><span class="Apple-converted-space">    </span></span><span class="s3">&lt;</span><span class="s6">meta</span><span class="s4"> </span><span class="s2">name</span><span class="s3">=</span><span class="s1">"viewport"</span><span class="s4"> </span><span class="s2">content</span><span class="s3">=</span><span class="s1">"width=device-width, initial-scale=1.0"</span><span class="s3">&gt;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">    </span></span><span class="s3">&lt;</span><span class="s6">title</span><span class="s3">&gt;</span><span class="s1">Full Screen Canvas Clock - MBRS Style</span><span class="s3">&lt;/</span><span class="s6">title</span><span class="s3">&gt;</span></p>
<p class="p1"><span class="s4"><span class="Apple-converted-space">    </span></span><span class="s3">&lt;</span><span class="s1">style</span><span class="s3">&gt;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s6">body</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s2">margin:</span><span class="s1"> </span><span class="s8">0</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s2">overflow:</span><span class="s1"> </span><span class="s5">hidden</span><span class="s7">;</span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s2">background-color:</span><span class="s4"> #FFFFFF</span><span class="s7">;</span><span class="s4"> </span><span class="s1">/* Primary page background as per style guide */</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s2">display:</span><span class="s1"> </span><span class="s5">flex</span><span class="s7">;</span></p>
<p class="p2"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s1">justify-content:</span><span class="s4"> </span><span class="s5">center</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s2">align-items:</span><span class="s1"> </span><span class="s5">center</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s2">height:</span><span class="s1"> </span><span class="s8">100vh</span><span class="s7">;</span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s2">font-family:</span><span class="s4"> </span><span class="s9">'Montserrat'</span><span class="s7">,</span><span class="s4"> </span><span class="s5">sans-serif</span><span class="s7">;</span><span class="s4"> </span><span class="s1">/* Default body font, though canvas sets its own */</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s7">}</span></p>
<p class="p1"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">#myCubeCanvas</span><span class="s4"> </span><span class="s7">{</span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s2">background-color:</span><span class="s4"> #FFFFFF</span><span class="s7">;</span><span class="s4"> </span><span class="s1">/* Canvas background, clock face is also white by default */</span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s2">cursor:</span><span class="s4"> </span><span class="s5">default</span><span class="s7">;</span><span class="s4"> </span><span class="s1">/* Default cursor, will change on grab */</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s2">display:</span><span class="s1"> </span><span class="s5">block</span><span class="s7">;</span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s1">/* width and height will be set by JS to match window */</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s7">}</span></p>
<p class="p1"><span class="s4"><span class="Apple-converted-space">    </span></span><span class="s3">&lt;/</span><span class="s1">style</span><span class="s3">&gt;</span></p>
<p class="p1"><span class="s3">&lt;/</span><span class="s1">head</span><span class="s3">&gt;</span></p>
<p class="p1"><span class="s3">&lt;</span><span class="s1">body</span><span class="s3">&gt;</span></p>
<p class="p3"><span class="s4"><span class="Apple-converted-space">    </span></span><span class="s3">&lt;</span><span class="s6">canvas</span><span class="s4"> </span><span class="s2">id</span><span class="s3">=</span><span class="s1">"myCubeCanvas"</span><span class="s3">&gt;&lt;/</span><span class="s6">canvas</span><span class="s3">&gt;</span></p>
<p class="p1"><span class="s4"><span class="Apple-converted-space">    </span></span><span class="s3">&lt;</span><span class="s1">script</span><span class="s3">&gt;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> canvas </span><span class="s7">=</span><span class="s1"> document</span><span class="s7">.</span><span class="s1">getElementById</span><span class="s7">(</span><span class="s9">'myCubeCanvas'</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> ctx </span><span class="s7">=</span><span class="s1"> canvas</span><span class="s7">.</span><span class="s1">getContext</span><span class="s7">(</span><span class="s9">'2d'</span><span class="s7">);</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">// --- Style Guide Color assignments ---</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> clockFaceColor </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'#FFFFFF'</span><span class="s7">;</span><span class="s1"><span class="Apple-converted-space">    </span></span><span class="s12">// Solid White for clock face</span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s4"> mainOutlineColor </span><span class="s7">=</span><span class="s4"> </span><span class="s9">'#000000'</span><span class="s7">;</span><span class="s4"> </span><span class="s1">// Solid Black for numbers, border</span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s4"> hourHandColor </span><span class="s7">=</span><span class="s4"> </span><span class="s9">'#FF0000'</span><span class="s7">;</span><span class="s4"> <span class="Apple-converted-space">    </span></span><span class="s1">// Red for hour hand (User-Provided Palette)</span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s4"> minuteHandColor </span><span class="s7">=</span><span class="s4"> </span><span class="s9">'#0000FF'</span><span class="s7">;</span><span class="s4"> <span class="Apple-converted-space">  </span></span><span class="s1">// Blue for minute hand (User-Provided Palette)</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> labelColor </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'#000000'</span><span class="s7">;</span><span class="s1"> <span class="Apple-converted-space">      </span></span><span class="s12">// Solid Black for labels</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">// --- Transparency ---</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s1"> geometricShapeTransparency </span><span class="s7">=</span><span class="s1"> </span><span class="s13">1.0</span><span class="s7">;</span><span class="s1"> </span><span class="s12">// Fully opaque for flat style</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">// --- Line Styling ---</span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s10">const</span><span class="s4"> mainOutlineWidth </span><span class="s7">=</span><span class="s4"> </span><span class="s13">2</span><span class="s7">;</span><span class="s4"> </span><span class="s1">// Slightly increased for prominence</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">// --- Clock object ---</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">let</span><span class="s1"> clock </span><span class="s7">=</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> x</span><span class="s7">:</span><span class="s13">0</span><span class="s7">,</span><span class="s1"> y</span><span class="s7">:</span><span class="s13">0</span><span class="s7">,</span><span class="s1"> radius</span><span class="s7">:</span><span class="s13">0</span><span class="s7">,</span><span class="s1"> color</span><span class="s7">:</span><span class="s1"> clockFaceColor</span><span class="s7">,</span><span class="s1"> type</span><span class="s7">:</span><span class="s1"> </span><span class="s9">'clock'</span><span class="s7">};</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">// --- Dragging and Selection variables ---</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">let</span><span class="s1"> isDragging </span><span class="s7">=</span><span class="s1"> </span><span class="s10">false</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">let</span><span class="s1"> draggedItem </span><span class="s7">=</span><span class="s1"> </span><span class="s10">null</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">let</span><span class="s1"> selectedItems </span><span class="s7">=</span><span class="s1"> </span><span class="s7">[];</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">let</span><span class="s1"> lastMouseX</span><span class="s7">,</span><span class="s1"> lastMouseY</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">let</span><span class="s1"> dragStartOffsets </span><span class="s7">=</span><span class="s1"> </span><span class="s7">[];</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">// --- RENDER CLOCK ---</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">function</span><span class="s1"> renderClock</span><span class="s7">(</span><span class="s1">item</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">save</span><span class="s7">();</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">translate</span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">x</span><span class="s7">,</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">y</span><span class="s7">);</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s1">// No shadow for a flat design as per style guide</span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s1">// applyShadow(); // Removed</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">globalAlpha </span><span class="s7">=</span><span class="s1"> geometricShapeTransparency</span><span class="s7">;</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s1">// Clock face</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">fillStyle </span><span class="s7">=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">color</span><span class="s7">;</span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s1">// Scaled outline width for prominence</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">const</span><span class="s1"> scaledOutlineWidth </span><span class="s7">=</span><span class="s1"> </span><span class="s14">Math</span><span class="s7">.</span><span class="s1">max</span><span class="s7">(</span><span class="s13">1.5</span><span class="s7">,</span><span class="s1"> mainOutlineWidth </span><span class="s7">*</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">/</span><span class="s1"> </span><span class="s13">150</span><span class="s7">));</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">strokeStyle </span><span class="s7">=</span><span class="s1"> mainOutlineColor</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">lineWidth </span><span class="s7">=</span><span class="s1"> scaledOutlineWidth</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">beginPath</span><span class="s7">();</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">arc</span><span class="s7">(</span><span class="s13">0</span><span class="s7">,</span><span class="s1"> </span><span class="s13">0</span><span class="s7">,</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">radius</span><span class="s7">,</span><span class="s1"> </span><span class="s13">0</span><span class="s7">,</span><span class="s1"> </span><span class="s13">2</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s14">Math</span><span class="s7">.</span><span class="s14">PI</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">fill</span><span class="s7">();</span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s1">// clearShadow(); // Removed</span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s4">stroke</span><span class="s7">();</span><span class="s4"> </span><span class="s1">// Border for the clock face</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s1">// Clock Numbers</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">fillStyle </span><span class="s7">=</span><span class="s1"> mainOutlineColor</span><span class="s7">;</span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s1">// Increased font size for prominence, using Montserrat</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">const</span><span class="s1"> fontSize </span><span class="s7">=</span><span class="s1"> </span><span class="s14">Math</span><span class="s7">.</span><span class="s1">max</span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">*</span><span class="s1"> </span><span class="s13">0.16</span><span class="s7">,</span><span class="s1"> </span><span class="s13">22</span><span class="s7">);</span><span class="s1"> </span><span class="s12">// Min 22px, 16% of radius</span></p>
<p class="p7"><span class="s4"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s4">font </span><span class="s7">=</span><span class="s4"> </span><span class="s1">`900 </span><span class="s7">${</span><span class="s4">fontSize</span><span class="s7">}</span><span class="s1">px Montserrat, Arial, sans-serif`</span><span class="s7">;</span><span class="s4"> </span><span class="s12">// Extra Bold (900) Montserrat</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">textAlign </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'center'</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">textBaseline </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'middle'</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">for</span><span class="s1"> </span><span class="s7">(</span><span class="s10">let</span><span class="s1"> i </span><span class="s7">=</span><span class="s1"> </span><span class="s13">1</span><span class="s7">;</span><span class="s1"> i </span><span class="s7">&lt;=</span><span class="s1"> </span><span class="s13">12</span><span class="s7">;</span><span class="s1"> i</span><span class="s7">++)</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">const</span><span class="s1"> angle </span><span class="s7">=</span><span class="s1"> </span><span class="s7">(</span><span class="s1">i </span><span class="s7">-</span><span class="s1"> </span><span class="s13">3</span><span class="s7">)</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s7">(</span><span class="s14">Math</span><span class="s7">.</span><span class="s14">PI</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s13">2</span><span class="s7">)</span><span class="s1"> </span><span class="s7">/</span><span class="s1"> </span><span class="s13">12</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">const</span><span class="s1"> numX </span><span class="s7">=</span><span class="s1"> </span><span class="s14">Math</span><span class="s7">.</span><span class="s1">cos</span><span class="s7">(</span><span class="s1">angle</span><span class="s7">)</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">*</span><span class="s1"> </span><span class="s13">0.82</span><span class="s7">);</span><span class="s1"> </span><span class="s12">// Slightly closer to edge</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">const</span><span class="s1"> numY </span><span class="s7">=</span><span class="s1"> </span><span class="s14">Math</span><span class="s7">.</span><span class="s1">sin</span><span class="s7">(</span><span class="s1">angle</span><span class="s7">)</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">*</span><span class="s1"> </span><span class="s13">0.82</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>ctx</span><span class="s7">.</span><span class="s1">fillText</span><span class="s7">(</span><span class="s1">i</span><span class="s7">.</span><span class="s1">toString</span><span class="s7">(),</span><span class="s1"> numX</span><span class="s7">,</span><span class="s1"> numY</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s7">}</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s1">// Clock Hands</span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s10">const</span><span class="s4"> hours </span><span class="s7">=</span><span class="s4"> </span><span class="s13">3</span><span class="s7">;</span><span class="s4"><span class="Apple-converted-space">  </span></span><span class="s1">// Hour hand points to 3</span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s10">const</span><span class="s4"> minutes </span><span class="s7">=</span><span class="s4"> </span><span class="s13">0</span><span class="s7">;</span><span class="s4"> </span><span class="s1">// Minute hand points to 12 (0 minutes)</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s1">// Hour hand (Short, Thick, Red)</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">const</span><span class="s1"> hourAngle </span><span class="s7">=</span><span class="s1"> </span><span class="s7">(</span><span class="s1"> </span><span class="s7">(</span><span class="s1">hours </span><span class="s7">%</span><span class="s1"> </span><span class="s13">12</span><span class="s7">)</span><span class="s1"> </span><span class="s7">+</span><span class="s1"> minutes </span><span class="s7">/</span><span class="s1"> </span><span class="s13">60</span><span class="s1"> </span><span class="s7">-</span><span class="s1"> </span><span class="s13">3</span><span class="s7">)</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s7">(</span><span class="s14">Math</span><span class="s7">.</span><span class="s14">PI</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s13">2</span><span class="s7">)</span><span class="s1"> </span><span class="s7">/</span><span class="s1"> </span><span class="s13">12</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">beginPath</span><span class="s7">();</span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s1">// Ensure hour hand is thick and prominent</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">lineWidth </span><span class="s7">=</span><span class="s1"> </span><span class="s14">Math</span><span class="s7">.</span><span class="s1">max</span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">*</span><span class="s1"> </span><span class="s13">0.07</span><span class="s7">,</span><span class="s1"> </span><span class="s13">5</span><span class="s7">);</span><span class="s1"> </span><span class="s12">// 7% of radius, min 5px</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">strokeStyle </span><span class="s7">=</span><span class="s1"> hourHandColor</span><span class="s7">;</span><span class="s1"> </span><span class="s12">// Red</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">lineCap </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'round'</span><span class="s7">;</span><span class="s1"> </span><span class="s12">// Rounded ends for hands</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">moveTo</span><span class="s7">(</span><span class="s13">0</span><span class="s7">,</span><span class="s1"> </span><span class="s13">0</span><span class="s7">);</span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s1">// Hour hand length: 50% of radius</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">lineTo</span><span class="s7">(</span><span class="s14">Math</span><span class="s7">.</span><span class="s1">cos</span><span class="s7">(</span><span class="s1">hourAngle</span><span class="s7">)</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">*</span><span class="s1"> </span><span class="s13">0.50</span><span class="s7">,</span><span class="s1"> </span><span class="s14">Math</span><span class="s7">.</span><span class="s1">sin</span><span class="s7">(</span><span class="s1">hourAngle</span><span class="s7">)</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">*</span><span class="s1"> </span><span class="s13">0.50</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">stroke</span><span class="s7">();</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s1">// Minute hand (Long, Thin, Blue)</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">const</span><span class="s1"> minuteAngle </span><span class="s7">=</span><span class="s1"> </span><span class="s7">(</span><span class="s1">minutes </span><span class="s7">-</span><span class="s1"> </span><span class="s13">15</span><span class="s7">)</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s7">(</span><span class="s14">Math</span><span class="s7">.</span><span class="s14">PI</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s13">2</span><span class="s7">)</span><span class="s1"> </span><span class="s7">/</span><span class="s1"> </span><span class="s13">60</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">beginPath</span><span class="s7">();</span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s1">// Ensure minute hand is distinct, thinner than hour hand</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">lineWidth </span><span class="s7">=</span><span class="s1"> </span><span class="s14">Math</span><span class="s7">.</span><span class="s1">max</span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">*</span><span class="s1"> </span><span class="s13">0.035</span><span class="s7">,</span><span class="s1"> </span><span class="s13">3</span><span class="s7">);</span><span class="s1"> </span><span class="s12">// 3.5% of radius, min 3px</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">strokeStyle </span><span class="s7">=</span><span class="s1"> minuteHandColor</span><span class="s7">;</span><span class="s1"> </span><span class="s12">// Blue</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">lineCap </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'round'</span><span class="s7">;</span><span class="s1"> </span><span class="s12">// Rounded ends for hands</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">moveTo</span><span class="s7">(</span><span class="s13">0</span><span class="s7">,</span><span class="s1"> </span><span class="s13">0</span><span class="s7">);</span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s1">// Minute hand length: 75% of radius</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">lineTo</span><span class="s7">(</span><span class="s14">Math</span><span class="s7">.</span><span class="s1">cos</span><span class="s7">(</span><span class="s1">minuteAngle</span><span class="s7">)</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">*</span><span class="s1"> </span><span class="s13">0.75</span><span class="s7">,</span><span class="s1"> </span><span class="s14">Math</span><span class="s7">.</span><span class="s1">sin</span><span class="s7">(</span><span class="s1">minuteAngle</span><span class="s7">)</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">*</span><span class="s1"> </span><span class="s13">0.75</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">stroke</span><span class="s7">();</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s1">// Center dot</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">beginPath</span><span class="s7">();</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">fillStyle </span><span class="s7">=</span><span class="s1"> mainOutlineColor</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">arc</span><span class="s7">(</span><span class="s13">0</span><span class="s7">,</span><span class="s13">0</span><span class="s7">,</span><span class="s1"> </span><span class="s14">Math</span><span class="s7">.</span><span class="s1">max</span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">*</span><span class="s1"> </span><span class="s13">0.04</span><span class="s7">,</span><span class="s1"> </span><span class="s13">4</span><span class="s7">),</span><span class="s1"> </span><span class="s13">0</span><span class="s7">,</span><span class="s1"> </span><span class="s13">2</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s14">Math</span><span class="s7">.</span><span class="s14">PI</span><span class="s7">);</span><span class="s1"> </span><span class="s12">// Prominent center dot</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">fill</span><span class="s7">();</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s1">// Labels for hands</span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s1">// Increased label font size for prominence, using Montserrat</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">const</span><span class="s1"> labelFontSize </span><span class="s7">=</span><span class="s1"> </span><span class="s14">Math</span><span class="s7">.</span><span class="s1">max</span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">*</span><span class="s1"> </span><span class="s13">0.07</span><span class="s7">,</span><span class="s1"> </span><span class="s13">18</span><span class="s7">);</span><span class="s1"> </span><span class="s12">// Min 18px, 7% of radius</span></p>
<p class="p7"><span class="s4"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s4">font </span><span class="s7">=</span><span class="s4"> </span><span class="s1">`900 </span><span class="s7">${</span><span class="s4">labelFontSize</span><span class="s7">}</span><span class="s1">px Montserrat, Arial, sans-serif`</span><span class="s7">;</span><span class="s4"> </span><span class="s12">// Extra Bold (900) Montserrat</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">fillStyle </span><span class="s7">=</span><span class="s1"> labelColor</span><span class="s7">;</span><span class="s1"> </span><span class="s12">// Black</span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s4">textAlign </span><span class="s7">=</span><span class="s4"> </span><span class="s9">'left'</span><span class="s7">;</span><span class="s4"> </span><span class="s1">// Default, will be adjusted</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">textBaseline </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'middle'</span><span class="s7">;</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s1">// Hour Hand Label</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">const</span><span class="s1"> hourLabelText </span><span class="s7">=</span><span class="s1"> </span><span class="s9">"Hour Hand (short)"</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">const</span><span class="s1"> hourTextMetrics </span><span class="s7">=</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">measureText</span><span class="s7">(</span><span class="s1">hourLabelText</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">const</span><span class="s1"> hourLabelAngleOffset </span><span class="s7">=</span><span class="s1"> </span><span class="s14">Math</span><span class="s7">.</span><span class="s14">PI</span><span class="s1"> </span><span class="s7">/</span><span class="s1"> </span><span class="s13">12</span><span class="s7">;</span><span class="s1"> </span><span class="s12">// Adjust offset for better placement</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">const</span><span class="s1"> hourLabelEffectiveAngle </span><span class="s7">=</span><span class="s1"> hourAngle </span><span class="s7">+</span><span class="s1"> </span><span class="s7">(</span><span class="s1">hourAngle </span><span class="s7">&gt;</span><span class="s1"> </span><span class="s14">Math</span><span class="s7">.</span><span class="s14">PI</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s13">0.5</span><span class="s1"> </span><span class="s7">&amp;&amp;</span><span class="s1"> hourAngle </span><span class="s7">&lt;</span><span class="s1"> </span><span class="s14">Math</span><span class="s7">.</span><span class="s14">PI</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s13">1.5</span><span class="s1"> </span><span class="s7">?</span><span class="s1"> </span><span class="s7">-</span><span class="s1">hourLabelAngleOffset </span><span class="s7">:</span><span class="s1"> hourLabelAngleOffset</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">let</span><span class="s1"> hourLabelX </span><span class="s7">=</span><span class="s1"> </span><span class="s14">Math</span><span class="s7">.</span><span class="s1">cos</span><span class="s7">(</span><span class="s1">hourLabelEffectiveAngle</span><span class="s7">)</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">*</span><span class="s1"> </span><span class="s13">0.50</span><span class="s1"> </span><span class="s7">+</span><span class="s1"> </span><span class="s13">10</span><span class="s7">);</span><span class="s1"> </span><span class="s12">// Position beyond hand tip</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">let</span><span class="s1"> hourLabelY </span><span class="s7">=</span><span class="s1"> </span><span class="s14">Math</span><span class="s7">.</span><span class="s1">sin</span><span class="s7">(</span><span class="s1">hourLabelEffectiveAngle</span><span class="s7">)</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">*</span><span class="s1"> </span><span class="s13">0.50</span><span class="s1"> </span><span class="s7">+</span><span class="s1"> </span><span class="s13">10</span><span class="s7">);</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s14">Math</span><span class="s7">.</span><span class="s1">abs</span><span class="s7">(</span><span class="s14">Math</span><span class="s7">.</span><span class="s1">cos</span><span class="s7">(</span><span class="s1">hourLabelEffectiveAngle</span><span class="s7">))</span><span class="s1"> </span><span class="s7">&lt;</span><span class="s1"> </span><span class="s13">0.1</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> </span><span class="s12">// Near vertical</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>ctx</span><span class="s7">.</span><span class="s1">textAlign </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'center'</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s7">}</span><span class="s1"> </span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s14">Math</span><span class="s7">.</span><span class="s1">cos</span><span class="s7">(</span><span class="s1">hourLabelEffectiveAngle</span><span class="s7">)</span><span class="s1"> </span><span class="s7">&lt;</span><span class="s1"> </span><span class="s13">0</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> </span><span class="s12">// Pointing left</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>ctx</span><span class="s7">.</span><span class="s1">textAlign </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'right'</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>hourLabelX </span><span class="s7">-=</span><span class="s1"> </span><span class="s13">5</span><span class="s7">;</span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s7">}</span><span class="s4"> </span><span class="s10">else</span><span class="s4"> </span><span class="s7">{</span><span class="s4"> </span><span class="s1">// Pointing right</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>ctx</span><span class="s7">.</span><span class="s1">textAlign </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'left'</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>hourLabelX </span><span class="s7">+=</span><span class="s1"> </span><span class="s13">5</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">fillText</span><span class="s7">(</span><span class="s1">hourLabelText</span><span class="s7">,</span><span class="s1"> hourLabelX</span><span class="s7">,</span><span class="s1"> hourLabelY</span><span class="s7">);</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s1">// Minute Hand Label</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">const</span><span class="s1"> minuteLabelText </span><span class="s7">=</span><span class="s1"> </span><span class="s9">"Minute Hand (long)"</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">const</span><span class="s1"> minuteTextMetrics </span><span class="s7">=</span><span class="s1"> ctx</span><span class="s7">.</span><span class="s1">measureText</span><span class="s7">(</span><span class="s1">minuteLabelText</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">const</span><span class="s1"> minuteLabelAngleOffset </span><span class="s7">=</span><span class="s1"> </span><span class="s14">Math</span><span class="s7">.</span><span class="s14">PI</span><span class="s1"> </span><span class="s7">/</span><span class="s1"> </span><span class="s13">18</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">const</span><span class="s1"> minuteLabelEffectiveAngle </span><span class="s7">=</span><span class="s1"> minuteAngle </span><span class="s7">+</span><span class="s1"> </span><span class="s7">(</span><span class="s1">minuteAngle </span><span class="s7">&gt;</span><span class="s1"> </span><span class="s14">Math</span><span class="s7">.</span><span class="s14">PI</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s13">0.5</span><span class="s1"> </span><span class="s7">&amp;&amp;</span><span class="s1"> minuteAngle </span><span class="s7">&lt;</span><span class="s1"> </span><span class="s14">Math</span><span class="s7">.</span><span class="s14">PI</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s13">1.5</span><span class="s1"> </span><span class="s7">?</span><span class="s1"> minuteLabelAngleOffset </span><span class="s7">:</span><span class="s1"> </span><span class="s7">-</span><span class="s1">minuteLabelAngleOffset</span><span class="s7">);</span><span class="s1"> </span><span class="s12">// Adjust offset</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">let</span><span class="s1"> minuteLabelX </span><span class="s7">=</span><span class="s1"> </span><span class="s14">Math</span><span class="s7">.</span><span class="s1">cos</span><span class="s7">(</span><span class="s1">minuteLabelEffectiveAngle</span><span class="s7">)</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">*</span><span class="s1"> </span><span class="s13">0.80</span><span class="s1"> </span><span class="s7">+</span><span class="s1"> </span><span class="s13">10</span><span class="s7">);</span><span class="s1"> </span><span class="s12">// Position beyond hand tip</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">let</span><span class="s1"> minuteLabelY </span><span class="s7">=</span><span class="s1"> </span><span class="s14">Math</span><span class="s7">.</span><span class="s1">sin</span><span class="s7">(</span><span class="s1">minuteLabelEffectiveAngle</span><span class="s7">)</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">*</span><span class="s1"> </span><span class="s13">0.80</span><span class="s1"> </span><span class="s7">+</span><span class="s1"> </span><span class="s13">10</span><span class="s7">);</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s14">Math</span><span class="s7">.</span><span class="s1">abs</span><span class="s7">(</span><span class="s14">Math</span><span class="s7">.</span><span class="s1">cos</span><span class="s7">(</span><span class="s1">minuteLabelEffectiveAngle</span><span class="s7">))</span><span class="s1"> </span><span class="s7">&lt;</span><span class="s1"> </span><span class="s13">0.1</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> </span><span class="s12">// Near vertical (like 12 or 6)</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>ctx</span><span class="s7">.</span><span class="s1">textAlign </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'center'</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                 </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s14">Math</span><span class="s7">.</span><span class="s1">sin</span><span class="s7">(</span><span class="s1">minuteLabelEffectiveAngle</span><span class="s7">)</span><span class="s1"> </span><span class="s7">&lt;</span><span class="s1"> </span><span class="s13">0</span><span class="s7">)</span><span class="s1"> minuteLabelY </span><span class="s7">-=</span><span class="s1"> labelFontSize </span><span class="s7">*</span><span class="s1"> </span><span class="s13">0.5</span><span class="s7">;</span><span class="s1"> </span><span class="s12">// If pointing up, shift label text up</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                 </span></span><span class="s10">else</span><span class="s1"> minuteLabelY </span><span class="s7">+=</span><span class="s1"> labelFontSize </span><span class="s7">*</span><span class="s1"> </span><span class="s13">0.5</span><span class="s7">;</span><span class="s1"> </span><span class="s12">// If pointing down, shift label text down</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s7">}</span><span class="s1"> </span><span class="s10">else</span><span class="s1"> </span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s14">Math</span><span class="s7">.</span><span class="s1">cos</span><span class="s7">(</span><span class="s1">minuteLabelEffectiveAngle</span><span class="s7">)</span><span class="s1"> </span><span class="s7">&lt;</span><span class="s1"> </span><span class="s13">0</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span><span class="s1"> </span><span class="s12">// Pointing left</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>ctx</span><span class="s7">.</span><span class="s1">textAlign </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'right'</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>minuteLabelX </span><span class="s7">-=</span><span class="s1"> </span><span class="s13">5</span><span class="s7">;</span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s7">}</span><span class="s4"> </span><span class="s10">else</span><span class="s4"> </span><span class="s7">{</span><span class="s4"> </span><span class="s1">// Pointing right</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>ctx</span><span class="s7">.</span><span class="s1">textAlign </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'left'</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>minuteLabelX </span><span class="s7">+=</span><span class="s1"> </span><span class="s13">5</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">             </span>ctx</span><span class="s7">.</span><span class="s1">fillText</span><span class="s7">(</span><span class="s1">minuteLabelText</span><span class="s7">,</span><span class="s1"> minuteLabelX</span><span class="s7">,</span><span class="s1"> minuteLabelY</span><span class="s7">);</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">globalAlpha </span><span class="s7">=</span><span class="s1"> </span><span class="s13">1.0</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">restore</span><span class="s7">();</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s7">}</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">// --- Main drawing function ---</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">function</span><span class="s1"> drawScene</span><span class="s7">()</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s1">// canvas.style.backgroundColor is not needed as body is white and canvas is transparent to it or white itself.</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">clearRect</span><span class="s7">(</span><span class="s13">0</span><span class="s7">,</span><span class="s1"> </span><span class="s13">0</span><span class="s7">,</span><span class="s1"> canvas</span><span class="s7">.</span><span class="s1">width</span><span class="s7">,</span><span class="s1"> canvas</span><span class="s7">.</span><span class="s1">height</span><span class="s7">);</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">clock </span><span class="s7">&amp;&amp;</span><span class="s1"> clock</span><span class="s7">.</span><span class="s1">type </span><span class="s7">===</span><span class="s1"> </span><span class="s9">'clock'</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>renderClock</span><span class="s7">(</span><span class="s1">clock</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s7">}</span><span class="s1"> </span><span class="s10">else</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p7"><span class="s4"><span class="Apple-converted-space">                </span>console</span><span class="s7">.</span><span class="s4">error</span><span class="s7">(</span><span class="s1">"Clock object not found or not of type 'clock' for drawing."</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s7">}</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">selectedItems</span><span class="s7">.</span><span class="s1">includes</span><span class="s7">(</span><span class="s1">clock</span><span class="s7">))</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>highlightItem</span><span class="s7">(</span><span class="s1">clock</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s7">}</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">// --- Highlight selected item (the clock) ---</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">function</span><span class="s1"> highlightItem</span><span class="s7">(</span><span class="s1">item</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(!</span><span class="s1">item </span><span class="s7">||</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">!==</span><span class="s1"> </span><span class="s9">'clock'</span><span class="s7">)</span><span class="s1"> </span><span class="s10">return</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">save</span><span class="s7">();</span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s1">// Subtle highlight as per style guide (rgba(0, 0, 0, 0.1)) - using a border for clarity</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">strokeStyle </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'rgba(0, 100, 255, 0.5)'</span><span class="s7">;</span><span class="s1"> </span><span class="s12">// A visible selection blue</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">lineWidth </span><span class="s7">=</span><span class="s1"> </span><span class="s13">4</span><span class="s7">;</span><span class="s1"> </span><span class="s12">// Prominent highlight</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">setLineDash</span><span class="s7">([</span><span class="s13">8</span><span class="s7">,</span><span class="s1"> </span><span class="s13">4</span><span class="s7">]);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">beginPath</span><span class="s7">();</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">arc</span><span class="s7">(</span><span class="s1">item</span><span class="s7">.</span><span class="s1">x</span><span class="s7">,</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">y</span><span class="s7">,</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">+</span><span class="s1"> </span><span class="s13">8</span><span class="s7">,</span><span class="s1"> </span><span class="s13">0</span><span class="s7">,</span><span class="s1"> </span><span class="s13">2</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s14">Math</span><span class="s7">.</span><span class="s14">PI</span><span class="s7">);</span><span class="s1"> </span><span class="s12">// Slightly larger radius for highlight</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">stroke</span><span class="s7">();</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>ctx</span><span class="s7">.</span><span class="s1">restore</span><span class="s7">();</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s7">}</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">// --- Initialize or update clock's position and size ---</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">function</span><span class="s1"> initializeShapesInLayout</span><span class="s7">()</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(!</span><span class="s1">clock</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p7"><span class="s4"><span class="Apple-converted-space">                </span>console</span><span class="s7">.</span><span class="s4">error</span><span class="s7">(</span><span class="s1">"Clock object is not defined during layout initialization!"</span><span class="s7">);</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">return</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>clock</span><span class="s7">.</span><span class="s1">x </span><span class="s7">=</span><span class="s1"> canvas</span><span class="s7">.</span><span class="s1">width </span><span class="s7">/</span><span class="s1"> </span><span class="s13">2</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>clock</span><span class="s7">.</span><span class="s1">y </span><span class="s7">=</span><span class="s1"> canvas</span><span class="s7">.</span><span class="s1">height </span><span class="s7">/</span><span class="s1"> </span><span class="s13">2</span><span class="s7">;</span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">            </span></span><span class="s1">// Ensure clock is very large, using 90% of the smaller dimension for diameter</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>clock</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">=</span><span class="s1"> </span><span class="s14">Math</span><span class="s7">.</span><span class="s1">min</span><span class="s7">(</span><span class="s1">canvas</span><span class="s7">.</span><span class="s1">width</span><span class="s7">,</span><span class="s1"> canvas</span><span class="s7">.</span><span class="s1">height</span><span class="s7">)</span><span class="s1"> </span><span class="s7">*</span><span class="s1"> </span><span class="s13">0.45</span><span class="s7">;</span><span class="s1"> </span><span class="s12">// 0.5 * 0.90</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">clock</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">&lt;</span><span class="s1"> </span><span class="s13">100</span><span class="s7">)</span><span class="s1"> clock</span><span class="s7">.</span><span class="s1">radius </span><span class="s7">=</span><span class="s1"> </span><span class="s13">100</span><span class="s7">;</span><span class="s1"> </span><span class="s12">// Ensure a minimum prominent size</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s7">}</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">// --- Handle canvas resizing ---</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">function</span><span class="s1"> resizeCanvasAndDraw</span><span class="s7">()</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>canvas</span><span class="s7">.</span><span class="s1">width </span><span class="s7">=</span><span class="s1"> window</span><span class="s7">.</span><span class="s1">innerWidth</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>canvas</span><span class="s7">.</span><span class="s1">height </span><span class="s7">=</span><span class="s1"> window</span><span class="s7">.</span><span class="s1">innerHeight</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>initializeShapesInLayout</span><span class="s7">();</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>drawScene</span><span class="s7">();</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s7">}</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">// --- Mouse Event Listeners for Dragging ---</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span>canvas</span><span class="s7">.</span><span class="s1">addEventListener</span><span class="s7">(</span><span class="s9">'mousedown'</span><span class="s7">,</span><span class="s1"> </span><span class="s7">(</span><span class="s1">e</span><span class="s7">)</span><span class="s1"> </span><span class="s7">=&gt;</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>lastMouseX </span><span class="s7">=</span><span class="s1"> e</span><span class="s7">.</span><span class="s1">offsetX</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>lastMouseY </span><span class="s7">=</span><span class="s1"> e</span><span class="s7">.</span><span class="s1">offsetY</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">clock </span><span class="s7">&amp;&amp;</span><span class="s1"> isPointOverItem</span><span class="s7">(</span><span class="s1">lastMouseX</span><span class="s7">,</span><span class="s1"> lastMouseY</span><span class="s7">,</span><span class="s1"> clock</span><span class="s7">))</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>isDragging </span><span class="s7">=</span><span class="s1"> </span><span class="s10">true</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>draggedItem </span><span class="s7">=</span><span class="s1"> clock</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>selectedItems </span><span class="s7">=</span><span class="s1"> </span><span class="s7">[</span><span class="s1">clock</span><span class="s7">];</span><span class="s1"> </span><span class="s12">// Select the clock</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>dragStartOffsets </span><span class="s7">=</span><span class="s1"> </span><span class="s7">[{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                    </span>item</span><span class="s7">:</span><span class="s1"> clock</span><span class="s7">,</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                    </span>dx</span><span class="s7">:</span><span class="s1"> clock</span><span class="s7">.</span><span class="s1">x </span><span class="s7">-</span><span class="s1"> lastMouseX</span><span class="s7">,</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                    </span>dy</span><span class="s7">:</span><span class="s1"> clock</span><span class="s7">.</span><span class="s1">y </span><span class="s7">-</span><span class="s1"> lastMouseY</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s7">}];</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>canvas</span><span class="s7">.</span><span class="s1">style</span><span class="s7">.</span><span class="s1">cursor </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'grabbing'</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s7">}</span><span class="s1"> </span><span class="s10">else</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>selectedItems </span><span class="s7">=</span><span class="s1"> </span><span class="s7">[];</span><span class="s1"> </span><span class="s12">// Deselect if clicked outside</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>draggedItem </span><span class="s7">=</span><span class="s1"> </span><span class="s10">null</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>canvas</span><span class="s7">.</span><span class="s1">style</span><span class="s7">.</span><span class="s1">cursor </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'default'</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>drawScene</span><span class="s7">();</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s7">});</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span>canvas</span><span class="s7">.</span><span class="s1">addEventListener</span><span class="s7">(</span><span class="s9">'mousemove'</span><span class="s7">,</span><span class="s1"> </span><span class="s7">(</span><span class="s1">e</span><span class="s7">)</span><span class="s1"> </span><span class="s7">=&gt;</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">isDragging </span><span class="s7">&amp;&amp;</span><span class="s1"> draggedItem</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">const</span><span class="s1"> currentX </span><span class="s7">=</span><span class="s1"> e</span><span class="s7">.</span><span class="s1">offsetX</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span></span><span class="s10">const</span><span class="s1"> currentY </span><span class="s7">=</span><span class="s1"> e</span><span class="s7">.</span><span class="s1">offsetY</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>draggedItem</span><span class="s7">.</span><span class="s1">x </span><span class="s7">=</span><span class="s1"> currentX </span><span class="s7">+</span><span class="s1"> dragStartOffsets</span><span class="s7">[</span><span class="s13">0</span><span class="s7">].</span><span class="s1">dx</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>draggedItem</span><span class="s7">.</span><span class="s1">y </span><span class="s7">=</span><span class="s1"> currentY </span><span class="s7">+</span><span class="s1"> dragStartOffsets</span><span class="s7">[</span><span class="s13">0</span><span class="s7">].</span><span class="s1">dy</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>drawScene</span><span class="s7">();</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s7">});</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span>canvas</span><span class="s7">.</span><span class="s1">addEventListener</span><span class="s7">(</span><span class="s9">'mouseup'</span><span class="s7">,</span><span class="s1"> </span><span class="s7">(</span><span class="s1">e</span><span class="s7">)</span><span class="s1"> </span><span class="s7">=&gt;</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">isDragging</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>isDragging </span><span class="s7">=</span><span class="s1"> </span><span class="s10">false</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>canvas</span><span class="s7">.</span><span class="s1">style</span><span class="s7">.</span><span class="s1">cursor </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'default'</span><span class="s7">;</span></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">                </span></span><span class="s1">// Clock remains selected</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>drawScene</span><span class="s7">();</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s7">});</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span>canvas</span><span class="s7">.</span><span class="s1">addEventListener</span><span class="s7">(</span><span class="s9">'mouseleave'</span><span class="s7">,</span><span class="s1"> </span><span class="s7">()</span><span class="s1"> </span><span class="s7">=&gt;</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(</span><span class="s1">isDragging</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>isDragging </span><span class="s7">=</span><span class="s1"> </span><span class="s10">false</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>canvas</span><span class="s7">.</span><span class="s1">style</span><span class="s7">.</span><span class="s1">cursor </span><span class="s7">=</span><span class="s1"> </span><span class="s9">'default'</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">                </span>drawScene</span><span class="s7">();</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s7">}</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s7">});</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">// --- Check if a point (mouse click) is over the clock ---</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s10">function</span><span class="s1"> isPointOverItem</span><span class="s7">(</span><span class="s1">mouseX</span><span class="s7">,</span><span class="s1"> mouseY</span><span class="s7">,</span><span class="s1"> item</span><span class="s7">)</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">if</span><span class="s1"> </span><span class="s7">(!</span><span class="s1">item </span><span class="s7">||</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">type </span><span class="s7">!==</span><span class="s1"> </span><span class="s9">'clock'</span><span class="s7">)</span><span class="s1"> </span><span class="s10">return</span><span class="s1"> </span><span class="s10">false</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">const</span><span class="s1"> distance </span><span class="s7">=</span><span class="s1"> </span><span class="s14">Math</span><span class="s7">.</span><span class="s1">sqrt</span><span class="s7">(</span><span class="s14">Math</span><span class="s7">.</span><span class="s1">pow</span><span class="s7">(</span><span class="s1">mouseX </span><span class="s7">-</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">x</span><span class="s7">,</span><span class="s1"> </span><span class="s13">2</span><span class="s7">)</span><span class="s1"> </span><span class="s7">+</span><span class="s1"> </span><span class="s14">Math</span><span class="s7">.</span><span class="s1">pow</span><span class="s7">(</span><span class="s1">mouseY </span><span class="s7">-</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">y</span><span class="s7">,</span><span class="s1"> </span><span class="s13">2</span><span class="s7">));</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span></span><span class="s10">return</span><span class="s1"> distance </span><span class="s7">&lt;=</span><span class="s1"> item</span><span class="s7">.</span><span class="s1">radius</span><span class="s7">;</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s7">}</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">// --- Initial setup on window load ---</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span>window</span><span class="s7">.</span><span class="s1">onload </span><span class="s7">=</span><span class="s1"> </span><span class="s7">()</span><span class="s1"> </span><span class="s7">=&gt;</span><span class="s1"> </span><span class="s7">{</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">            </span>resizeCanvasAndDraw</span><span class="s7">();</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span></span><span class="s7">};</span></p>
<p class="p6"><span class="s11"></span><br></p>
<p class="p5"><span class="s4"><span class="Apple-converted-space">        </span></span><span class="s1">// --- Handle window resize events ---</span></p>
<p class="p4"><span class="s1"><span class="Apple-converted-space">        </span>window</span><span class="s7">.</span><span class="s1">addEventListener</span><span class="s7">(</span><span class="s9">'resize'</span><span class="s7">,</span><span class="s1"> resizeCanvasAndDraw</span><span class="s7">);</span></p>
<p class="p1"><span class="s4"><span class="Apple-converted-space">    </span></span><span class="s3">&lt;/</span><span class="s1">script</span><span class="s3">&gt;</span></p>
<p class="p1"><span class="s3">&lt;/</span><span class="s1">body</span><span class="s3">&gt;</span></p>
<p class="p1"><span class="s3">&lt;/</span><span class="s1">html</span><span class="s3">&gt;</span></p>
<p class="p6"><span class="s11"></span><br></p>
</body>
</html>
