"use client"

import { Suspense } from "react"
import { useSearchParams } from "next/navigation"
import HtmlGeneratorTest from "@/components/html-generator-test"
import Link from "next/link"
import { ArrowLeft, Wand2 } from "lucide-react"

// Component that uses useSearchParams
function HtmlGeneratorContent() {
  const searchParams = useSearchParams()
  
  // Get context from URL parameters
  const slideNumber = searchParams.get('slide') ? parseInt(searchParams.get('slide')!) : undefined
  const unitNumber = searchParams.get('unit') || undefined
  const lessonNumber = searchParams.get('lesson') || undefined
  const gradeLevel = searchParams.get('grade') || undefined

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-600 via-blue-500 to-cyan-400">
      {/* Header */}
      <div className="bg-white/10  border-b border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Link 
                href="/"
                className="flex items-center gap-2 text-white hover:text-white/80 transition-colors"
              >
                <ArrowLeft className="h-5 w-5" />
                <span>Back to Home</span>
              </Link>
              <div className="h-6 w-px bg-white/30"></div>
              <div className="flex items-center gap-2">
                <Wand2 className="h-6 w-6 text-white" />
                <h1 className="text-xl font-bold text-white">HTML Generator</h1>
              </div>
            </div>
            
            {/* Context Display */}
            {(slideNumber || unitNumber || lessonNumber || gradeLevel) && (
              <div className="hidden md:flex items-center gap-4 text-white/90 text-sm">
                {gradeLevel && <span>Grade: {gradeLevel}</span>}
                {unitNumber && <span>Unit: {unitNumber}</span>}
                {lessonNumber && <span>Lesson: {lessonNumber}</span>}
                {slideNumber && <span>Slide: {slideNumber}</span>}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="py-8">
        <HtmlGeneratorTest
          slideNumber={slideNumber}
          unitNumber={unitNumber}
          lessonNumber={lessonNumber}
          gradeLevel={gradeLevel}
        />
      </div>

      {/* Footer */}
      <div className="bg-white/10  border-t border-white/20 mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="text-center text-white/70 text-sm">
            <p>Powered by Google Gemini AI • Generate educational HTML content with AI assistance</p>
          </div>
        </div>
      </div>
    </div>
  )
}

// Main page component with Suspense boundary
export default function HtmlGeneratorPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-blue-600 via-blue-500 to-cyan-400 flex items-center justify-center">
        <div className="text-white text-center">
          <Wand2 className="h-12 w-12 mx-auto mb-4 animate-pulse" />
          <p>Loading HTML Generator...</p>
        </div>
      </div>
    }>
      <HtmlGeneratorContent />
    </Suspense>
  )
}
