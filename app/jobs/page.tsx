"use client"

import { Suspense } from "react"
import { useSearchParams } from "next/navigation"
import JobsManager from "@/components/jobs-manager"
import Link from "next/link"
import { ArrowLeft, Briefcase, FileEdit } from "lucide-react"

// Component that uses useSearchParams
function JobsContent() {
  const searchParams = useSearchParams()
  const documentId = searchParams.get('documentId') || undefined
  const grade = searchParams.get('grade') || undefined
  const unit = searchParams.get('unit') || undefined
  const lesson = searchParams.get('lesson') || undefined
  const slide = searchParams.get('slide') || undefined
  const curriculum = searchParams.get('curriculum') || undefined

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-600 via-blue-500 to-cyan-400">
      {/* Header */}
      <div className="bg-white/10 backdrop-blur-sm border-b border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Link
                href={`/?${new URLSearchParams({
                  ...(grade && { grade }),
                  ...(unit && { unit }),
                  ...(lesson && { lesson }),
                  ...(slide && { slide }),
                  ...(curriculum && { curriculum }),
                  refresh: 'true'
                }).toString()}`}
                className="flex items-center gap-2 text-white hover:text-white/80 transition-colors"
                onClick={(e) => {
                  console.log('🏠 CLICK: Back to Home clicked, saving params to localStorage...');

                  // Save current URL params to localStorage
                  const paramsToSave: Record<string, string> = {};
                  if (grade) paramsToSave.grade = grade;
                  if (unit) paramsToSave.unit = unit;
                  if (lesson) paramsToSave.lesson = lesson;
                  if (slide) paramsToSave.slide = slide;
                  if (curriculum) paramsToSave.curriculum = curriculum;

                  console.log('🏠 CLICK: Saving params:', paramsToSave);
                  localStorage.setItem('back_to_home_params', JSON.stringify(paramsToSave));
                  localStorage.setItem('refresh_requested', 'true');
                }}
              >
                <ArrowLeft className="h-5 w-5" />
                <span>Back to Home</span>
              </Link>
              <div className="h-6 w-px bg-white/30"></div>
              <div className="flex items-center gap-2">
                <Briefcase className="h-6 w-6 text-white" />
                <h1 className="text-xl font-bold text-white">Jobs Manager</h1>
              </div>
            </div>

            {/* Document ID Display and Edit JSON Button */}
            {documentId && (
              <div className="flex items-center gap-4">
                <div className="hidden md:flex items-center gap-4 text-white/90 text-sm">
                  <span>Document: {documentId}</span>
                </div>
                <Link
                  href={`/edit-json?${new URLSearchParams({
                    ...(documentId && { id: documentId }),
                    ...(grade && { grade }),
                    ...(unit && { unit }),
                    ...(lesson && { lesson }),
                    ...(slide && { slide }),
                    ...(curriculum && { curriculum })
                  }).toString()}`}
                  
                  className="flex items-center gap-2 px-3 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors border border-white/20"
                >
                  <FileEdit className="h-4 w-4" />
                  <span className="hidden sm:inline">Edit JSON</span>
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="py-8">
        <JobsManager
          documentId={documentId}
          grade={grade}
          unit={unit}
          lesson={lesson}
          slide={slide}
        />
      </div>

      {/* Footer */}
      <div className="bg-white/10 backdrop-blur-sm border-t border-white/20 mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="text-center text-white/70 text-sm">
            <p>HTML Generation Jobs • Powered by Google Gemini AI</p>
          </div>
        </div>
      </div>
    </div>
  )
}

// Main page component with Suspense boundary
export default function JobsPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-blue-600 via-blue-500 to-cyan-400 flex items-center justify-center">
        <div className="text-white text-center">
          <Briefcase className="h-12 w-12 mx-auto mb-4 animate-pulse" />
          <p>Loading Jobs Manager...</p>
        </div>
      </div>
    }>
      <JobsContent />
    </Suspense>
  )
}
