import { NextResponse } from 'next/server'
import { GoogleGenerativeAI } from '@google/generative-ai'

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || '')

export async function GET() {
  try {
    // Check if Gemini API key is configured
    if (!process.env.GEMINI_API_KEY) {
      return NextResponse.json(
        { error: 'Gemini API key not configured' },
        { status: 500 }
      )
    }

    // List available models
    const models = await genAI.listModels()
    
    console.log('Available Gemini models:', models)
    
    return NextResponse.json({
      success: true,
      models: models,
      count: models.length,
      modelNames: models.map(model => model.name)
    })

  } catch (error) {
    console.error('Error listing Gemini models:', error)
    
    return NextResponse.json({
      error: 'Failed to list available models',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
