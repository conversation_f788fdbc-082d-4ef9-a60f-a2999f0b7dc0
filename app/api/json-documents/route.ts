import { NextResponse } from 'next/server';
import { fetchJsonDocument, formatJsonDocumentData } from '@/services/dbJsonDocumentService';

// GET handler for fetching JSON documents with optional filters
export async function GET(req: Request) {
  try {
    // Get search parameters from URL
    const url = new URL(req.url);
    const unit_number = url.searchParams.get('unit_number') || undefined;
    const lesson_number = url.searchParams.get('lesson_number') || undefined;

    // Handle URL-encoded spaces in grade_level
    let grade_level = url.searchParams.get('grade_level') || undefined;
    if (grade_level) {
      grade_level = grade_level.replace(/\+/g, ' ');
    }

    const lang = url.searchParams.get('lang') || undefined;

    console.log('API Request for JSON Document:', {
      unit_number,
      lesson_number,
      grade_level,
      lang
    });

    // Validate required parameters
    if (!unit_number || !lesson_number || !grade_level) {
      return NextResponse.json(
        { message: 'Missing required parameters: unit_number, lesson_number, grade_level' },
        { status: 400 }
      );
    }

    // Fetch document from database
    const document = await fetchJsonDocument({
      unit_number,
      lesson_number,
      grade_level,
      lang
    });

    if (!document) {
      return NextResponse.json(
        { message: 'Document not found' },
        { status: 404 }
      );
    }

    console.log(`Found document with ID: ${document.documentId}`);

    // Format document to match expected JSON format
    const formattedData = formatJsonDocumentData(document);

    // Return formatted document
    return NextResponse.json(formattedData);
  } catch (error) {
    console.error('Error fetching JSON document:', error);
    return NextResponse.json(
      { message: 'An error occurred while fetching JSON document', error: String(error) },
      { status: 500 }
    );
  }
}
