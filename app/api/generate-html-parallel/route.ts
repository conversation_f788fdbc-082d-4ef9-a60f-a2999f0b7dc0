import { NextRequest, NextResponse } from 'next/server'
import { GoogleGenerativeAI } from '@google/generative-ai'
import { GoogleAIFileManager } from '@google/generative-ai/server'
import { connectWithRetry } from '@/lib/mongodb'
import ReferenceFile from '@/models/ReferenceFile'

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || '')
const fileManager = new GoogleAIFileManager(process.env.GEMINI_API_KEY || '')

// Rate limiting configuration - Conservative for quota limits
const RATE_LIMITS = {
  MAX_CONCURRENT: 10, // Reduced from 200 to avoid quota issues
  REQUESTS_PER_MINUTE: 30, // Reduced from 120 to stay within quota
  MAX_RETRIES: 3, // Reduced from 5 to avoid long waits
  INITIAL_DELAY: 2000, // 2 seconds - longer initial delay
  MAX_DELAY: 60000, // 60 seconds - respect RetryInfo delay
}

// Cache for uploaded files URIs
let uploadedFilesCache: any = null

// Rate limiter class
class RateLimiter {
  private requests: number[] = []
  private readonly windowMs = 60000 // 1 minute

  canMakeRequest(): boolean {
    const now = Date.now()
    // Remove requests older than 1 minute
    this.requests = this.requests.filter(time => now - time < this.windowMs)
    
    if (this.requests.length >= RATE_LIMITS.REQUESTS_PER_MINUTE) {
      return false
    }
    
    this.requests.push(now)
    return true
  }

  getWaitTime(): number {
    if (this.requests.length === 0) return 0
    const oldestRequest = Math.min(...this.requests)
    const waitTime = this.windowMs - (Date.now() - oldestRequest)
    return Math.max(0, waitTime)
  }
}

const rateLimiter = new RateLimiter()

// Circuit breaker for quota errors
class CircuitBreaker {
  private failures = 0
  private lastFailureTime = 0
  private readonly maxFailures = 3
  private readonly resetTimeMs = 300000 // 5 minutes

  isOpen(): boolean {
    if (this.failures >= this.maxFailures) {
      const timeSinceLastFailure = Date.now() - this.lastFailureTime
      if (timeSinceLastFailure < this.resetTimeMs) {
        return true // Circuit is open
      } else {
        this.reset() // Reset after timeout
      }
    }
    return false
  }

  recordFailure(): void {
    this.failures++
    this.lastFailureTime = Date.now()
    console.warn(`🔴 Circuit breaker failure count: ${this.failures}/${this.maxFailures}`)
  }

  recordSuccess(): void {
    this.reset()
  }

  reset(): void {
    this.failures = 0
    this.lastFailureTime = 0
  }

  getStatus(): { isOpen: boolean; failures: number; timeUntilReset: number } {
    const timeUntilReset = this.isOpen() ?
      this.resetTimeMs - (Date.now() - this.lastFailureTime) : 0

    return {
      isOpen: this.isOpen(),
      failures: this.failures,
      timeUntilReset
    }
  }
}

const circuitBreaker = new CircuitBreaker()

// HTML validation function - very lenient to avoid false positives
function validateHTML(html: string): { isValid: boolean; error?: string } {
  try {
    if (!html || typeof html !== 'string') {
      return { isValid: false, error: 'HTML is empty or not a string' }
    }

    // Basic HTML structure validation
    const trimmedHtml = html.trim()
    if (trimmedHtml.length === 0) {
      return { isValid: false, error: 'HTML is empty after trimming' }
    }

    // Very basic check - just ensure it looks like HTML
    const hasHtmlTags = /<[^>]+>/g.test(trimmedHtml)
    if (!hasHtmlTags) {
      return { isValid: false, error: 'No HTML tags found' }
    }

    // Try jsdom validation if available, but don't fail if it's not
    try {
      const { JSDOM } = require('jsdom')
      const dom = new JSDOM(trimmedHtml)
      const document = dom.window.document

      // Only check for severe parser errors
      const parserErrors = document.getElementsByTagName('parsererror')
      if (parserErrors.length > 0) {
        console.warn('HTML parser errors detected, but allowing through:', parserErrors[0]?.textContent)
        // Don't fail - just log the warning
      }

    } catch (jsdomError) {
      // JSDOM not available or failed - use very basic validation
      console.log('JSDOM validation not available, using basic validation')

      // Only check for completely broken HTML
      if (trimmedHtml.includes('<') && !trimmedHtml.includes('>')) {
        return { isValid: false, error: 'HTML contains opening brackets but no closing brackets' }
      }

      // Check for obvious syntax errors
      const unclosedTags = trimmedHtml.match(/<[^/>][^>]*(?<!\/)>/g) || []
      const closedTags = trimmedHtml.match(/<\/[^>]+>/g) || []

      // Only fail if there's a massive imbalance (more than 20 unclosed tags)
      if (unclosedTags.length > closedTags.length + 20) {
        return { isValid: false, error: `Too many unclosed HTML tags: ${unclosedTags.length} open vs ${closedTags.length} closed` }
      }
    }

    // If we get here, the HTML is probably fine
    return { isValid: true }
  } catch (error) {
    // Even validation errors shouldn't fail the request - log and allow through
    console.warn('HTML validation error (allowing through):', error instanceof Error ? error.message : 'Unknown error')
    return { isValid: true }
  }
}

// Sleep function for delays
const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// Exponential backoff with jitter
function calculateBackoffDelay(attempt: number): number {
  const baseDelay = RATE_LIMITS.INITIAL_DELAY
  const exponentialDelay = baseDelay * Math.pow(2, attempt)
  const jitter = Math.random() * 0.1 * exponentialDelay // 10% jitter
  return Math.min(exponentialDelay + jitter, RATE_LIMITS.MAX_DELAY)
}

// Upload helper files to Gemini File API (cached)
async function uploadHelperFiles() {
  if (uploadedFilesCache) {
    console.log('📁 Using cached helper files')
    return uploadedFilesCache
  }

  try {
    console.log('📤 Uploading helper files to Gemini File API...')
    const helpersDir = process.cwd() + '/helpers_html_generator'

    // Upload files to Gemini File API
    const clockFile = await fileManager.uploadFile(
      helpersDir + '/Clock.html',
      {
        mimeType: 'text/html',
        displayName: 'Clock Example HTML'
      }
    )

    const mathToolsFile = await fileManager.uploadFile(
      helpersDir + '/Math Tools.html',
      {
        mimeType: 'text/html',
        displayName: 'Math Tools Reference HTML'
      }
    )

    const exampleFile = await fileManager.uploadFile(
      helpersDir + '/Updated_html_for_prompt_to_include_clocks.html',
      {
        mimeType: 'text/html',
        displayName: 'Interactive Example HTML'
      }
    )

    uploadedFilesCache = {
      clockFile: clockFile.file,
      mathToolsFile: mathToolsFile.file,
      exampleFile: exampleFile.file
    }

    console.log('✅ Helper files uploaded and cached')
    return uploadedFilesCache
  } catch (error) {
    console.error('❌ Error uploading helper files:', error)
    throw error
  }
}

// Get style guides from database with retry logic
async function getStyleGuides() {
  let retries = 3

  while (retries > 0) {
    try {
      await connectWithRetry()

      const goodReferences = await ReferenceFile.findOne({ type: 'good-references' })
      const badReferences = await ReferenceFile.findOne({ type: 'bad-references' })

      return {
        goodReferences: goodReferences?.content || '',
        badReferences: badReferences?.content || ''
      }
    } catch (error) {
      retries--
      console.error(`❌ Error fetching style guides (${3 - retries}/3):`, error)

      if (retries === 0) {
        console.warn('⚠️ Using empty style guides due to database errors')
        return {
          goodReferences: '',
          badReferences: ''
        }
      }

      // Wait before retry
      await sleep(1000 * (4 - retries)) // 1s, 2s, 3s delays
    }
  }

  return {
    goodReferences: '',
    badReferences: ''
  }
}

// Interface for request data
interface GenerationRequest {
  id: string
  prompt: string
  slideNumber?: number
  unitNumber?: string
  lessonNumber?: string
  gradeLevel?: string
  curriculum?: string
  lang?: string
}

interface GenerationResult {
  id: string
  success: boolean
  html?: string
  error?: string
  duration: number
  attempts: number
  modelVersion?: string
}

// Single HTML generation with retry logic
async function generateSingleHTML(request: GenerationRequest, uploadedFiles: any, styleGuides: any): Promise<GenerationResult> {
  const startTime = Date.now()
  let lastError: Error | null = null

  // Check circuit breaker
  if (circuitBreaker.isOpen()) {
    const status = circuitBreaker.getStatus()
    console.warn(`🔴 Circuit breaker is open for request ${request.id}. Time until reset: ${Math.round(status.timeUntilReset / 1000)}s`)
    return {
      id: request.id,
      success: false,
      error: `Circuit breaker is open due to quota issues. Try again in ${Math.round(status.timeUntilReset / 1000)} seconds.`,
      duration: Date.now() - startTime,
      attempts: 0
    }
  }

  for (let attempt = 0; attempt < RATE_LIMITS.MAX_RETRIES; attempt++) {
    try {
      // Rate limiting check
      while (!rateLimiter.canMakeRequest()) {
        const waitTime = rateLimiter.getWaitTime()
        console.log(`⏳ Rate limit reached for request ${request.id}, waiting ${waitTime}ms`)
        await sleep(waitTime + 100) // Add small buffer
      }

      console.log(`🚀 Generating HTML for request ${request.id} (attempt ${attempt + 1}/${RATE_LIMITS.MAX_RETRIES})`)

      // Use configurable Gemini model from environment variable
      const selectedModel = process.env.GEMINI_MODEL || 'gemini-2.0-flash-exp'
      
      const modelConfigs: Record<string, { maxOutputTokens: number; description: string }> = {
        'gemini-2.0-flash-exp': {
          maxOutputTokens: 8192,
          description: 'Gemini 2.0 Flash Experimental'
        },
        'gemini-2.5-flash-preview-05-20': {
          maxOutputTokens: 32768,
          description: 'Gemini 2.5 Flash Preview'
        }
      }

      const config = modelConfigs[selectedModel]
      if (!config) {
        throw new Error(`Unsupported model: ${selectedModel}`)
      }

      const model = genAI.getGenerativeModel({
        model: selectedModel,
        generationConfig: {
          maxOutputTokens: config.maxOutputTokens,
          temperature: 0.1,
        },
      })

      // Build the prompt
      const fullPrompt = buildPrompt(request, styleGuides)

      // Generate content
      const result = await model.generateContent([
        {
          fileData: {
            mimeType: uploadedFiles.clockFile.mimeType,
            fileUri: uploadedFiles.clockFile.uri
          }
        },
        {
          fileData: {
            mimeType: uploadedFiles.mathToolsFile.mimeType,
            fileUri: uploadedFiles.mathToolsFile.uri
          }
        },
        {
          fileData: {
            mimeType: uploadedFiles.exampleFile.mimeType,
            fileUri: uploadedFiles.exampleFile.uri
          }
        },
        { text: fullPrompt }
      ])

      const response = result.response
      const generatedHtml = response.text()

      // Validate HTML
      const validation = validateHTML(generatedHtml)
      if (!validation.isValid) {
        throw new Error(`HTML validation failed: ${validation.error}`)
      }

      const duration = Date.now() - startTime
      console.log(`✅ Successfully generated HTML for request ${request.id} in ${duration}ms`)

      // Record success in circuit breaker
      circuitBreaker.recordSuccess()

      return {
        id: request.id,
        success: true,
        html: generatedHtml,
        duration,
        attempts: attempt + 1,
        modelVersion: selectedModel
      }

    } catch (error) {
      lastError = error instanceof Error ? error : new Error('Unknown error')
      console.error(`❌ Attempt ${attempt + 1} failed for request ${request.id}:`, lastError.message)

      // Check if it's a rate limit error or quota exceeded
      if (lastError.message.includes('429') || lastError.message.includes('QUOTA') || lastError.message.includes('rate limit') || lastError.message.includes('quota')) {
        // Record failure in circuit breaker
        circuitBreaker.recordFailure()

        // Extract retry delay from Gemini API response if available
        let retryDelay = calculateBackoffDelay(attempt)

        // Look for RetryInfo delay in error message
        const retryMatch = lastError.message.match(/retryDelay":"(\d+)s"/)
        if (retryMatch) {
          const suggestedDelay = parseInt(retryMatch[1]) * 1000 // Convert to ms
          retryDelay = Math.max(retryDelay, suggestedDelay)
        }

        console.log(`⏳ Quota/Rate limit error, backing off for ${retryDelay}ms`)
        await sleep(retryDelay)
        continue
      }

      // Check if it's a service overload error (503)
      if (lastError.message.includes('503') || lastError.message.includes('overloaded')) {
        const backoffDelay = calculateBackoffDelay(attempt) * 2 // Double delay for overload
        console.log(`⏳ Service overloaded, backing off for ${backoffDelay}ms`)
        await sleep(backoffDelay)
        continue
      }

      // For other errors, wait a shorter time before retry
      if (attempt < RATE_LIMITS.MAX_RETRIES - 1) {
        const retryDelay = Math.min(1000 * (attempt + 1), 5000)
        await sleep(retryDelay)
      }
    }
  }

  const duration = Date.now() - startTime
  console.error(`💥 All attempts failed for request ${request.id} after ${duration}ms`)

  return {
    id: request.id,
    success: false,
    error: lastError?.message || 'Unknown error after all retries',
    duration,
    attempts: RATE_LIMITS.MAX_RETRIES
  }
}

// Build prompt function (simplified version of your existing logic)
function buildPrompt(request: GenerationRequest, styleGuides: any): string {
  const { prompt, slideNumber, unitNumber, lessonNumber, gradeLevel, curriculum, lang } = request
  
  // Base prompt with style guides
  let fullPrompt = `You are an expert HTML/CSS developer creating interactive educational content for math lessons.

${styleGuides.goodReferences ? `GOOD EXAMPLES TO FOLLOW:\n${styleGuides.goodReferences}\n\n` : ''}
${styleGuides.badReferences ? `BAD EXAMPLES TO AVOID:\n${styleGuides.badReferences}\n\n` : ''}

CONTEXT:
- Grade Level: ${gradeLevel || 'Not specified'}
- Unit: ${unitNumber || 'Not specified'}
- Lesson: ${lessonNumber || 'Not specified'}
- Slide: ${slideNumber || 'Not specified'}
- Curriculum: ${curriculum || 'CCSS'}
- Language: ${lang || 'en'}

REQUIREMENTS:
1. Create a complete HTML document with DOCTYPE, html, head, and body tags
2. Use responsive design with proper proportions (615:368 ratio)
3. Include interactive elements where appropriate
4. Use modern CSS with proper styling
5. Ensure all elements are properly contained and don't overlap
6. Make it visually appealing and educational

TASK:
${prompt}

Generate only the HTML code, no explanations or markdown formatting.`

  return fullPrompt
}

// Main POST handler for parallel processing
export async function POST(request: NextRequest) {
  const overallStartTime = Date.now()

  try {
    const body = await request.json()
    const { requests } = body

    if (!requests || !Array.isArray(requests)) {
      return NextResponse.json({
        error: 'Invalid request format. Expected { requests: GenerationRequest[] }'
      }, { status: 400 })
    }

    if (requests.length === 0) {
      return NextResponse.json({
        error: 'No requests provided'
      }, { status: 400 })
    }

    if (requests.length > RATE_LIMITS.MAX_CONCURRENT) {
      return NextResponse.json({
        error: `Too many requests. Maximum ${RATE_LIMITS.MAX_CONCURRENT} concurrent requests allowed`
      }, { status: 400 })
    }

    if (!process.env.GEMINI_API_KEY) {
      return NextResponse.json({
        error: 'Gemini API key not configured'
      }, { status: 500 })
    }

    console.log(`🚀 Starting parallel HTML generation for ${requests.length} requests`)

    // Validate request format
    for (const req of requests) {
      if (!req.id || !req.prompt) {
        return NextResponse.json({
          error: 'Each request must have id and prompt fields'
        }, { status: 400 })
      }
    }

    // Initialize dependencies
    const [uploadedFiles, styleGuides] = await Promise.all([
      uploadHelperFiles(),
      getStyleGuides()
    ])

    console.log('📋 Dependencies loaded, starting parallel processing...')

    // Process all requests in parallel using Promise.allSettled
    const results = await Promise.allSettled(
      requests.map((req: GenerationRequest) =>
        generateSingleHTML(req, uploadedFiles, styleGuides)
      )
    )

    // Process results
    const successfulResults: GenerationResult[] = []
    const failedResults: GenerationResult[] = []

    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        if (result.value.success) {
          successfulResults.push(result.value)
        } else {
          failedResults.push(result.value)
        }
      } else {
        // Promise was rejected
        failedResults.push({
          id: requests[index].id,
          success: false,
          error: `Promise rejected: ${result.reason}`,
          duration: 0,
          attempts: 0
        })
      }
    })

    const overallDuration = Date.now() - overallStartTime

    // Collect successful HTML fragments
    const htmlFragments = successfulResults.map(result => ({
      id: result.id,
      html: result.html,
      duration: result.duration,
      attempts: result.attempts,
      modelVersion: result.modelVersion
    }))

    // Log summary
    console.log(`📊 Parallel processing completed in ${overallDuration}ms:`)
    console.log(`✅ Successful: ${successfulResults.length}`)
    console.log(`❌ Failed: ${failedResults.length}`)
    console.log(`📈 Success rate: ${((successfulResults.length / requests.length) * 100).toFixed(1)}%`)

    // Log individual results
    successfulResults.forEach(result => {
      console.log(`✅ ${result.id}: ${result.duration}ms (${result.attempts} attempts)`)
    })

    failedResults.forEach(result => {
      console.log(`❌ ${result.id}: ${result.error} (${result.attempts} attempts)`)
    })

    return NextResponse.json({
      success: true,
      summary: {
        totalRequests: requests.length,
        successful: successfulResults.length,
        failed: failedResults.length,
        successRate: (successfulResults.length / requests.length) * 100,
        totalDuration: overallDuration
      },
      results: htmlFragments,
      failures: failedResults.map(result => ({
        id: result.id,
        error: result.error,
        attempts: result.attempts
      }))
    })

  } catch (error) {
    const overallDuration = Date.now() - overallStartTime
    console.error('💥 Parallel processing failed:', error)

    if (error instanceof Error) {
      if (error.message.includes('API_KEY')) {
        return NextResponse.json({ error: 'Invalid Gemini API key' }, { status: 401 })
      }
      if (error.message.includes('QUOTA')) {
        return NextResponse.json({ error: 'Gemini API quota exceeded' }, { status: 429 })
      }
    }

    return NextResponse.json({
      error: 'Parallel HTML generation failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      duration: overallDuration
    }, { status: 500 })
  }
}

// GET endpoint for API information
export async function GET() {
  return NextResponse.json({
    message: 'Parallel Gemini HTML Generator API',
    endpoints: {
      POST: '/api/generate-html-parallel',
      description: 'Generate multiple HTML contents in parallel using Gemini AI'
    },
    configuration: {
      maxConcurrent: RATE_LIMITS.MAX_CONCURRENT,
      requestsPerMinute: RATE_LIMITS.REQUESTS_PER_MINUTE,
      maxRetries: RATE_LIMITS.MAX_RETRIES,
      supportedModels: ['gemini-2.0-flash-exp', 'gemini-2.5-flash-preview-05-20']
    },
    requestFormat: {
      requests: [
        {
          id: 'unique-request-id',
          prompt: 'HTML generation prompt',
          slideNumber: 1,
          unitNumber: '1',
          lessonNumber: '1',
          gradeLevel: 'Grade 3',
          curriculum: 'CCSS',
          lang: 'en'
        }
      ]
    },
    responseFormat: {
      success: true,
      summary: {
        totalRequests: 'number',
        successful: 'number',
        failed: 'number',
        successRate: 'percentage',
        totalDuration: 'milliseconds'
      },
      results: [
        {
          id: 'request-id',
          html: 'generated-html',
          duration: 'milliseconds',
          attempts: 'number',
          modelVersion: 'model-name'
        }
      ],
      failures: [
        {
          id: 'request-id',
          error: 'error-message',
          attempts: 'number'
        }
      ]
    }
  })
}
