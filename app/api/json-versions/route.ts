import { NextRequest, NextResponse } from 'next/server'
import { MongoClient } from 'mongodb'

const uri = process.env.MONGODB_URI
if (!uri) {
  throw new Error('MONGODB_URI is not defined')
}

const client = new MongoClient(uri)

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const documentId = searchParams.get('documentId')
    
    if (!documentId) {
      return NextResponse.json({ error: 'Document ID is required' }, { status: 400 })
    }

    await client.connect()
    const db = client.db('math-lesson-slider')
    const collection = db.collection('jsondocuments')

    // Get all versions of the document
    const versions = await collection
      .find({ 
        $or: [
          { _id: documentId },
          { originalDocumentId: documentId }
        ]
      })
      .sort({ createdAt: -1 })
      .toArray()

    return NextResponse.json({ versions })
  } catch (error) {
    console.error('Error fetching document versions:', error)
    return NextResponse.json({ error: 'Failed to fetch versions' }, { status: 500 })
  } finally {
    await client.close()
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { documentId, updatedData, changeDescription } = body

    if (!documentId || !updatedData) {
      return NextResponse.json({ error: 'Document ID and updated data are required' }, { status: 400 })
    }

    await client.connect()
    const db = client.db('math-lesson-slider')
    const collection = db.collection('jsondocuments')

    // Get the current document
    const currentDoc = await collection.findOne({ _id: documentId })
    if (!currentDoc) {
      return NextResponse.json({ error: 'Document not found' }, { status: 404 })
    }

    // Mark current document as not current
    await collection.updateOne(
      { _id: documentId },
      { 
        $set: { 
          isCurrent: false,
          archivedAt: new Date()
        }
      }
    )

    // Create new version with updated data
    const newVersion = {
      ...updatedData,
      _id: `${documentId}_v${Date.now()}`,
      originalDocumentId: documentId,
      isCurrent: true,
      createdAt: new Date(),
      changeDescription: changeDescription || 'Manual edit',
      version: (currentDoc.version || 1) + 1,
      editedBy: 'admin' // TODO: Get from session
    }

    await collection.insertOne(newVersion)

    return NextResponse.json({ 
      success: true, 
      newVersionId: newVersion._id,
      version: newVersion.version
    })
  } catch (error) {
    console.error('Error creating document version:', error)
    return NextResponse.json({ error: 'Failed to create version' }, { status: 500 })
  } finally {
    await client.close()
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { versionId } = body

    if (!versionId) {
      return NextResponse.json({ error: 'Version ID is required' }, { status: 400 })
    }

    await client.connect()
    const db = client.db('math-lesson-slider')
    const collection = db.collection('jsondocuments')

    // Get the version to restore
    const versionToRestore = await collection.findOne({ _id: versionId })
    if (!versionToRestore) {
      return NextResponse.json({ error: 'Version not found' }, { status: 404 })
    }

    const originalDocumentId = versionToRestore.originalDocumentId || versionId

    // Mark all versions as not current
    await collection.updateMany(
      { 
        $or: [
          { _id: originalDocumentId },
          { originalDocumentId: originalDocumentId }
        ]
      },
      { $set: { isCurrent: false } }
    )

    // Mark selected version as current
    await collection.updateOne(
      { _id: versionId },
      { $set: { isCurrent: true } }
    )

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error restoring version:', error)
    return NextResponse.json({ error: 'Failed to restore version' }, { status: 500 })
  } finally {
    await client.close()
  }
}
