import { NextRequest, NextResponse } from 'next/server';
import connectToDatabase from '@/lib/mongodb';
import JsonDocument from '@/models/JsonDocument';

export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const updateType = searchParams.get('updateType') || 'all';
    const unitNumber = searchParams.get('unitNumber');
    const lessonNumber = searchParams.get('lessonNumber');
    const gradeLevel = searchParams.get('gradeLevel');

    // Connect to the database
    await connectToDatabase();

    // Prepare the query
    const query: any = {};
    
    if (updateType === 'specific') {
      if (unitNumber) {
        query.unit_number = unitNumber;
      }
      
      if (lessonNumber) {
        query.lesson_number = lessonNumber;
      }
      
      if (gradeLevel) {
        query.grade_level = gradeLevel;
      }
    }

    // Find all documents matching the query
    const documents = await JsonDocument.find(query);
    
    if (!documents || documents.length === 0) {
      return NextResponse.json({
        message: 'No documents found matching the criteria',
        results: []
      });
    }

    // Process each document
    const results = [];
    
    for (const document of documents) {
      try {
        const documentId = document.documentId;
        const content = document.content || {};
        let updatedSlides = 0;
        
        // Process each slide in the document
        for (const slideKey of Object.keys(content)) {
          // Skip non-slide keys
          if (!slideKey.startsWith('Slide ')) {
            continue;
          }
          
          const slide = content[slideKey];
          let updated = false;
          
          // Fix slide key format if needed
          const slideNumberMatch = slideKey.match(/Slide (\d+)/);
          if (slideNumberMatch) {
            const slideNumber = parseInt(slideNumberMatch[1], 10);
            
            // Ensure the slide has a type
            if (!slide.type) {
              slide.type = getSlideType(slideNumber);
              updated = true;
            }
            
            // Ensure the slide has a pedagogical name
            if (!slide.slide_pedagogical_name) {
              slide.slide_pedagogical_name = `Slide ${slideNumber}`;
              updated = true;
            }
            
            // Ensure the slide has teacher tips
            if (!slide.teacher_tips) {
              slide.teacher_tips = {
                general_tip: "",
                misconception_tip: ""
              };
              updated = true;
            }
            
            // Fix the generated_html_content if it's a string that looks like an error message
            if (slide.generated_html_content && 
                typeof slide.generated_html_content === 'string' && 
                (slide.generated_html_content.includes('Document not found') || 
                 slide.generated_html_content.includes('{"message":'))) {
              
              // Clear the invalid content
              slide.generated_html_content = "";
              updated = true;
            }
            
            if (updated) {
              updatedSlides++;
            }
          }
        }
        
        // Save the updated document
        if (updatedSlides > 0) {
          document.content = content;
          document.updatedAt = new Date();
          await document.save();
          
          results.push({
            status: 'success',
            documentId,
            message: `Updated document successfully`,
            updatedSlides
          });
        } else {
          results.push({
            status: 'info',
            documentId,
            message: 'No updates needed for this document',
            updatedSlides: 0
          });
        }
      } catch (error) {
        console.error('Error processing document:', error);
        results.push({
          status: 'error',
          documentId: document.documentId,
          message: `Error processing document: ${error.message}`
        });
      }
    }
    
    return NextResponse.json({
      message: 'Slides update process completed',
      results
    });
  } catch (error) {
    console.error('Error updating slides:', error);
    return NextResponse.json(
      { message: 'An error occurred while updating slides', error: String(error) },
      { status: 500 }
    );
  }
}

/**
 * Gets the slide type based on the slide number
 * @param slideNumber The slide number
 * @returns The slide type
 */
function getSlideType(slideNumber: number): string {
  // Map slide numbers to their types
  switch (slideNumber) {
    case 1: return "Quick_Review";
    case 2: return "Learning_Goals";
    case 3: return "Vocabulary";
    case 4: return "hook_1";
    case 5: return "hook_2";
    case 6: return "hook_3";
    case 7: return "teach_1_1";
    case 8: return "teach_1_2";
    case 9: return "teach_1_3";
    case 10: return "talk_1_1";
    case 11: return "talk_1_2";
    case 12: return "talk_1_3";
    case 13: return "try_1";
    case 14: return "teach_2_1";
    case 15: return "teach_2_2";
    case 16: return "teach_2_3";
    case 17: return "talk_2_1";
    case 18: return "talk_2_2";
    case 19: return "talk_2_3";
    case 20: return "try_2";
    case 21: return "practice";
    case 22: return "on_ramp_teach_1";
    case 23: return "on_ramp_talk_1";
    case 24: return "on_ramp_try_1";
    case 25: return "Lesson_Guide_PDF_Link";
    case 26: return "Practice_PDF_Link";
    case 27: return "Accelerator_PDF_Link";
    default: return "unknown";
  }
}
