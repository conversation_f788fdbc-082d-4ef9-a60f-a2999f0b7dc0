import { NextRequest, NextResponse } from 'next/server'
import { checkDatabaseHealth } from '@/lib/mongodb'

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Checking database health...')
    
    const health = await checkDatabaseHealth()
    
    if (health.status === 'healthy') {
      console.log('✅ Database health check passed')
      return NextResponse.json({
        status: 'healthy',
        database: health,
        timestamp: new Date().toISOString()
      })
    } else {
      console.error('❌ Database health check failed:', health.error)
      return NextResponse.json({
        status: 'unhealthy',
        database: health,
        timestamp: new Date().toISOString()
      }, { status: 503 })
    }
  } catch (error) {
    console.error('💥 Database health check error:', error)
    return NextResponse.json({
      status: 'error',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
