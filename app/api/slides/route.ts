import { NextResponse } from 'next/server';
import { fetchSlides, formatSlidesData } from '@/services/dbSlideService';

// GET handler for fetching slides with optional filters
export async function GET(req: Request) {
  try {
    // Get search parameters from URL
    const url = new URL(req.url);
    const unit_number = url.searchParams.get('unit_number') || undefined;
    const unit_title = url.searchParams.get('unit_title') || undefined;
    const lesson_number = url.searchParams.get('lesson_number') || undefined;

    // Handle URL-encoded spaces in grade_level
    let grade_level = url.searchParams.get('grade_level') || undefined;
    if (grade_level) {
      grade_level = grade_level.replace(/\+/g, ' ');
    }

    const slide_number = url.searchParams.get('slide_number')
      ? parseInt(url.searchParams.get('slide_number')!)
      : undefined;
    const lang = url.searchParams.get('lang') || undefined;

    console.log('API Request:', {
      unit_number,
      unit_title,
      lesson_number,
      grade_level,
      slide_number,
      lang
    });

    // Fetch slides from database
    const slides = await fetchSlides({
      unit_number,
      unit_title,
      lesson_number,
      grade_level,
      slide_number,
      lang
    });

    console.log(`Found ${slides.length} slides`);

    // Format slides to match expected JSON format
    const formattedData = formatSlidesData(slides);

    // Return formatted slides
    return NextResponse.json(formattedData);
  } catch (error) {
    console.error('Error fetching slides:', error);
    return NextResponse.json(
      { message: 'An error occurred while fetching slides', error: String(error) },
      { status: 500 }
    );
  }
}
