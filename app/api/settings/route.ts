import { NextRequest, NextResponse } from 'next/server'
import connectToDatabase from '@/lib/mongodb'
import Settings from '@/models/Settings'

// Default values for settings
const DEFAULT_STYLE_GUIDE = `
STYLE GUIDE FOR HTML GENERATION:

DESIGN SYSTEM:
- Background: #FFFFFF (Solid White) for main page background and .grid-container transparency.
- Text, Numbers, Symbols: #000000 (Solid Black).
- Object Colors: User-Provided Palette (Categories: Grays/Whites, Reds/Pinks/Purples, Blues/Teals, Greens/Yellows/Oranges) - ensure strong contrast on #FFFFFF.
- Other Accents: Use if needed (e.g., --brand-green-primary: #5CB85C; --brand-yellow-orange: #FFC300;).
- Highlighting: rgba(0, 0, 0, 0.1) (subtle shadow/highlight).
- Negative/Attention: Bright Red (e.g., #E63946).
- Font: Montserrat, Extra Bold (900) for all text and numbers.

LAYOUT PATTERNS:
- Grid: 13-column, 8-row HTML/CSS grid for development (invisible in final output).
- Main Container: .grid-container MUST be transparent.
- Placement: Use grid-column/grid-row for precise element placement.
- Overall Area: Active content should use 70-80% of 13x8 grid.
- Major Objects/Groups: Span significantly (e.g., single focus: 6-9 cols wide, 4-6 rows high; collections: 8-11 cols wide, 3-5 rows high).
- Components:
    - Base-ten "ten rod": Height ~1-2 grid rows.
    - Base-ten "unit cubes": Proportional to rods, side ~1/4-1/3 grid column width.
    - Other countable items: Fill their conceptual "slot" significantly.
- Internal Cards: Backgrounds must contrast with black text, fit white/black theme.
- Responsive Wrapper: If used, outer .illustration-wrapper should have aspect ratio ~13/8.

SPACING AND GAPS:
- MANDATORY: Add proper gaps (8-16px minimum) between all objects and elements
- Use CSS gap property or margin/padding for visual separation
- Ensure no elements touch or overlap unless intentionally grouped
- Text should have adequate spacing from edges and other elements
- Groups of objects should be visually separated from other groups

ELEMENT SIZING & PROMINENCE:
- Elements MUST be very large, prominent, and grid-scaled.
- Numbers & Labels: Prominent standalone numbers: text height ~1-1.5 grid rows. Labels: min. 34px, scaled legibly.
- ALL TEXT MUST BE FULLY VISIBLE - no cut-off or overflow issues
- Use proper text wrapping and container sizing to prevent text truncation
- Ensure adequate container height and width for all text content

ARTISTIC STYLE & EXECUTION:
- Overall Look: Clean, Flat, Diagrammatic, Grid-Native.
- Primary Shape Source: All illustrative objects MUST be derived from shapes defined in "HTML Illustration Basics.html".
- Object Identity: Clear and contextually appropriate representation when using shapes for real-world items.
- Lines & Shapes: Clean, intentional. Core illustration lines (borders, clock hands) are typically black. Subtle object outlines from shape rendering code.
- Finish: Expertly crafted, uncluttered, precisely aligned.
- Perspective, Light & Dimension: Primarily 2D and flat. Use inherent 3D-like rendering of provided shapes. Minimize additional shadows.

TECHNICAL REQUIREMENTS:
- Foundation: HTML (.grid-container: 13-col, transparent; .cell classes). Body/root wrapper: #FFFFFF background.
- JavaScript: Prioritize for precision, dynamic sizing, and maintaining aspect ratios within grid cells. Primary for shape rendering from "HTML Illustration Basics.html".
- NO ARROWS: Do not include any arrow elements, SVG arrows, or directional indicators unless specifically requested
- Use flexbox or grid with proper gap properties for spacing
- Ensure responsive design that maintains proper spacing on all screen sizes

WHAT TO STRICTLY AVOID:
- Visible development grid lines.
- .grid-container with visible background.
- Small/lost elements relative to grid; non-prominent objects.
- Poor contrast (object colors on white, non-black text/symbols).
- Clutter, excessive decoration.
- Text/symbols smaller than minimums or not scaled for impact.
- Over-reliance on static CSS if it breaks responsiveness/proportionality.
- Using shapes not defined in "HTML Illustration Basics.html" for primary illustrative objects.
- ARROWS OR DIRECTIONAL INDICATORS (unless specifically requested in prompt).
- Elements touching or overlapping without proper gaps.
- Text cut-off, overflow, or truncation issues.
- Cramped layouts without adequate spacing.
- Elements positioned outside visible area or shifted incorrectly.

OUTPUT REQUIREMENTS:
- Generate clean, semantic HTML with proper spacing and gaps.
- Ensure mathematical accuracy and clarity.
- Maintain consistent execution and professional appearance.
- All text must be fully visible and properly spaced.
- No arrows unless specifically requested.
- Proper gaps between all visual elements.
- Adhere to the core brand philosophy of making K-12 math accessible and engaging.
`

const DEFAULT_INSTRUCTIONS = `
Generate high-quality, visually appealing HTML content that creates a professional flat design illustration using a <canvas> element and JavaScript. Follow these enhanced requirements:

1. Canvas Setup:
   - Fixed dimensions: width="568" height="361"
   - High-quality anti-aliasing and smooth rendering
   - Proper scaling for retina displays

2. Visual Elements:
   - Clean, professional design with perfect symmetry
   - Rich, contrasting colors that follow the style guide
   - MANDATORY: Add proper gaps (8-16px minimum) between all objects and elements
   - NO ARROWS: Do not include any arrow elements or directional indicators
   - Precise mathematical calculations for all geometric shapes
   - Professional typography with proper kerning and spacing
   - Ensure all text is fully visible with no cut-off or overflow

3. Spacing and Layout:
   - Use CSS gap property or margin/padding for visual separation
   - Ensure no elements touch or overlap unless intentionally grouped
   - Text should have adequate spacing from edges and other elements
   - Groups of objects should be visually separated from other groups
   - Use proper flexbox or grid layouts with gap properties
   - Ensure responsive design that maintains proper spacing

4. Code Quality:
   - Optimized JavaScript for smooth performance
   - Responsive design principles
   - Clean, well-structured HTML and CSS
   - Cross-browser compatibility
   - Proper error handling
   - No text truncation or overflow issues

5. Text Requirements:
   - ALL TEXT MUST BE FULLY VISIBLE
   - Use proper text wrapping and container sizing
   - Ensure adequate container height and width for all text content
   - No cut-off text or overflow issues

The final result must be polished, professional, and visually striking while maintaining mathematical accuracy and educational clarity. Focus on proper spacing, no arrows, and fully visible text.

Return ONLY the complete HTML content, including <!DOCTYPE html>, <html>, <head>, <style>, and <body> tags. Do not include any explanatory text or comments outside the HTML.
`

// GET - Retrieve all settings or specific setting
export async function GET(request: NextRequest) {
  try {
    await connectToDatabase()

    const { searchParams } = new URL(request.url)
    const key = searchParams.get('key')

    if (key) {
      // Get specific setting
      let setting = await Settings.findOne({ key })
      
      if (!setting) {
        // Create default setting if it doesn't exist
        const defaultValue = key === 'STYLE_GUIDE' ? DEFAULT_STYLE_GUIDE : DEFAULT_INSTRUCTIONS
        const description = key === 'STYLE_GUIDE' 
          ? 'Style guide for HTML generation - defines design system, layout patterns, and visual requirements'
          : 'Instructions for HTML generation - defines technical requirements and generation process'
        
        setting = new Settings({
          key,
          value: defaultValue,
          description
        })
        await setting.save()
      }

      return NextResponse.json({
        success: true,
        setting: setting.toObject()
      })
    } else {
      // Get all settings
      let settings = await Settings.find({})
      
      // Create default settings if they don't exist
      if (settings.length === 0) {
        const defaultSettings = [
          {
            key: 'STYLE_GUIDE',
            value: DEFAULT_STYLE_GUIDE,
            description: 'Style guide for HTML generation - defines design system, layout patterns, and visual requirements'
          },
          {
            key: 'INSTRUCTIONS',
            value: DEFAULT_INSTRUCTIONS,
            description: 'Instructions for HTML generation - defines technical requirements and generation process'
          }
        ]

        for (const settingData of defaultSettings) {
          const setting = new Settings(settingData)
          await setting.save()
        }

        settings = await Settings.find({})
      }

      return NextResponse.json({
        success: true,
        settings: settings.map(s => s.toObject())
      })
    }

  } catch (error) {
    console.error('Error retrieving settings:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to retrieve settings: ' + (error instanceof Error ? error.message : 'Unknown error')
      },
      { status: 500 }
    )
  }
}

// POST - Update setting
export async function POST(request: NextRequest) {
  try {
    await connectToDatabase()

    const { key, value, updatedBy = 'admin' } = await request.json()

    if (!key || !value) {
      return NextResponse.json(
        { error: 'Key and value are required' },
        { status: 400 }
      )
    }

    if (!['STYLE_GUIDE', 'INSTRUCTIONS'].includes(key)) {
      return NextResponse.json(
        { error: 'Invalid key. Must be STYLE_GUIDE or INSTRUCTIONS' },
        { status: 400 }
      )
    }

    console.log(`🔄 Updating setting: ${key}`)
    console.log(`📝 New value length: ${value.length} characters`)

    // Update or create setting
    const setting = await Settings.findOneAndUpdate(
      { key },
      { 
        value: value.trim(),
        updatedBy,
        updatedAt: new Date()
      },
      { 
        new: true, 
        upsert: true,
        setDefaultsOnInsert: true
      }
    )

    console.log(`✅ Setting ${key} updated successfully`)

    return NextResponse.json({
      success: true,
      message: `Setting ${key} updated successfully`,
      setting: setting.toObject()
    })

  } catch (error) {
    console.error('Error updating setting:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to update setting: ' + (error instanceof Error ? error.message : 'Unknown error')
      },
      { status: 500 }
    )
  }
}
