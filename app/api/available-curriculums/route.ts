import { NextRequest, NextResponse } from 'next/server';
import connectToDatabase from '@/lib/mongodb';
import JsonDocument from '@/models/JsonDocument';
import { CurriculumType } from '@/types/curriculumTypes';

export async function GET(request: NextRequest) {
  try {
    await connectToDatabase();

    const availableCurriculums: CurriculumType[] = [];

    // Check for CCSS documents
    const ccssDoc = await JsonDocument.findOne({
      'content.common_core': CurriculumType.CCSS
    }).limit(1);

    if (ccssDoc) {
      availableCurriculums.push(CurriculumType.CCSS);
    }

    // Check for Ontario documents
    const ontarioDoc = await JsonDocument.findOne({
      'content.common_core': CurriculumType.ONTARIO
    }).limit(1);

    if (ontarioDoc) {
      availableCurriculums.push(CurriculumType.ONTARIO);
    }

    // Check for Alberta documents
    const albertaDoc = await JsonDocument.findOne({
      'content.common_core': CurriculumType.ALBERTA
    }).limit(1);

    if (albertaDoc) {
      availableCurriculums.push(CurriculumType.ALBERTA);
    }

    // Check for British Columbia documents
    const bcDoc = await JsonDocument.findOne({
      'content.common_core': CurriculumType.BRITISH_COLUMBIA
    }).limit(1);

    if (bcDoc) {
      availableCurriculums.push(CurriculumType.BRITISH_COLUMBIA);
    }

    // Check for Australia documents
    const australiaDoc = await JsonDocument.findOne({
      'content.common_core': CurriculumType.AUSTRALIA
    }).limit(1);

    if (australiaDoc) {
      availableCurriculums.push(CurriculumType.AUSTRALIA);
    }

    // Check for UK documents
    const ukDoc = await JsonDocument.findOne({
      'content.common_core': CurriculumType.UK
    }).limit(1);

    if (ukDoc) {
      availableCurriculums.push(CurriculumType.UK);
    }

    // If no specific curriculum found, default to CCSS
    if (availableCurriculums.length === 0) {
      availableCurriculums.push(CurriculumType.CCSS);
    }

    return NextResponse.json({
      curriculums: availableCurriculums
    });
  } catch (error) {
    console.error('Error fetching available curriculums:', error);
    return NextResponse.json(
      { message: 'Failed to fetch available curriculums', error: String(error) },
      { status: 500 }
    );
  }
}
