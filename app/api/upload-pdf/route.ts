import { NextRequest, NextResponse } from 'next/server'
import { getToken } from 'next-auth/jwt'
import connectToDatabase from '@/lib/mongodb'
import JsonDocument from '@/models/JsonDocument'

export async function POST(request: NextRequest) {
  try {
    console.log('📄 PDF Upload API called')

    // Check authentication and admin role
    const token = await getToken({ req: request, secret: process.env.NEXTAUTH_SECRET })

    console.log('🔐 Token check:', { hasToken: !!token, role: token?.role })

    if (!token) {
      console.log('❌ No token found')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin
    if (token.role !== 'admin') {
      console.log('❌ User is not admin:', token.role)
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    console.log('📋 Parsing form data...')
    const formData = await request.formData()
    const file = formData.get('file') as File
    const slideType = formData.get('slideType') as string // 'practice', 'lesson_guide', or 'accelerator'
    const documentId = formData.get('documentId') as string

    console.log('📊 Form data parsed:', {
      hasFile: !!file,
      fileName: file?.name,
      fileSize: file?.size,
      fileType: file?.type,
      slideType,
      documentId
    })

    if (!file) {
      console.log('❌ No file uploaded')
      return NextResponse.json({ error: 'No file uploaded' }, { status: 400 })
    }

    if (!slideType || !['practice', 'lesson_guide', 'accelerator'].includes(slideType)) {
      console.log('❌ Invalid slide type:', slideType)
      return NextResponse.json({ error: 'Invalid slide type' }, { status: 400 })
    }

    if (!documentId) {
      console.log('❌ No document ID provided')
      return NextResponse.json({ error: 'Document ID is required' }, { status: 400 })
    }

    // Validate file type
    if (file.type !== 'application/pdf') {
      console.log('❌ Invalid file type:', file.type)
      return NextResponse.json({ error: 'Only PDF files are allowed' }, { status: 400 })
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      console.log('❌ File too large:', file.size)
      return NextResponse.json({ error: 'File size must be less than 10MB' }, { status: 400 })
    }

    // Convert file to base64 for database storage
    console.log('💾 Converting file to base64...')
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    const base64Data = buffer.toString('base64')

    console.log('📊 File converted:', {
      originalSize: file.size,
      base64Size: base64Data.length
    })

    // Connect to database
    await connectToDatabase()

    // Determine which slide to update based on slide type
    const slideMapping = {
      'practice': 'Slide 26: Practice_PDF_Link',
      'lesson_guide': 'Slide 25: Lesson_Guide_PDF_Link',
      'accelerator': 'Slide 27: Accelerator_PDF_Link'
    }

    const slideKey = slideMapping[slideType as keyof typeof slideMapping]

    // Determine the field name for the PDF link
    const fieldMapping = {
      'practice': 'link_to_practice_pdf',
      'lesson_guide': 'link_to_lesson_guide_pdf',
      'accelerator': 'link_to_accelerator_pdf'
    }

    const fieldName = fieldMapping[slideType as keyof typeof fieldMapping]

    // Generate data URL for the PDF
    const dataUrl = `data:application/pdf;base64,${base64Data}`

    console.log('💾 Saving to database:', {
      documentId,
      slideKey,
      fieldName,
      dataUrlLength: dataUrl.length
    })

    // Update the document
    const updatePath = `content.${slideKey}.${fieldName}`

    const result = await JsonDocument.updateOne(
      { documentId },
      {
        $set: {
          [updatePath]: dataUrl,
          updatedAt: new Date()
        }
      }
    )

    if (result.matchedCount === 0) {
      throw new Error('Document not found')
    }

    console.log(`📄 PDF uploaded successfully:`, {
      documentId,
      slideType,
      size: file.size,
      modifiedCount: result.modifiedCount,
      dataUrlLength: dataUrl.length
    })

    return NextResponse.json({
      success: true,
      url: dataUrl,
      size: file.size,
      slideType,
      documentId,
      fieldName,
      modifiedCount: result.modifiedCount
    })

  } catch (error) {
    console.error('❌ Error uploading PDF:', error)
    console.error('❌ Error stack:', error instanceof Error ? error.stack : 'No stack trace')

    return NextResponse.json(
      {
        error: 'Failed to upload PDF',
        details: error instanceof Error ? error.message : 'Unknown error',
        stack: process.env.NODE_ENV === 'development' ? (error instanceof Error ? error.stack : null) : undefined
      },
      { status: 500 }
    )
  }
}
