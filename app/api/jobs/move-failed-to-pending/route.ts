import { NextRequest, NextResponse } from 'next/server'
import { connectWithRetry } from '@/lib/mongodb'
import Job from '@/models/Job'

// Move all failed jobs to pending status for retry
export async function POST(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    console.log(`🔄 MOVE FAILED TO PENDING: Starting to move failed jobs to pending...`)
    
    await connectWithRetry()

    // Get all failed jobs
    const failedJobs = await Job.find({ status: 'failed' })

    console.log(`📋 Found ${failedJobs.length} failed jobs to move to pending`)

    if (failedJobs.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No failed jobs found to move',
        movedCount: 0,
        duration: Date.now() - startTime
      })
    }

    // Update all failed jobs to pending
    const updateResult = await Job.updateMany(
      { status: 'failed' },
      {
        $set: {
          status: 'pending',
          errorMessage: 'Moved from failed to pending for retry',
          processedAt: null,
          processingStartedAt: null,
          updatedAt: new Date()
        },
        $unset: {
          generatedHtml: ""
        }
      }
    )

    const duration = Date.now() - startTime
    
    console.log(`✅ MOVE FAILED TO PENDING: Completed in ${duration}ms`)
    console.log(`📊 Results: ${updateResult.modifiedCount} jobs moved to pending`)

    return NextResponse.json({
      success: true,
      message: `Successfully moved ${updateResult.modifiedCount} failed jobs to pending status`,
      movedCount: updateResult.modifiedCount,
      totalFound: failedJobs.length,
      duration
    })

  } catch (error) {
    const duration = Date.now() - startTime
    console.error('❌ MOVE FAILED TO PENDING failed:', error)

    return NextResponse.json({
      success: false,
      error: 'Failed to move failed jobs to pending',
      details: error instanceof Error ? error.message : 'Unknown error',
      duration
    }, { status: 500 })
  }
}

// GET endpoint for info
export async function GET() {
  return NextResponse.json({
    message: 'Move Failed Jobs to Pending API',
    description: 'Move all failed jobs to pending status for retry',
    endpoint: 'POST /api/jobs/move-failed-to-pending',
    usage: 'Call this endpoint to move all failed jobs back to pending for retry'
  })
}
