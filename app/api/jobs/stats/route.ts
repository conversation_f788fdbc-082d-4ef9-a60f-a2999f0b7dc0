import { NextRequest, NextResponse } from 'next/server'
import connectToDatabase from '@/lib/mongodb'
import Job from '@/models/Job'

// Get job statistics without pagination
export async function GET(request: NextRequest) {
  try {
    await connectToDatabase()

    const { searchParams } = new URL(request.url)
    const documentId = searchParams.get('documentId')
    const gradeLevel = searchParams.get('gradeLevel')
    const unitNumber = searchParams.get('unitNumber')
    const lessonNumber = searchParams.get('lessonNumber')
    const slideNumber = searchParams.get('slideNumber')

    // Build query to match the same filters as jobs list
    const query: any = {}
    if (documentId) query.documentId = documentId
    if (gradeLevel) query.gradeLevel = gradeLevel
    if (unitNumber) query.unitNumber = unitNumber
    if (lessonNumber) query.lessonNumber = lessonNumber
    if (slideNumber) query.slideNumber = parseInt(slideNumber)

    console.log('Jobs Stats API: MongoDB query:', query)

    // Get job statistics using aggregation
    const stats = await Job.aggregate([
      { $match: query },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          latestCreated: { $max: '$createdAt' },
          latestProcessed: { $max: '$processedAt' }
        }
      }
    ])

    // Get failed jobs that can be retried
    const retryableFailedJobs = await Job.countDocuments({
      ...query,
      status: 'failed',
      retryCount: { $lt: 3 }
    })

    // Format statistics
    const statusCounts = stats.reduce((acc, stat) => {
      acc[stat._id] = {
        count: stat.count,
        latestCreated: stat.latestCreated,
        latestProcessed: stat.latestProcessed
      }
      return acc
    }, {} as Record<string, any>)

    const totalJobs = stats.reduce((sum, stat) => sum + stat.count, 0)

    console.log('Jobs Stats API: Statistics:', {
      pending: statusCounts.pending?.count || 0,
      processing: statusCounts.processing?.count || 0,
      success: statusCounts.success?.count || 0,
      failed: statusCounts.failed?.count || 0,
      archived: statusCounts.archived?.count || 0,
      retryableFailedJobs,
      totalJobs
    })

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      query,
      statistics: {
        pending: statusCounts.pending?.count || 0,
        processing: statusCounts.processing?.count || 0,
        success: statusCounts.success?.count || 0,
        failed: statusCounts.failed?.count || 0,
        archived: statusCounts.archived?.count || 0,
        retryableFailedJobs,
        totalJobs
      },
      details: statusCounts
    })

  } catch (error) {
    console.error('Error fetching job statistics:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch job statistics',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
