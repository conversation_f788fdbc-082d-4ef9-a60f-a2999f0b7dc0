import { NextRequest, NextResponse } from 'next/server'
import connectToDatabase from '@/lib/mongodb'
import Job from '@/models/Job'

// Retry all failed jobs - for cron or batch processing
export async function POST(request: NextRequest) {
  try {
    await connectToDatabase()

    const { limit = 10, dryRun = false } = await request.json()

    console.log(`🔄 Starting failed jobs retry (limit: ${limit}, dryRun: ${dryRun})`)

    // Find failed jobs
    const failedJobs = await Job.find({ status: 'failed' })
      .sort({ createdAt: 1 }) // Process oldest first
      .limit(limit)

    console.log(`📋 Found ${failedJobs.length} failed jobs`)

    if (failedJobs.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No failed jobs found',
        processed: 0,
        results: []
      })
    }

    if (dryRun) {
      console.log(`🧪 Dry run mode - would retry ${failedJobs.length} failed jobs`)
      return NextResponse.json({
        success: true,
        message: `Dry run: Found ${failedJobs.length} failed jobs to retry`,
        processed: 0,
        jobs: failedJobs.map(job => ({
          jobId: job.jobId,
          documentId: job.documentId,
          slideKey: job.slideKey,
          prompt: job.prompt.substring(0, 100) + '...',
          errorMessage: job.errorMessage
        }))
      })
    }

    const results = []
    let successCount = 0
    let failureCount = 0

    // Process each failed job
    for (const job of failedJobs) {
      try {
        console.log(`🚀 Retrying failed job ${successCount + failureCount + 1}/${failedJobs.length}: ${job.jobId}`)

        // Call the universal job processor
        const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/jobs/process-job`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ jobId: job.jobId }),
        })

        const data = await response.json()

        if (data.success) {
          console.log(`✅ Failed job ${job.jobId} retried successfully`)
          successCount++
          results.push({
            jobId: job.jobId,
            status: 'success',
            htmlLength: data.htmlLength,
            message: 'Retried successfully'
          })
        } else {
          console.log(`❌ Failed job ${job.jobId} retry failed: ${data.error}`)
          failureCount++
          results.push({
            jobId: job.jobId,
            status: 'failed',
            error: data.error,
            message: 'Retry failed'
          })
        }

      } catch (error) {
        console.error(`❌ Error retrying failed job ${job.jobId}:`, error)
        failureCount++
        results.push({
          jobId: job.jobId,
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error',
          message: 'Retry error'
        })
      }

      // Add a small delay between jobs to avoid overwhelming the system
      await new Promise(resolve => setTimeout(resolve, 1000))
    }

    console.log(`🏁 Failed jobs retry completed: ${successCount} success, ${failureCount} failures`)

    return NextResponse.json({
      success: true,
      message: `Retried ${failedJobs.length} failed jobs: ${successCount} success, ${failureCount} still failed`,
      processed: failedJobs.length,
      successCount,
      failureCount,
      results
    })

  } catch (error) {
    console.error('❌ Error in failed jobs retry:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to retry jobs: ' + (error instanceof Error ? error.message : 'Unknown error')
      },
      { status: 500 }
    )
  }
}

// GET endpoint for status and testing
export async function GET() {
  try {
    await connectToDatabase()

    // Get failed job statistics
    const failedJobs = await Job.find({ status: 'failed' })
    const totalFailed = failedJobs.length

    // Get recent failed jobs with error messages
    const recentFailedJobs = failedJobs.slice(0, 5).map(job => ({
      jobId: job.jobId,
      documentId: job.documentId,
      slideKey: job.slideKey,
      errorMessage: job.errorMessage,
      createdAt: job.createdAt,
      retryCount: job.retryCount
    }))

    return NextResponse.json({
      message: 'Retry Failed Jobs API',
      description: 'Retry all failed jobs in batch - for cron or manual batch processing',
      usage: {
        POST: 'Retry failed jobs',
        parameters: {
          limit: 'Maximum number of failed jobs to retry (default: 10)',
          dryRun: 'If true, only shows what would be retried (default: false)'
        }
      },
      currentStats: {
        totalFailed,
        recentFailedJobs
      },
      examples: {
        retryAll: {
          method: 'POST',
          body: { limit: 10, dryRun: false }
        },
        dryRun: {
          method: 'POST',
          body: { limit: 5, dryRun: true }
        }
      }
    })

  } catch (error) {
    console.error('Error getting failed job stats:', error)
    return NextResponse.json(
      { error: 'Failed to get failed job statistics' },
      { status: 500 }
    )
  }
}
