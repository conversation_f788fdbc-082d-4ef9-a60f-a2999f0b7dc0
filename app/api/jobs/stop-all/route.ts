import { NextRequest, NextResponse } from 'next/server'
import { connectWithRetry } from '@/lib/mongodb'
import Job from '@/models/Job'

export async function POST(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    console.log('🛑 STOP ALL: Resetting all processing jobs to pending...')
    
    await connectWithRetry()

    // Reset all processing jobs to pending
    const result = await Job.updateMany(
      { status: 'processing' },
      { 
        $set: { 
          status: 'pending',
          errorMessage: 'Reset by stop-all command',
          processedAt: new Date()
        }
      }
    )

    const duration = Date.now() - startTime
    
    console.log(`✅ STOP ALL: Reset ${result.modifiedCount} processing jobs to pending in ${duration}ms`)

    return NextResponse.json({
      success: true,
      message: `Successfully reset ${result.modifiedCount} processing jobs to pending`,
      modifiedCount: result.modifiedCount,
      duration
    })

  } catch (error) {
    const duration = Date.now() - startTime
    console.error('❌ STOP ALL failed:', error)

    return NextResponse.json({
      success: false,
      error: 'Failed to stop all jobs',
      details: error instanceof Error ? error.message : 'Unknown error',
      duration
    }, { status: 500 })
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Stop All Jobs API',
    description: 'Reset all processing jobs to pending status',
    endpoint: 'POST /api/jobs/stop-all',
    usage: 'Call this endpoint to immediately stop all currently processing jobs'
  })
}
