import { NextRequest, NextResponse } from 'next/server'
import { connectWithRetry } from '@/lib/mongodb'
import Job from '@/models/Job'

// Delete all archived jobs
export async function POST(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    console.log(`🗑️ DELETE ARCHIVED JOBS: Starting...`)
    
    await connectWithRetry()

    // Find all archived jobs
    const archivedJobs = await Job.find({ status: 'archived' })
    console.log(`📋 Found ${archivedJobs.length} archived jobs to delete`)

    if (archivedJobs.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No archived jobs found to delete',
        totalFound: 0,
        deletedCount: 0,
        duration: Date.now() - startTime
      })
    }

    // Log some examples of what will be deleted
    console.log(`📄 Examples of archived jobs to delete:`)
    archivedJobs.slice(0, 5).forEach(job => {
      console.log(`   - ${job.jobId} (${job.documentId}) - ${job.errorMessage}`)
    })

    // Delete all archived jobs
    const deleteResult = await Job.deleteMany({ status: 'archived' })

    const duration = Date.now() - startTime
    
    console.log(`✅ DELETE ARCHIVED COMPLETE: Deleted ${deleteResult.deletedCount} jobs in ${duration}ms`)

    return NextResponse.json({
      success: true,
      message: `Deleted ${deleteResult.deletedCount} archived jobs`,
      totalFound: archivedJobs.length,
      deletedCount: deleteResult.deletedCount,
      duration
    })

  } catch (error) {
    const duration = Date.now() - startTime
    console.error('❌ DELETE ARCHIVED JOBS failed:', error)

    return NextResponse.json({
      success: false,
      error: 'Failed to delete archived jobs',
      details: error instanceof Error ? error.message : 'Unknown error',
      duration
    }, { status: 500 })
  }
}

// GET endpoint for info
export async function GET() {
  try {
    await connectWithRetry()
    
    // Get count of archived jobs
    const archivedCount = await Job.countDocuments({ status: 'archived' })
    
    // Get some examples
    const examples = await Job.find({ status: 'archived' }, {
      jobId: 1,
      documentId: 1,
      errorMessage: 1,
      createdAt: 1
    }).limit(5).sort({ createdAt: -1 })

    return NextResponse.json({
      message: 'Delete Archived Jobs API',
      description: 'Delete all jobs marked as archived (for deleted lessons)',
      endpoint: 'POST /api/jobs/delete-archived',
      currentArchivedJobs: archivedCount,
      examples: examples.map(job => ({
        jobId: job.jobId,
        documentId: job.documentId,
        reason: job.errorMessage,
        createdAt: job.createdAt
      }))
    })
  } catch (error) {
    return NextResponse.json({
      message: 'Delete Archived Jobs API',
      description: 'Delete all jobs marked as archived (for deleted lessons)',
      endpoint: 'POST /api/jobs/delete-archived',
      error: 'Failed to get archived jobs info'
    })
  }
}
