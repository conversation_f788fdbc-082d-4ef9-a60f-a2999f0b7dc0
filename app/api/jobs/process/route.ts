import { NextRequest, NextResponse } from 'next/server'
import connectToDatabase from '@/lib/mongodb'
import Job from '@/models/Job'

// POST - Process a specific job
export async function POST(request: NextRequest) {
  try {
    await connectToDatabase()

    const { jobId } = await request.json()

    if (!jobId) {
      return NextResponse.json(
        { error: 'Job ID is required' },
        { status: 400 }
      )
    }

    // Find the job
    const job = await Job.findOne({ jobId })
    if (!job) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      )
    }

    // Check if job is already processed
    if (job.status === 'success') {
      return NextResponse.json({
        success: true,
        message: 'Job already completed',
        job: job.toObject()
      })
    }

    // Update status to processing
    await job.updateStatus('processing')

    try {
      console.log(`🤖 Starting HTML generation for job: ${job.jobId}`)

      // Generate HTML using existing generate-html API
      const generatedHtml = await generateHtmlUsingAPI(job)
      console.log(`✅ HTML generated successfully, length: ${generatedHtml?.length}`)

      console.log(`💾 Starting HTML save to lesson document...`)
      // Save HTML to lesson document (replaces html_css_description_of_image)
      await saveHtmlToLesson(job, generatedHtml)
      console.log(`✅ HTML saved to lesson document successfully`)

      console.log(`📝 Updating job status to success...`)
      // Update job status to success and save HTML for preview
      await job.updateStatus('success', {
        modelVersion: 'gemini-2.5-flash-preview-05-20',
        processedAt: new Date(),
        generatedHtml: generatedHtml // Save HTML in job for preview
      })
      console.log(`✅ Job status updated to success`)

      return NextResponse.json({
        success: true,
        message: 'Job processed successfully',
        job: job.toObject()
      })

    } catch (error) {
      console.error('Error processing job:', error)

      // Update job status to failed
      await job.updateStatus('failed', {
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        retryCount: job.retryCount + 1
      })

      return NextResponse.json(
        { error: 'Failed to process job: ' + (error instanceof Error ? error.message : 'Unknown error') },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Error in job processing:', error)
    return NextResponse.json(
      { error: 'Failed to process job' },
      { status: 500 }
    )
  }
}

// Generate HTML using existing generate-html API
async function generateHtmlUsingAPI(job: any): Promise<string> {
  try {
    console.log(`🔗 Calling generate-html API for job: ${job.jobId}`)
    console.log(`📝 Prompt: ${job.prompt.substring(0, 100)}...`)

    // Call the existing generate-html API
    const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/generate-html`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt: job.prompt,
        slideNumber: job.slideNumber,
        unitNumber: job.unitNumber,
        lessonNumber: job.lessonNumber,
        gradeLevel: job.gradeLevel,
        curriculum: job.curriculum || 'CCSS',
        lang: job.lang || 'en'
      }),
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(`Generate-html API failed: ${errorData.error || response.statusText}`)
    }

    const data = await response.json()

    if (!data.success || !data.html) {
      throw new Error('Generate-html API returned invalid response')
    }

    console.log(`✅ Generate-html API returned HTML (${data.html?.length} characters)`)
    console.log(`🎯 Model used: ${data.metadata?.modelVersion || 'unknown'}`)

    return data.html

  } catch (error) {
    console.error('❌ Error calling generate-html API:', error)
    throw new Error(`Failed to generate HTML: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Save HTML to lesson document
async function saveHtmlToLesson(job: any, html: string): Promise<void> {
  try {
    const connectToDatabase = (await import('@/lib/mongodb')).default
    await connectToDatabase()

    const { MongoClient } = await import('mongodb')
    const client = new MongoClient(process.env.MONGODB_URI!)

    try {
      await client.connect()
      const db = client.db()
      const collection = db.collection('jsondocuments')

      console.log(`🔍 Saving HTML to document: ${job.documentId}, slide: ${job.slideKey}`)
      console.log(`📄 HTML length: ${html?.length} characters`)
      console.log(`🎯 HTML preview: ${html?.substring(0, 100)}...`)

      // Find the lesson document
      console.log(`🔎 Looking for document with documentId: ${job.documentId}`)
      const lesson = await collection.findOne({ documentId: job.documentId })

      if (!lesson) {
        console.log(`❌ Document not found with documentId: ${job.documentId}`)
        throw new Error(`Lesson document not found: ${job.documentId}`)
      }

      console.log(`✅ Found lesson document`)
      console.log(`📝 Document has content:`, lesson.content ? 'Yes' : 'No')

      if (lesson.content) {
        console.log(`📝 Document content slides:`, Object.keys(lesson.content).filter(key => key.startsWith('Slide')))
      }

      // Check if the slide exists in the content
      if (!lesson.content || !lesson.content[job.slideKey]) {
        console.log(`❌ Slide not found: ${job.slideKey}`)
        console.log(`📝 Available slides:`, lesson.content ? Object.keys(lesson.content) : 'No content')
        throw new Error(`Slide not found: ${job.slideKey}`)
      }

      console.log(`✅ Found slide: ${job.slideKey}`)

      // Update the specific slide with HTML (replace html_css_description_of_image)
      // NOTE: JsonDocuments store slides under "content" field
      const updatePath = `content.${job.slideKey}.html_css_description_of_image`
      const metadataPath1 = `content.${job.slideKey}.html_generated_at`
      const metadataPath2 = `content.${job.slideKey}.html_job_id`

      console.log(`🎯 Update paths (corrected for JsonDocument structure):`)
      console.log(`   - ${updatePath}`)
      console.log(`   - ${metadataPath1}`)
      console.log(`   - ${metadataPath2}`)

      const updateQuery = { documentId: job.documentId }
      const updateData = {
        $set: {
          [updatePath]: html,
          [metadataPath1]: new Date(),
          [metadataPath2]: job.jobId
        }
      }

      console.log(`🔄 Executing update with query:`, updateQuery)
      const updateResult = await collection.updateOne(updateQuery, updateData)

      console.log(`📊 Update result:`, {
        acknowledged: updateResult.acknowledged,
        modifiedCount: updateResult.modifiedCount,
        matchedCount: updateResult.matchedCount
      })

      if (updateResult.matchedCount === 0) {
        throw new Error(`Document not found for update: ${job.documentId}`)
      }

      if (updateResult.modifiedCount === 0) {
        console.log(`⚠️ No documents were modified. This might mean the data is the same.`)
      } else {
        console.log(`✅ Document successfully modified!`)
      }

      console.log(`✅ HTML successfully saved to ${job.documentId}.content.${job.slideKey}.html_css_description_of_image`)

      // Verify the save by reading back the document
      const verifyDoc = await collection.findOne({ documentId: job.documentId })
      const savedHtml = verifyDoc?.content?.[job.slideKey]?.html_css_description_of_image

      if (savedHtml) {
        console.log(`🔍 Verification successful - HTML saved (${savedHtml.length} characters)`)
        console.log(`📄 First 100 chars: ${savedHtml.substring(0, 100)}...`)
      } else {
        console.log(`❌ Verification failed - HTML not found after save`)
        // throw new Error('HTML verification failed after save')
      }

    } finally {
      await client.close()
    }

  } catch (error) {
    console.error('❌ Error saving HTML to lesson:', error)
    throw error
  }
}
