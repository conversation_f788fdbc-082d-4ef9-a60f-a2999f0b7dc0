import { NextRequest, NextResponse } from 'next/server'
import connectToDatabase from '@/lib/mongodb'
import Job from '@/models/Job'

// Check if document exists before processing job
async function checkDocumentExists(job: any): Promise<{ exists: boolean; documentId?: string; reason?: string }> {
  try {
    const JsonDocument = (await import('@/models/JsonDocument')).default

    console.log(`🔍 Checking document existence for job: ${job.documentId}`)
    console.log(`🔍 Job metadata: unit=${job.unitNumber}, lesson=${job.lessonNumber}, grade=${job.gradeLevel}`)

    // Try multiple approaches to find the document
    let lesson = null
    const searchAttempts = []

    // Attempt 1: Exact documentId match
    searchAttempts.push(`exact documentId: ${job.documentId}`)
    lesson = await JsonDocument.findOne({ documentId: job.documentId })

    // Attempt 2: Search by metadata fields (most reliable)
    if (!lesson) {
      searchAttempts.push(`metadata: unit=${job.unitNumber}, lesson=${job.lessonNumber}, grade=${job.gradeLevel}`)
      lesson = await JsonDocument.findOne({
        unit_number: job.unitNumber,
        lesson_number: job.lessonNumber,
        grade_level: job.gradeLevel,
        lang: 'en'
      })
    }

    // Attempt 3: Constructed documentId
    if (!lesson) {
      const constructedDocumentId = `${job.unitNumber}-${job.lessonNumber}-${job.gradeLevel}`
      searchAttempts.push(`constructed documentId: ${constructedDocumentId}`)
      lesson = await JsonDocument.findOne({ documentId: constructedDocumentId })
    }

    // Attempt 4: Try with different lang values
    if (!lesson) {
      searchAttempts.push(`metadata with any lang`)
      lesson = await JsonDocument.findOne({
        unit_number: job.unitNumber,
        lesson_number: job.lessonNumber,
        grade_level: job.gradeLevel
      })
    }

    if (lesson) {
      console.log(`✅ Document found: ${lesson.documentId} (lang: ${lesson.lang})`)
      return {
        exists: true,
        documentId: lesson.documentId
      }
    }

    // Document not found - provide detailed reason
    console.log(`❌ Document not found after ${searchAttempts.length} attempts:`)
    searchAttempts.forEach((attempt, i) => {
      console.log(`   ${i + 1}. ${attempt}`)
    })

    // Get available documents for comparison
    const availableDocs = await JsonDocument.find({}, {
      documentId: 1,
      lang: 1,
      unit_number: 1,
      lesson_number: 1,
      grade_level: 1
    }).limit(5)

    const availableList = availableDocs.map(doc =>
      `${doc.documentId} (${doc.unit_number}-${doc.lesson_number}-${doc.grade_level}, lang: ${doc.lang})`
    ).join(', ')

    return {
      exists: false,
      reason: `Lesson was likely deleted. Searched: ${searchAttempts.join(', ')}. Available: ${availableList}`
    }

  } catch (error) {
    console.error('❌ Error checking document existence:', error)
    return {
      exists: false,
      reason: `Error checking document: ${error instanceof Error ? error.message : 'Unknown error'}`
    }
  }
}

// Universal job processor - can be used from frontend and cron
export async function POST(request: NextRequest) {
  try {
    await connectToDatabase()

    const { jobId } = await request.json()

    if (!jobId) {
      return NextResponse.json(
        { error: 'Job ID is required' },
        { status: 400 }
      )
    }

    console.log(`🔄 Processing job: ${jobId}`)

    // Find the job
    const job = await Job.findOne({ jobId })
    if (!job) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      )
    }

    // Allow regeneration of successful jobs
    if (job.status === 'success') {
      console.log(`🔄 Job ${jobId} already completed, but regenerating as requested`)
    }

    // Check if job is currently processing
    if (job.status === 'processing') {
      console.log(`⏳ Job ${jobId} is already being processed`)
      return NextResponse.json({
        success: false,
        message: 'Job is already being processed',
        job: job.toObject()
      })
    }

    // STEP 1: Check if document exists before processing
    console.log(`🔍 PRE-CHECK: Verifying document exists before processing...`)
    const documentCheck = await checkDocumentExists(job)

    if (!documentCheck.exists) {
      console.log(`❌ Document not found: ${job.documentId} - archiving job`)

      // Archive the job instead of failing it
      await job.updateStatus('archived', {
        errorMessage: `Document not found: ${job.documentId}. ${documentCheck.reason}`,
        processedAt: new Date()
      })

      return NextResponse.json({
        success: false,
        error: `Document not found: ${job.documentId}`,
        archived: true,
        reason: documentCheck.reason,
        job: job.toObject()
      }, { status: 404 })
    }

    console.log(`✅ Document verification passed: ${documentCheck.documentId}`)

    // Update status to processing (even if it was already successful)
    const previousStatus = job.status
    await job.updateStatus('processing')
    console.log(`🚀 Job ${jobId} status updated from '${previousStatus}' to 'processing'`)

    try {
      console.log(`🤖 Starting HTML generation for job: ${job.jobId}`)

      // Generate HTML using existing generate-html API
      const generatedHtml = await generateHtmlUsingAPI(job)

      // Validate HTML before saving
      if (!generatedHtml || typeof generatedHtml !== 'string' || generatedHtml.trim().length === 0) {
        throw new Error('Generated HTML is empty or invalid')
      }

      // Check if HTML looks valid (contains basic HTML structure)
      if (!generatedHtml.includes('<html') && !generatedHtml.includes('<!DOCTYPE')) {
        throw new Error('Generated HTML does not contain valid HTML structure')
      }

      console.log(`✅ HTML generated and validated successfully, length: ${generatedHtml.length}`)

      // Additional HTML validation before saving
      console.log(`🔍 Performing additional HTML validation before save...`)
      if (!generatedHtml || typeof generatedHtml !== 'string' || generatedHtml.trim().length === 0) {
        throw new Error('Generated HTML is empty or invalid')
      }

      if (!generatedHtml.includes('<html') && !generatedHtml.includes('<!DOCTYPE')) {
        throw new Error('Generated HTML does not contain proper HTML structure')
      }

      if (generatedHtml.length < 100) {
        throw new Error(`Generated HTML is too short (${generatedHtml.length} chars) - likely incomplete`)
      }

      console.log(`✅ HTML validation passed - proceeding with save`)

      console.log(`💾 Starting HTML save to lesson document...`)
      // Save HTML to lesson document (replaces html_css_description_of_image)
      await saveHtmlToLesson(job, generatedHtml)
      console.log(`✅ HTML saved to lesson document successfully`)

      console.log(`📝 Updating job status to success...`)
      // Update job status to success and save HTML for preview
      await job.updateStatus('success', {
        modelVersion: 'gemini-2.5-flash-preview-05-20',
        processedAt: new Date(),
        generatedHtml: generatedHtml // Save HTML in job for preview
      })
      console.log(`✅ Job ${jobId} status updated to success`)

      const isRegeneration = previousStatus === 'success'
      return NextResponse.json({
        success: true,
        message: isRegeneration ? 'Job regenerated successfully' : 'Job processed successfully',
        job: job.toObject(),
        htmlLength: generatedHtml?.length,
        isRegeneration
      })

    } catch (error) {
      console.error(`❌ Error processing job ${jobId}:`, error)

      // Create detailed error message
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      const isRateLimitError = errorMessage.includes('429') || errorMessage.includes('quota') || errorMessage.includes('Too Many Requests')

      console.log(`📝 Job ${jobId} failed with error: ${errorMessage}`)
      console.log(`🔄 Rate limit error: ${isRateLimitError}`)

      // Update job status to failed
      await job.updateStatus('failed', {
        errorMessage: `${errorMessage}${isRateLimitError ? ' (Rate Limit)' : ''}`,
        retryCount: job.retryCount + 1,
        processedAt: new Date(),
        generatedHtml: null // Clear any partial HTML
      })

      return NextResponse.json(
        {
          success: false,
          error: 'Failed to process job: ' + (error instanceof Error ? error.message : 'Unknown error'),
          job: job.toObject()
        },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('❌ Error in job processing:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to process job: ' + (error instanceof Error ? error.message : 'Unknown error')
      },
      { status: 500 }
    )
  }
}

// Generate HTML using existing generate-html API
async function generateHtmlUsingAPI(job: any): Promise<any> {
  try {
    console.log(`🔗 Calling generate-html API for job: ${job.jobId}`)
    console.log(`📝 Prompt: ${job.prompt.substring(0, 100)}...`)

    // Call the existing generate-html API
    const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/generate-html`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt: job.prompt
      }),
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(`Generate-html API failed: ${errorData.error || response.statusText}`)
    }

    const data = await response.json()

    if (!data.success || !data.html) {
      throw new Error('Generate-html API returned invalid response')
    }

    console.log(`✅ Generate-html API returned HTML (${data.html?.length} characters)`)
    console.log(`🎯 Model used: ${data.metadata?.modelVersion || 'unknown'}`)

    return data.html

  } catch (error) {
    console.error('❌ Error calling generate-html API:', error)
    throw new Error(`Failed to generate HTML: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Save HTML to lesson document
async function saveHtmlToLesson(job: any, html: string): Promise<void> {
  // Validate input
  if (!html || typeof html !== 'string' || html.trim().length === 0) {
    throw new Error('Cannot save empty or invalid HTML to lesson document')
  }

  try {
    const connectToDatabase = (await import('@/lib/mongodb')).default
    await connectToDatabase()

    console.log(`🔍 Saving HTML to document: ${job.documentId}, slide: ${job.slideKey}`)
    console.log(`📄 HTML length: ${html?.length} characters`)
    console.log(`🎯 HTML preview: ${html?.substring(0, 100)}...`)

      // Find the lesson document using JsonDocument model (simpler approach)
      console.log(`🔍 Searching for lesson document: ${job.documentId}`)

      // Use JsonDocument model instead of raw MongoDB
      const JsonDocument = (await import('@/models/JsonDocument')).default

      // First try to find the document as-is
      let lesson = await JsonDocument.findOne({ documentId: job.documentId })

      // If not found, try alternative approaches
      if (!lesson) {
        console.log(`🔍 Document not found with exact documentId, trying alternatives...`)

        // Try to construct documentId from job metadata
        const constructedDocumentId = `${job.unitNumber}-${job.lessonNumber}-${job.gradeLevel}`
        console.log(`🔍 Trying constructed documentId: ${constructedDocumentId}`)
        lesson = await JsonDocument.findOne({ documentId: constructedDocumentId })

        // If still not found, try with lang filter
        if (!lesson) {
          console.log(`🔍 Trying with lang: 'en'`)
          lesson = await JsonDocument.findOne({ documentId: constructedDocumentId, lang: 'en' })
        }
      }

      if (!lesson) {
        console.log(`❌ Lesson document not found: ${job.documentId}`)
        console.log(`🔍 Available documents:`)
        const availableDocs = await JsonDocument.find({}, { documentId: 1, lang: 1, unit_number: 1, lesson_number: 1, grade_level: 1 }).limit(10)
        availableDocs.forEach(doc => {
          console.log(`   - ${doc.documentId} (lang: ${doc.lang}) - ${doc.unit_number}-${doc.lesson_number}-${doc.grade_level}`)
        })
        throw new Error(`Lesson document not found: ${job.documentId}`)
      }

      console.log(`✅ Found lesson document`)
      console.log(`📝 Document has content:`, lesson.content ? 'Yes' : 'No')

      if (lesson.content) {
        console.log(`📝 Document content slides:`, Object.keys(lesson.content).filter(key => key.startsWith('Slide')))
      }

      // Check if the slide exists in the content
      if (!lesson.content || !lesson.content[job.slideKey]) {
        console.log(`❌ Slide not found: ${job.slideKey}`)
        console.log(`📝 Available slides:`, lesson.content ? Object.keys(lesson.content) : 'No content')
        throw new Error(`Slide not found: ${job.slideKey}`)
      }

      console.log(`✅ Found slide: ${job.slideKey}`)

      // Update the specific slide with HTML (replace html_css_description_of_image)
      // NOTE: JsonDocuments store slides under "content" field
      const updatePath = `content.${job.slideKey}.html_css_description_of_image`
      const metadataPath1 = `content.${job.slideKey}.html_generated_at`
      const metadataPath2 = `content.${job.slideKey}.html_job_id`

      console.log(`🎯 Update paths (corrected for JsonDocument structure):`)
      console.log(`   - ${updatePath}`)
      console.log(`   - ${metadataPath1}`)
      console.log(`   - ${metadataPath2}`)

      // Update using JsonDocument model (simpler and more reliable)
      const updateData = {
        [`content.${job.slideKey}.html_css_description_of_image`]: html,
        [`content.${job.slideKey}.html_generated_at`]: new Date(),
        [`content.${job.slideKey}.html_job_id`]: job.jobId
      }

      console.log(`🔄 Updating document using JsonDocument.findByIdAndUpdate`)
      const updateResult = await JsonDocument.findByIdAndUpdate(
        lesson._id,
        { $set: updateData },
        { new: true }
      )

      if (!updateResult) {
        throw new Error(`Document update failed: ${job.documentId}`)
      }

      console.log(`✅ Document successfully updated using JsonDocument model`)
      console.log(`✅ HTML successfully saved to ${job.documentId}.content.${job.slideKey}.html_css_description_of_image`)

      // Verify the save by checking the updated document
      const savedHtml = updateResult.content?.[job.slideKey]?.html_css_description_of_image

      if (savedHtml) {
        console.log(`🔍 Verification successful - HTML saved (${savedHtml.length} characters)`)
        console.log(`📄 First 100 chars: ${savedHtml.substring(0, 100)}...`)

        // Additional verification - check if saved HTML matches what we tried to save
        if (savedHtml !== html) {
          console.log(`⚠️ Warning: Saved HTML differs from original HTML`)
          console.log(`Original length: ${html.length}, Saved length: ${savedHtml.length}`)
        } else {
          console.log(`✅ Perfect match - HTML saved exactly as generated`)
        }
      } else {
        console.log(`❌ Verification failed - HTML not found after save`)
        throw new Error('HTML verification failed after save - HTML not found in document')
      }

  } catch (error) {
    console.error('❌ Error saving HTML to lesson:', error)
    throw error
  }
}

// GET endpoint for testing
export async function GET() {
  return NextResponse.json({
    message: 'Universal Job Processor API',
    description: 'Process individual jobs - can be used from frontend or cron',
    usage: 'POST with { jobId }',
    example: {
      jobId: '1-15-Grade 5-slide-1-1748438837236'
    }
  })
}
