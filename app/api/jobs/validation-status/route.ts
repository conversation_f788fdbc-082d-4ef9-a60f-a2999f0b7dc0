import { NextRequest, NextResponse } from 'next/server'
import { connectWithRetry } from '@/lib/mongodb'
import Job from '@/models/Job'

// Get validation status and progress
export async function GET(request: NextRequest) {
  try {
    await connectWithRetry()

    // Get current job statistics
    const stats = await Job.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ])

    const statusCounts = {
      pending: 0,
      processing: 0,
      success: 0,
      failed: 0
    }

    stats.forEach(stat => {
      if (stat._id in statusCounts) {
        statusCounts[stat._id as keyof typeof statusCounts] = stat.count
      }
    })

    // Get recent validation activity (jobs moved to pending in last hour)
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)
    const recentValidationActivity = await Job.countDocuments({
      status: 'pending',
      errorMessage: { $regex: /validation failed/i },
      updatedAt: { $gte: oneHourAgo }
    })

    // Get jobs with validation error messages
    const validationErrors = await Job.find({
      errorMessage: { $regex: /validation failed/i }
    }).limit(10).sort({ updatedAt: -1 })

    return NextResponse.json({
      success: true,
      statusCounts,
      recentValidationActivity,
      validationErrors: validationErrors.map(job => ({
        jobId: job.jobId,
        documentId: job.documentId,
        status: job.status,
        errorMessage: job.errorMessage,
        updatedAt: job.updatedAt
      })),
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ Validation status check failed:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to get validation status',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
