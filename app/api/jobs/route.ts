import { NextRequest, NextResponse } from 'next/server'
import connectToDatabase from '@/lib/mongodb'
import Job from '@/models/Job'

// GET - Fetch all jobs or jobs for specific document
export async function GET(request: NextRequest) {
  try {
    console.log('Jobs API: Connecting to database...')
    await connectToDatabase()
    console.log('Jobs API: Connected to database')

    const { searchParams } = new URL(request.url)
    const documentId = searchParams.get('documentId')
    const status = searchParams.get('status')
    const gradeLevel = searchParams.get('gradeLevel')
    const unitNumber = searchParams.get('unitNumber')
    const lessonNumber = searchParams.get('lessonNumber')
    const slideNumber = searchParams.get('slideNumber')
    const limit = parseInt(searchParams.get('limit') || '50')
    const page = parseInt(searchParams.get('page') || '1')

    console.log('Jobs API: Query params:', {
      documentId, status, gradeLevel, unitNumber, lessonNumber, slideNumber, limit, page
    })

    // Build query
    const query: any = {}
    if (documentId) query.documentId = documentId
    if (status) query.status = status
    if (gradeLevel) query.gradeLevel = gradeLevel
    if (unitNumber) query.unitNumber = unitNumber
    if (lessonNumber) query.lessonNumber = lessonNumber
    if (slideNumber) query.slideNumber = parseInt(slideNumber)

    console.log('Jobs API: MongoDB query:', query)

    // Execute query with pagination
    const skip = (page - 1) * limit
    const jobs = await Job.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean()

    console.log('Jobs API: Found jobs:', jobs.length)

    const totalJobs = await Job.countDocuments(query)
    const totalPages = Math.ceil(totalJobs / limit)

    console.log('Jobs API: Total jobs in DB:', totalJobs)

    return NextResponse.json({
      success: true,
      jobs,
      pagination: {
        page,
        limit,
        totalJobs,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    })

  } catch (error) {
    console.error('Error fetching jobs:', error)
    return NextResponse.json(
      { error: 'Failed to fetch jobs' },
      { status: 500 }
    )
  }
}

// POST - Create new job
export async function POST(request: NextRequest) {
  try {
    await connectToDatabase()

    const jobData = await request.json()

    // Validate required fields
    const requiredFields = ['documentId', 'unitNumber', 'lessonNumber', 'gradeLevel', 'slideNumber', 'slideKey', 'slideType']
    for (const field of requiredFields) {
      if (!jobData[field]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        )
      }
    }

    // Generate job ID
    const jobId = Job.generateJobId(jobData.documentId, jobData.slideNumber)

    // Create job
    const job = new Job({
      ...jobData,
      jobId
    })

    await job.save()

    return NextResponse.json({
      success: true,
      job: job.toObject()
    })

  } catch (error) {
    console.error('Error creating job:', error)
    return NextResponse.json(
      { error: 'Failed to create job' },
      { status: 500 }
    )
  }
}

// PUT - Update job status
export async function PUT(request: NextRequest) {
  try {
    await connectToDatabase()

    const { jobId, status, generatedHtml, errorMessage, modelVersion } = await request.json()

    if (!jobId) {
      return NextResponse.json(
        { error: 'Job ID is required' },
        { status: 400 }
      )
    }

    const job = await Job.findOne({ jobId })
    if (!job) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      )
    }

    // Update job
    await job.updateStatus(status, {
      generatedHtml,
      errorMessage,
      modelVersion
    })

    return NextResponse.json({
      success: true,
      job: job.toObject()
    })

  } catch (error) {
    console.error('Error updating job:', error)
    return NextResponse.json(
      { error: 'Failed to update job' },
      { status: 500 }
    )
  }
}

// DELETE - Delete job
export async function DELETE(request: NextRequest) {
  try {
    await connectToDatabase()

    const { searchParams } = new URL(request.url)
    const jobId = searchParams.get('jobId')

    if (!jobId) {
      return NextResponse.json(
        { error: 'Job ID is required' },
        { status: 400 }
      )
    }

    const result = await Job.deleteOne({ jobId })

    if (result.deletedCount === 0) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Job deleted successfully'
    })

  } catch (error) {
    console.error('Error deleting job:', error)
    return NextResponse.json(
      { error: 'Failed to delete job' },
      { status: 500 }
    )
  }
}
