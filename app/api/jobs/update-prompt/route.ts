import { NextRequest, NextResponse } from 'next/server'
import connectToDatabase from '@/lib/mongodb'
import Job from '@/models/Job'

// Update job prompt
export async function POST(request: NextRequest) {
  try {
    await connectToDatabase()

    const { jobId, newPrompt } = await request.json()

    if (!jobId || !newPrompt) {
      return NextResponse.json(
        { error: 'Job ID and new prompt are required' },
        { status: 400 }
      )
    }

    console.log(`🔄 Updating prompt for job: ${jobId}`)
    console.log(`📝 New prompt: ${newPrompt.substring(0, 100)}...`)

    // Find the job
    const job = await Job.findOne({ jobId })
    if (!job) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      )
    }

    // Store the original prompt if not already stored
    if (!job.originalPrompt) {
      job.originalPrompt = job.prompt
    }

    // Update the prompt
    job.prompt = newPrompt.trim()
    job.updatedAt = new Date()

    // If the job was successful, reset it to pending so it can be reprocessed with new prompt
    if (job.status === 'success') {
      job.status = 'pending'
      job.generatedHtml = null
      job.processedAt = null
      job.errorMessage = null
      console.log(`🔄 Job status reset to pending for reprocessing`)
    }

    await job.save()

    console.log(`✅ Job prompt updated successfully`)

    return NextResponse.json({
      success: true,
      message: 'Job prompt updated successfully',
      job: job.toObject()
    })

  } catch (error) {
    console.error('❌ Error updating job prompt:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to update job prompt: ' + (error instanceof Error ? error.message : 'Unknown error')
      },
      { status: 500 }
    )
  }
}

// GET endpoint for testing
export async function GET() {
  return NextResponse.json({
    message: 'Update Job Prompt API',
    description: 'Update the prompt for a specific job',
    usage: 'POST with { jobId, newPrompt }',
    example: {
      jobId: '1-15-Grade 5-slide-1-1748438837236',
      newPrompt: 'New description for HTML generation'
    }
  })
}
