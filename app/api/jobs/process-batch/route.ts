import { NextRequest, NextResponse } from 'next/server'
import { connectWithRetry } from '@/lib/mongodb'
import Job from '@/models/Job'
import JsonDocument from '@/models/JsonDocument'

// Batch job processing using the new parallel API
export async function POST(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    const body = await request.json()
    const { batchSize = 5 } = body // Process up to 5 jobs at once for faster processing

    // Connect to database with retry logic
    await connectWithRetry()

    console.log('🚀 Starting batch job processing...')

    // Find pending jobs first (priority), then failed jobs with retry count < maxRetries
    const pendingJobs = await Job.find({ status: 'pending' })
      .sort({ createdAt: 1 })
      .limit(batchSize)

    let jobsToProcess = pendingJobs

    // If we don't have enough pending jobs, add some failed jobs
    if (pendingJobs.length < batchSize) {
      const remainingSlots = batchSize - pendingJobs.length
      const failedJobs = await Job.find({ 
        status: 'failed',
        retryCount: { $lt: 3 }
      })
      .sort({ createdAt: 1 })
      .limit(remainingSlots)

      jobsToProcess = [...pendingJobs, ...failedJobs]
    }

    if (jobsToProcess.length === 0) {
      console.log('📭 No jobs to process')
      return NextResponse.json({
        success: true,
        message: 'No jobs to process',
        processed: 0,
        duration: Date.now() - startTime
      })
    }

    console.log(`📋 Found ${jobsToProcess.length} jobs to process (${pendingJobs.length} pending, ${jobsToProcess.length - pendingJobs.length} failed)`)

    // Mark jobs as processing
    const jobIds = jobsToProcess.map(job => job._id)
    await Job.updateMany(
      { _id: { $in: jobIds } },
      {
        $set: {
          status: 'processing',
          processingStartedAt: new Date(),
          processedAt: null
        },
        $inc: { retryCount: 1 }
      }
    )

    // Prepare requests for parallel processing
    const requests = jobsToProcess.map(job => ({
      id: job.jobId,
      prompt: job.prompt,
      slideNumber: job.slideNumber,
      unitNumber: job.unitNumber,
      lessonNumber: job.lessonNumber,
      gradeLevel: job.gradeLevel,
      curriculum: job.curriculum || 'CCSS',
      lang: job.lang || 'en'
    }))

    console.log('🔄 Calling parallel HTML generation API...')

    // Call the parallel processing API
    const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/generate-html-parallel`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ requests }),
    })

    if (!response.ok) {
      throw new Error(`Parallel API failed: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()

    if (!data.success) {
      throw new Error(`Parallel API returned error: ${data.error}`)
    }

    console.log(`📊 Parallel processing completed: ${data.summary.successful}/${data.summary.totalRequests} successful`)

    // Process successful results
    const successfulJobs: any[] = []
    const failedJobs: any[] = []

    // Update successful jobs
    for (const result of data.results) {
      const job = jobsToProcess.find(j => j.jobId === result.id)
      if (job && result.html) {
        try {
          // Save HTML to lesson document
          await saveHtmlToLesson(job, result.html)
          
          // Update job status
          await Job.findByIdAndUpdate(job._id, {
            $set: {
              status: 'success',
              generatedHtml: result.html,
              modelVersion: result.modelVersion,
              processedAt: new Date(),
              processingStartedAt: null,
              errorMessage: null
            }
          })

          successfulJobs.push({
            jobId: job.jobId,
            duration: result.duration,
            attempts: result.attempts,
            htmlLength: result.html.length
          })

          console.log(`✅ Job ${job.jobId} completed successfully`)
        } catch (saveError) {
          console.error(`❌ Failed to save HTML for job ${job.jobId}:`, saveError)
          failedJobs.push({
            jobId: job.jobId,
            error: `Failed to save HTML: ${saveError instanceof Error ? saveError.message : 'Unknown error'}`
          })
          
          // Update job as failed
          await Job.findByIdAndUpdate(job._id, {
            $set: {
              status: 'failed',
              errorMessage: `Failed to save HTML: ${saveError instanceof Error ? saveError.message : 'Unknown error'}`,
              processedAt: new Date(),
              processingStartedAt: null
            }
          })
        }
      }
    }

    // Update failed jobs
    for (const failure of data.failures) {
      const job = jobsToProcess.find(j => j.jobId === failure.id)
      if (job) {
        await Job.findByIdAndUpdate(job._id, {
          $set: {
            status: 'failed',
            errorMessage: failure.error,
            processedAt: new Date(),
            processingStartedAt: null
          }
        })

        failedJobs.push({
          jobId: job.jobId,
          error: failure.error,
          attempts: failure.attempts
        })

        console.log(`❌ Job ${job.jobId} failed: ${failure.error}`)
      }
    }

    const totalDuration = Date.now() - startTime

    console.log(`🏁 Batch processing completed in ${totalDuration}ms:`)
    console.log(`✅ Successful: ${successfulJobs.length}`)
    console.log(`❌ Failed: ${failedJobs.length}`)
    console.log(`📈 Success rate: ${((successfulJobs.length / jobsToProcess.length) * 100).toFixed(1)}%`)

    return NextResponse.json({
      success: true,
      summary: {
        totalJobs: jobsToProcess.length,
        successful: successfulJobs.length,
        failed: failedJobs.length,
        successRate: (successfulJobs.length / jobsToProcess.length) * 100,
        totalDuration,
        parallelApiDuration: data.summary.totalDuration
      },
      successfulJobs,
      failedJobs
    })

  } catch (error) {
    const totalDuration = Date.now() - startTime
    console.error('💥 Batch job processing failed:', error)

    return NextResponse.json({
      success: false,
      error: 'Batch job processing failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      duration: totalDuration
    }, { status: 500 })
  }
}

// Save HTML to lesson document (replaces html_css_description_of_image)
async function saveHtmlToLesson(job: any, generatedHtml: string): Promise<void> {
  try {
    console.log(`💾 Saving HTML to lesson document: ${job.documentId}, slide: ${job.slideKey}`)
    console.log(`📄 HTML length: ${generatedHtml?.length} characters`)

    // Find the lesson document
    const lessonDoc = await JsonDocument.findOne({ documentId: job.documentId })

    if (!lessonDoc) {
      throw new Error(`Lesson document not found: ${job.documentId}`)
    }

    console.log(`🔎 Looking for slide with slideKey: ${job.slideKey}`)

    // Check if the slide exists in the content structure (use same logic as working process-job)
    if (!lessonDoc.content || !lessonDoc.content[job.slideKey]) {
      console.error(`❌ Slide not found in content structure`)
      console.error(`Available slides:`, Object.keys(lessonDoc.content || {}))
      throw new Error(`Slide ${job.slideKey} not found in lesson ${job.documentId}`)
    }

    console.log(`✅ Found slide: ${job.slideKey}`)

    // Update the specific slide with HTML (use same logic as working process-job)
    const updatePath = `content.${job.slideKey}.html_css_description_of_image`
    const metadataPath1 = `content.${job.slideKey}.html_generated_at`
    const metadataPath2 = `content.${job.slideKey}.html_job_id`

    console.log(`🎯 Update paths:`)
    console.log(`   - ${updatePath}`)
    console.log(`   - ${metadataPath1}`)
    console.log(`   - ${metadataPath2}`)

    await JsonDocument.findOneAndUpdate(
      { documentId: job.documentId },
      {
        $set: {
          [updatePath]: generatedHtml,
          [metadataPath1]: new Date(),
          [metadataPath2]: job.jobId,
          updatedAt: new Date()
        }
      }
    )

    console.log(`✅ HTML saved to ${job.documentId}.content.${job.slideKey}.html_css_description_of_image`)
  } catch (error) {
    console.error(`❌ Error saving HTML to lesson:`, error)
    throw error
  }
}

// GET endpoint for API information
export async function GET() {
  return NextResponse.json({
    message: 'Batch Job Processing API',
    description: 'Process multiple jobs in parallel using the optimized HTML generation API',
    endpoints: {
      POST: '/api/jobs/process-batch',
      description: 'Process a batch of pending and failed jobs in parallel'
    },
    configuration: {
      defaultBatchSize: 20,
      maxBatchSize: 200,
      processingOrder: 'pending jobs first, then failed jobs with retries < 3'
    },
    requestFormat: {
      batchSize: 'number (optional, default: 50, max: 200)'
    },
    responseFormat: {
      success: 'boolean',
      summary: {
        totalJobs: 'number',
        successful: 'number',
        failed: 'number',
        successRate: 'percentage',
        totalDuration: 'milliseconds',
        parallelApiDuration: 'milliseconds'
      },
      successfulJobs: 'array of successful job details',
      failedJobs: 'array of failed job details'
    }
  })
}
