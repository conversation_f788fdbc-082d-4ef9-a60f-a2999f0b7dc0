import { NextRequest, NextResponse } from 'next/server'
import connectToDatabase from '@/lib/mongodb'
import Job from '@/models/Job'

// Process all pending jobs - for cron or batch processing
export async function POST(request: NextRequest) {
  try {
    await connectToDatabase()

    const { limit = 5, dryRun = false } = await request.json()

    console.log(`🔄 Starting batch job processing (limit: ${limit}, dryRun: ${dryRun})`)

    // Find pending jobs
    const pendingJobs = await Job.find({ status: 'pending' })
      .sort({ createdAt: 1 }) // Process oldest first
      .limit(limit)

    console.log(`📋 Found ${pendingJobs.length} pending jobs`)

    if (pendingJobs.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No pending jobs found',
        processed: 0,
        results: []
      })
    }

    if (dryRun) {
      console.log(`🧪 Dry run mode - would process ${pendingJobs.length} jobs`)
      return NextResponse.json({
        success: true,
        message: `Dry run: Found ${pendingJobs.length} jobs to process`,
        processed: 0,
        jobs: pendingJobs.map(job => ({
          jobId: job.jobId,
          documentId: job.documentId,
          slideKey: job.slideKey,
          prompt: job.prompt.substring(0, 100) + '...'
        }))
      })
    }

    const results = []
    let successCount = 0
    let failureCount = 0

    // Process each job
    for (const job of pendingJobs) {
      try {
        console.log(`🚀 Processing job ${successCount + failureCount + 1}/${pendingJobs.length}: ${job.jobId}`)

        // Call the universal job processor
        const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/jobs/process-job`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ jobId: job.jobId }),
        })

        const data = await response.json()

        if (data.success) {
          console.log(`✅ Job ${job.jobId} processed successfully`)
          successCount++
          results.push({
            jobId: job.jobId,
            status: 'success',
            htmlLength: data.htmlLength,
            message: 'Processed successfully'
          })
        } else {
          console.log(`❌ Job ${job.jobId} failed: ${data.error}`)
          failureCount++
          results.push({
            jobId: job.jobId,
            status: 'failed',
            error: data.error,
            message: 'Processing failed'
          })
        }

      } catch (error) {
        console.error(`❌ Error processing job ${job.jobId}:`, error)
        failureCount++
        results.push({
          jobId: job.jobId,
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error',
          message: 'Processing error'
        })
      }

      // Add a small delay between jobs to avoid overwhelming the system
      await new Promise(resolve => setTimeout(resolve, 1000))
    }

    console.log(`🏁 Batch processing completed: ${successCount} success, ${failureCount} failures`)

    return NextResponse.json({
      success: true,
      message: `Processed ${pendingJobs.length} jobs: ${successCount} success, ${failureCount} failures`,
      processed: pendingJobs.length,
      successCount,
      failureCount,
      results
    })

  } catch (error) {
    console.error('❌ Error in batch job processing:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to process jobs: ' + (error instanceof Error ? error.message : 'Unknown error')
      },
      { status: 500 }
    )
  }
}

// GET endpoint for status and testing
export async function GET() {
  try {
    await connectToDatabase()

    // Get job statistics
    const stats = await Job.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ])

    const statusCounts = stats.reduce((acc, stat) => {
      acc[stat._id] = stat.count
      return acc
    }, {} as Record<string, number>)

    return NextResponse.json({
      message: 'Batch Job Processor API',
      description: 'Process all pending jobs in batch - for cron or manual batch processing',
      usage: {
        POST: 'Process pending jobs',
        parameters: {
          limit: 'Maximum number of jobs to process (default: 10)',
          dryRun: 'If true, only shows what would be processed (default: false)'
        }
      },
      currentStats: {
        pending: statusCounts.pending || 0,
        processing: statusCounts.processing || 0,
        success: statusCounts.success || 0,
        failed: statusCounts.failed || 0,
        total: Object.values(statusCounts).reduce((sum: any, count:  any) => sum + count, 0)
      },
      examples: {
        processAll: {
          method: 'POST',
          body: { limit: 10, dryRun: false }
        },
        dryRun: {
          method: 'POST',
          body: { limit: 5, dryRun: true }
        }
      }
    })

  } catch (error) {
    console.error('Error getting job stats:', error)
    return NextResponse.json(
      { error: 'Failed to get job statistics' },
      { status: 500 }
    )
  }
}
