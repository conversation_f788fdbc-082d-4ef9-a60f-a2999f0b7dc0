import { NextRequest, NextResponse } from 'next/server'
import { connectWithRetry } from '@/lib/mongodb'
import Job from '@/models/Job'
import JsonDocument from '@/models/JsonDocument'

// Async function to process all success jobs in batches
async function processAllSuccessJobsAsync(totalJobs: number, batchSize: number) {
  const startTime = Date.now()
  console.log(`🚀 ASYNC VALIDATION: Starting validation of ${totalJobs} jobs in batches of ${batchSize}`)

  try {
    await connectWithRetry()

    const totalBatches = Math.ceil(totalJobs / batchSize)
    let totalValidCount = 0
    let totalInvalidCount = 0
    let totalFixedCount = 0

    // Create array of batch promises
    const batchPromises = []

    for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
      const skip = batchIndex * batchSize

      // Create promise for this batch
      const batchPromise = processBatch(batchIndex, skip, batchSize)
      batchPromises.push(batchPromise)

      // Process in groups of 2 batches to avoid MongoDB connection pool issues
      if (batchPromises.length >= 2 || batchIndex === totalBatches - 1) {
        console.log(`🔄 Processing batch group: ${batchIndex - batchPromises.length + 1} to ${batchIndex}`)

        const batchResults = await Promise.allSettled(batchPromises)

        // Aggregate results
        batchResults.forEach((result, index) => {
          if (result.status === 'fulfilled') {
            totalValidCount += result.value.validCount
            totalInvalidCount += result.value.invalidCount
            totalFixedCount += result.value.fixedCount
          } else {
            console.error(`❌ Batch ${batchIndex - batchPromises.length + 1 + index} failed:`, result.reason)
          }
        })

        // Clear promises array for next group
        batchPromises.length = 0

        // Longer delay between batch groups to let connections close
        await new Promise(resolve => setTimeout(resolve, 3000))
      }
    }

    const duration = Date.now() - startTime
    console.log(`✅ ASYNC VALIDATION COMPLETE: ${totalValidCount} valid, ${totalInvalidCount} invalid, ${totalFixedCount} fixed in ${duration}ms`)

  } catch (error) {
    console.error('❌ ASYNC VALIDATION FAILED:', error)
  }
}

// Process a single batch of jobs with retry logic
async function processBatch(batchIndex: number, skip: number, limit: number, retryCount = 0) {
  console.log(`🔍 Processing batch ${batchIndex + 1}: skip=${skip}, limit=${limit}, retry=${retryCount}`)

  try {
    // Ensure fresh connection for each batch
    await connectWithRetry()

    const successJobs = await Job.find({ status: 'success' })
      .sort({ processedAt: -1 })
      .skip(skip)
      .limit(limit)

    console.log(`📋 Batch ${batchIndex + 1}: Found ${successJobs.length} jobs to validate`)

    let validCount = 0
    let invalidCount = 0
    let fixedCount = 0

  // Process jobs in smaller chunks to avoid overwhelming MongoDB
  const chunkSize = 5
  for (let i = 0; i < successJobs.length; i += chunkSize) {
    const jobChunk = successJobs.slice(i, i + chunkSize)

    // Process chunk jobs sequentially to avoid connection pool issues
    for (const job of jobChunk) {
      try {
        // Find the lesson document using multiple approaches
        let lesson = null

        // Attempt 1: Exact documentId match
        lesson = await JsonDocument.findOne({ documentId: job.documentId })

        // Attempt 2: Search by metadata fields
        if (!lesson) {
          lesson = await JsonDocument.findOne({
            unit_number: job.unitNumber,
            lesson_number: job.lessonNumber,
            grade_level: job.gradeLevel,
            lang: 'en'
          })
        }

        // Attempt 3: Search by metadata with any lang
        if (!lesson) {
          lesson = await JsonDocument.findOne({
            unit_number: job.unitNumber,
            lesson_number: job.lessonNumber,
            grade_level: job.gradeLevel
          })
        }

      if (!lesson) {
        console.log(`❌ Document not found: ${job.documentId}`)
        // await Job.findByIdAndUpdate(job._id, {
        //   status: 'pending',
        //   errorMessage: 'Validation failed: Document not found - moved to pending for regeneration',
        //   processedAt: null,
        //   processingStartedAt: null
        // })
        fixedCount++
        invalidCount++
        continue
      }

      // Check if slide exists
      if (!lesson.content || !lesson.content[job.slideKey]) {
        console.log(`❌ Slide not found: ${job.slideKey} in ${job.documentId}`)
        // await Job.findByIdAndUpdate(job._id, {
        //   status: 'pending',
        //   errorMessage: 'Validation failed: Slide not found in document - moved to pending for regeneration',
        //   processedAt: null,
        //   processingStartedAt: null
        // })
        fixedCount++
        invalidCount++
        continue
      }

      // Check if HTML exists and is valid
      const htmlContent = lesson.content[job.slideKey].html_css_description_of_image

      if (!htmlContent || typeof htmlContent !== 'string' || htmlContent.trim().length === 0) {
        console.log(`❌ Empty HTML in ${job.documentId}, slide ${job.slideKey}`)
        await Job.findByIdAndUpdate(job._id, {
          status: 'pending',
          errorMessage: 'Validation failed: Empty HTML content - moved to pending for regeneration',
          processedAt: null,
          processingStartedAt: null
        })
        fixedCount++
        invalidCount++
        continue
      }

      // Check if HTML looks valid
      if (!htmlContent.includes('<html') && !htmlContent.includes('<!DOCTYPE')) {
        console.log(`❌ Invalid HTML structure in ${job.documentId}, slide ${job.slideKey}`)
        await Job.findByIdAndUpdate(job._id, {
          status: 'pending',
          errorMessage: 'Validation failed: Invalid HTML structure - moved to pending for regeneration',
          processedAt: null,
          processingStartedAt: null
        })
        fixedCount++
        invalidCount++
        continue
      }

        // HTML is valid
        validCount++

      } catch (error) {
        console.error(`❌ Error validating job ${job.jobId}:`, error)
        invalidCount++
      }
    }

    // Small delay between chunks to prevent overwhelming MongoDB
    if (i + chunkSize < successJobs.length) {
      await new Promise(resolve => setTimeout(resolve, 100))
    }
  }

    console.log(`✅ Batch ${batchIndex + 1} complete: ${validCount} valid, ${invalidCount} invalid, ${fixedCount} fixed`)

    return { validCount, invalidCount, fixedCount }

  } catch (error) {
    console.error(`❌ Batch ${batchIndex + 1} failed:`, error)

    // Retry logic for connection issues
    if (retryCount < 2 && (
      error instanceof Error && (
        error.message.includes('MongoWaitQueueTimeoutError') ||
        error.message.includes('connection pool') ||
        error.message.includes('timeout')
      )
    )) {
      console.log(`🔄 Retrying batch ${batchIndex + 1} (attempt ${retryCount + 1})`)
      await new Promise(resolve => setTimeout(resolve, 5000 * (retryCount + 1))) // Exponential backoff
      return processBatch(batchIndex, skip, limit, retryCount + 1)
    }

    // If retry failed or not a connection error, return zeros
    return { validCount: 0, invalidCount: 0, fixedCount: 0 }
  }
}

// Validate success jobs to check if HTML is actually saved correctly
export async function POST(request: NextRequest) {
  const startTime = Date.now()

  try {
    const { limit = 50, async = false } = await request.json()

    console.log(`🔍 VALIDATE SUCCESS: Starting validation of success jobs (limit: ${limit}, async: ${async})...`)

    await connectWithRetry()

    // Get total count of success jobs
    const totalSuccessJobs = await Job.countDocuments({ status: 'success' })
    console.log(`📊 Total success jobs to validate: ${totalSuccessJobs}`)

    if (async && totalSuccessJobs > limit) {
      // Start async processing and return immediately
      console.log(`🚀 Starting async validation of ${totalSuccessJobs} jobs in batches of ${limit}`)

      // Don't await this - let it run in background
      processAllSuccessJobsAsync(totalSuccessJobs, limit).catch(error => {
        console.error('❌ Async validation failed:', error)
      })

      return NextResponse.json({
        success: true,
        message: `Async validation started for ${totalSuccessJobs} success jobs`,
        totalJobs: totalSuccessJobs,
        batchSize: limit,
        estimatedBatches: Math.ceil(totalSuccessJobs / limit),
        async: true,
        startedAt: new Date().toISOString()
      })
    }

    // Synchronous processing (original behavior)
    const successJobs = await Job.find({ status: 'success' })
      .sort({ processedAt: -1 })
      .limit(limit)

    console.log(`📋 Found ${successJobs.length} success jobs to validate`)

    let validCount = 0
    let invalidCount = 0
    let fixedCount = 0
    const invalidJobs = []

    for (const job of successJobs) {
      try {
        console.log(`🔍 Validating job ${job.jobId} (${job.documentId}, slide ${job.slideKey})`)

        // Find the lesson document - try both English and Spanish versions
        console.log(`🔍 Searching for lesson document: ${job.documentId}`)

        // First try to find the document as-is
        let lesson = await JsonDocument.findOne({ documentId: job.documentId })

        // If not found and documentId doesn't end with -esp, try with lang: 'en'
        if (!lesson && !job.documentId.endsWith('-esp')) {
          console.log(`🔍 Trying with lang: 'en' for documentId: ${job.documentId}`)
          lesson = await JsonDocument.findOne({ documentId: job.documentId, lang: 'en' })
        }

        // If still not found and documentId ends with -esp, try without -esp suffix
        if (!lesson && job.documentId.endsWith('-esp')) {
          const baseDocumentId = job.documentId.replace('-esp', '')
          console.log(`🔍 Trying base documentId: ${baseDocumentId} with lang: 'esp'`)
          lesson = await JsonDocument.findOne({ documentId: baseDocumentId, lang: 'esp' })
        }

        if (!lesson) {
          console.log(`❌ Document not found: ${job.documentId}`)
          invalidJobs.push({
            jobId: job.jobId,
            documentId: job.documentId,
            slideKey: job.slideKey,
            issue: 'Document not found'
          })
          invalidCount++
          continue
        }

        // Check if slide exists
        if (!lesson.content || !lesson.content[job.slideKey]) {
          console.log(`❌ Slide not found: ${job.slideKey} in ${job.documentId}`)
          invalidJobs.push({
            jobId: job.jobId,
            documentId: job.documentId,
            slideKey: job.slideKey,
            issue: 'Slide not found in document'
          })
          invalidCount++
          continue
        }

        // Check if HTML exists and is valid
        const htmlContent = lesson.content[job.slideKey].html_css_description_of_image
        
        if (!htmlContent || typeof htmlContent !== 'string' || htmlContent.trim().length === 0) {
          console.log(`❌ Empty HTML in ${job.documentId}, slide ${job.slideKey}`)

          // Mark job as pending since HTML is empty - needs to be regenerated
          await Job.findByIdAndUpdate(job._id, {
            status: 'pending',
            errorMessage: 'HTML validation failed: Empty or missing HTML content in lesson document - moved to pending for regeneration',
            processedAt: null,
            processingStartedAt: null
          })

          fixedCount++
          invalidJobs.push({
            jobId: job.jobId,
            documentId: job.documentId,
            slideKey: job.slideKey,
            issue: 'Empty HTML - job moved to pending'
          })
          invalidCount++
          continue
        }

        // Check if HTML looks valid
        if (!htmlContent.includes('<html') && !htmlContent.includes('<!DOCTYPE')) {
          console.log(`❌ Invalid HTML structure in ${job.documentId}, slide ${job.slideKey}`)

          // Mark job as pending since HTML is invalid - needs to be regenerated
          await Job.findByIdAndUpdate(job._id, {
            status: 'pending',
            errorMessage: 'HTML validation failed: Invalid HTML structure in lesson document - moved to pending for regeneration',
            processedAt: null,
            processingStartedAt: null
          })

          fixedCount++
          invalidJobs.push({
            jobId: job.jobId,
            documentId: job.documentId,
            slideKey: job.slideKey,
            issue: 'Invalid HTML structure - job moved to pending'
          })
          invalidCount++
          continue
        }

        // HTML is valid
        console.log(`✅ Valid HTML found in ${job.documentId}, slide ${job.slideKey} (${htmlContent.length} chars)`)
        validCount++

      } catch (error) {
        console.error(`❌ Error validating job ${job.jobId}:`, error)
        invalidJobs.push({
          jobId: job.jobId,
          documentId: job.documentId,
          slideKey: job.slideKey,
          issue: `Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`
        })
        invalidCount++
      }
    }

    const duration = Date.now() - startTime
    
    console.log(`✅ VALIDATE SUCCESS: Completed validation in ${duration}ms`)
    console.log(`📊 Results: ${validCount} valid, ${invalidCount} invalid, ${fixedCount} fixed`)

    return NextResponse.json({
      success: true,
      message: `Validation completed: ${validCount} valid, ${invalidCount} invalid, ${fixedCount} fixed`,
      totalChecked: successJobs.length,
      validCount,
      invalidCount,
      fixedCount,
      invalidJobs: invalidJobs.slice(0, 10), // Return first 10 invalid jobs for debugging
      duration
    })

  } catch (error) {
    const duration = Date.now() - startTime
    console.error('❌ VALIDATE SUCCESS failed:', error)

    return NextResponse.json({
      success: false,
      error: 'Failed to validate success jobs',
      details: error instanceof Error ? error.message : 'Unknown error',
      duration
    }, { status: 500 })
  }
}

// GET endpoint for info
export async function GET() {
  return NextResponse.json({
    message: 'Validate Success Jobs API',
    description: 'Check if success jobs actually have valid HTML saved in lesson documents',
    endpoint: 'POST /api/jobs/validate-success',
    parameters: {
      limit: 'Number of success jobs to validate (default: 50)'
    },
    usage: 'Call this endpoint to validate that success jobs have valid HTML content'
  })
}
