import { NextRequest, NextResponse } from 'next/server';
import connectToDatabase from '@/lib/mongodb';
import mongoose from 'mongoose';

export async function POST(request: NextRequest) {
  try {
    // Parse the JSON data from the request
    const data = await request.json();

    if (!data || !data.slide_number || data.html === undefined) {
      return NextResponse.json(
        { message: 'Missing required fields: slide_number, html' },
        { status: 400 }
      );
    }

    const { slide_number, html, unit_number, lesson_number, grade_level, lang, common_core } = data;

    // Connect to the database
    await connectToDatabase();

    // Construct the document ID from the provided parameters or use default
    let documentId;

    if (unit_number && lesson_number && grade_level) {
      documentId = `${unit_number}-${lesson_number}-${grade_level}`;
    } else {
      // Fallback to default document ID if parameters are not provided
      documentId = "3-2-Grade 1";
    }

    console.log(`Using document ID: ${documentId} (unit: ${unit_number}, lesson: ${lesson_number}, grade: ${grade_level})`);

    console.log(`Updating HTML for document ID: ${documentId}, slide number: ${slide_number}`);

    // Determine the slide key format
    const slideKey = `Slide ${slide_number}`;
    const slideKeyWithType = `Slide ${slide_number}: ${getSlideType(slide_number)}`;

    console.log(`Possible slide keys: "${slideKey}" or "${slideKeyWithType}"`);

    // Use direct MongoDB operations for more reliable updates
    if (!mongoose.connection.db) {
      throw new Error('MongoDB connection not established');
    }

    const db = mongoose.connection.db;
    const collection = db.collection('jsondocuments');

    // Build query to find the document
    const query: any = { documentId };

    // Add common_core filter if provided
    if (common_core) {
      query['content.common_core'] = common_core;
    }

    // Add language filter if provided
    if (lang) {
      query.lang = lang;
    }

    console.log(`Looking for document with query:`, query);
    const existingDoc = await collection.findOne(query);

    // If not found, try to find a similar document
    if (!existingDoc) {
      console.error(`Document with ID ${documentId} not found, trying to find similar documents...`);

      // Try to find documents with the same unit and lesson number
      const similarDocs = await collection.find({
        documentId: { $regex: `^${unit_number}-${lesson_number}-` }
      }).toArray();

      if (similarDocs && similarDocs.length > 0) {
        console.log(`Found ${similarDocs.length} similar documents with unit ${unit_number} and lesson ${lesson_number}`);

        // Use the first similar document
        const similarDoc = similarDocs[0];
        console.log(`Using similar document with ID: ${similarDoc.documentId}`);

        // Return a warning but continue with the similar document
        return NextResponse.json(
          {
            message: `Document with ID ${documentId} not found. Using similar document with ID ${similarDoc.documentId} instead.`,
            warning: true,
            similarDocumentId: similarDoc.documentId,
            availableDocuments: similarDocs.map(doc => ({ id: doc.documentId }))
          },
          { status: 404 }
        );
      }

      // If no similar documents found, return error with available documents
      const allDocs = await collection.find({}).limit(10).toArray();
      const availableDocIds = allDocs.map(doc => doc.documentId).filter(Boolean);

      return NextResponse.json(
        {
          message: `Document with ID ${documentId} not found. Please create a document first.`,
          availableDocuments: availableDocIds
        },
        { status: 404 }
      );
    }

    // Log the found document ID
    console.log(`Found document with ID: ${existingDoc.documentId || existingDoc._id}`);

    // Document exists, now determine which slide key format is used
    const content = existingDoc.content || {};

    // Find the actual slide key that exists in the document
    const actualSlideKey = Object.keys(content).find(key =>
      key === slideKey || key === slideKeyWithType || key.startsWith(`Slide ${slide_number}:`)
    );

    // Prepare the update operation
    let updateOperation;

    if (!actualSlideKey) {
      console.log(`Slide ${slide_number} not found in document, will create it with key "${slideKey}"`);

      // Create a new slide object
      const newSlide = {
        slide_pedagogical_name: `Slide ${slide_number}`,
        type: getSlideType(slide_number),
        html_css_description_of_image: html,
        slide_text_1: "",
        slide_text_2: "",
        slide_text_3: "",
        slide_text_4: "",
        teacher_tips: {
          general_tip: "",
          misconception_tip: ""
        }
      };

      // Update operation to add the new slide
      updateOperation = {
        $set: {
          [`content.${slideKey}`]: newSlide,
          updatedAt: new Date()
        }
      };
    } else {
      console.log(`Found actual slide key: "${actualSlideKey}"`);

      // Update operation to update the HTML content
      updateOperation = {
        $set: {
          [`content.${actualSlideKey}.html_css_description_of_image`]: html,
          updatedAt: new Date()
        }
      };

      // If the slide has generated_html_content, remove it
      if (content[actualSlideKey].generated_html_content) {
        // Create a separate operation to remove the generated_html_content
        const unsetOperation = {
          $unset: {
            [`content.${actualSlideKey}.generated_html_content`]: ""
          }
        };

        // Execute the unset operation first
        await collection.updateOne(
          { _id: existingDoc._id },
          unsetOperation
        );
      }
    }

    // Perform the update
    console.log(`Updating document with operation:`, JSON.stringify(updateOperation, null, 2));
    const result = await collection.updateOne(
      { _id: existingDoc._id },
      updateOperation
    );

    console.log('Update result:', result);

    if (result.modifiedCount === 0 && result.matchedCount > 0) {
      console.log('Document matched but not modified. Trying alternative update approach...');

      // Try a different approach - update only the specific field
      const fallbackOperation = {
        $set: {
          [`content.${actualSlideKey || slideKey}.html_css_description_of_image`]: html,
          updatedAt: new Date()
        }
      };

      const fallbackResult = await collection.updateOne(
        { _id: existingDoc._id },
        fallbackOperation
      );

      console.log('Fallback update result:', fallbackResult);
    }

    // Verify the update was successful
    const updatedDoc = await collection.findOne({ _id: existingDoc._id });
    const verifySlideKey = actualSlideKey || slideKey;

    if (updatedDoc && updatedDoc.content && updatedDoc.content[verifySlideKey]) {
      const savedHtml = updatedDoc.content[verifySlideKey].html_css_description_of_image;
      console.log(`Verified HTML content in database: ${savedHtml ? 'Present' : 'Missing'} (${savedHtml ? savedHtml.length : 0} characters)`);

      if (!savedHtml) {
        console.error('HTML content verification failed - content missing');
        return NextResponse.json(
          { message: 'HTML content verification failed - content missing' },
          { status: 500 }
        );
      }
    } else {
      console.error('HTML content verification failed - slide not found after update');
      return NextResponse.json(
        { message: 'HTML content verification failed - slide not found after update' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: 'HTML content updated successfully',
      documentId,
      slideKey: actualSlideKey || slideKey
    });
  } catch (error) {
    console.error('Error updating HTML content:', error);
    return NextResponse.json(
      { message: 'An error occurred while updating HTML content', error: String(error) },
      { status: 500 }
    );
  }
}

// Helper function to get slide type
function getSlideType(slideNumber: number): string {
  // Map slide numbers to their types
  switch (slideNumber) {
    case 1: return "Quick_Review";
    case 2: return "Learning_Goals";
    case 3: return "Vocabulary";
    case 4: return "hook_1";
    case 5: return "hook_2";
    case 6: return "hook_3";
    case 7: return "teach_1_1";
    case 8: return "teach_1_2";
    case 9: return "teach_1_3";
    case 10: return "talk_1_1";
    case 11: return "talk_1_2";
    case 12: return "talk_1_3";
    case 13: return "try_1";
    case 14: return "teach_2_1";
    case 15: return "teach_2_2";
    case 16: return "teach_2_3";
    case 17: return "talk_2_1";
    case 18: return "talk_2_2";
    case 19: return "talk_2_3";
    case 20: return "try_2";
    case 21: return "practice";
    case 22: return "on_ramp_teach_1";
    case 23: return "on_ramp_talk_1";
    case 24: return "on_ramp_try_1";
    case 25: return "Lesson_Guide_PDF_Link";
    case 26: return "Practice_PDF_Link";
    case 27: return "Accelerator_PDF_Link";
    default: return "unknown";
  }
}
