import { NextRequest, NextResponse } from 'next/server'
import { MongoClient } from 'mongodb'
import { fetchJsonDocument } from '@/services/dbJsonDocumentService'
import connectToDatabase from '@/lib/mongodb'
import JsonDocument from '@/models/JsonDocument'

const uri = process.env.MONGODB_URI
if (!uri) {
  throw new Error('MONGODB_URI is not defined')
}

const client = new MongoClient(uri)

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    const grade = searchParams.get('grade')
    const unit = searchParams.get('unit')
    const lesson = searchParams.get('lesson')
    const curriculum = searchParams.get('curriculum')
    const lang = searchParams.get('lang')

    let documentId: string

    if (id) {
      documentId = id
    } else if (grade && unit && lesson) {
      // Format: unit-lesson-grade (e.g., "7-60-Grade 1")
      documentId = `${unit}-${lesson}-${grade}`
    } else {
      return NextResponse.json({ error: 'Document ID or grade/unit/lesson parameters are required' }, { status: 400 })
    }

    console.log('🔍 Edit JSON API - GET request received')
    console.log('🔍 Edit JSON API - Looking for document:', documentId)
    console.log('🔍 Edit JSON API - Parameters:', { id, grade, unit, lesson })

    // If we have grade, unit, lesson - use the existing service
    if (grade && unit && lesson) {
      try {
        const document = await fetchJsonDocument({
          unit_number: unit,
          lesson_number: lesson,
          grade_level: grade,
          lang: lang || undefined
        })

        if (document) {
          console.log('Edit JSON API - Found document via service:', document.documentId)
          console.log('Edit JSON API - Document structure:', Object.keys(document))

          // Convert Mongoose document to plain object and return content
          const plainDocument = document.toObject ? document.toObject() : document


          // Convert slide keys to slides array
          const slides = []
          if (plainDocument.content) {
            for (const [key, value] of Object.entries(plainDocument.content)) {
              if (key.startsWith('Slide ') && key.includes(':')) {
                const slideMatch = key.match(/Slide (\d+): (.+)/)
                if (slideMatch) {
                  const slideNumber = parseInt(slideMatch[1])
                  const slideName = slideMatch[2]
                  // Create slide object with content and also copy fields to top level for UI compatibility
                  const slideData = {
                    slide_number: slideNumber,
                    slide_name: slideName,
                    slide_type: (value as any)?.type || 'unknown',
                    title: (value as any)?.slide_pedagogical_name || slideName,
                    content: value
                  }

                  // Copy fields from content to top level for UI compatibility
                  if (value && typeof value === 'object') {
                    const contentFields = ['script', 'teacher_tips', 'html_css_description_of_image',
                                         'q1', 'a1', 'q2', 'a2', 'q3', 'a3', 'q4', 'a4', 'q5', 'a5']
                    contentFields.forEach(field => {
                      if ((value as any)[field] !== undefined) {
                        (slideData as any)[field] = (value as any)[field]
                      }
                    })
                  }

                  slides.push(slideData)
                }
              }
            }
          }

          // Sort slides by slide_number
          slides.sort((a, b) => a.slide_number - b.slide_number)

          const documentData = {
            _id: document.documentId,
            documentId: document.documentId,
            grade_level: document.grade_level,
            unit_number: document.unit_number,
            lesson_number: document.lesson_number,
            unit_title: document.unit_title,
            lesson_title: document.lesson_title,
            slides: slides
          }

          console.log('Edit JSON API - Returning document data with slides count:', documentData.slides?.length || 0)

          // Log first slide structure for debugging
          if (documentData.slides.length > 0) {
            console.log('🔍 First slide structure:', JSON.stringify(documentData.slides[0], null, 2))
          }

          return NextResponse.json({ document: documentData })
        }
      } catch (error) {
        console.error('Edit JSON API - Error fetching via service:', error)
      }
    }

    // Fallback to direct MongoDB query for versioned documents
    await client.connect()
    const db = client.db('math-lesson-slider')
    const collection = db.collection('jsondocuments')

    // Get the current version of the document
    console.log('Edit JSON API - Searching for document with query:', {
      $or: [
        { _id: documentId, isCurrent: { $ne: false } },
        { originalDocumentId: documentId, isCurrent: true }
      ]
    })

    const document = await collection.findOne({
      $or: [
        { _id: documentId as any, isCurrent: { $ne: false } },
        { originalDocumentId: documentId, isCurrent: true }
      ]
    })

    console.log('Edit JSON API - Found document:', document ? 'YES' : 'NO')

    if (!document) {
      // If no current version found, get the original document
      console.log('Edit JSON API - Searching for original document with ID:', documentId)
      const originalDoc = await collection.findOne({ _id: documentId as any })
      console.log('Edit JSON API - Found original document:', originalDoc ? 'YES' : 'NO')

      if (originalDoc) {
        // Mark it as current if it doesn't have the flag
        if (originalDoc.isCurrent === undefined) {
          console.log('Edit JSON API - Marking original document as current')
          await collection.updateOne(
            { _id: documentId as any },
            { $set: { isCurrent: true, version: 1 } }
          )
          originalDoc.isCurrent = true
          originalDoc.version = 1
        }
        return NextResponse.json({ document: originalDoc })
      }
      console.log('Edit JSON API - Document not found anywhere')
      return NextResponse.json({ error: 'Document not found' }, { status: 404 })
    }

    return NextResponse.json({ document })
  } catch (error) {
    console.error('Error fetching document:', error)
    return NextResponse.json({ error: 'Failed to fetch document' }, { status: 500 })
  } finally {
    await client.close()
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('🔍 Edit JSON API - POST request received')

    const body = await request.json()
    console.log('🔍 Edit JSON API - POST body:', { documentId: body.documentId, slidesCount: body.slides?.length })

    const { documentId, slides, updatedData, changeDescription } = body

    // Handle simple slide updates using Mongoose (same as GET request)
    if (documentId && slides) {
      console.log('Edit JSON API - Simple slide update mode for documentId:', documentId)

      try {
        // Connect to database using Mongoose (same as GET request)
        await connectToDatabase()

        // Parse documentId to get individual components
        const parts = documentId.split('-')
        const unit_number = parts[0]
        const lesson_number = parts[1]
        const grade_level = parts.slice(2).join('-')

        console.log('Edit JSON API - Searching for document with:', {
          unit_number,
          lesson_number,
          grade_level
        })

        // Find document using the same method as GET request
        let document = await JsonDocument.findOne({
          documentId: documentId
        })

        if (!document) {
          console.log('Edit JSON API - Document not found with documentId:', documentId)

          // Try alternative search by individual fields
          document = await JsonDocument.findOne({
            unit_number,
            lesson_number,
            grade_level
          })

          if (document) {
            console.log('Edit JSON API - Found document with alternative query!')
            console.log('Edit JSON API - Alternative document ID:', document.documentId)
          } else {
            console.log('Edit JSON API - No document found with alternative query either')
            return NextResponse.json({ error: 'Document not found' }, { status: 404 })
          }
        }

        console.log('Edit JSON API - Found document, updating slides in content...')
        console.log('Edit JSON API - Document ID:', document.documentId)
        console.log('Edit JSON API - Original document content keys:', Object.keys(document.content || {}))
        console.log('Edit JSON API - Incoming slides count:', slides.length)
        console.log('Edit JSON API - First incoming slide:', JSON.stringify(slides[0], null, 2))

        // Convert slides array back to the original content format
        // The database stores slides as "Slide 1: Quick_Review" keys in content
        const updatedContent = { ...document.content }

        // Clear existing slide entries
        for (const key of Object.keys(updatedContent)) {
          if (key.startsWith('Slide ') && key.includes(':')) {
            delete updatedContent[key]
          }
        }

        // Add updated slides back in the correct format
        slides.forEach((slide: any, index: number) => {
          console.log(`Edit JSON API - Processing slide ${index + 1}:`, {
            slide_number: slide.slide_number,
            slide_name: slide.slide_name,
            slide_type: slide.slide_type,
            title: slide.title,
            hasContent: !!slide.content,
            contentKeys: slide.content ? Object.keys(slide.content) : [],
            directFields: Object.keys(slide).filter(k => !['slide_number', 'slide_name', 'slide_type', 'title', 'content'].includes(k))
          })

          // Use slide_name from slide object, or fallback to content.slide_pedagogical_name, or generate from slide number
          const slideName = slide.slide_name || slide.content?.slide_pedagogical_name || `Slide_${slide.slide_number}`
          const slideKey = `Slide ${slide.slide_number}: ${slideName}`
          console.log(`Edit JSON API - Updating slide key: ${slideKey}`)

          // Create slide content object with all the fields
          const slideContent: any = {}

          // First copy old content as base (if exists)
          if (slide.content) {
            Object.assign(slideContent, slide.content)
            console.log(`Edit JSON API - Copied base content for ${slideKey}:`, Object.keys(slide.content))
          }

          // The frontend logic puts edited values in different places:
          // - If field exists in content, it updates slide.content[field]
          // - If field doesn't exist in content, it updates slide[field]
          // So we need to merge both sources properly

          const allPossibleFields = ['script', 'teacher_tips', 'html_css_description_of_image',
                                   'q1', 'a1', 'q2', 'a2', 'q3', 'a3', 'q4', 'a4', 'q5', 'a5',
                                   'title', 'slide_pedagogical_name', 'link_to_lesson_guide_pdf',
                                   'link_to_practice_pdf', 'link_to_accelerator_pdf']

          allPossibleFields.forEach(field => {
            // Check if field was edited and exists in slide.content (updated by frontend)
            const hasUpdatedContentValue = slide.content && slide.content[field] !== undefined
            // Check if field was edited and exists on top level (updated by frontend)
            const hasDirectValue = slide[field] !== undefined && slide[field] !== null && slide[field] !== ''

            if (hasUpdatedContentValue) {
              // Field was edited in UI and stored in slide.content - use this value
              slideContent[field] = slide.content[field]
              console.log(`Edit JSON API - ✅ Using UPDATED content value for ${field}:`, typeof slide.content[field] === 'string' ? slide.content[field].substring(0, 50) + '...' : slide.content[field])
            } else if (hasDirectValue) {
              // Field was edited in UI and stored on top level - use this value
              slideContent[field] = slide[field]
              console.log(`Edit JSON API - ✅ Using DIRECT value for ${field}:`, typeof slide[field] === 'string' ? slide[field].substring(0, 50) + '...' : slide[field])
            } else {
              console.log(`Edit JSON API - ⚠️  Field ${field} not edited, keeping existing value`)
            }
          })

          // Set required fields with fallbacks
          slideContent.slide_pedagogical_name = slide.title || slide.slide_pedagogical_name || slide.content?.slide_pedagogical_name || slideName
          slideContent.type = slide.slide_type || slide.content?.type || 'unknown'

          updatedContent[slideKey] = slideContent

          // Special logging for q1, a1 fields to debug the issue
          if (slideContent.q1 || slideContent.a1) {
            console.log(`Edit JSON API - 🔍 SPECIAL DEBUG for ${slideKey}:`)
            console.log(`Edit JSON API - 🔍 q1 final value:`, slideContent.q1)
            console.log(`Edit JSON API - 🔍 a1 final value:`, slideContent.a1)
            console.log(`Edit JSON API - 🔍 q1 from slide.content:`, slide.content?.q1)
            console.log(`Edit JSON API - 🔍 q1 from slide direct:`, slide.q1)
            console.log(`Edit JSON API - 🔍 a1 from slide.content:`, slide.content?.a1)
            console.log(`Edit JSON API - 🔍 a1 from slide direct:`, slide.a1)
          }

          console.log(`Edit JSON API - Final slide content for ${slideKey}:`, JSON.stringify(slideContent, null, 2))
        })

        console.log('Edit JSON API - Updated content keys:', Object.keys(updatedContent).filter(k => k.startsWith('Slide')))
        console.log('Edit JSON API - Total content keys after update:', Object.keys(updatedContent).length)

        // Log a comparison of before and after
        const originalSlideKeys = Object.keys(document.content || {}).filter(k => k.startsWith('Slide'))
        const newSlideKeys = Object.keys(updatedContent).filter(k => k.startsWith('Slide'))
        console.log('Edit JSON API - Slide keys comparison:', {
          original: originalSlideKeys.length,
          new: newSlideKeys.length,
          originalKeys: originalSlideKeys,
          newKeys: newSlideKeys
        })

        // Update the entire content object
        console.log('Edit JSON API - About to update document with ID:', document._id)
        console.log('Edit JSON API - Update payload size:', JSON.stringify(updatedContent).length, 'characters')

        const result = await JsonDocument.updateOne(
          { _id: document._id },
          {
            $set: {
              content: updatedContent,
              updatedAt: new Date()
            }
          }
        )

        console.log('Edit JSON API - Update result:', {
          acknowledged: result.acknowledged,
          matchedCount: result.matchedCount,
          modifiedCount: result.modifiedCount,
          upsertedCount: result.upsertedCount,
          upsertedId: result.upsertedId
        })

        if (result.matchedCount === 0) {
          console.log('Edit JSON API - ERROR: No documents matched the update query')
          return NextResponse.json({ error: 'Document not found during update' }, { status: 404 })
        }

        if (result.modifiedCount === 0) {
          console.log('Edit JSON API - WARNING: Document matched but was not modified, trying alternative update method...')

          // Try alternative update method - direct document modification
          try {
            document.content = updatedContent
            document.updatedAt = new Date()
            const saveResult = await document.save()
            console.log('Edit JSON API - Alternative save result:', saveResult ? 'SUCCESS' : 'FAILED')

            if (saveResult) {
              console.log('Edit JSON API - Alternative method succeeded')
            } else {
              console.log('Edit JSON API - Alternative method also failed, trying MongoDB driver...')

              // Last resort - use MongoDB driver directly
              try {
                await client.connect()
                const db = client.db('math-lesson-slider')
                const collection = db.collection('jsondocuments')

                const directResult = await collection.updateOne(
                  { _id: document._id },
                  {
                    $set: {
                      content: updatedContent,
                      updatedAt: new Date()
                    }
                  }
                )

                console.log('Edit JSON API - Direct MongoDB result:', directResult)
                await client.close()
              } catch (directError) {
                console.error('Edit JSON API - Direct MongoDB error:', directError)
              }
            }
          } catch (saveError) {
            console.error('Edit JSON API - Alternative save method error:', saveError)
          }
        }



        // Verify the update by fetching the document again
        const verifyDocument = await JsonDocument.findOne({ _id: document._id })
        if (verifyDocument) {
          const verifySlideKeys = Object.keys(verifyDocument.content || {}).filter(k => k.startsWith('Slide'))
          console.log('Edit JSON API - Verification: Document updated successfully, slide keys:', verifySlideKeys.length)

          // Check if specific changes were saved
          const firstSlideKey = verifySlideKeys[0]
          if (firstSlideKey && verifyDocument.content[firstSlideKey]) {
            console.log('Edit JSON API - Verification: First slide content after save:', JSON.stringify(verifyDocument.content[firstSlideKey], null, 2))
          }

          // Compare with what we tried to save
          const expectedFirstSlideKey = Object.keys(updatedContent).filter(k => k.startsWith('Slide'))[0]
          if (expectedFirstSlideKey && updatedContent[expectedFirstSlideKey]) {
            const savedContent = verifyDocument.content[expectedFirstSlideKey]
            const expectedContent = updatedContent[expectedFirstSlideKey]
            const contentMatches = JSON.stringify(savedContent) === JSON.stringify(expectedContent)
            console.log('Edit JSON API - Verification: Content matches expected:', contentMatches)
            if (!contentMatches) {
              console.log('Edit JSON API - Verification: Expected content:', JSON.stringify(expectedContent, null, 2))
              console.log('Edit JSON API - Verification: Actual saved content:', JSON.stringify(savedContent, null, 2))
            }
          }
        } else {
          console.log('Edit JSON API - Warning: Could not verify document update')
        }

        return NextResponse.json({
          success: true,
          modifiedCount: result.modifiedCount,
          message: 'Slides updated successfully in content.slides'
        })
      } catch (error) {
        console.error('Edit JSON API - Error in simple slide update:', error)
        const errorMessage = error instanceof Error ? error.message : 'Unknown error'
        return NextResponse.json({ error: 'Failed to update slides: ' + errorMessage }, { status: 500 })
      }
    }

    // Handle versioned updates (legacy)
    if (!documentId || !updatedData) {
      return NextResponse.json({ error: 'Document ID and updated data are required' }, { status: 400 })
    }

    await client.connect()
    const db = client.db('math-lesson-slider')
    const collection = db.collection('jsondocuments')

    // Get the current document
    const currentDoc = await collection.findOne({ 
      $or: [
        { _id: documentId, isCurrent: { $ne: false } },
        { originalDocumentId: documentId, isCurrent: true }
      ]
    })
    
    if (!currentDoc) {
      return NextResponse.json({ error: 'Document not found' }, { status: 404 })
    }

    // Mark current document as not current
    await collection.updateOne(
      { _id: currentDoc._id },
      { 
        $set: { 
          isCurrent: false,
          archivedAt: new Date()
        }
      }
    )

    // Create new version with updated data
    const originalDocumentId = currentDoc.originalDocumentId || documentId
    const newVersionId = `${originalDocumentId}_v${Date.now()}`
    
    const newVersion = {
      ...updatedData,
      _id: newVersionId,
      originalDocumentId: originalDocumentId,
      isCurrent: true,
      createdAt: new Date(),
      changeDescription: changeDescription || 'Manual edit',
      version: (currentDoc.version || 1) + 1,
      editedBy: 'admin' // TODO: Get from session
    }

    await collection.insertOne(newVersion)

    return NextResponse.json({ 
      success: true, 
      newVersionId: newVersion._id,
      version: newVersion.version
    })
  } catch (error) {
    console.error('Error creating document version:', error)
    return NextResponse.json({ error: 'Failed to create version' }, { status: 500 })
  } finally {
    await client.close()
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { versionId } = body

    if (!versionId) {
      return NextResponse.json({ error: 'Version ID is required' }, { status: 400 })
    }

    await client.connect()
    const db = client.db('math-lesson-slider')
    const collection = db.collection('jsondocuments')

    // Get the version to restore
    const versionToRestore = await collection.findOne({ _id: versionId as any })
    if (!versionToRestore) {
      return NextResponse.json({ error: 'Version not found' }, { status: 404 })
    }

    const originalDocumentId = versionToRestore.originalDocumentId || versionId

    // Mark all versions as not current
    await collection.updateMany(
      {
        $or: [
          { _id: originalDocumentId as any },
          { originalDocumentId: originalDocumentId }
        ]
      },
      { $set: { isCurrent: false } }
    )

    // Mark selected version as current
    await collection.updateOne(
      { _id: versionId as any },
      { $set: { isCurrent: true } }
    )

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error restoring version:', error)
    return NextResponse.json({ error: 'Failed to restore version' }, { status: 500 })
  } finally {
    await client.close()
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json()
    const { versionId } = body

    if (!versionId) {
      return NextResponse.json({ error: 'Version ID is required' }, { status: 400 })
    }

    await client.connect()
    const db = client.db('math-lesson-slider')
    const collection = db.collection('jsondocuments')

    // Get the version to delete
    const versionToDelete = await collection.findOne({ _id: versionId as any })
    if (!versionToDelete) {
      return NextResponse.json({ error: 'Version not found' }, { status: 404 })
    }

    // Don't allow deletion of current version
    if (versionToDelete.isCurrent) {
      return NextResponse.json({ error: 'Cannot delete current version' }, { status: 400 })
    }

    // Don't allow deletion of original document
    if (!versionToDelete.originalDocumentId) {
      return NextResponse.json({ error: 'Cannot delete original document' }, { status: 400 })
    }

    // Delete the version
    await collection.deleteOne({ _id: versionId as any })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting version:', error)
    return NextResponse.json({ error: 'Failed to delete version' }, { status: 500 })
  } finally {
    await client.close()
  }
}
