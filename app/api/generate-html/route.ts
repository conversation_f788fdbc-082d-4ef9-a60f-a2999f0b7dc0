import { NextRequest, NextResponse } from 'next/server'
import { GoogleGenerativeAI } from '@google/generative-ai'
import { GoogleAIFileManager } from '@google/generative-ai/server'
import connectToDatabase from '@/lib/mongodb'
import Settings from '@/models/Settings'
import ReferenceFile from '@/models/ReferenceFile'
import fs from 'fs'
import path from 'path'

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || '')
const fileManager = new GoogleAIFileManager(process.env.GEMINI_API_KEY || '')

// Cache for uploaded files URIs
let uploadedFilesCache: any = null

// Function to clear cache (for development)
function clearCache() {
  uploadedFilesCache = null
  console.log('File cache cleared')
}

// Upload helper files to Gemini File API
async function uploadHelperFiles() {
  // Return cached version if available
  if (uploadedFilesCache) {
    console.log('Using cached uploaded files')
    return uploadedFilesCache
  }

  try {
    console.log('Uploading helper files to Gemini File API...')
    const helpersDir = path.join(process.cwd(), 'helpers_html_generator')

    // Upload files to Gemini File API
    const clockFile = await fileManager.uploadFile(
      path.join(helpersDir, 'Clock.html'),
      {
        mimeType: 'text/html',
        displayName: 'Clock Example HTML'
      }
    )

    const mathToolsFile = await fileManager.uploadFile(
      path.join(helpersDir, 'Math Tools.html'),
      {
        mimeType: 'text/html',
        displayName: 'Math Tools Reference HTML'
      }
    )

    const exampleFile = await fileManager.uploadFile(
      path.join(helpersDir, 'Updated_html_for_prompt_to_include_clocks.html'),
      {
        mimeType: 'text/html',
        displayName: 'Interactive Example HTML'
      }
    )

    const result = {
      clockFile: clockFile.file,
      mathToolsFile: mathToolsFile.file,
      exampleFile: exampleFile.file
    }

    console.log('Helper files uploaded successfully:', {
      clock: clockFile.file.uri,
      mathTools: mathToolsFile.file.uri,
      example: exampleFile.file.uri
    })

    // Cache the result
    uploadedFilesCache = result
    return result
  } catch (error) {
    console.error('Error uploading helper files:', error)
    // Fallback to text-based approach
    return loadHelperFilesAsText()
  }
}

// Fallback: Load helper files as text (old approach)
function loadHelperFilesAsText() {
  try {
    console.log('Fallback: Loading helper files as text...')
    const helpersDir = path.join(process.cwd(), 'helpers_html_generator')

    const clockExample = fs.readFileSync(path.join(helpersDir, 'Clock.html'), 'utf-8')
    const mathToolsExample = fs.readFileSync(path.join(helpersDir, 'Math Tools.html'), 'utf-8')
    const updatedHtml = fs.readFileSync(path.join(helpersDir, 'Updated_html_for_prompt_to_include_clocks.html'), 'utf-8')

    return {
      clockExample: clockExample.substring(0, 2000),
      mathToolsExample: mathToolsExample.substring(0, 3000),
      updatedHtml: updatedHtml.substring(0, 4000),
      isTextBased: true
    }
  } catch (error) {
    console.error('Error loading helper files as text:', error)
    return {
      clockExample: '',
      mathToolsExample: '',
      updatedHtml: '',
      isTextBased: true
    }
  }
}

// Upload reference files to Gemini File API
async function uploadReferenceFiles() {
  try {
    await connectToDatabase()

    const goodReferences = await ReferenceFile.find({ type: 'good' }).sort({ uploadedAt: -1 })
    const badReferences = await ReferenceFile.find({ type: 'bad' }).sort({ uploadedAt: -1 })

    console.log(`Found ${goodReferences.length} good references and ${badReferences.length} bad references`)

    const uploadedGoodFiles = []
    const uploadedBadFiles = []

    // Upload good reference files
    for (const ref of goodReferences) {
      try {
        console.log(`Uploading good reference: ${ref.filename}`)

        // Create a temporary file in /tmp (Vercel serverless compatible)
        const tempFilePath = path.join('/tmp', `good_${ref._id}.html`)

        // Ensure temp directory exists
        const tempDir = path.dirname(tempFilePath)
        if (!fs.existsSync(tempDir)) {
          fs.mkdirSync(tempDir, { recursive: true })
        }

        // Write content to temp file
        fs.writeFileSync(tempFilePath, ref.content)

        // Upload to Gemini File API
        const uploadedFile = await fileManager.uploadFile(tempFilePath, {
          mimeType: 'text/html',
          displayName: `Good Example: ${ref.filename}`
        })

        uploadedGoodFiles.push({
          filename: ref.filename,
          description: ref.description,
          fileUri: uploadedFile.file.uri
        })

        // Clean up temp file
        fs.unlinkSync(tempFilePath)

      } catch (error) {
        console.error(`Error uploading good reference ${ref.filename}:`, error)
      }
    }

    // Upload bad reference files
    for (const ref of badReferences) {
      try {
        console.log(`Uploading bad reference: ${ref.filename}`)

        // Create a temporary file in /tmp (Vercel serverless compatible)
        const tempFilePath = path.join('/tmp', `bad_${ref._id}.html`)

        // Ensure temp directory exists
        const tempDir = path.dirname(tempFilePath)
        if (!fs.existsSync(tempDir)) {
          fs.mkdirSync(tempDir, { recursive: true })
        }

        // Write content to temp file
        fs.writeFileSync(tempFilePath, ref.content)

        // Upload to Gemini File API
        const uploadedFile = await fileManager.uploadFile(tempFilePath, {
          mimeType: 'text/html',
          displayName: `Bad Example: ${ref.filename}`
        })

        uploadedBadFiles.push({
          filename: ref.filename,
          description: ref.description,
          fileUri: uploadedFile.file.uri
        })

        // Clean up temp file
        fs.unlinkSync(tempFilePath)

      } catch (error) {
        console.error(`Error uploading bad reference ${ref.filename}:`, error)
      }
    }

    return {
      goodReferences: uploadedGoodFiles,
      badReferences: uploadedBadFiles
    }
  } catch (error) {
    console.error('Error uploading reference files:', error)
    return {
      goodReferences: [],
      badReferences: []
    }
  }
}

// Get settings from database
async function getSettings() {
  try {
    await connectToDatabase()

    const styleGuideSetting = await Settings.findOne({ key: 'STYLE_GUIDE' })

    return {
      styleGuide: styleGuideSetting?.value || DEFAULT_STYLE_GUIDE
    }
  } catch (error) {
    console.error('Error loading settings, using defaults:', error)
    return {
      styleGuide: DEFAULT_STYLE_GUIDE
    }
  }
}

// Default style guide (fallback)
const DEFAULT_STYLE_GUIDE = ``;

export async function POST(request: NextRequest) {
  try {
    const { prompt, slideNumber, unitNumber, lessonNumber, gradeLevel, curriculum, lang } = await request.json()

    if (!prompt) {
      return NextResponse.json({ error: 'Prompt is required' }, { status: 400 })
    }

    if (!process.env.GEMINI_API_KEY) {
      return NextResponse.json({ error: 'Gemini API key not configured' }, { status: 500 })
    }

    // Use configurable Gemini model from environment variable
    let model
    let modelName = ''

    // Get model from environment variable, default to gemini-2.0-flash-exp
    const selectedModel = process.env.GEMINI_MODEL || 'gemini-2.0-flash-exp'

    // Model configurations
    const modelConfigs: Record<string, { maxOutputTokens: number; description: string }> = {
      'gemini-2.0-flash-exp': {
        maxOutputTokens: 8192,
        description: 'Gemini 2.0 Flash Experimental'
      },
      'gemini-2.5-flash-preview-05-20': {
        maxOutputTokens: 32768,
        description: 'Gemini 2.5 Flash Preview'
      }
    }

    const config = modelConfigs[selectedModel]
    if (!config) {
      return NextResponse.json(
        {
          error: `Unsupported model: ${selectedModel}`,
          suggestion: `Supported models: ${Object.keys(modelConfigs).join(', ')}`,
          details: 'Check GEMINI_MODEL environment variable'
        },
        { status: 400 }
      )
    }

    try {
      model = genAI.getGenerativeModel({
        model: selectedModel,
        generationConfig: {
          temperature: 0.7, // Balanced creativity and coherence
          topP: 0.9,
          topK: 40,
          maxOutputTokens: config.maxOutputTokens
        }
      })
      modelName = selectedModel
      console.log(`Using ${selectedModel} (${config.description}) with max tokens:`, config.maxOutputTokens)
    } catch (error) {
      console.error(`${selectedModel} failed:`, error)
      return NextResponse.json(
        {
          error: `${config.description} model not available. Please check your API key.`,
          suggestion: 'Visit /api/list-models to see available models',
          details: `Tried: ${selectedModel}`
        },
        { status: 500 }
      )
    }

    // Load settings and upload reference files to Gemini File API
    const settings = await getSettings()
    const referenceFiles = await uploadReferenceFiles()
    console.log('Loaded settings from database')
    console.log('Loaded reference files:', {
      goodReferences: referenceFiles.goodReferences.length,
      badReferences: referenceFiles.badReferences.length
    })

    // Clear cache to force re-upload of updated files
    clearCache()

    // Upload helper files to Gemini File API
    const helperFiles = await uploadHelperFiles()

    // Build reference examples section
    let referenceExamplesSection = `
REFERENCE EXAMPLES:
Clock Example: ${helperFiles.clockExample}

Math Tools Reference: ${helperFiles.mathToolsExample}

Interactive Example: ${helperFiles.updatedHtml}`

    // Reference files are now passed as actual files through fileParts
    // Gemini will automatically have access to their content
    if (referenceFiles.goodReferences.length > 0 || referenceFiles.badReferences.length > 0) {
      referenceExamplesSection += `

REFERENCE EXAMPLES:
I have provided ${referenceFiles.goodReferences.length} good example(s) and ${referenceFiles.badReferences.length} bad example(s) as uploaded files. Please use the good examples as templates to follow and avoid the patterns shown in the bad examples.`
    }

    let fullPrompt: string
    let fileParts: any[] = []

    if (helperFiles.isTextBased) {
      // Fallback to text-based approach
      console.log('Using text-based helper files')
      fullPrompt = `Create HTML for: ${prompt}

CRITICAL IFRAME CONTAINER REQUIREMENTS:
- Your HTML will be displayed in an iframe with 16:9 aspect ratio
- Design for 800px width × 450px height as base container size
- NEVER use 100vh or 100vw - use percentage heights relative to container
- Body: height: 100%; margin: 0; padding: 20px; box-sizing: border-box;
- Grid container: max-height: calc(100% - 40px) to fit within iframe
- All content MUST be visible without scrolling in slide view

CRITICAL RESPONSIVE REQUIREMENTS:
- NEVER use fixed viewport units without constraints
- ALWAYS add max-width: 100% to prevent horizontal scrolling
- Use min-width/min-height to prevent elements from becoming too small
- Add media queries for screens < 768px to reduce grid columns if needed
- Ensure NO element overlap or collision on small screens (test at 320px width)
- Use clamp() for responsive font sizes: clamp(16px, 4vw, 48px)
- Grid gaps should use relative units (%, em, rem) not fixed pixels

STYLE GUIDE: ${settings.styleGuide}
${referenceExamplesSection}

Use the reference examples as templates. Include interactive elements with click handlers when appropriate. Return complete HTML starting with <!DOCTYPE html>`
    } else {
      // Use uploaded files as context
      console.log('Using uploaded files as context')

      // Build additional reference section for uploaded files context
      let additionalReferencesSection = ''

      // Reference files are passed as actual files, not text
      if (referenceFiles.goodReferences.length > 0 || referenceFiles.badReferences.length > 0) {
        additionalReferencesSection += `

REFERENCE EXAMPLES:
Use the uploaded reference files as examples. Follow the patterns from good examples and avoid the patterns from bad examples.`
      }

      fullPrompt = `Create HTML for: ${prompt}

CRITICAL IFRAME CONTAINER REQUIREMENTS:
- Your HTML will be displayed in an iframe with 16:9 aspect ratio
- Design for 800px width × 450px height as base container size
- NEVER use 100vh or 100vw - use percentage heights relative to container
- Body: height: 100%; margin: 0; padding: 20px; box-sizing: border-box;
- Grid container: max-height: calc(100% - 40px) to fit within iframe
- All content MUST be visible without scrolling in slide view

CRITICAL RESPONSIVE REQUIREMENTS:
- NEVER use fixed viewport units without constraints
- ALWAYS add max-width: 100% to prevent horizontal scrolling
- Use min-width/min-height to prevent elements from becoming too small
- Add media queries for screens < 768px to reduce grid columns if needed
- Ensure NO element overlap or collision on small screens (test at 320px width)
- Use clamp() for responsive font sizes: clamp(16px, 4vw, 48px)
- Grid gaps should use relative units (%, em, rem) not fixed pixels

STYLE GUIDE: ${settings.styleGuide}

Please refer to the uploaded files for:
- Clock Example HTML (see uploaded file)
- Math Tools Reference HTML (see uploaded file)
- Interactive Example HTML (see uploaded file)
${additionalReferencesSection}

Use the reference files as templates. Include interactive elements with click handlers when appropriate. Return complete HTML starting with <!DOCTYPE html>`

      // Add file parts for Gemini
      fileParts = [
        {
          fileData: {
            mimeType: helperFiles.clockFile.mimeType,
            fileUri: helperFiles.clockFile.uri
          }
        },
        {
          fileData: {
            mimeType: helperFiles.mathToolsFile.mimeType,
            fileUri: helperFiles.mathToolsFile.uri
          }
        },
        {
          fileData: {
            mimeType: helperFiles.exampleFile.mimeType,
            fileUri: helperFiles.exampleFile.uri
          }
        }
      ]

      // Add reference files to fileParts
      referenceFiles.goodReferences.forEach((ref: any) => {
        fileParts.push({
          fileData: {
            mimeType: 'text/html',
            fileUri: ref.fileUri
          }
        })
      })

      referenceFiles.badReferences.forEach((ref: any) => {
        fileParts.push({
          fileData: {
            mimeType: 'text/html',
            fileUri: ref.fileUri
          }
        })
      })
    }

    console.log('Prompt length:', fullPrompt.length, 'characters')
    console.log('Estimated input tokens:', Math.ceil(fullPrompt.length / 4)) // Rough estimate
    console.log('Total files being sent to Gemini:', fileParts.length)
    console.log('Helper files: 3 (clock, mathTools, example)')
    console.log('Reference files uploaded:', {
      good: referenceFiles.goodReferences.length,
      bad: referenceFiles.badReferences.length
    })
    console.log('Reference file URIs:', {
      good: referenceFiles.goodReferences.map((ref: any) => ({ filename: ref.filename, uri: ref.fileUri })),
      bad: referenceFiles.badReferences.map((ref: any) => ({ filename: ref.filename, uri: ref.fileUri }))
    })

    let result

    // Retry logic with exponential backoff for rate limiting
    const maxRetries = 3
    let retryCount = 0

    while (retryCount <= maxRetries) {
      try {
        if (fileParts.length > 0) {
          // Use files + text prompt
          console.log(`Generating content with uploaded files and text prompt (attempt ${retryCount + 1}/${maxRetries + 1})`)
          result = await model.generateContent([
            { text: fullPrompt },
            ...fileParts
          ])
        } else {
          // Use text-only prompt
          console.log(`Generating content with text-only prompt (attempt ${retryCount + 1}/${maxRetries + 1})`)
          result = await model.generateContent(fullPrompt)
        }

        // If successful, break out of retry loop
        break

      } catch (error: any) {
        console.error(`Attempt ${retryCount + 1} failed:`, error)

        // Check if it's a rate limit error (429)
        if (error.status === 429 || error.message?.includes('429') || error.message?.includes('quota')) {
          retryCount++

          if (retryCount <= maxRetries) {
            // Exponential backoff: 2^retryCount * 15 seconds + random jitter
            const baseDelay = Math.pow(2, retryCount) * 30000 // 60s, 120s, 240s (doubled from 15s to 30s)
            const jitter = Math.random() * 10000 // 0-10s random (increased from 5s)
            const delay = baseDelay + jitter

            console.log(`Rate limit hit. Waiting ${Math.round(delay/1000)}s before retry ${retryCount}/${maxRetries}...`)
            await new Promise(resolve => setTimeout(resolve, delay))
            continue
          }
        }

        // If not a rate limit error or max retries exceeded, throw the error
        throw error
      }
    }

    if (!result) {
      throw new Error('Failed to generate content after all retry attempts')
    }

    const response = result.response

    console.log('=== RESPONSE STRUCTURE DEBUG ===')
    console.log('result keys:', Object.keys(result))
    console.log('response keys:', Object.keys(response))
    console.log('response.candidates:', response.candidates)
    console.log('response.candidates[0]:', response.candidates?.[0])
    console.log('response.candidates[0].content:', response.candidates?.[0]?.content)
    console.log('response.candidates[0].content.parts:', response.candidates?.[0]?.content?.parts)

    // Try different ways to get the text
    let generatedHtml = ''

    try {
      // Method 1: Standard text() method
      generatedHtml = response.text()
      console.log('Method 1 (response.text()) - length:', generatedHtml.length)
    } catch (error) {
      console.log('Method 1 failed:', error instanceof Error ? error.message : String(error))
    }

    if (!generatedHtml && response.candidates?.[0]?.content?.parts?.[0]?.text) {
      // Method 2: Direct access to parts
      generatedHtml = response.candidates[0].content.parts[0].text
      console.log('Method 2 (direct parts access) - length:', generatedHtml.length)
    }

    if (!generatedHtml && response.candidates?.[0]?.content?.parts) {
      // Method 3: Concatenate all parts
      generatedHtml = response.candidates[0].content.parts
        .map((part: any) => part.text || '')
        .join('')
      console.log('Method 3 (concatenate parts) - length:', generatedHtml.length)
    }

    console.log('=== HTML GENERATION DEBUG ===')
    console.log('Final generatedHtml length:', generatedHtml.length)
    console.log('Raw generated HTML (first 500 chars):', generatedHtml.substring(0, 500))
    console.log('Raw generated HTML (last 500 chars):', generatedHtml.substring(Math.max(0, generatedHtml.length - 500)))

    // Check if we got any content
    if (!generatedHtml || generatedHtml.trim().length === 0) {
      console.error('No HTML content generated!')
      console.log('Full response object:', JSON.stringify(response, null, 2))

      // Check if it's a MAX_TOKENS issue
      if (response.candidates?.[0]?.finishReason === 'MAX_TOKENS') {
        throw new Error('AI model reached maximum token limit. Try with a shorter prompt or simpler requirements.')
      }

      throw new Error('No HTML content was generated by the AI model')
    }

    // Enhanced cleanup and validation
    let cleanHtml = generatedHtml
      .replace(/```html/g, '')
      .replace(/```/g, '')
      .replace(/^[^<]*/, '') // Remove any text before the first HTML tag
      .replace(/[^>]*$/, '') // Remove any text after the last HTML tag
      .trim()

    console.log('After initial cleanup - length:', cleanHtml.length)
    console.log('After initial cleanup (first 300 chars):', cleanHtml.substring(0, 300))

    // Ensure it starts with DOCTYPE
    if (!cleanHtml.startsWith('<!DOCTYPE html>')) {
      console.log('HTML does not start with DOCTYPE, looking for it...')
      // Find the start of HTML content
      const htmlStart = cleanHtml.indexOf('<!DOCTYPE html>')
      if (htmlStart > 0) {
        console.log('Found DOCTYPE at position:', htmlStart)
        cleanHtml = cleanHtml.substring(htmlStart)
      } else {
        console.log('No DOCTYPE found in content')
      }
    }

    console.log('After DOCTYPE processing - length:', cleanHtml.length)
    console.log('After DOCTYPE processing (first 200 chars):', cleanHtml.substring(0, 200))

    // Check for basic HTML structure
    const hasDoctype = cleanHtml.includes('<!DOCTYPE html>')
    const hasHtmlTag = cleanHtml.includes('<html')
    const hasBodyTag = cleanHtml.includes('<body')
    const hasCanvas = true
    // const hasCanvas = cleanHtml.includes('<canvas') || cleanHtml.includes('canvas')

    console.log('Validation checks:')
    console.log('- Has DOCTYPE:', hasDoctype)
    console.log('- Has <html> tag:', hasHtmlTag)
    console.log('- Has <body> tag:', hasBodyTag)
    console.log('- Has canvas:', hasCanvas)

    // More flexible validation - just check if it looks like HTML
    if (!hasHtmlTag || !hasBodyTag) {
      console.error('Generated content is missing basic HTML structure')
      throw new Error('Generated HTML is invalid or incomplete - missing basic HTML structure')
    }

    // Additional validation to ensure no explanatory text
    const lines = cleanHtml.split('\n')
    console.log('Total lines before filtering:', lines.length)

    const filteredLines = lines.filter(line => {
      const trimmed = line.trim()
      // Keep HTML tags, empty lines, and content within tags
      return trimmed === '' ||
             trimmed.startsWith('<') ||
             trimmed.includes('<') ||
             !trimmed.includes('Key improvements') &&
             !trimmed.includes('adherence to') &&
             !trimmed.includes('Canvas Setup') &&
             !trimmed.includes('Visual Elements')
    })

    console.log('Total lines after filtering:', filteredLines.length)
    cleanHtml = filteredLines.join('\n').trim()
    console.log('Final HTML length:', cleanHtml.length)
    console.log('Final HTML (first 200 chars):', cleanHtml.substring(0, 200))

    console.log('High-quality HTML generated successfully:', {
      prompt: prompt.substring(0, 100) + '...',
      slideNumber,
      unitNumber,
      lessonNumber,
      gradeLevel,
      curriculum,
      lang,
      htmlLength: cleanHtml.length
    })

    return NextResponse.json({
      success: true,
      html: cleanHtml,
      metadata: {
        slideNumber,
        unitNumber,
        lessonNumber,
        gradeLevel,
        curriculum,
        lang,
        generatedAt: new Date().toISOString(),
        modelVersion: modelName
      }
    })

  } catch (error) {
    console.error('Error generating HTML with Gemini:', error)

    if (error instanceof Error) {
      if (error.message.includes('API_KEY')) {
        return NextResponse.json({ error: 'Invalid Gemini API key' }, { status: 401 })
      }
      if (error.message.includes('QUOTA')) {
        return NextResponse.json({ error: 'Gemini API quota exceeded' }, { status: 429 })
      }
      if (error.message.includes('models/') && error.message.includes('not found')) {
        return NextResponse.json({
          error: 'Gemini model not available. Please check your API key has access to the latest models.',
          suggestion: 'Visit /api/list-models to see available models'
        }, { status: 404 })
      }
    }

    return NextResponse.json({ error: 'Failed to generate HTML content' }, { status: 500 })
  }
}

// GET endpoint for testing
export async function GET() {
  return NextResponse.json({
    message: 'Gemini HTML Generator API',
    endpoints: {
      POST: '/api/generate-html',
      description: 'Generate HTML content from text prompts using Gemini AI'
    },
    requiredFields: ['prompt'],
    optionalFields: ['slideNumber', 'unitNumber', 'lessonNumber', 'gradeLevel'],
    example: {
      prompt: 'Create a math problem about fractions for grade 3',
      slideNumber: 5,
      unitNumber: 2,
      lessonNumber: 3,
      gradeLevel: 'Grade 3'
    }
  })
}
