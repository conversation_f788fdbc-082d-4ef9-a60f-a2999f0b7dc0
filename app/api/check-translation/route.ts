import { NextRequest, NextResponse } from 'next/server';
import connectToDatabase from '@/lib/mongodb';
import JsonDocument from '@/models/JsonDocument';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const unit_number = searchParams.get('unit_number');
    const lesson_number = searchParams.get('lesson_number');
    const grade_level = searchParams.get('grade_level');
    const lang = searchParams.get('lang') || 'esp';

    if (!unit_number || !lesson_number || !grade_level) {
      return NextResponse.json(
        { error: 'Missing required parameters: unit_number, lesson_number, grade_level' },
        { status: 400 }
      );
    }

    await connectToDatabase();

    // Create document ID (base format for English, with -esp suffix for Spanish)
    const baseDocumentId = `${unit_number}-${lesson_number}-${grade_level}`;
    const englishDocumentId = baseDocumentId;
    const spanishDocumentId = `${baseDocumentId}-esp`;

    console.log('Checking translation availability for:', { baseDocumentId, lang });

    // Check if English version exists
    const englishDocument = await JsonDocument.findOne({ documentId: englishDocumentId, lang: 'en' });

    // Check if Spanish version exists
    const spanishDocument = await JsonDocument.findOne({ documentId: spanishDocumentId, lang: 'esp' });

    // Determine if the requested language exists and if translation is available
    const exists = lang === 'en' ? !!englishDocument : !!spanishDocument;
    const hasTranslation = lang === 'en' ? !!spanishDocument : !!englishDocument;

    console.log('Translation check results:', {
      englishExists: !!englishDocument,
      spanishExists: !!spanishDocument,
      requestedLang: lang,
      exists,
      hasTranslation
    });

    return NextResponse.json({
      documentId: baseDocumentId,
      lang,
      exists,
      hasTranslation
    });

  } catch (error) {
    console.error('Error checking translation:', error);
    return NextResponse.json(
      { error: 'Failed to check translation availability' },
      { status: 500 }
    );
  }
}
