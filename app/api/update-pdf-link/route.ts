import { NextRequest, NextResponse } from 'next/server'
import { getToken } from 'next-auth/jwt'
import connectToDatabase from '@/lib/mongodb'
import JsonDocument from '@/models/JsonDocument'

export async function POST(request: NextRequest) {
  try {
    // Check authentication and admin role
    const token = await getToken({ req: request, secret: process.env.NEXTAUTH_SECRET })
    
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin
    if (token.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const { documentId, slideType, pdfUrl } = await request.json()

    if (!documentId || !slideType || !pdfUrl) {
      return NextResponse.json({ 
        error: 'Document ID, slide type, and PDF URL are required' 
      }, { status: 400 })
    }

    if (!['practice', 'lesson_guide', 'accelerator'].includes(slideType)) {
      return NextResponse.json({ error: 'Invalid slide type' }, { status: 400 })
    }

    await connectToDatabase()

    // Find the document
    const document = await JsonDocument.findOne({ documentId })

    if (!document) {
      return NextResponse.json({ error: 'Document not found' }, { status: 404 })
    }

    // Determine which slide to update based on slide type
    const slideMapping = {
      'practice': 'Slide 26: Practice_PDF_Link',
      'lesson_guide': 'Slide 25: Lesson_Guide_PDF_Link', 
      'accelerator': 'Slide 27: Accelerator_PDF_Link'
    }

    const slideKey = slideMapping[slideType as keyof typeof slideMapping]
    
    // Determine the field name for the PDF link
    const fieldMapping = {
      'practice': 'link_to_practice_pdf',
      'lesson_guide': 'link_to_lesson_guide_pdf',
      'accelerator': 'link_to_accelerator_pdf'
    }

    const fieldName = fieldMapping[slideType as keyof typeof fieldMapping]

    // Update the document
    const updatePath = `content.${slideKey}.${fieldName}`
    
    const result = await JsonDocument.updateOne(
      { documentId },
      {
        $set: {
          [updatePath]: pdfUrl,
          updatedAt: new Date()
        }
      }
    )

    if (result.matchedCount === 0) {
      return NextResponse.json({ error: 'Document not found' }, { status: 404 })
    }

    console.log(`📄 PDF link updated successfully:`, {
      documentId,
      slideType,
      slideKey,
      fieldName,
      pdfUrl,
      modifiedCount: result.modifiedCount
    })

    return NextResponse.json({
      success: true,
      documentId,
      slideType,
      pdfUrl,
      modifiedCount: result.modifiedCount
    })

  } catch (error) {
    console.error('Error updating PDF link:', error)
    return NextResponse.json(
      { error: 'Failed to update PDF link' },
      { status: 500 }
    )
  }
}
