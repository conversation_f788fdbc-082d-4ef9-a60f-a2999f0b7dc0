import { NextRequest, NextResponse } from 'next/server'
import { connectWithRetry } from '@/lib/mongodb'
import Job from '@/models/Job'

// Emergency stop flag
let emergencyStopActive = false
let emergencyStopReason = ''
let emergencyStopTime: Date | null = null

export async function GET(request: NextRequest) {
  return NextResponse.json({
    emergencyStopActive,
    emergencyStopReason,
    emergencyStopTime,
    message: emergencyStopActive ? 
      'Emergency stop is ACTIVE - job processing is paused' : 
      'Emergency stop is INACTIVE - job processing is normal'
  })
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, reason = 'Manual emergency stop' } = body

    if (action === 'activate') {
      emergencyStopActive = true
      emergencyStopReason = reason
      emergencyStopTime = new Date()
      
      console.warn(`🚨 EMERGENCY STOP ACTIVATED: ${reason}`)
      
      // Optionally pause all processing jobs
      try {
        await connectWithRetry()
        await Job.updateMany(
          { status: 'processing' },
          { 
            $set: { 
              status: 'pending',
              errorMessage: `Emergency stop: ${reason}`,
              processedAt: new Date()
            }
          }
        )
        console.log('📋 All processing jobs reset to pending due to emergency stop')
      } catch (dbError) {
        console.error('❌ Failed to reset processing jobs:', dbError)
      }
      
      return NextResponse.json({
        message: 'Emergency stop activated',
        emergencyStopActive: true,
        reason,
        time: emergencyStopTime
      })
      
    } else if (action === 'deactivate') {
      emergencyStopActive = false
      emergencyStopReason = ''
      emergencyStopTime = null
      
      console.log('✅ Emergency stop deactivated - normal processing resumed')
      
      return NextResponse.json({
        message: 'Emergency stop deactivated',
        emergencyStopActive: false
      })
      
    } else {
      return NextResponse.json({
        error: 'Invalid action. Use "activate" or "deactivate"'
      }, { status: 400 })
    }
    
  } catch (error) {
    return NextResponse.json({
      error: 'Failed to update emergency stop status',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// Export the emergency stop status for other modules
export function isEmergencyStopActive(): boolean {
  return emergencyStopActive
}

export function getEmergencyStopStatus() {
  return {
    active: emergencyStopActive,
    reason: emergencyStopReason,
    time: emergencyStopTime
  }
}
