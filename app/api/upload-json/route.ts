import { NextRequest, NextResponse } from 'next/server';
import connectToDatabase from '@/lib/mongodb';
import LessonSlide from '@/models/LessonSlide';
import { SlideType } from '@/types/slideTypes';

export async function POST(request: NextRequest) {
  try {
    // Parse the JSON data from the request
    const data = await request.json();

    if (!data || !Array.isArray(data.slides) || data.slides.length === 0) {
      return NextResponse.json(
        { message: 'Invalid data format. Expected an array of slides.' },
        { status: 400 }
      );
    }

    // Connect to the database
    await connectToDatabase();

    // Process each slide
    const results = [];

    for (const slide of data.slides) {
      // Validate required fields
      if (!slide.unit_number || !slide.lesson_number || !slide.grade_level || !slide.slide_number) {
        results.push({
          status: 'error',
          message: 'Missing required fields: unit_number, lesson_number, grade_level, slide_number',
          slide
        });
        continue;
      }

      // Make sure unit_title is set
      if (!slide.unit_title) {
        slide.unit_title = `Unit ${slide.unit_number}`;
      }

      // Make sure slide has a valid type
      if (!slide.type) {
        // Try to determine type from slide_pedagogical_name
        if (slide.slide_pedagogical_name) {
          const name = slide.slide_pedagogical_name.toLowerCase();
          if (name.includes('quick review')) {
            slide.slide_type = SlideType.INTRODUCTION;
          } else if (name.includes('learning goals')) {
            slide.slide_type = SlideType.INTRODUCTION;
          } else if (name.includes('vocabulary')) {
            slide.slide_type = SlideType.INTRODUCTION;
          } else if (name.includes('hook')) {
            slide.slide_type = SlideType.HOOK;
          } else if (name.includes('teach 1')) {
            slide.slide_type = SlideType.TEACH_1;
          } else if (name.includes('talk 1')) {
            slide.slide_type = SlideType.TALK_1;
          } else if (name.includes('try 1')) {
            slide.slide_type = SlideType.TRY_1;
          } else if (name.includes('teach 2')) {
            slide.slide_type = SlideType.TEACH_2;
          } else if (name.includes('talk 2')) {
            slide.slide_type = SlideType.TALK_2;
          } else if (name.includes('try 2')) {
            slide.slide_type = SlideType.TRY_2;
          } else if (name.includes('practice')) {
            slide.slide_type = SlideType.PRACTICE;
          } else if (name.includes('on ramp')) {
            slide.slide_type = SlideType.ON_RAMP;
          } else if (name.includes('pdf') || name.includes('printable')) {
            slide.slide_type = SlideType.PRINTABLES;
          } else {
            // Default to INTRODUCTION if we can't determine
            slide.slide_type = SlideType.INTRODUCTION;
          }
        } else {
          // Default to INTRODUCTION if no slide_pedagogical_name
          slide.slide_type = SlideType.INTRODUCTION;
        }
      } else {
        // Map the type string to SlideType enum
        slide.slide_type = slide.type;
      }

      // Create a unique ID for the slide
      const slideId = `${slide.unit_number}-${slide.lesson_number}-${slide.grade_level}`;

      try {
        // Check if the slide already exists
        const existingSlide = await LessonSlide.findOne({
          unit_number: slide.unit_number,
          lesson_number: slide.lesson_number,
          grade_level: slide.grade_level,
          slide_number: slide.slide_number
        });

        if (existingSlide) {
          // Update the existing slide
          const updatedSlide = await LessonSlide.findByIdAndUpdate(
            existingSlide._id,
            { ...slide, updatedAt: new Date() },
            { new: true }
          );

          results.push({
            status: 'updated',
            message: `Slide ${slide.slide_number} updated successfully`,
            id: slideId,
            slide: updatedSlide
          });
        } else {
          // Create a new slide
          const newSlide = await LessonSlide.create({
            ...slide,
            createdAt: new Date(),
            updatedAt: new Date()
          });

          results.push({
            status: 'created',
            message: `Slide ${slide.slide_number} created successfully`,
            id: slideId,
            slide: newSlide
          });
        }
      } catch (error) {
        console.error('Error processing slide:', error);
        results.push({
          status: 'error',
          message: `Error processing slide ${slide.slide_number}: ${error.message}`,
          id: slideId,
          slide
        });
      }
    }

    // Return the results
    return NextResponse.json({
      message: 'Upload processed',
      results
    });
  } catch (error) {
    console.error('Error uploading JSON:', error);
    return NextResponse.json(
      { message: 'An error occurred while processing the upload', error: String(error) },
      { status: 500 }
    );
  }
}
