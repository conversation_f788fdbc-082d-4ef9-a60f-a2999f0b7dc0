import { NextResponse } from 'next/server';
import connectToDatabase from '@/lib/mongodb';
import JsonDocument from '@/models/JsonDocument';
import Job from '@/models/Job';

// GET handler for testing database connection
export async function GET(req: Request) {
  try {
    console.log('Testing database connection...');

    // Connect to the database
    await connectToDatabase();
    console.log('Database connection successful');

    // Count documents in the JsonDocument collection
    const documentCount = await JsonDocument.countDocuments({});
    console.log(`Total documents in JsonDocument collection: ${documentCount}`);

    // Count jobs in the Job collection
    const jobCount = await Job.countDocuments({});
    console.log(`Total jobs in Job collection: ${jobCount}`);

    // Get a sample document
    const sampleDocument = await JsonDocument.findOne({});

    // Get sample jobs
    const sampleJobs = await Job.find({}).limit(3).lean();
    console.log('Sample jobs:', sampleJobs);

    // Return success response
    return NextResponse.json({
      status: 'success',
      message: 'Database connection successful',
      documentCount,
      jobCount,
      sampleDocument: sampleDocument ? {
        id: sampleDocument._id,
        documentId: sampleDocument.documentId,
        unit_number: sampleDocument.unit_number,
        lesson_number: sampleDocument.lesson_number,
        grade_level: sampleDocument.grade_level
      } : null,
      sampleJobs: sampleJobs.map(job => ({
        jobId: job.jobId,
        documentId: job.documentId,
        slideNumber: job.slideNumber,
        status: job.status,
        createdAt: job.createdAt
      }))
    });
  } catch (error) {
    console.error('Error testing database connection:', error);
    return NextResponse.json(
      {
        status: 'error',
        message: 'An error occurred while testing database connection',
        error: String(error)
      },
      { status: 500 }
    );
  }
}
