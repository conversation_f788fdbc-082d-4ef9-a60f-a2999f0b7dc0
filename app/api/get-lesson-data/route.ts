import { NextRequest, NextResponse } from 'next/server'
import connectToDatabase from '@/lib/mongodb'
import JsonDocument from '@/models/JsonDocument'

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const documentId = searchParams.get('documentId')
    const lang = searchParams.get('lang') || 'en'

    if (!documentId) {
      return NextResponse.json({ error: 'Document ID is required' }, { status: 400 })
    }

    // Create the correct documentId based on language
    const actualDocumentId = lang === 'esp' ? `${documentId}-esp` : documentId;

    console.log('📄 Loading lesson data for baseDocumentId:', documentId, 'actualDocumentId:', actualDocumentId, 'lang:', lang)

    await connectToDatabase()

    // Find the document with language support
    const document = await JsonDocument.findOne({ documentId: actualDocumentId, lang })

    if (!document) {
      console.log('❌ Document not found:', actualDocumentId, 'lang:', lang)
      return NextResponse.json({ error: 'Document not found' }, { status: 404 })
    }

    console.log('✅ Document found:', {
      baseDocumentId: documentId,
      actualDocumentId,
      hasContent: !!document.content,
      contentKeys: document.content ? Object.keys(document.content) : []
    })

    return NextResponse.json({
      success: true,
      documentId: document.documentId,
      content: document.content,
      updatedAt: document.updatedAt
    })

  } catch (error) {
    console.error('❌ Error loading lesson data:', error)
    return NextResponse.json(
      { 
        error: 'Failed to load lesson data',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
