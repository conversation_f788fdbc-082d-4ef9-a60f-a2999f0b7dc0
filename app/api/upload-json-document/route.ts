import { NextRequest, NextResponse } from 'next/server';
import connectToDatabase from '@/lib/mongodb';
import JsonDocument from '@/models/JsonDocument';
import Job from '@/models/Job';
import { detectCurriculumType } from '@/types/curriculumTypes';

export async function POST(request: NextRequest) {
  try {
    // Parse the JSON data from the request
    const data = await request.json();

    if (!data || !data.documents || !Array.isArray(data.documents) || data.documents.length === 0) {
      return NextResponse.json(
        { message: 'Invalid data format. Expected an array of documents.' },
        { status: 400 }
      );
    }

    // Connect to the database
    await connectToDatabase();

    // Process each document
    const results = [];

    for (const doc of data.documents) {
      try {
        // Extract metadata from the document
        const { content, filename } = doc;

        // Extract required fields from content
        let unit_number = '1';
        let lesson_number = '1';
        let grade_level = 'Grade 1';
        let unit_title = null;
        let lesson_title = null;
        let lang = 'en';

        // Try to extract metadata from content
        if (content) {
          unit_number = content.unit_number || '1';
          lesson_number = content.lesson_number || '1';
          grade_level = content.grade_level || 'Grade 1';
          unit_title = content.unit_title || `Unit ${unit_number}`;
          lesson_title = content.lesson_title || `Lesson ${lesson_number}`;
          lang = content.lang || (content.lenguage === 'Spanish' ? 'esp' : 'en');

          // Process curriculum type
          if (content.common_core) {
            const detectedCurriculum = detectCurriculumType(content.common_core);
            content.common_core = detectedCurriculum;
          } else {
            // Default to CCSS if no common_core field
            content.common_core = 'CCSS';
          }

          console.log('Extracted metadata:', {
            unit_number,
            lesson_number,
            grade_level,
            unit_title,
            lesson_title,
            lang,
            common_core: content.common_core
          });
        }

        // Create a unique ID for the document
        const englishGradeLevel = lang === 'esp' ? grade_level.replace('Grado', 'Grade') : grade_level;
        const baseDocumentId = `${unit_number}-${lesson_number}-${englishGradeLevel}`;
        const documentId = lang === 'esp' ? `${baseDocumentId}-esp` : baseDocumentId;

        // Check if the document already exists (include lang in search for translations)
        const existingDocument = await JsonDocument.findOne({ documentId, lang });

        if (existingDocument) {
          // Update the existing document
          await JsonDocument.findByIdAndUpdate(
            existingDocument._id,
            {
              content,
              filename,
              unit_number,
              lesson_number,
              grade_level,
              unit_title,
              lesson_title,
              lang,
              updatedAt: new Date()
            },
            { new: true }
          );

          // Handle HTML content copying and job creation based on language
          let jobsCreated = 0;
          let htmlCopied = 0;
          if (lang === 'esp') {
            // For Spanish documents, copy HTML from English version before updating
            htmlCopied = await copyHtmlFromEnglishVersion(baseDocumentId, content);

            // Update the document with the modified content that now includes HTML
            await JsonDocument.findByIdAndUpdate(
              existingDocument._id,
              { content, updatedAt: new Date() }
            );
          } else {
            // For English documents, create jobs as usual
            jobsCreated = await createJobsForDocument(documentId, content, unit_number, lesson_number, grade_level);
          }

          results.push({
            status: 'updated',
            message: `Document ${documentId} (${lang}) updated successfully. ${lang === 'esp' ? `HTML copied from English version (${htmlCopied} slides).` : `${jobsCreated} jobs created.`}`,
            id: documentId,
            filename,
            jobsCreated: lang === 'esp' ? 0 : jobsCreated,
            htmlCopied: lang === 'esp' ? htmlCopied : 0
          });
        } else {
          // Create a new document
          await JsonDocument.create({
            documentId,
            content,
            filename,
            unit_number,
            lesson_number,
            grade_level,
            unit_title,
            lesson_title,
            lang,
            createdAt: new Date(),
            updatedAt: new Date()
          });

          // Handle HTML content copying and job creation based on language
          let jobsCreated = 0;
          let htmlCopied = 0;
          if (lang === 'esp') {
            // For Spanish documents, copy HTML from English version before saving
            htmlCopied = await copyHtmlFromEnglishVersion(baseDocumentId, content);

            // Update the document with the modified content that now includes HTML
            await JsonDocument.findOneAndUpdate(
              { documentId, lang: 'esp' },
              { content, updatedAt: new Date() }
            );
          } else {
            // For English documents, create jobs as usual
            jobsCreated = await createJobsForDocument(documentId, content, unit_number, lesson_number, grade_level);
          }

          results.push({
            status: 'created',
            message: `Document ${documentId} (${lang}) created successfully. ${lang === 'esp' ? `HTML copied from English version (${htmlCopied} slides).` : `${jobsCreated} jobs created.`}`,
            id: documentId,
            filename,
            jobsCreated: lang === 'esp' ? 0 : jobsCreated,
            htmlCopied: lang === 'esp' ? htmlCopied : 0
          });
        }
      } catch (error: any) {
        console.error('Error processing document:', error);
        results.push({
          status: 'error',
          message: `Error processing document: ${error.message}`,
          filename: doc.filename
        });
      }
    }

    // Return the results
    return NextResponse.json({
      message: 'Upload processed',
      results
    });
  } catch (error) {
    console.error('Error uploading JSON:', error);
    return NextResponse.json(
      { message: 'An error occurred while processing the upload', error: String(error) },
      { status: 500 }
    );
  }
}

// Function to create jobs for all slides in a document
async function createJobsForDocument(
  documentId: string,
  content: any,
  unitNumber: string,
  lessonNumber: string,
  gradeLevel: string
): Promise<number> {
  let jobsCreated = 0;

  try {
    // Delete existing jobs for this document to avoid duplicates
    await Job.deleteMany({ documentId });

    // Iterate through all slides in the content
    for (const [slideKey, slideData] of Object.entries(content)) {
      // Skip non-slide properties
      if (!slideKey.startsWith('Slide ') || typeof slideData !== 'object' || slideData === null) {
        continue;
      }

      const slide = slideData as any;

      // Extract slide information
      const slideNumber = parseInt(slideKey.match(/Slide (\d+)/)?.[1] || '0');
      const slideType = slide.type || 'UNKNOWN';

      // Only create job if slide has html_css_description_of_image field
      if (!slide.html_css_description_of_image ||
          typeof slide.html_css_description_of_image !== 'string' ||
          slide.html_css_description_of_image.trim().length === 0) {
        console.log(`Skipping slide ${slideNumber} - no html_css_description_of_image field`);
        continue;
      }

      // Check if it's already HTML (starts with < or contains HTML tags)
      const content = slide.html_css_description_of_image.trim();
      if (content.startsWith('<') || content.includes('</') || content.includes('<!DOCTYPE')) {
        console.log(`Skipping slide ${slideNumber} - already contains HTML content`);
        continue;
      }

      // Use html_css_description_of_image directly as the prompt
      const originalPrompt = slide.html_css_description_of_image.trim();

      // Create job
      const jobId = (Job as any).generateJobId(documentId, slideNumber);

      const job = new Job({
        jobId,
        documentId,
        unitNumber,
        lessonNumber,
        gradeLevel,
        slideNumber,
        slideKey,
        slideType,
        prompt: originalPrompt, // Use original description as main prompt
        originalPrompt: originalPrompt, // Keep copy for reference
        status: 'pending'
      });

      await job.save();
      jobsCreated++;

      console.log(`Created job for ${documentId}, slide ${slideNumber}: ${jobId}`);
    }

  } catch (error) {
    console.error('Error creating jobs:', error);
  }

  return jobsCreated;
}



// Function to copy HTML content from English version to Spanish version
async function copyHtmlFromEnglishVersion(
  baseDocumentId: string,
  spanishContent: any
): Promise<number> {
  let htmlCopied = 0;

  try {
    console.log(`Looking for English version of document: ${baseDocumentId}`);

    // Find the English version of the document (English version has no language suffix)
    const englishDocument = await JsonDocument.findOne({ documentId: baseDocumentId, lang: 'en' });

    if (!englishDocument) {
      console.log(`No English version found for ${baseDocumentId} to copy HTML from`);
      return 0;
    }

    console.log(`Found English version, copying HTML content...`);

    const englishContent = englishDocument.content;

    // Iterate through all slides in the Spanish content
    for (const [slideKey, slideData] of Object.entries(spanishContent)) {
      // Skip non-slide properties
      if (!slideKey.startsWith('Slide ') || typeof slideData !== 'object' || slideData === null) {
        continue;
      }

      const spanishSlide = slideData as any;
      const englishSlide = englishContent[slideKey];

      if (!englishSlide) {
        console.log(`No corresponding English slide found for ${slideKey}`);
        continue;
      }

      // Copy HTML content from English to Spanish slide
      if (englishSlide.html_css_description_of_image &&
          typeof englishSlide.html_css_description_of_image === 'string' &&
          englishSlide.html_css_description_of_image.trim().length > 0) {

        // Check if English slide has HTML content (starts with < or contains HTML tags)
        const englishHtmlContent = englishSlide.html_css_description_of_image.trim();
        if (englishHtmlContent.startsWith('<') || englishHtmlContent.includes('</') || englishHtmlContent.includes('<!DOCTYPE')) {
          // Copy the HTML content to Spanish slide
          spanishSlide.html_css_description_of_image = englishHtmlContent;
          htmlCopied++;
          console.log(`Copied HTML content from English to Spanish for ${slideKey}`);
        } else {
          // If English slide doesn't have HTML yet, copy the description
          if (!spanishSlide.html_css_description_of_image || spanishSlide.html_css_description_of_image.trim() === '') {
            spanishSlide.html_css_description_of_image = englishHtmlContent;
            console.log(`Copied description from English to Spanish for ${slideKey}`);
          }
        }
      }
    }

    // Note: We don't need to update the document here as it will be updated by the calling function

    console.log(`Copied HTML content for ${htmlCopied} slides from English to Spanish version of ${baseDocumentId}`);

  } catch (error) {
    console.error('Error copying HTML from English version:', error);
  }

  return htmlCopied;
}
