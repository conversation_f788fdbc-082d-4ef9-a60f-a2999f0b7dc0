import { NextRequest, NextResponse } from 'next/server'

// Test cron job endpoint - for local testing
export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing cron job manually...')

    // Import and call the cron function directly to avoid fetch issues
    const { GET: cronHandler } = await import('../process-jobs/route')

    // Create a mock request with the authorization header
    const mockRequest = new NextRequest('http://localhost:3000/api/cron/process-jobs', {
      headers: {
        'Authorization': `Bearer ${process.env.CRON_SECRET}`,
        'Content-Type': 'application/json',
      },
    })

    const cronResponse = await cronHandler(mockRequest)
    const data = await cronResponse.json()

    console.log('🧪 Cron test result:', data)

    return NextResponse.json({
      success: true,
      message: 'Cron job test completed',
      cronResponse: data,
      testInfo: {
        method: 'direct_import',
        timestamp: new Date().toISOString(),
        responseStatus: cronResponse.status
      }
    })

  } catch (error) {
    console.error('❌ Cron test error:', error)
    return NextResponse.json({
      success: false,
      error: 'Cron test failed: ' + (error instanceof Error ? error.message : 'Unknown error'),
      testInfo: {
        timestamp: new Date().toISOString()
      }
    }, { status: 500 })
  }
}

// POST method for manual testing
export async function POST(request: NextRequest) {
  console.log('🧪 Manual cron job test triggered via POST')
  return GET(request)
}
