import { NextRequest, NextResponse } from 'next/server'
import { connectWithRetry } from '@/lib/mongodb'
import Job from '@/models/Job'

// Smart cron job with processing limits and concurrency control
export async function GET(request: NextRequest) {
  try {
    // Verify this is a cron request (optional security check)
    const authHeader = request.headers.get('authorization')
    if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    await connectWithRetry()

    console.log('🕐 Smart Cron job started: Checking processing limits...')

    // STEP 0: Reset stuck jobs that have been processing for too long
    const timeoutThreshold = new Date(Date.now() - 3 * 60 * 1000) // 5 minutes ago (Gemini takes 60-90 seconds per job)

    const stuckJobs = await Job.find({
      status: 'processing',
      processingStartedAt: { $lt: timeoutThreshold }
    })

    if (stuckJobs.length > 0) {
      console.log(`🔄 Found ${stuckJobs.length} stuck jobs (processing > 5 minutes). Resetting to failed...`)

      for (const job of stuckJobs) {
        const processingDuration = Math.round((Date.now() - new Date(job.processingStartedAt).getTime()) / (1000 * 60))
        console.log(`⏰ Resetting stuck job ${job.jobId} (${job.documentId}, slide ${job.slideKey}) - stuck for ${processingDuration} minutes`)

        await Job.findByIdAndUpdate(job._id, {
          status: 'failed',
          error: `Job stuck in processing for ${processingDuration} minutes - automatically reset by cron (Gemini timeout)`,
          retryCount: job.retryCount || 0, // Keep current retry count
          processingStartedAt: null,
          updatedAt: new Date()
        })
      }

      console.log(`✅ Reset ${stuckJobs.length} stuck jobs to failed status`)
    }

    // STEP 1: Check current processing jobs (after cleanup)
    const processingCount = await Job.countDocuments({ status: 'processing' })

    // STEP 2: Define limits based on your setup (ULTRA CONSERVATIVE for Gemini API quota)
    const LIMITS = {
      MAX_CONCURRENT_PROCESSING: 20, // Maximum jobs in processing status (reduced from 10 to 3)
      GEMINI_REQUESTS_PER_MINUTE: 5, // Ultra conservative Gemini API limit (reduced from 5 to 2)
      MONGODB_CONNECTIONS: 5, // Conservative MongoDB connection limit
      BATCH_SIZE: 20, // Process only 5 jobs per cron run (reduced from 10 to 2)
      PROCESSING_TIMEOUT_MINUTES: 1 // Reset jobs stuck in processing for more than 2 minutes
    }

    console.log(`📊 Current processing jobs: ${processingCount}/${LIMITS.MAX_CONCURRENT_PROCESSING}`)

    // STEP 3: Check if we're at the limit
    if (processingCount >= LIMITS.MAX_CONCURRENT_PROCESSING) {
      console.log(`⏸️ Processing limit reached (${processingCount}/${LIMITS.MAX_CONCURRENT_PROCESSING}). Skipping this cron run.`)
      return NextResponse.json({
        success: true,
        message: `Processing limit reached: ${processingCount}/${LIMITS.MAX_CONCURRENT_PROCESSING} jobs currently processing`,
        processed: 0,
        processingJobs: processingCount,
        skipped: true
      })
    }

    // STEP 4: Calculate how many new jobs we can start
    const availableSlots = LIMITS.MAX_CONCURRENT_PROCESSING - processingCount
    const maxNewJobs = Math.min(availableSlots, LIMITS.BATCH_SIZE)

    console.log(`🎯 Available slots: ${availableSlots}, will process: ${maxNewJobs} jobs`)

    // STEP 5: Check if there are any jobs to process
    const pendingCount = await Job.countDocuments({ status: 'pending' })
    const failedCount = await Job.countDocuments({
      status: 'failed',
      retryCount: { $lt: 3 }
    })

    const totalAvailableJobs = pendingCount + failedCount

    if (totalAvailableJobs === 0) {
      console.log('📭 No jobs to process')
      return NextResponse.json({
        success: true,
        message: 'No jobs to process',
        processed: 0,
        pendingJobs: 0,
        failedJobs: 0,
        processingJobs: processingCount
      })
    }

    console.log(`📋 Found ${totalAvailableJobs} available jobs (${pendingCount} pending, ${failedCount} failed)`)

    // STEP 6: Determine final batch size
    const batchSize = Math.min(maxNewJobs, totalAvailableJobs)

    console.log(`🚀 Starting individual job processing: ${batchSize} jobs`)

    // Get the jobs to process
    const pendingJobs = await Job.find({ status: 'pending' })
      .sort({ createdAt: 1 })
      .limit(batchSize)

    const failedJobs = batchSize > pendingJobs.length
      ? await Job.find({
          status: 'failed',
          retryCount: { $lt: 3 }
        })
        .sort({ createdAt: 1 })
        .limit(batchSize - pendingJobs.length)
      : []

    const jobsToProcess = [...pendingJobs, ...failedJobs]

    if (jobsToProcess.length === 0) {
      console.log('📭 No jobs found to process')
      return NextResponse.json({
        success: true,
        message: 'No jobs to process',
        processed: 0,
        triggered: 0
      })
    }

    console.log(`📋 Found ${jobsToProcess.length} jobs to process (${pendingJobs.length} pending, ${failedJobs.length} failed)`)

    // Trigger each job individually (fire-and-forget)
    let triggeredCount = 0
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000'

    console.log(`⏱️ Starting ultra-conservative job triggering (1 job at a time)...`)

    for (const job of jobsToProcess) {
      try {
        // Fire-and-forget: don't await the response
        fetch(`${baseUrl}/api/jobs/process-job`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ jobId: job.jobId }),
        }).catch(error => {
          console.error(`❌ Failed to trigger job ${job.jobId}:`, error)
        })

        triggeredCount++
        console.log(`🎯 Triggered job ${job.jobId} (${job.documentId}, slide ${job.slideKey})`)

        // Add delay between job triggers to avoid overwhelming the API (increased for Gemini quota)
        if (triggeredCount < jobsToProcess.length) {
          await new Promise(resolve => setTimeout(resolve, 8500)) // 8.5 second delay to prevent rate limiting
        }

      } catch (error) {
        console.error(`❌ Error triggering job ${job.jobId}:`, error)
      }
    }

    console.log(`✅ Cron job completed: triggered ${triggeredCount}/${jobsToProcess.length} jobs`)

    return NextResponse.json({
      success: true,
      message: `Successfully triggered ${triggeredCount} individual jobs`,
      triggered: triggeredCount,
      totalJobs: jobsToProcess.length,
      pendingJobs: pendingJobs.length,
      failedJobs: failedJobs.length
    })

  } catch (error) {
    console.error('❌ Cron job error:', error)
    return NextResponse.json({
      success: false,
      error: 'Cron job failed: ' + (error instanceof Error ? error.message : 'Unknown error')
    }, { status: 500 })
  }
}

// POST method for manual testing
export async function POST(request: NextRequest) {
  console.log('🧪 Manual cron job test triggered')
  return GET(request)
}
