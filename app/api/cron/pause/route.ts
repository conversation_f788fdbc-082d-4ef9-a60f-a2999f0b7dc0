import { NextRequest, NextResponse } from 'next/server'
import { connectWithRetry } from '@/lib/mongodb'
import Settings from '@/models/Settings'

// Pause/Resume cron job processing
export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json() // 'pause' or 'resume'
    
    if (!action || !['pause', 'resume'].includes(action)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid action. Use "pause" or "resume"'
      }, { status: 400 })
    }

    await connectWithRetry()

    // Store pause state in database
    const pauseKey = 'CRON_PAUSED'
    const isPaused = action === 'pause'
    
    await Settings.findOneAndUpdate(
      { key: pauseKey },
      { 
        key: pauseKey,
        value: isPaused ? 'true' : 'false',
        updatedAt: new Date()
      },
      { upsert: true }
    )

    console.log(`🔄 Cron jobs ${action}d by user request`)

    return NextResponse.json({
      success: true,
      message: `Cron jobs ${action}d successfully`,
      action,
      isPaused,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ Error updating cron pause state:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to update cron pause state',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// Get current pause state
export async function GET() {
  try {
    await connectWithRetry()

    const pauseSetting = await Settings.findOne({ key: 'CRON_PAUSED' })
    const isPaused = pauseSetting?.value === 'true'

    return NextResponse.json({
      success: true,
      isPaused,
      lastUpdated: pauseSetting?.updatedAt || null,
      message: isPaused ? 'Cron jobs are currently paused' : 'Cron jobs are running normally'
    })

  } catch (error) {
    console.error('❌ Error getting cron pause state:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to get cron pause state',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
