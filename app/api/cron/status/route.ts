import { NextRequest, NextResponse } from 'next/server'
import connectToDatabase from '@/lib/mongodb'
import Job from '@/models/Job'

// Get cron job status and statistics
export async function GET(request: NextRequest) {
  try {
    await connectToDatabase()

    // Get job statistics
    const stats = await Job.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          latestCreated: { $max: '$createdAt' },
          latestProcessed: { $max: '$processedAt' }
        }
      }
    ])

    // Get failed jobs that can be retried
    const retryableFailedJobs = await Job.countDocuments({
      status: 'failed',
      retryCount: { $lt: 3 }
    })

    // Get recent job activity (last 10 jobs)
    const recentJobs = await Job.find({})
      .sort({ createdAt: -1 })
      .limit(10)
      .select('jobId status createdAt processedAt retryCount errorMessage')

    // Format statistics
    const statusCounts = stats.reduce((acc, stat) => {
      acc[stat._id] = {
        count: stat.count,
        latestCreated: stat.latestCreated,
        latestProcessed: stat.latestProcessed
      }
      return acc
    }, {} as Record<string, any>)

    // Calculate next job to process
    const nextPendingJob = await Job.findOne({ status: 'pending' })
      .sort({ createdAt: 1 })
      .select('jobId createdAt documentId slideNumber')

    const nextFailedJob = await Job.findOne({ 
      status: 'failed',
      retryCount: { $lt: 3 }
    })
      .sort({ createdAt: 1 })
      .select('jobId createdAt documentId slideNumber retryCount')

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      statistics: {
        pending: statusCounts.pending?.count || 0,
        processing: statusCounts.processing?.count || 0,
        success: statusCounts.success?.count || 0,
        failed: statusCounts.failed?.count || 0,
        retryableFailedJobs,
        totalJobs: stats.reduce((sum, stat) => sum + stat.count, 0)
      },
      nextJobToProcess: nextPendingJob || nextFailedJob || null,
      cronInfo: {
        schedule: "Every minute (* * * * *)",
        priority: "1. Pending jobs first, 2. Failed jobs with retries left",
        maxRetries: 3,
        endpoint: "/api/cron/process-jobs"
      },
      recentActivity: recentJobs.map(job => ({
        jobId: job.jobId,
        status: job.status,
        createdAt: job.createdAt,
        processedAt: job.processedAt,
        retryCount: job.retryCount,
        hasError: !!job.errorMessage,
        documentId: job.documentId,
        slideNumber: job.slideNumber
      }))
    })

  } catch (error) {
    console.error('❌ Error getting cron status:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to get cron status: ' + (error instanceof Error ? error.message : 'Unknown error')
    }, { status: 500 })
  }
}
