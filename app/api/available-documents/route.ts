import { NextRequest, NextResponse } from 'next/server';
import connectToDatabase from '@/lib/mongodb';
import mongoose from 'mongoose';

export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const unit_number = searchParams.get('unit_number');
    const lesson_number = searchParams.get('lesson_number');
    const grade_level = searchParams.get('grade_level');
    
    // Connect to the database
    await connectToDatabase();
    
    // Use direct MongoDB operations
    if (!mongoose.connection.db) {
      throw new Error('MongoDB connection not established');
    }
    
    const db = mongoose.connection.db;
    const collection = db.collection('jsondocuments');
    
    // Build query based on provided parameters
    const query: any = {};
    
    // If we have specific parameters, use them to filter
    if (unit_number && lesson_number && grade_level) {
      // Try to find exact match first
      const exactDocumentId = `${unit_number}-${lesson_number}-${grade_level}`;
      console.log(`Looking for exact document with ID: ${exactDocumentId}`);
      
      const exactDoc = await collection.findOne({ documentId: exactDocumentId });
      
      if (exactDoc) {
        return NextResponse.json({
          message: 'Document found',
          documentExists: true,
          documents: [{ id: exactDocumentId }]
        });
      }
      
      // If exact match not found, look for similar documents
      console.log(`Exact document not found, looking for similar documents...`);
      query.documentId = { $regex: `^${unit_number}-${lesson_number}-` };
    } else if (unit_number && lesson_number) {
      // If we have unit and lesson but no grade, find all matching documents
      query.documentId = { $regex: `^${unit_number}-${lesson_number}-` };
    } else if (unit_number) {
      // If we only have unit, find all matching documents
      query.documentId = { $regex: `^${unit_number}-` };
    }
    
    // Find documents matching the query
    const documents = await collection.find(query).limit(20).toArray();
    
    // Extract document IDs
    const documentIds = documents
      .map(doc => doc.documentId)
      .filter(Boolean);
    
    console.log(`Found ${documentIds.length} documents matching query:`, query);
    
    return NextResponse.json({
      message: documentIds.length > 0 ? 'Documents found' : 'No documents found',
      documentExists: documentIds.length > 0,
      documents: documentIds.map(id => ({ id }))
    });
  } catch (error) {
    console.error('Error checking available documents:', error);
    return NextResponse.json(
      { message: 'An error occurred while checking available documents', error: String(error) },
      { status: 500 }
    );
  }
}
