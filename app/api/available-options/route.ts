import { NextResponse } from 'next/server';
import connectToDatabase from '@/lib/mongodb';
import JsonDocument from '@/models/JsonDocument';
import { CurriculumType } from '@/types/curriculumTypes';

// GET handler for fetching available options for dropdowns
export async function GET(req: Request) {
  try {
    // Get search parameters from URL
    const url = new URL(req.url);
    const unit_number = url.searchParams.get('unit_number') || undefined;
    const grade_level = url.searchParams.get('grade_level') || undefined;
    const curriculum = url.searchParams.get('curriculum') || undefined;
    const option_type = url.searchParams.get('option_type') || 'all'; // can be 'all', 'grade_level', 'unit_number', 'lesson_number', 'units_with_titles', 'lessons_with_titles', 'check_grade'

    console.log('API Request for available options:', {
      option_type,
      unit_number,
      grade_level,
      curriculum
    });

    await connectToDatabase();

    // Build query object based on provided parameters
    const query: any = {
      lang: 'en' // Only show English lessons in filters
    };

    if (unit_number) query.unit_number = unit_number.toString();
    if (grade_level) {
      // Handle URL-encoded spaces in grade_level
      const decodedGradeLevel = grade_level.replace(/\+/g, ' ');
      query.grade_level = decodedGradeLevel;
    }

    // Add curriculum filter
    if (curriculum) {
      query['content.common_core'] = curriculum;
    }

    console.log('Database query:', JSON.stringify(query));

    let result: any = {};

    // Fetch available options based on option_type
    if (option_type === 'check_grade') {
      // Check if the specific grade has data
      const gradeToCheck = grade_level;
      if (gradeToCheck) {
        const count = await JsonDocument.countDocuments({ grade_level: gradeToCheck });
        result.has_data = count > 0;
        result.grade_level = gradeToCheck;
      }
    } else if (option_type === 'all' || option_type === 'grade_level') {
      // Use aggregation to properly filter by curriculum
      const gradesAggregation = await JsonDocument.aggregate([
        { $match: query },
        { $group: { _id: "$grade_level" } },
        { $project: { grade_level: "$_id", _id: 0 } }
      ]);

      const grades = gradesAggregation.map(item => item.grade_level);

      // Sort grades with Kindergarten first, then Grade 1, Grade 2, etc.
      const sortedGrades = grades.sort((a: string, b: string) => {
        // Kindergarten always comes first
        if (a === 'Kindergarten') return -1;
        if (b === 'Kindergarten') return 1;

        // Extract numbers from "Grade X" format
        const aMatch = a.match(/Grade (\d+)/);
        const bMatch = b.match(/Grade (\d+)/);

        // If both are grades with numbers, sort numerically
        if (aMatch && bMatch) {
          return parseInt(aMatch[1]) - parseInt(bMatch[1]);
        }

        // Fallback to alphabetical sorting
        return a.localeCompare(b);
      });

      result.grade_levels = sortedGrades;
    }

    if (option_type === 'all' || option_type === 'unit_number') {
      const units = await JsonDocument.distinct('unit_number', query);
      result.unit_numbers = units;
    }

    if (option_type === 'all' || option_type === 'lesson_number') {
      const lessons = await JsonDocument.distinct('lesson_number', query);
      result.lesson_numbers = lessons;
    }

    // Get units with titles
    if (option_type === 'units_with_titles') {
      const unitsData = await JsonDocument.find(query, {
        unit_number: 1,
        unit_title: 1,
        content: 1,
        _id: 0
      }).distinct('unit_number');

      // Get unique units with their titles
      const unitsWithTitles = [];
      for (const unitNumber of unitsData) {
        const unitDoc = await JsonDocument.findOne(
          { ...query, unit_number: unitNumber },
          { unit_number: 1, unit_title: 1, content: 1, _id: 0 }
        );
        if (unitDoc) {
          // Try to get unit_title from the document field first, then from content
          let title = unitDoc.unit_title;
          if (!title && unitDoc.content && unitDoc.content.unit_title) {
            title = unitDoc.content.unit_title;
          }
          if (!title) {
            title = `Unit ${unitDoc.unit_number}`;
          }

          unitsWithTitles.push({
            unit_number: unitDoc.unit_number,
            unit_title: title
          });
        }
      }
      result.units_with_titles = unitsWithTitles;
    }

    // Get lessons with titles
    if (option_type === 'lessons_with_titles') {
      const lessonsData = await JsonDocument.find(query, {
        lesson_number: 1,
        lesson_title: 1,
        content: 1,
        _id: 0
      }).distinct('lesson_number');

      // Get unique lessons with their titles
      const lessonsWithTitles = [];
      for (const lessonNumber of lessonsData) {
        const lessonDoc = await JsonDocument.findOne(
          { ...query, lesson_number: lessonNumber },
          { lesson_number: 1, lesson_title: 1, content: 1, _id: 0 }
        );
        if (lessonDoc) {
          // Try to get lesson_title from the document field first, then from content
          let title = lessonDoc.lesson_title;
          if (!title && lessonDoc.content && lessonDoc.content.lesson_title) {
            title = lessonDoc.content.lesson_title;
          }
          if (!title) {
            title = `Lesson ${lessonDoc.lesson_number}`;
          }

          lessonsWithTitles.push({
            lesson_number: lessonDoc.lesson_number,
            lesson_title: title
          });
        }
      }
      result.lessons_with_titles = lessonsWithTitles;
    }

    console.log('Available options:', result);

    // Return available options
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching available options:', error);
    return NextResponse.json(
      { message: 'An error occurred while fetching available options', error: String(error) },
      { status: 500 }
    );
  }
}
