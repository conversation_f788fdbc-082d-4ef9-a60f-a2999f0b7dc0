import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '../auth/[...nextauth]/route'
import connectToDatabase from '@/lib/mongodb'
import ReferenceFile from '@/models/ReferenceFile'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user || (session.user as any)?.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') // 'good' or 'bad'

    if (!type || !['good', 'bad'].includes(type)) {
      return NextResponse.json({ error: 'Invalid type parameter' }, { status: 400 })
    }

    await connectToDatabase()

    const files = await ReferenceFile.find({ type }).sort({ uploadedAt: -1 })
    
    return NextResponse.json({ files })
  } catch (error) {
    console.error('Error fetching reference files:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user || (session.user as any)?.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const formData = await request.formData()
    const file = formData.get('file') as File
    const type = formData.get('type') as string
    const description = formData.get('description') as string

    if (!file || !type || !['good', 'bad'].includes(type)) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    const content = await file.text()

    await connectToDatabase()

    const referenceFile = await ReferenceFile.create({
      filename: file.name,
      content,
      description: description || '',
      type,
      uploadedBy: session.user.email
    })
    
    return NextResponse.json({
      success: true,
      id: referenceFile._id,
      message: `${type === 'good' ? 'Good' : 'Bad'} reference file uploaded successfully`
    })
  } catch (error) {
    console.error('Error uploading reference file:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user || (session.user as any)?.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    const type = searchParams.get('type')

    if (!id || !type || !['good', 'bad'].includes(type)) {
      return NextResponse.json({ error: 'Missing required parameters' }, { status: 400 })
    }

    await connectToDatabase()

    const result = await ReferenceFile.findByIdAndDelete(id)
    
    if (!result) {
      return NextResponse.json({ error: 'File not found' }, { status: 404 })
    }
    
    return NextResponse.json({ 
      success: true, 
      message: `${type === 'good' ? 'Good' : 'Bad'} reference file deleted successfully` 
    })
  } catch (error) {
    console.error('Error deleting reference file:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
