import { NextRequest, NextResponse } from 'next/server'

// Simple quota status tracking
let quotaStatus = {
  lastQuotaError: null as Date | null,
  quotaErrorCount: 0,
  lastSuccessfulRequest: null as Date | null,
  consecutiveFailures: 0,
  isQuotaExceeded: false
}

export async function GET(request: NextRequest) {
  try {
    const now = new Date()
    const timeSinceLastError = quotaStatus.lastQuotaError ? 
      now.getTime() - quotaStatus.lastQuotaError.getTime() : null

    // Reset quota status if it's been more than 1 hour since last error
    if (timeSinceLastError && timeSinceLastError > 3600000) { // 1 hour
      quotaStatus.isQuotaExceeded = false
      quotaStatus.consecutiveFailures = 0
    }

    return NextResponse.json({
      status: quotaStatus.isQuotaExceeded ? 'quota_exceeded' : 'healthy',
      quotaErrorCount: quotaStatus.quotaErrorCount,
      consecutiveFailures: quotaStatus.consecutiveFailures,
      lastQuotaError: quotaStatus.lastQuotaError,
      lastSuccessfulRequest: quotaStatus.lastSuccessfulRequest,
      timeSinceLastError: timeSinceLastError ? Math.round(timeSinceLastError / 1000) : null,
      recommendations: getRecommendations()
    })
  } catch (error) {
    return NextResponse.json({
      error: 'Failed to get quota status',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { type, error } = body

    const now = new Date()

    if (type === 'quota_error') {
      quotaStatus.lastQuotaError = now
      quotaStatus.quotaErrorCount++
      quotaStatus.consecutiveFailures++
      quotaStatus.isQuotaExceeded = true
      
      console.warn(`🔴 Quota error recorded: ${quotaStatus.quotaErrorCount} total, ${quotaStatus.consecutiveFailures} consecutive`)
    } else if (type === 'success') {
      quotaStatus.lastSuccessfulRequest = now
      quotaStatus.consecutiveFailures = 0
      quotaStatus.isQuotaExceeded = false
      
      console.log(`✅ Success recorded, quota status reset`)
    }

    return NextResponse.json({
      message: `Status updated: ${type}`,
      currentStatus: quotaStatus
    })
  } catch (error) {
    return NextResponse.json({
      error: 'Failed to update quota status',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

function getRecommendations(): string[] {
  const recommendations: string[] = []
  
  if (quotaStatus.isQuotaExceeded) {
    recommendations.push('Quota exceeded - reduce batch size or wait for quota reset')
    recommendations.push('Consider upgrading Gemini API plan for higher limits')
  }
  
  if (quotaStatus.consecutiveFailures > 5) {
    recommendations.push('Multiple consecutive failures - check API key and billing')
    recommendations.push('Consider pausing job processing temporarily')
  }
  
  const timeSinceLastError = quotaStatus.lastQuotaError ? 
    Date.now() - quotaStatus.lastQuotaError.getTime() : null
    
  if (timeSinceLastError && timeSinceLastError < 300000) { // 5 minutes
    recommendations.push('Recent quota error - wait before retrying')
  }
  
  if (recommendations.length === 0) {
    recommendations.push('System is healthy - normal processing can continue')
  }
  
  return recommendations
}
