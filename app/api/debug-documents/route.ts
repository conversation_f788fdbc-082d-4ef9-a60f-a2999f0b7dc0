import { NextRequest, NextResponse } from 'next/server'
import { connectWithRetry } from '@/lib/mongodb'
import JsonDocument from '@/models/JsonDocument'
import Job from '@/models/Job'

// Debug endpoint to check document and job alignment
export async function GET(request: NextRequest) {
  try {
    await connectWithRetry()

    // Get sample jobs
    const jobs = await Job.find({}).limit(10).sort({ createdAt: -1 })
    
    // Get sample documents
    const documents = await JsonDocument.find({}).limit(10).sort({ createdAt: -1 })

    // Check for mismatches
    const mismatches = []
    
    for (const job of jobs) {
      // Try to find matching document
      let matchingDoc = await JsonDocument.findOne({ documentId: job.documentId })
      
      if (!matchingDoc && !job.documentId.endsWith('-esp')) {
        matchingDoc = await JsonDocument.findOne({ documentId: job.documentId, lang: 'en' })
      }
      
      if (!matchingDoc && job.documentId.endsWith('-esp')) {
        const baseDocumentId = job.documentId.replace('-esp', '')
        matchingDoc = await JsonDocument.findOne({ documentId: baseDocumentId, lang: 'esp' })
      }
      
      if (!matchingDoc) {
        mismatches.push({
          jobId: job.jobId,
          jobDocumentId: job.documentId,
          jobGrade: job.gradeLevel,
          jobUnit: job.unitNumber,
          jobLesson: job.lessonNumber,
          status: 'NO_MATCHING_DOCUMENT'
        })
      }
    }

    return NextResponse.json({
      success: true,
      summary: {
        totalJobs: jobs.length,
        totalDocuments: documents.length,
        mismatches: mismatches.length
      },
      jobs: jobs.map(job => ({
        jobId: job.jobId,
        documentId: job.documentId,
        gradeLevel: job.gradeLevel,
        unitNumber: job.unitNumber,
        lessonNumber: job.lessonNumber,
        status: job.status
      })),
      documents: documents.map(doc => ({
        documentId: doc.documentId,
        lang: doc.lang,
        gradeLevel: doc.grade_level,
        unitNumber: doc.unit_number,
        lessonNumber: doc.lesson_number
      })),
      mismatches
    })

  } catch (error) {
    console.error('❌ Debug documents failed:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to debug documents',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
