"use client"

import type React from "react"

import { useState, useEffect, useCallback, Suspense } from "react"
import { ChevronDown, Globe, ChevronRight } from "lucide-react"
import { Sidebar } from "@/components/dashboard/sidebar"
import { SlideContent } from "@/components/dashboard/slide-content"
import { TabContent } from "@/components/dashboard/tab-content"

import MainHeader from "@/components/main-header"

// Component that contains the dashboard content
function DashboardContent() {
  const [currentSlide, setCurrentSlide] = useState(1)
  const [sidebarOpen, setSidebarOpen] = useState(true) // Set sidebar to open by default
  const [highContrast, setHighContrast] = useState(false)
  const [userMenuOpen, setUserMenuOpen] = useState(false)
  const totalSlides = 27
  const [activeTab, setActiveTab] = useState("home")

  const [showSettingsModal, setShowSettingsModal] = useState(false)
  const [userName, setUserName] = useState("Teacher Smith")
  const [isEditingName, setIsEditingName] = useState(false)
  const [userSettings, setUserSettings] = useState({
    school: "Lincoln Elementary",
    district: "Springfield School District",
    curriculum: "CCSS",
    grade: "5",
  })



  const [selectedLanguage, setSelectedLanguage] = useState("en")

  // Language menu state
  const [languageMenuOpen, setLanguageMenuOpen] = useState(false)

  // Curriculum, Grade, Unit, and Lesson state


  // State to track revealed items on the current slide
  const [revealedItems, setRevealedItems] = useState<number[]>([])
  // State to track the total number of revealable items on the current slide
  const [totalRevealableItems, setTotalRevealableItems] = useState(0)

  // Reset revealed items when changing slides
  useEffect(() => {
    setRevealedItems([])
  }, [currentSlide])

  // Function to reveal the next item
  const revealNextItem = useCallback(() => {
    if (revealedItems.length < totalRevealableItems) {
      const nextItemIndex = revealedItems.length
      setRevealedItems((prev) => [...prev, nextItemIndex])
      return true // Item was revealed
    }
    return false // No more items to reveal
  }, [revealedItems, totalRevealableItems])

  // Function to hide the last revealed item
  const hideLastItem = useCallback(() => {
    if (revealedItems.length > 0) {
      setRevealedItems((prev) => prev.slice(0, -1))
      return true // Item was hidden
    }
    return false // No items to hide
  }, [revealedItems])

  // Handle keyboard navigation - modified to use only arrow keys
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Get the currently focused element
      const target = e.target as HTMLElement
      const isEditableElement = target.tagName === "INPUT" || target.tagName === "TEXTAREA" || target.isContentEditable

      // Only prevent default for navigation keys when not in editable elements
      if ((e.code === "ArrowLeft" || e.code === "ArrowRight") && !isEditableElement) {
        e.preventDefault()
      }

      // Only prevent spacebar default when not in editable elements and not a button
      if (e.code === "Space" && !isEditableElement && target.tagName !== "BUTTON") {
        e.preventDefault()
      }

      // Only process arrow keys for navigation when not in editable elements
      if (!isEditableElement) {
        if (e.code === "ArrowRight") {
          // Try to reveal next item, if none left, go to next slide
          const revealed = revealNextItem()
          if (!revealed) {
            setCurrentSlide((prev) => Math.min(totalSlides, prev + 1))
          }
        } else if (e.code === "ArrowLeft") {
          // Try to hide last item, if none left, go to previous slide
          const hidden = hideLastItem()
          if (!hidden) {
            setCurrentSlide((prev) => Math.max(1, prev - 1))
          }
        }
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => {
      window.removeEventListener("keydown", handleKeyDown)
    }
  }, [revealNextItem, hideLastItem, totalSlides])

  // Function to be called from slide components to register their revealable items
  const registerRevealableItems = useCallback((count: number) => {
    setTotalRevealableItems(count)
  }, [])

  // Close user menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      if (userMenuOpen && !target.closest(".user-menu-container")) {
        setUserMenuOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [userMenuOpen])

  // Add an effect to close the language menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      if (languageMenuOpen && !target.closest(".language-menu-container")) {
        setLanguageMenuOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [languageMenuOpen])

  // Update header height CSS variable for proper button positioning
  useEffect(() => {
    const updateHeaderHeight = () => {
      const header = document.querySelector("header")
      if (header) {
        const headerHeight = header.offsetHeight
        document.documentElement.style.setProperty("--computed-header-height", `${headerHeight}px`)

        // Force a repaint to ensure buttons are positioned correctly
        const languageButton = document.querySelector(".language-menu-container") as HTMLElement

        if (languageButton) {
          languageButton.style.transform = 'translateZ(0)'
          setTimeout(() => {
            languageButton.style.transform = ''
          }, 0)
        }
      }
    }

    // Initial update
    updateHeaderHeight()

    // Update on resize
    window.addEventListener("resize", updateHeaderHeight)

    // Update when high contrast mode changes
    const timer = setTimeout(updateHeaderHeight, 100)

    return () => {
      window.removeEventListener("resize", updateHeaderHeight)
      clearTimeout(timer)
    }
  }, [])



  // Calculate header height for positioning
  useEffect(() => {
    const updateHeaderHeight = () => {
      const header = document.querySelector("header")
      if (header) {
        const headerHeight = header.offsetHeight
        document.documentElement.style.setProperty("--computed-header-height", `${headerHeight}px`)
      }
    }

    updateHeaderHeight()
    window.addEventListener("resize", updateHeaderHeight)

    return () => {
      window.removeEventListener("resize", updateHeaderHeight)
    }
  }, [])

  return (
    <div
      className="flex h-screen flex-col bg-white"
      style={{ "--header-height": "var(--computed-header-height, 116px)" } as React.CSSProperties}
    >
      {/* Header */}
      <MainHeader isDashboard={true} />

      {/* Language Selector */}
      <div className="language-menu-container fixed top-[calc(var(--header-height)+32px)] right-8 z-50">
        <button
          onClick={() => setLanguageMenuOpen(!languageMenuOpen)}
          className="flex h-10 w-10 items-center justify-center rounded-full bg-white/20 text-white hover:bg-white/30 transition-colors  shadow-md"
          aria-label="Change language"
        >
          <Globe className="h-5 w-5" />
        </button>

        {languageMenuOpen && (
          <div className="absolute right-0 mt-2 w-40 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 z-50">
            <div className="py-1">
              <button
                className={`flex w-full items-center px-4 py-2 text-sm text-left ${selectedLanguage === "en" ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                onClick={() => {
                  setSelectedLanguage("en")
                  setLanguageMenuOpen(false)
                }}
              >
                English
              </button>
              <button
                className={`flex w-full items-center px-4 py-2 text-sm text-left ${selectedLanguage === "es" ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                onClick={() => {
                  setSelectedLanguage("es")
                  setLanguageMenuOpen(false)
                }}
              >
                Español
              </button>
              <button
                className={`flex w-full items-center px-4 py-2 text-sm text-left ${selectedLanguage === "fr" ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                onClick={() => {
                  setSelectedLanguage("fr")
                  setLanguageMenuOpen(false)
                }}
              >
                Français
              </button>
              <button
                className={`flex w-full items-center px-4 py-2 text-sm text-left ${selectedLanguage === "ar" ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"}`}
                onClick={() => {
                  setSelectedLanguage("ar")
                  setLanguageMenuOpen(false)
                }}
              >
                العربية
              </button>
            </div>
          </div>
        )}
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar - conditionally rendered based on sidebarOpen state */}
        {sidebarOpen && (
          <Sidebar
            currentSlide={currentSlide}
            setCurrentSlide={setCurrentSlide}
            setSidebarOpen={setSidebarOpen}
            activeTab={activeTab}
            setActiveTab={setActiveTab}
          />
        )}

        {/* Sidebar toggle button - show when sidebar is closed */}
        {!sidebarOpen && (
          <div className="fixed top-[calc(var(--header-height)+32px)] left-8 z-50">
            <button
              onClick={() => setSidebarOpen(true)}
              className="flex h-10 w-10 items-center justify-center rounded-full bg-white/20 text-white hover:bg-white/30 transition-colors  shadow-md"
              aria-label="Open sidebar"
            >
              <ChevronRight size={16} />
            </button>
          </div>
        )}

        {/* Main content */}
        <div
          className={`flex-1 overflow-y-auto ${
            highContrast ? "bg-white text-black" : "bg-[linear-gradient(150deg,#2B6DFE_0%,#2B6DFE_70%,#00F2FF_100%)]"
          } p-0 text-lg`}
          style={
            highContrast
              ? ({
                  "--fadb9a": "#000000",
                  "--concept-number-bg": "#ffffff",
                  "--concept-number-text": "#000000",
                  "--concept-content-bg": "#ffffff",
                  "--concept-content-text": "#000000",
                } as React.CSSProperties)
              : {}
          }
        >
          <div className={`h-full ${highContrast ? "high-contrast-content" : ""}`}>
            {/* Show TabContent when activeTab is not empty, otherwise show SlideContent */}
            {activeTab ? (
              <Suspense fallback={<div className="flex items-center justify-center h-full text-white">Loading...</div>}>
                <TabContent activeTab={activeTab} setActiveTab={setActiveTab} />
              </Suspense>
            ) : (
              <SlideContent
                slideNumber={currentSlide}
                highContrast={highContrast}
                revealedItems={revealedItems}
                registerRevealableItems={registerRevealableItems}
                setRevealedItems={setRevealedItems}
              />
            )}
          </div>
        </div>
      </div>

      {/* Settings Modal */}
      {showSettingsModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-[linear-gradient(150deg,#2B6DFE_0%,#2B6DFE_70%,#00F2FF_100%)] rounded-lg w-full max-w-md shadow-xl text-white overflow-hidden">
            <div className="flex justify-between items-center p-6 border-b border-white/10">
              <h2 className="text-xl font-semibold text-white">Settings</h2>
              <button
                onClick={() => setShowSettingsModal(false)}
                className="text-white/70 hover:text-white transition-colors"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="bg-gray-50 p-6 space-y-5 text-gray-700 text-sm">
              {/* Name - Editable */}
              <div className="space-y-1">
                <label className="block font-medium text-gray-600">Name</label>
                <div className="flex items-center">
                  {isEditingName ? (
                    <input
                      type="text"
                      value={userName}
                      onChange={(e) => setUserName(e.target.value)}
                      className="flex-1 bg-white border border-gray-200 rounded-md px-3 py-2 text-gray-700 focus:outline-none focus:ring-2 focus:ring-[#2B6DFE]"
                      autoFocus
                    />
                  ) : (
                    <span className="flex-1 py-2 text-gray-700">{userName}</span>
                  )}
                  <button
                    onClick={() => setIsEditingName(!isEditingName)}
                    className="ml-2 text-gray-400 hover:text-[#2B6DFE] transition-colors"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
                      />
                    </svg>
                  </button>
                </div>
              </div>

              {/* School - Not Editable */}
              <div className="space-y-1">
                <label className="block font-medium text-gray-600">School</label>
                <div className="py-2 px-3 bg-gray-100 rounded-md border border-gray-200 text-gray-500">
                  {userSettings.school}
                </div>
              </div>

              {/* School District - Not Editable */}
              <div className="space-y-1">
                <label className="block font-medium text-gray-600">School District</label>
                <div className="py-2 px-3 bg-gray-100 rounded-md border border-gray-200 text-gray-500">
                  {userSettings.district}
                </div>
              </div>

              {/* High Contrast Toggle */}
              <div className="space-y-1">
                <label className="block font-medium text-gray-600">Display Mode</label>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">High Contrast Mode</span>
                  <button
                    onClick={() => setHighContrast(!highContrast)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-[#2B6DFE] focus:ring-offset-2 ${
                      highContrast ? "bg-[#2B6DFE]" : "bg-gray-200"
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        highContrast ? "translate-x-6" : "translate-x-1"
                      }`}
                    />
                  </button>
                </div>
              </div>

              {/* Default Curriculum - Dropdown */}
              <div className="space-y-1">
                <label className="block font-medium text-gray-600">Default Curriculum</label>
                <div className="relative">
                  <select
                    value={userSettings.curriculum}
                    disabled
                    onChange={(e) => setUserSettings({ ...userSettings, curriculum: e.target.value })}
                    className="w-full bg-white border border-gray-200 rounded-md px-3 py-2 text-gray-700 appearance-none focus:outline-none focus:ring-2 focus:ring-[#2B6DFE]"
                  >
                    <option value="CCSS">Common Core State Standards (CCSS)</option>
                    {/* <option value="Ontario">Ontario Curriculum</option>
                    <option value="Alberta">Alberta Curriculum</option>
                    <option value="BC">British Columbia Curriculum</option>
                    <option value="Australia">Australian Curriculum</option>
                    <option value="UK">UK National Curriculum</option> */}
                  </select>
                  <ChevronDown className="absolute right-2 top-1/2 h-4 w-4 -translate-y-1/2 pointer-events-none text-gray-400" />
                </div>
              </div>

              {/* Default Grade - Dropdown */}
              <div className="space-y-1">
                <label className="block font-medium text-gray-600">Default Grade</label>
                <div className="relative">
                  <select
                    value={userSettings.grade}
                    onChange={(e) => setUserSettings({ ...userSettings, grade: e.target.value })}
                    className="w-full bg-white border border-gray-200 rounded-md px-3 py-2 text-gray-700 appearance-none focus:outline-none focus:ring-2 focus:ring-[#2B6DFE]"
                  >
                    <option value="K">Kindergarten</option>
                    <option value="1">Grade 1</option>
                    <option value="2">Grade 2</option>
                    <option value="3">Grade 3</option>
                    <option value="4">Grade 4</option>
                    <option value="5">Grade 5</option>
                    <option value="6">Grade 6</option>
                    <option value="7">Grade 7</option>
                    <option value="8">Grade 8</option>
                    <option value="9">Grade 9</option>
                    <option value="10">Grade 10</option>
                    <option value="11">Grade 11</option>
                    <option value="12">Grade 12</option>
                  </select>
                  <ChevronDown className="absolute right-2 top-1/2 h-4 w-4 -translate-y-1/2 pointer-events-none text-gray-400" />
                </div>
              </div>
            </div>

            <div className="p-6 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
              <button
                onClick={() => setShowSettingsModal(false)}
                className="px-3 py-1.5 border border-gray-200 rounded-md text-gray-700 hover:bg-gray-100 transition-colors text-sm"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  // Save settings logic would go here
                  setShowSettingsModal(false)
                  setIsEditingName(false)
                }}
                className="px-3 py-1.5 bg-gray-700 text-white font-medium rounded-md hover:bg-gray-600 transition-colors text-sm"
              >
                Save Changes
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

// Main component with Suspense boundary
export default function DashboardPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <DashboardContent />
    </Suspense>
  )
}
