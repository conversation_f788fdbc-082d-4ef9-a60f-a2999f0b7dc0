import type React from "react"
import "./globals.css"
import type { Metada<PERSON> } from "next"
import { Public_Sans, Montserrat } from "next/font/google"
import { PresenterProvider } from "@/components/presenter-context"
import { LessonProvider } from "@/components/lesson-context"
import { HighContrastProvider } from "@/components/high-contrast-context"
import AuthProvider from "@/components/auth-provider"
import Script from "next/script"

const publicSans = Public_Sans({ subsets: ["latin"] })
const montserrat = Montserrat({
  subsets: ["latin"],
  weight: ["600","700", "800", "900"],
  variable: "--font-montserrat",
})

export const metadata: Metadata = {
  title: "EMBRS Math App",
  description: "Interactive math lesson with 27 slides",
  generator: 'v0.dev',
  icons: {
    icon: [
      { url: '/favicon.png?v=1', sizes: '32x32', type: 'image/png' },
      { url: '/icon-192.png?v=1', sizes: '192x192', type: 'image/png' },
      { url: '/icon-512.png?v=1', sizes: '512x512', type: 'image/png' }
    ],
    apple: [
      { url: '/icon-192.png?v=1', sizes: '192x192', type: 'image/png' }
    ]
  }
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" >
      <head>
        <link rel="manifest" href="/manifest.json?v=1" />

        <Script id="handle-hydration" strategy="beforeInteractive">
          {`
            (function() {
              // Remove any classes added by browser extensions before React hydration
              if (typeof window !== 'undefined') {
                const originalBodyClasses = document.body.className;
                const cleanBodyClasses = originalBodyClasses
                  .split(' ')
                  .filter(cls =>
                    cls.startsWith('__className_') ||
                    cls.startsWith('__variable_')
                  )
                  .join(' ');

                if (cleanBodyClasses !== originalBodyClasses) {
                  document.body.className = cleanBodyClasses;
                }
              }
            })();
          `}
        </Script>
      </head>
      <body className={`${publicSans.className} ${montserrat.variable}`} suppressHydrationWarning id="app-body">
        <AuthProvider>
          <HighContrastProvider>
            <LessonProvider>
              <PresenterProvider>
                {children}
              </PresenterProvider>
            </LessonProvider>
          </HighContrastProvider>
        </AuthProvider>
      </body>
    </html>
  )
}
