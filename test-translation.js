// Test script to verify translation functionality
// Run this in browser console on the lesson page

console.log('🧪 Testing Translation System...');

// Clear all caches
if (typeof window !== 'undefined' && window.__slidesGlobalCache) {
  window.__slidesGlobalCache.documents.clear();
  window.__slidesGlobalCache.slides.clear();
  window.__slidesGlobalCache.lastAccessed.clear();
  console.log('✅ Cleared global cache');
}

// Test API calls
async function testTranslationAPIs() {
  console.log('\n📡 Testing API calls...');
  
  // Test 1: Check translation availability
  try {
    const checkResponse = await fetch('/api/check-translation?documentId=6-49-Grade%201&lang=esp');
    const checkData = await checkResponse.json();
    console.log('1️⃣ Translation check:', checkData);
  } catch (error) {
    console.error('❌ Translation check failed:', error);
  }
  
  // Test 2: Fetch English document
  try {
    const enResponse = await fetch('/api/json-documents?unit_number=6&lesson_number=49&grade_level=Grade%201&lang=en');
    const enData = await enResponse.json();
    console.log('2️⃣ English document keys:', Object.keys(enData).filter(k => k.startsWith('Slide')).slice(0, 3));
  } catch (error) {
    console.error('❌ English document fetch failed:', error);
  }
  
  // Test 3: Fetch Spanish document
  try {
    const espResponse = await fetch('/api/json-documents?unit_number=6&lesson_number=49&grade_level=Grade%201&lang=esp');
    const espData = await espResponse.json();
    console.log('3️⃣ Spanish document keys:', Object.keys(espData).filter(k => k.startsWith('Slide')).slice(0, 3));
    
    // Check if first slide has content
    const slide1Key = Object.keys(espData).find(k => k.includes('Slide 1'));
    if (slide1Key) {
      const slide1 = espData[slide1Key];
      console.log('4️⃣ Spanish Slide 1 content:', {
        title: slide1.slide_pedagogical_name,
        hasScript: !!slide1.script,
        hasHTML: !!slide1.html_css_description_of_image
      });
    }
  } catch (error) {
    console.error('❌ Spanish document fetch failed:', error);
  }
  
  // Test 4: Test slideService directly
  try {
    const { getSlide } = await import('/services/slideService.ts');
    const slide = await getSlide(1, "6", "49", "Grade 1", "esp", true);
    console.log('5️⃣ SlideService result:', {
      found: !!slide,
      title: slide?.slide_pedagogical_name,
      hasScript: !!slide?.script,
      hasHTML: !!slide?.html_css_description_of_image
    });
  } catch (error) {
    console.error('❌ SlideService test failed:', error);
  }
}

// Run tests
testTranslationAPIs();

console.log('\n🔧 To manually test:');
console.log('1. Open browser console');
console.log('2. Navigate to: /?grade=Grade+1&unit=6&lesson=49&slide=1&lang=esp');
console.log('3. Check console for API calls and errors');
console.log('4. Verify slide content loads');
