# Cron Jobs for Automated HTML Generation Processing

## Overview

The system automatically processes pending and failed jobs every minute through Vercel Cron Functions.

## Configuration

### 1. Vercel Cron Configuration (`vercel.json`)
```json
{
  "crons": [
    {
      "path": "/api/cron/process-jobs",
      "schedule": "* * * * *"
    }
  ]
}
```

### 2. Environment Variables
Add to Vercel Environment Variables:
```
CRON_SECRET=cron_secret_key_2024_math_lesson
```

## API Endpoints

### `/api/cron/process-jobs` (GET)
- **Purpose**: Main cron endpoint
- **Schedule**: Every minute
- **Logic**:
  1. Finds one pending job (priority)
  2. If no pending, finds one failed job with retryCount < 3
  3. Processes the found job
  4. Updates status and saves HTML

### `/api/cron/status` (GET)
- **Purpose**: Monitor cron jobs status
- **Returns**: Statistics, next job to process, configuration

### `/api/cron/test` (GET/POST)
- **Purpose**: Test cron job locally
- **Usage**: For debugging and verification

## Processing Logic

1. **Priority**: Pending jobs are processed first
2. **Retry Logic**: Failed jobs are retried up to 3 times
3. **Rate Limiting**: One job at a time every minute
4. **HTML Storage**: HTML is saved both in lesson document and job (for preview)

## Monitoring

### In Jobs Manager UI:
1. "Cron Status" button - shows statistics
2. "Test Cron" button - tests cron job manually
3. Automatic statistics updates

### Logs:
- All cron operations are logged with 🕐 prefix
- Successful processing: ✅
- Errors: ❌

## Deployment on Vercel

1. **Push code** with `vercel.json`
2. **Add Environment Variables** in Vercel Dashboard:
   - `CRON_SECRET`
   - `MONGODB_URI`
   - `GEMINI_API_KEY`
   - `NEXTAUTH_URL` (production URL)
3. **Deploy** - cron jobs activate automatically

## Security

- Cron endpoint protected by `CRON_SECRET`
- Only Vercel can call cron endpoints
- Local testing through `/api/cron/test`

## Troubleshooting

### Cron not working:
1. Check `vercel.json` in root directory
2. Check Environment Variables in Vercel
3. Check logs in Vercel Functions

### Jobs not processing:
1. Check `/api/cron/status` for statistics
2. Use "Test Cron" for debugging
3. Check MongoDB connection

### Generation errors:
1. Check `GEMINI_API_KEY`
2. Check Gemini API quota
3. Check logs in `/api/generate-html`

## Production Monitoring

- Vercel Dashboard > Functions > Cron Jobs
- MongoDB logs for job updates
- Jobs Manager UI for real-time statistics
