export enum CurriculumType {
  CCSS = "CCSS",
  ONTARIO = "Ontario",
  ALBERTA = "Alberta",
  BRITISH_COLUMBIA = "British Columbia",
  AUSTRALIA = "Australia",
  UK = "UK"
}

export const CURRICULUM_DISPLAY_NAMES = {
  [CurriculumType.CCSS]: "Common Core (CCSS)",
  [CurriculumType.ONTARIO]: "Ontario",
  [CurriculumType.ALBERTA]: "Alberta",
  [CurriculumType.BRITISH_COLUMBIA]: "British Columbia",
  [CurriculumType.AUSTRALIA]: "Australia",
  [CurriculumType.UK]: "United Kingdom (UK)"
}

// Function to detect curriculum type from common_core field
export function detectCurriculumType(commonCore: string): CurriculumType {
  if (!commonCore) return CurriculumType.CCSS; // Default

  const lowerCase = commonCore.toLowerCase();

  if (lowerCase.includes('ontario')) {
    return CurriculumType.ONTARIO;
  } else if (lowerCase.includes('alberta')) {
    return CurriculumType.ALBERTA;
  } else if (lowerCase.includes('british columbia') || lowerCase.includes('bc')) {
    return CurriculumType.BRITISH_COLUMBIA;
  } else if (lowerCase.includes('australia') || lowerCase.includes('aus')) {
    return CurriculumType.AUSTRALIA;
  } else if (lowerCase.includes('uk') || lowerCase.includes('united kingdom')) {
    return CurriculumType.UK;
  } else if (lowerCase.includes('ccss') || lowerCase.includes('math.content')) {
    return CurriculumType.CCSS;
  }

  // Default to CCSS if can't determine
  return CurriculumType.CCSS;
}

// Function to get display name for curriculum
export function getCurriculumDisplayName(curriculumType: CurriculumType): string {
  return CURRICULUM_DISPLAY_NAMES[curriculumType] || curriculumType;
}
