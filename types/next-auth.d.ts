import NextAuth from "next-auth"

declare module "next-auth" {
  /**
   * Returned by `useSession`, `getSession` and received as a prop on the `SessionProvider` React Context
   */
  interface Session {
    user: {
      /** The user's id. */
      id: string
      /** The user's name. */
      name?: string | null
      /** The user's email address. */
      email?: string | null
      /** The user's image. */
      image?: string | null
      /** The user's role. */
      role?: string | null
      /** The user's school. */
      school?: string | null
      /** The user's school district. */
      schoolDistrict?: string | null
      /** The user's default curriculum. */
      defaultCurriculum?: string | null
      /** The user's default grade. */
      defaultGrade?: string | null
      /** The user's high contrast mode preference. */
      highContrastMode?: boolean | null
    }
  }

  interface User {
    id: string
    name?: string | null
    email?: string | null
    image?: string | null
    role?: string | null
    school?: string | null
    schoolDistrict?: string | null
    defaultCurriculum?: string | null
    defaultGrade?: string | null
    highContrastMode?: boolean | null
  }
}

declare module "next-auth/jwt" {
  /** Returned by the `jwt` callback and `getToken`, when using JWT sessions */
  interface JWT {
    /** The user's id. */
    id: string
    /** The user's role. */
    role?: string | null
    /** The user's school. */
    school?: string | null
    /** The user's school district. */
    schoolDistrict?: string | null
    /** The user's default curriculum. */
    defaultCurriculum?: string | null
    /** The user's default grade. */
    defaultGrade?: string | null
    /** The user's high contrast mode preference. */
    highContrastMode?: boolean | null
  }
}
