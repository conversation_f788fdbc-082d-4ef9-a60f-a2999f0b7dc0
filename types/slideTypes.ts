export enum SlideType {
  INTRODUCTION = "introduction",
  HOOK = "hook",
  TEACH_1 = "teach_1",
  TALK_1 = "talk_1",
  TRY_1 = "try_1",
  TEACH_2 = "teach_2",
  TALK_2 = "talk_2",
  TRY_2 = "try_2",
  PRACTICE = "practice",
  ON_RAMP = "on_ramp",
  PRINTABLES = "printables"
}

export interface SlideData {
  slide_pedagogical_name: string;
  type?: SlideType;
  slide_text_1?: string;
  slide_text_2?: string;
  slide_text_3?: string;
  slide_text_4?: string;
  html_css_description_of_image?: string;
  generated_html_content?: string; // New field for generated HTML content
  script?: string;
  teacher_tips?: {
    general_tip?: string;
    misconception_tip?: string;
  };
  // Fields for talk slides
  slide_q?: string;
  slide_a?: string;
  // Fields for try and practice slides
  q1?: string;
  a1?: string;
  q2?: string;
  a2?: string;
  q3?: string;
  a3?: string;
  q4?: string;
  a4?: string;
  q5?: string;
  a5?: string;
  q6?: string;
  a6?: string;
  q7?: string;
  a7?: string;
  q8?: string;
  a8?: string;
  q9?: string;
  a9?: string;
  q10?: string;
  a10?: string;
  q11?: string;
  a11?: string;
  q12?: string;
  a12?: string;
  // Fields for printables slides
  link_to_lesson_guide_pdf?: string;
  link_to_practice_pdf?: string;
  link_to_accelerator_pdf?: string;
}

export interface SlidePoint {
  title: string;
  content: string;
  isQuestion?: boolean;
  isAnswer?: boolean;
  hasAnswer?: boolean;
  questionIndex?: number;
}

export interface SlideContentProps {
  slideNumber: number;
  slideData?: SlideData;
  title?: string;
  points?: SlidePoint[];
  imageUrl?: string;
  onPointClick?: (index: number) => void;
  currentPointIndex?: number;
  showAllPoints?: boolean;
  slideType?: string;
}
